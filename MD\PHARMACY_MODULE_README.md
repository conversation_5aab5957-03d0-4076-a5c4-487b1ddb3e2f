# Module Pharmacy - POS System GT2

## Vue d'ensemble

Le module **Pharmacy** (Pharmacie) est un module complet de gestion pharmaceutique pour le système POS GT2. Il permet la gestion des médicaments, des ordonnances, des interactions médicamenteuses, du contrôle de conformité et de la gestion des stocks spécialisée pour les pharmacies.

## 🚀 Fonctionnalités Principales

### 1. **Gestion des Médicaments**
- ✅ Catalogue complet des médicaments avec toutes les informations pharmaceutiques
- ✅ Classification par type (prescription, vente libre, stupéfiants, usage hospitalier)
- ✅ Gestion des codes CIP, ATC, code-barres
- ✅ Informations posologiques complètes (adulte, pédiatrique, gériatrique)
- ✅ Contraindications, effets indésirables, mises en garde
- ✅ Conditions de conservation et dates d'expiration
- ✅ Prix et taux de remboursement

### 2. **Gestion des Ordonnances**
- ✅ Création et validation d'ordonnances
- ✅ Informations complètes du médecin prescripteur (nom, RPPS, spécialité)
- ✅ Gestion des statuts (en attente, validée, délivrée, partiellement délivrée)
- ✅ Contrôle des dates de validité
- ✅ Calcul automatique des totaux et remboursements
- ✅ Historique complet des délivrances

### 3. **Interactions Médicamenteuses**
- ✅ Base de données d'interactions avec classification par sévérité
- ✅ Vérification automatique lors des prescriptions
- ✅ Gestion des contre-indications absolues
- ✅ Recommandations de surveillance et de prise en charge
- ✅ Références scientifiques et niveaux de preuve

### 4. **Contrôle de Conformité**
- ✅ Vérification automatique de la validité des ordonnances
- ✅ Contrôle des prescriptions de stupéfiants (RPPS obligatoire)
- ✅ Vérification de la disponibilité des stocks
- ✅ Alerte sur les dates d'expiration
- ✅ Contrôle des allergies des patients

### 5. **Gestion des Stocks Avancée**
- ✅ Suivi des mouvements avec lots et dates d'expiration
- ✅ Alertes de stock faible et péremption
- ✅ Gestion des fournisseurs et coûts d'achat
- ✅ Déduction automatique lors des délivrances
- ✅ Rapports de valorisation des stocks

### 6. **Allergies des Patients**
- ✅ Base de données des allergies par patient
- ✅ Classification par type (médicament, alimentaire, environnemental)
- ✅ Niveaux de sévérité (léger, modéré, sévère)
- ✅ Vérification automatique lors des prescriptions

## 📋 Structure du Module

```
app/modules/pharmacy/
├── __init__.py              # Configuration du blueprint
├── models.py                # Modèles de données SQLAlchemy
├── routes.py                # Routes et API endpoints
├── forms.py                 # Formulaires WTForms
├── utils.py                 # Classes utilitaires et logique métier
└── templates/pharmacy/      # Templates Jinja2
    ├── index.html           # Dashboard principal
    ├── medications/         # Templates médicaments
    │   ├── list.html
    │   ├── form.html
    │   └── detail.html
    ├── prescriptions/       # Templates ordonnances
    │   ├── list.html
    │   ├── form.html
    │   ├── detail.html
    │   └── dispense.html
    ├── interactions/        # Templates interactions
    │   ├── list.html
    │   ├── form.html
    │   └── detail.html
    └── stock/              # Templates gestion stock
        ├── index.html
        └── movements.html
```

## 🗄️ Modèles de Données

### Principales Entités

1. **Medication** : Médicaments avec toutes les informations pharmaceutiques
2. **Prescription** : Ordonnances médicales
3. **PrescriptionItem** : Articles d'une ordonnance
4. **DrugInteraction** : Interactions médicamenteuses
5. **ComplianceCheck** : Contrôles de conformité
6. **MedicationStockMovement** : Mouvements de stock
7. **PatientAllergy** : Allergies des patients

### Énumérations

- **MedicationClass** : `prescription`, `otc`, `controlled`, `hospital_only`
- **PrescriptionStatus** : `pending`, `validated`, `dispensed`, `partially_dispensed`, `cancelled`, `expired`
- **InteractionSeverity** : `minor`, `moderate`, `major`, `contraindicated`

## 🔧 Classes Utilitaires

### PharmacyInteractionChecker
```python
checker = PharmacyInteractionChecker(business_id)
interactions = checker.check_medication_interactions([med1_id, med2_id])
allergies = checker.check_patient_allergies(customer, medication_ids)
```

### PharmacyComplianceChecker
```python
compliance = PharmacyComplianceChecker(business_id)
results = compliance.check_prescription_compliance(prescription)
```

### PharmacyStockManager
```python
stock_manager = PharmacyStockManager(business_id)
stock_manager.add_stock(medication_id, quantity, batch_number, expiry_date)
stock_manager.update_stock_from_prescription(prescription, user_id)
```

### PharmacyReportGenerator
```python
reports = PharmacyReportGenerator(business_id)
daily_report = reports.generate_daily_sales_report()
stock_report = reports.generate_stock_report()
```

## 🌐 Routes Principales

### Interface Web
- `/pharmacy/` - Dashboard principal
- `/pharmacy/medications` - Gestion des médicaments
- `/pharmacy/prescriptions` - Gestion des ordonnances
- `/pharmacy/interactions` - Gestion des interactions
- `/pharmacy/stock` - Gestion des stocks
- `/pharmacy/allergies` - Gestion des allergies

### API Endpoints
- `GET /pharmacy/api/medications/search` - Recherche de médicaments
- `GET /pharmacy/api/customers/search` - Recherche de patients
- `POST /pharmacy/api/prescriptions/{id}/validate` - Validation d'ordonnance
- `POST /pharmacy/api/interactions/check` - Vérification d'interactions
- `GET /pharmacy/api/compliance/check/{id}` - Contrôle de conformité
- `POST /pharmacy/api/stock/{id}/movement` - Mouvement de stock
- `POST /pharmacy/api/quick-dispense` - Délivrance rapide (vente libre)

## 🎯 Workflow Typique

### 1. Réception d'une Ordonnance
1. Créer une nouvelle ordonnance (`/pharmacy/prescriptions/new`)
2. Saisir les informations patient et médecin
3. Ajouter les médicaments prescrits
4. Valider l'ordonnance (vérifications automatiques)

### 2. Délivrance des Médicaments
1. Ouvrir l'ordonnance validée
2. Cliquer sur "Délivrer" (`/pharmacy/prescriptions/{id}/dispense`)
3. Vérifier les interactions et la conformité
4. Ajuster les quantités selon les stocks
5. Confirmer la délivrance (mise à jour automatique des stocks)

### 3. Vente Libre
1. Utiliser l'API de délivrance rapide (`/pharmacy/api/quick-dispense`)
2. Scanner ou rechercher le médicament
3. Vérifier qu'il ne nécessite pas d'ordonnance
4. Confirmer la vente (déduction automatique du stock)

## ⚡ Fonctionnalités Avancées

### Vérifications Automatiques
- **Interactions** : Détection automatique des interactions dangereuses
- **Allergies** : Alerte si médicament incompatible avec les allergies connues
- **Conformité** : Vérification des ordonnances et des prescriptions de stupéfiants
- **Stocks** : Contrôle de disponibilité et alerte péremption

### Rapports et Analyses
- Rapport de ventes quotidien
- Valorisation des stocks
- Top des médicaments vendus
- Analyse des interactions détectées
- Suivi des péremptions

### Intégrations
- Compatible avec le système POS principal
- Intégration avec le module customers pour les patients
- Synchronisation avec le module inventory pour les stocks
- Support des workflows d'approbation

## 🛡️ Sécurité et Conformité

### Réglementaire
- Gestion des codes RPPS pour les stupéfiants
- Respect des durées de validité des ordonnances
- Traçabilité complète des délivrances
- Audit des actions utilisateur

### Sécurité des Données
- Protection CSRF sur toutes les routes
- Authentification requise pour toutes les opérations
- Isolation des données par business
- Logs d'audit automatiques

## 🚀 Installation et Configuration

Le module est automatiquement configuré lors de l'installation du système POS GT2. Il est accessible via :
- URL : `/pharmacy`
- Navigation principale
- Type d'entreprise "Pharmacie" dans les paramètres

## 📊 Tests

Exécuter les tests du module :
```bash
python test_pharmacy.py
```

## 🔄 Intégration avec les Autres Modules

- **Customers** : Gestion des patients et allergies
- **Inventory** : Synchronisation des stocks
- **Sales** : Historique des ventes
- **Staff** : Gestion des pharmaciens et autorisations
- **Reports** : Rapports financiers et analytiques
- **Settings** : Configuration du type d'entreprise pharmacie

---

*Ce module respecte toutes les bonnes pratiques pharmaceutiques et réglementaires françaises. Il est conçu pour être évolutif et s'adapter aux besoins spécifiques de chaque pharmacie.*