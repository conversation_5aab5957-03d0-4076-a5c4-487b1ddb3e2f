{% extends "base.html" %}

{% block title %}Gestion des Ingrédients et Recettes{% endblock %}

{% block content %}
<div class="grid grid-cols-1 gap-6">
    <!-- Header -->
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
            <h1 class="text-2xl font-bold text-slate-100 mb-2">
                <i class="fas fa-carrot text-cyan-500 mr-2"></i>
                Gestion des Ingrédients et Recettes
            </h1>
            <p class="text-slate-400">Gérez vos ingrédients, recettes et coûts de production</p>
        </div>
        <div class="flex flex-wrap gap-2">
            <button type="button" class="px-4 py-2 bg-cyan-600 hover:bg-cyan-500 text-white rounded-lg flex items-center" 
                    onclick="document.getElementById('addIngredientModal').classList.remove('hidden')">
                <i class="fas fa-plus mr-1"></i> Nouvel Ingrédient
            </button>
            <button type="button" class="px-4 py-2 bg-green-600 hover:bg-green-500 text-white rounded-lg flex items-center" 
                    onclick="document.getElementById('addRecipeModal').classList.remove('hidden')">
                <i class="fas fa-utensils mr-1"></i> Nouvelle Recette
            </button>
        </div>
    </div>

    <!-- Navigation Tabs -->
    <div class="border-b border-slate-700">
        <nav class="flex flex-wrap -mb-px">
            <button class="px-4 py-2 font-medium text-cyan-400 border-b-2 border-cyan-400 mr-4" 
                    onclick="showTab('ingredients', event)">Ingrédients</button>
            <button class="px-4 py-2 font-medium text-slate-400 hover:text-slate-300 border-b-2 border-transparent mr-4" 
                    onclick="showTab('recipes', event)">Recettes</button>
            <button class="px-4 py-2 font-medium text-slate-400 hover:text-slate-300 border-b-2 border-transparent mr-4" 
                    onclick="showTab('categories', event)">Catégories</button>
            <button class="px-4 py-2 font-medium text-slate-400 hover:text-slate-300 border-b-2 border-transparent" 
                    onclick="showTab('costs', event)">Coûts & Stocks</button>
        </nav>
    </div>

    <!-- Tab Content -->
    <div class="tab-content">
        <!-- Ingrédients Tab -->
        <div id="tab-ingredients" class="tab-pane active">
            <div class="bg-slate-900 rounded-xl border border-slate-700 p-6">
                <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
                    <h2 class="text-xl font-semibold text-slate-100">Liste des Ingrédients</h2>
                    <div class="flex w-full md:w-auto">
                        <input type="text" class="bg-slate-800 border border-slate-700 rounded-l-lg px-4 py-2 w-full" 
                               placeholder="Rechercher un ingrédient...">
                        <button class="bg-slate-700 hover:bg-slate-600 border border-slate-700 border-l-0 rounded-r-lg px-4 py-2">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-slate-700">
                        <thead>
                            <tr>
                                <th class="px-4 py-3 text-left text-xs font-medium text-slate-400 uppercase tracking-wider">Nom</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-slate-400 uppercase tracking-wider">Catégorie</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-slate-400 uppercase tracking-wider">SKU</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-slate-400 uppercase tracking-wider">Stock</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-slate-400 uppercase tracking-wider">Coût/Unité</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-slate-400 uppercase tracking-wider">Statut</th>
                                <th class="px-4 py-3 text-right text-xs font-medium text-slate-400 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-slate-800">
                            {% for ingredient in ingredients %}
                            <tr class="hover:bg-slate-800">
                                <td class="px-4 py-3 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-8 w-8 bg-cyan-600 rounded-full flex items-center justify-center mr-3">
                                            <i class="fas fa-carrot text-white text-xs"></i>
                                        </div>
                                        <div>
                                            <div class="text-sm font-medium text-slate-100">{{ ingredient.name }}</div>
                                            <div class="text-xs text-slate-400">{{ ingredient.unit }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-4 py-3 whitespace-nowrap">
                                    {% if ingredient.category %}
                                        <span class="px-2 py-1 text-xs rounded-full bg-blue-900 text-blue-300">{{ ingredient.category.name }}</span>
                                    {% else %}
                                        <span class="text-slate-500">-</span>
                                    {% endif %}
                                </td>
                                <td class="px-4 py-3 whitespace-nowrap text-sm text-slate-400">
                                    <code>{{ ingredient.sku }}</code>
                                </td>
                                <td class="px-4 py-3 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <span class="mr-2 text-slate-300">{{ ingredient.current_stock }}</span>
                                        {% if ingredient.current_stock <= ingredient.min_stock_level %}
                                            <span class="px-2 py-1 text-xs rounded-full bg-red-900 text-red-300">Stock Faible</span>
                                        {% elif ingredient.current_stock <= ingredient.min_stock_level * 1.5 %}
                                            <span class="px-2 py-1 text-xs rounded-full bg-yellow-900 text-yellow-300">Attention</span>
                                        {% else %}
                                            <span class="px-2 py-1 text-xs rounded-full bg-green-900 text-green-300">OK</span>
                                        {% endif %}
                                    </div>
                                </td>
                                <td class="px-4 py-3 whitespace-nowrap text-sm text-cyan-400">
                                    {{ "%.2f"|format(ingredient.cost_per_unit_cents / 100) }} €
                                </td>
                                <td class="px-4 py-3 whitespace-nowrap">
                                    {% if ingredient.is_active %}
                                        <span class="px-2 py-1 text-xs rounded-full bg-green-900 text-green-300">Actif</span>
                                    {% else %}
                                        <span class="px-2 py-1 text-xs rounded-full bg-slate-700 text-slate-300">Inactif</span>
                                    {% endif %}
                                </td>
                                <td class="px-4 py-3 whitespace-nowrap text-right text-sm font-medium">
                                    <div class="flex justify-end space-x-1">
                                        <button type="button" class="p-2 bg-slate-700 hover:bg-slate-600 rounded-lg text-blue-400" 
                                                onclick="editIngredient({{ ingredient.id }})">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" class="p-2 bg-slate-700 hover:bg-slate-600 rounded-lg text-cyan-400" 
                                                onclick="viewIngredient({{ ingredient.id }})">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button type="button" class="p-2 bg-slate-700 hover:bg-slate-600 rounded-lg text-red-400" 
                                                onclick="deleteIngredient({{ ingredient.id }})">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Recettes Tab -->
        <div id="tab-recipes" class="tab-pane hidden">
            <div class="bg-slate-900 rounded-xl border border-slate-700 p-6">
                <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
                    <h2 class="text-xl font-semibold text-slate-100">Liste des Recettes</h2>
                    <div class="flex w-full md:w-auto">
                        <input type="text" class="bg-slate-800 border border-slate-700 rounded-l-lg px-4 py-2 w-full" 
                               placeholder="Rechercher une recette...">
                        <button class="bg-slate-700 hover:bg-slate-600 border border-slate-700 border-l-0 rounded-r-lg px-4 py-2">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {% for recipe in recipes %}
                    <div class="bg-slate-800 rounded-lg border border-slate-700 overflow-hidden">
                        <div class="p-4">
                            <div class="flex justify-between items-start mb-2">
                                <h3 class="font-semibold text-slate-100">{{ recipe.name }}</h3>
                                <span class="px-2 py-1 text-xs rounded-full bg-cyan-900 text-cyan-300">{{ recipe.servings }} portions</span>
                            </div>
                            <p class="text-sm text-slate-400 mb-4">{{ recipe.description or "Aucune description" }}</p>
                            
                            <div class="grid grid-cols-3 gap-2 mb-4 text-center">
                                <div>
                                    <div class="text-xs text-slate-400">Préparation</div>
                                    <div class="font-semibold text-slate-200">{{ recipe.preparation_time_minutes }} min</div>
                                </div>
                                <div>
                                    <div class="text-xs text-slate-400">Cuisson</div>
                                    <div class="font-semibold text-slate-200">{{ recipe.cooking_time_minutes }} min</div>
                                </div>
                                <div>
                                    <div class="text-xs text-slate-400">Total</div>
                                    <div class="font-semibold text-slate-200">{{ recipe.preparation_time_minutes + recipe.cooking_time_minutes }} min</div>
                                </div>
                            </div>

                            <div class="flex justify-between items-center">
                                <small class="text-slate-500 text-xs">Produit: {{ recipe.product.name }}</small>
                                <div class="flex space-x-1">
                                    <button type="button" class="p-2 bg-slate-700 hover:bg-slate-600 rounded-lg text-blue-400" 
                                            onclick="editRecipe({{ recipe.id }})">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="p-2 bg-slate-700 hover:bg-slate-600 rounded-lg text-cyan-400" 
                                            onclick="viewRecipe({{ recipe.id }})">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button type="button" class="p-2 bg-slate-700 hover:bg-slate-600 rounded-lg text-red-400" 
                                            onclick="deleteRecipe({{ recipe.id }})">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- Catégories Tab -->
        <div id="tab-categories" class="tab-pane hidden">
            <div class="bg-slate-900 rounded-xl border border-slate-700 p-6">
                <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
                    <h2 class="text-xl font-semibold text-slate-100">Catégories d'Ingrédients</h2>
                    <button type="button" class="px-4 py-2 bg-cyan-600 hover:bg-cyan-500 text-white rounded-lg flex items-center" 
                            onclick="document.getElementById('addCategoryModal').classList.remove('hidden')">
                        <i class="fas fa-plus mr-1"></i> Nouvelle Catégorie
                    </button>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {% for category in categories %}
                    <div class="bg-slate-800 rounded-lg border border-slate-700 p-4 text-center">
                        <div class="w-12 h-12 bg-cyan-600 rounded-full flex items-center justify-center mx-auto mb-3">
                            <i class="fas fa-tags text-white"></i>
                        </div>
                        <h3 class="font-semibold text-slate-100 mb-1">{{ category.name }}</h3>
                        <p class="text-sm text-slate-400 mb-4">{{ category.description or "Aucune description" }}</p>
                        <div class="flex justify-center space-x-2">
                            <button type="button" class="p-2 bg-slate-700 hover:bg-slate-600 rounded-lg text-blue-400" 
                                    onclick="editCategory({{ category.id }})">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button type="button" class="p-2 bg-slate-700 hover:bg-slate-600 rounded-lg text-red-400" 
                                    onclick="deleteCategory({{ category.id }})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- Coûts & Stocks Tab -->
        <div id="tab-costs" class="tab-pane hidden">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                <!-- KPIs -->
                <div class="bg-slate-900 rounded-xl border border-slate-700 p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-dollar-sign text-cyan-400 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-slate-400">Valeur Stock Total</p>
                            <p class="text-2xl font-semibold text-slate-100">{{ "%.2f"|format(total_stock_value / 100) }} €</p>
                        </div>
                    </div>
                </div>

                <div class="bg-slate-900 rounded-xl border border-slate-700 p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-carrot text-green-400 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-slate-400">Ingrédients Actifs</p>
                            <p class="text-2xl font-semibold text-slate-100">{{ active_ingredients_count }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-slate-900 rounded-xl border border-slate-700 p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-triangle text-yellow-400 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-slate-400">Stock Faible</p>
                            <p class="text-2xl font-semibold text-slate-100">{{ low_stock_count }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-slate-900 rounded-xl border border-slate-700 p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-utensils text-cyan-400 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-slate-400">Recettes Actives</p>
                            <p class="text-2xl font-semibold text-slate-100">{{ active_recipes_count }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Graphiques -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div class="bg-slate-900 rounded-xl border border-slate-700 p-6">
                    <h3 class="text-lg font-semibold text-slate-100 mb-4">Répartition par Catégorie</h3>
                    <canvas id="categoryChart" width="400" height="200"></canvas>
                </div>

                <div class="bg-slate-900 rounded-xl border border-slate-700 p-6">
                    <h3 class="text-lg font-semibold text-slate-100 mb-4">Évolution des Coûts</h3>
                    <canvas id="costChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modals -->
{% include 'ingredients/modals/add_ingredient_modal.html' %}
{% include 'ingredients/modals/add_recipe_modal.html' %}
{% include 'ingredients/modals/add_category_modal.html' %}
{% include 'ingredients/modals/edit_ingredient_modal.html' %}
{% include 'ingredients/modals/edit_recipe_modal.html' %}
{% include 'ingredients/modals/edit_category_modal.html' %}
{% include 'ingredients/modals/view_ingredient_modal.html' %}
{% include 'ingredients/modals/view_recipe_modal.html' %}

{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/ingredients.js') }}"></script>
<script>
    // Variables globales pour les graphiques
    var categoryChart = null;
    var costChart = null;
    var activeTab = 'ingredients';
    var chartsInitialized = false;

    // Fonction pour gérer les onglets
    function showTab(tabName, event) {
        // Empêcher le comportement par défaut
        if (event) {
            event.preventDefault();
            event.stopPropagation();
        }

        // Si c'est le même onglet, ne rien faire
        if (activeTab === tabName) {
            return;
        }

        // Masquer tous les onglets
        document.querySelectorAll('.tab-pane').forEach(tab => {
            tab.classList.add('hidden');
        });

        // Afficher l'onglet sélectionné
        const targetTab = document.getElementById('tab-' + tabName);
        if (targetTab) {
            targetTab.classList.remove('hidden');
        }

        // Mettre à jour les styles des boutons d'onglet
        document.querySelectorAll('nav button').forEach(button => {
            button.classList.remove('text-cyan-400', 'border-cyan-400');
            button.classList.add('text-slate-400', 'hover:text-slate-300', 'border-transparent');
        });

        // Mettre en surbrillance le bouton actif
        if (event && event.target) {
            event.target.classList.remove('text-slate-400', 'hover:text-slate-300', 'border-transparent');
            event.target.classList.add('text-cyan-400', 'border-cyan-400');
        }

        // Initialiser les graphiques si l'onglet coûts est sélectionné et pas encore initialisés
        if (tabName === 'costs' && !chartsInitialized) {
            setTimeout(function() {
                initializeCharts();
                chartsInitialized = true;
            }, 100);
        }

        // Mettre à jour l'onglet actif
        activeTab = tabName;
    }

    // Initialisation des graphiques
    function initializeCharts() {
        // Vérifier si Chart.js est disponible
        if (typeof Chart === 'undefined') {
            console.error('Chart.js n\'est pas chargé');
            return;
        }

        // Détruire les graphiques existants s'ils existent
        if (categoryChart) {
            categoryChart.destroy();
            categoryChart = null;
        }
        if (costChart) {
            costChart.destroy();
            costChart = null;
        }

        // Graphique des catégories
        const categoryCtx = document.getElementById('categoryChart');
        if (categoryCtx && categoryCtx.getContext) {
            try {
                categoryChart = new Chart(categoryCtx.getContext('2d'), {
                    type: 'doughnut',
                    data: {
                        labels: {{ category_labels | tojson }},
                        datasets: [{
                            data: {{ category_data | tojson }},
                            backgroundColor: [
                                '#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b',
                                '#858796', '#5a5c69', '#2e59d9', '#17a673', '#2c9faf'
                            ]
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    color: '#e2e8f0',
                                    font: {
                                        size: 12
                                    }
                                }
                            }
                        }
                    }
                });
            } catch (error) {
                console.error('Erreur lors de la création du graphique des catégories:', error);
            }
        }

        // Graphique des coûts
        const costCtx = document.getElementById('costChart');
        if (costCtx && costCtx.getContext) {
            try {
                costChart = new Chart(costCtx.getContext('2d'), {
                    type: 'line',
                    data: {
                        labels: {{ cost_labels | tojson }},
                        datasets: [{
                            label: 'Coût Moyen',
                            data: {{ cost_data | tojson }},
                            borderColor: '#4e73df',
                            backgroundColor: 'rgba(78, 115, 223, 0.1)',
                            tension: 0.4,
                            fill: true
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                grid: {
                                    color: 'rgba(255, 255, 255, 0.1)'
                                },
                                ticks: {
                                    color: '#94a3b8'
                                }
                            },
                            x: {
                                grid: {
                                    color: 'rgba(255, 255, 255, 0.1)'
                                },
                                ticks: {
                                    color: '#94a3b8'
                                }
                            }
                        },
                        plugins: {
                            legend: {
                                labels: {
                                    color: '#e2e8f0',
                                    font: {
                                        size: 12
                                    }
                                }
                            }
                        }
                    }
                });
            } catch (error) {
                console.error('Erreur lors de la création du graphique des coûts:', error);
            }
        }
    }
</script>
{% endblock %}