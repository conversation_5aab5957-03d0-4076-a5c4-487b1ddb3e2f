# Déploiement de l'application POS System

## Prérequis

Avant de déployer l'application, assurez-vous d'avoir installé les éléments suivants :

- Docker et Docker Compose
- Git
- Accès à un serveur Linux (recommandé : Ubuntu 20.04+)

## Architecture de déploiement

L'application utilise une architecture conteneurisée avec les services suivants :

1. **Application Flask** : Le cœur de l'application POS
2. **PostgreSQL** : Base de données principale
3. **Redis** : Cache et stockage de sessions
4. **Nginx** : Reverse proxy et serveur web
5. **Celery** : Traitement des tâches asynchrones
6. **Prometheus** : Monitoring et métriques
7. **Grafana** : Visualisation des métriques

## Déploiement avec Docker Compose

### 1. <PERSON><PERSON><PERSON> le dépôt

```bash
git clone <url-du-depot>
cd pos-system
```

### 2. Configuration des variables d'environnement

Créez un fichier `.env` à la racine du projet :

```env
# Configuration de la base de données
POSTGRES_DB=pos_system
POSTGRES_USER=pos_user
POSTGRES_PASSWORD=your_secure_password

# Configuration de l'application
SECRET_KEY=your-very-secure-secret-key
JWT_SECRET_KEY=your-jwt-secret-key

# Configuration des emails
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=true
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# Configuration de Docker
COMPOSE_PROJECT_NAME=pos_system
```

### 3. Démarrage des services

```bash
# Construire et démarrer tous les services
docker-compose up -d

# Vérifier que tous les services sont en cours d'exécution
docker-compose ps
```

### 4. Initialisation de l'application

```bash
# Exécuter les migrations de la base de données
docker-compose exec app flask db upgrade

# Créer un utilisateur administrateur
docker-compose exec app flask create-admin

# Initialiser les données de base
docker-compose exec app flask init-data
```

## Déploiement en production

### Configuration du reverse proxy (Nginx)

Le fichier `nginx.conf` est configuré pour :

- Servir les fichiers statiques
- Proxy les requêtes vers l'application Flask
- Gérer le HTTPS (à configurer avec vos certificats SSL)
- Appliquer des en-têtes de sécurité

### Configuration SSL

Pour activer HTTPS, vous devez :

1. Obtenir un certificat SSL (par exemple avec Let's Encrypt)
2. Placer les fichiers de certificat dans le répertoire `ssl/`
3. Décommenter la section HTTPS dans `nginx.conf`
4. Redémarrer Nginx

### Sauvegardes automatiques

Le système de sauvegarde est configuré pour :

- Sauvegarder quotidiennement la base de données
- Sauvegarder les fichiers de l'application
- Conserver les sauvegardes pendant 30 jours
- Compresser les sauvegardes pour économiser de l'espace

### Monitoring

Le monitoring est mis en place avec :

- **Prometheus** : Collecte des métriques
- **Grafana** : Visualisation des métriques
- **Alertes** : Notifications en cas de problèmes

## Mise à jour de l'application

### Processus de mise à jour

1. **Sauvegarde** : Effectuer une sauvegarde complète avant la mise à jour
2. **Maintenance** : Activer le mode maintenance si nécessaire
3. **Mise à jour du code** : Tirer la dernière version du dépôt
4. **Migration** : Appliquer les migrations de base de données
5. **Redémarrage** : Redémarrer les services
6. **Vérification** : Vérifier que tout fonctionne correctement

### Commandes de mise à jour

```bash
# 1. Sauvegarde
docker-compose exec backup ./backup.sh

# 2. Mise à jour du code
git pull origin main

# 3. Reconstruction des images
docker-compose build

# 4. Arrêt des services
docker-compose down

# 5. Démarrage des services
docker-compose up -d

# 6. Application des migrations
docker-compose exec app flask db upgrade

# 7. Redémarrage de l'application
docker-compose restart app
```

## Résolution des problèmes

### Problèmes courants

1. **L'application ne démarre pas**
   - Vérifier les logs : `docker-compose logs app`
   - Vérifier la configuration de la base de données
   - Vérifier les variables d'environnement

2. **Problèmes de base de données**
   - Vérifier que PostgreSQL est en cours d'exécution
   - Vérifier les identifiants de connexion
   - Vérifier les permissions de l'utilisateur

3. **Problèmes de cache**
   - Vérifier que Redis est en cours d'exécution
   - Vider le cache si nécessaire : `docker-compose exec cache redis-cli flushall`

### Commandes de diagnostic

```bash
# Vérifier l'état des services
docker-compose ps

# Voir les logs de l'application
docker-compose logs app

# Voir les logs de la base de données
docker-compose logs database

# Voir les logs de Nginx
docker-compose logs nginx

# Exécuter une commande dans le conteneur de l'application
docker-compose exec app bash
```

## Sécurité

### Bonnes pratiques

1. **Mots de passe** : Utiliser des mots de passe forts et uniques
2. **Accès SSH** : Limiter l'accès SSH aux adresses IP autorisées
3. **Pare-feu** : Configurer un pare-feu pour limiter les ports ouverts
4. **Mises à jour** : Maintenir les systèmes à jour avec les derniers correctifs de sécurité
5. **Sauvegardes** : Effectuer des sauvegardes régulières et les tester

### Configuration de sécurité

- Les cookies sont sécurisés en production
- CSRF protection est activée
- Les en-têtes de sécurité sont configurés
- Le rate limiting est mis en place pour prévenir les abus

## Performance

### Optimisations

1. **Cache** : Utilisation de Redis pour le cache d'application
2. **Base de données** : Pooling de connexions et indexation
3. **Compression** : Compression Gzip pour les réponses HTTP
4. **Fichiers statiques** : Mise en cache des fichiers statiques

### Monitoring des performances

- Temps de réponse des requêtes
- Utilisation CPU et mémoire
- Taux d'erreurs
- Cache hit ratio