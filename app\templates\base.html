<!doctype html>
<html lang="fr" class="h-full bg-slate-950">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>{% block title %}GT2 POS{% endblock %}</title>
    <script src="{{ config['TAILWIND_CDN'] }}"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/locale/fr.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <meta name="csrf-token" content="{{ csrf_token() }}" />
    {% block head %}{% endblock %}
  </head>
  <body class="h-full text-slate-100 antialiased">
    {% include 'navbar.html' %}
    <main class="max-w-7xl mx-auto p-4">
      {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
          <div class="space-y-2 mb-4">
            {% for category, message in messages %}
              <div class="rounded border px-3 py-2 text-sm {{ 'border-cyan-600 text-cyan-300' if category=='success' else ('border-yellow-600 text-yellow-300' if category=='info' else 'border-rose-600 text-rose-300') }}">{{ message }}</div>
            {% endfor %}
          </div>
        {% endif %}
      {% endwith %}
      {% block content %}{% endblock %}
    </main>
    <script src="https://cdn.socket.io/4.8.1/socket.io.min.js"></script>
    <script>
      const socket = io();
      socket.on('order_created', (payload) => {
        console.log('Nouvelle commande', payload);
      });
      
      // Initialize moment.js locale
      moment.locale('fr');
    </script>
    {% block scripts %}{% endblock %}
  </body>
  </html>