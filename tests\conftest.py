"""Configuration des tests pour l'application POS"""
import pytest
import os
import sys
from flask import Flask
from app import create_app
from app.extensions import db as _db

# Ajouter le répertoire racine au path pour les imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

@pytest.fixture(scope='session')
def app():
    """Crée une instance de l'application Flask pour les tests"""
    # Créer une application de test
    app = create_app()
    app.config['TESTING'] = True
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
    app.config['WTF_CSRF_ENABLED'] = False
    app.config['CACHE_TYPE'] = 'null'  # Désactiver le cache pendant les tests
    
    with app.app_context():
        yield app

@pytest.fixture(scope='session')
def db(app):
    """Crée la base de données pour les tests"""
    with app.app_context():
        _db.create_all()
        yield _db
        _db.drop_all()

@pytest.fixture(scope='function')
def session(db):
    """Crée une session de base de données pour chaque test"""
    connection = db.engine.connect()
    transaction = connection.begin()
    
    options = dict(bind=connection, binds={})
    session = db.create_scoped_session(options=options)
    
    db.session = session
    
    yield session
    
    transaction.rollback()
    connection.close()
    session.remove()

@pytest.fixture(scope='function')
def client(app):
    """Crée un client de test pour les requêtes HTTP"""
    with app.test_client() as client:
        yield client

@pytest.fixture(scope='function')
def runner(app):
    """Crée un runner de commandes pour les tests CLI"""
    return app.test_cli_runner()

# Fixtures pour les modèles de test
@pytest.fixture(scope='function')
def test_user(session):
    """Crée un utilisateur de test"""
    from app.modules.accounts.models import User
    
    user = User(
        username='testuser',
        email='<EMAIL>',
        first_name='Test',
        last_name='User'
    )
    user.set_password('password123')
    
    session.add(user)
    session.commit()
    
    return user

@pytest.fixture(scope='function')
def test_business(session):
    """Crée une entreprise de test"""
    from app.modules.accounts.models import Business, BusinessType
    
    # Créer un type d'entreprise
    business_type = BusinessType(
        name='Test Business Type',
        description='Type for testing'
    )
    session.add(business_type)
    session.commit()
    
    # Créer une entreprise
    business = Business(
        name='Test Business',
        business_type_id=business_type.id,
        address='123 Test St',
        phone='555-1234',
        email='<EMAIL>'
    )
    session.add(business)
    session.commit()
    
    return business

@pytest.fixture(scope='function')
def test_product(session, test_business):
    """Crée un produit de test"""
    from app.modules.catalog.models import Product, Category
    
    # Créer une catégorie
    category = Category(
        name='Test Category',
        business_id_fk=test_business.id
    )
    session.add(category)
    session.commit()
    
    # Créer un produit
    product = Product(
        sku='TEST001',
        name='Test Product',
        description='A test product',
        category_id_fk=category.id,
        price_cents=1000,  # 10.00€
        cost_cents=500,    # 5.00€
        business_id_fk=test_business.id
    )
    session.add(product)
    session.commit()
    
    return product