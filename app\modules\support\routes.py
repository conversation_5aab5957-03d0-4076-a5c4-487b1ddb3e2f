from flask import render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from sqlalchemy import desc, asc, func, and_, or_
from app.extensions import db
from . import bp
from .forms import (
    SupportTicketForm, EditTicketForm, TicketCommentForm, TicketSearchForm,
    FAQForm, KnowledgeArticleForm, CustomerSatisfactionForm, SupportReportForm
)
from .models import (
    SupportTicket, TicketComment, TicketAttachment, TicketHistory,
    FAQ, KnowledgeArticle, SupportMetrics,
    TicketStatus, TicketPriority, TicketCategory
)
from datetime import datetime, date, timedelta
import uuid


def generate_ticket_number():
    """Génère un numéro de ticket unique"""
    timestamp = datetime.now().strftime('%Y%m%d')
    random_part = str(uuid.uuid4().hex)[:6].upper()
    return f"TK-{timestamp}-{random_part}"


@bp.route("/")
@login_required
def index():
    """Page principale du support - tableau de bord"""
    # Statistiques rapides
    total_tickets = SupportTicket.query.filter_by(business_id_fk=current_user.business_id_fk).count()
    open_tickets = SupportTicket.query.filter_by(
        business_id_fk=current_user.business_id_fk,
        status=TicketStatus.OPEN
    ).count()
    in_progress_tickets = SupportTicket.query.filter_by(
        business_id_fk=current_user.business_id_fk,
        status=TicketStatus.IN_PROGRESS
    ).count()
    resolved_today = SupportTicket.query.filter(
        SupportTicket.business_id_fk == current_user.business_id_fk,
        SupportTicket.status == TicketStatus.RESOLVED,
        func.date(SupportTicket.resolved_at) == date.today()
    ).count()
    
    # Tickets urgents
    urgent_tickets = SupportTicket.query.filter_by(
        business_id_fk=current_user.business_id_fk,
        priority=TicketPriority.URGENT
    ).filter(SupportTicket.status.in_([TicketStatus.OPEN, TicketStatus.IN_PROGRESS])).all()
    
    # Tickets en retard
    overdue_tickets = SupportTicket.query.filter(
        SupportTicket.business_id_fk == current_user.business_id_fk,
        SupportTicket.due_date < datetime.utcnow(),
        SupportTicket.status.in_([TicketStatus.OPEN, TicketStatus.IN_PROGRESS])
    ).all()
    
    # Tickets récents
    recent_tickets = SupportTicket.query.filter_by(
        business_id_fk=current_user.business_id_fk
    ).order_by(desc(SupportTicket.created_at)).limit(10).all()
    
    # Métriques du mois
    start_of_month = date.today().replace(day=1)
    monthly_metrics = {
        'created': SupportTicket.query.filter(
            SupportTicket.business_id_fk == current_user.business_id_fk,
            func.date(SupportTicket.created_at) >= start_of_month
        ).count(),
        'resolved': SupportTicket.query.filter(
            SupportTicket.business_id_fk == current_user.business_id_fk,
            func.date(SupportTicket.resolved_at) >= start_of_month
        ).count()
    }
    
    return render_template(
        'support/index.html',
        total_tickets=total_tickets,
        open_tickets=open_tickets,
        in_progress_tickets=in_progress_tickets,
        resolved_today=resolved_today,
        urgent_tickets=urgent_tickets,
        overdue_tickets=overdue_tickets,
        recent_tickets=recent_tickets,
        monthly_metrics=monthly_metrics
    )


@bp.route("/tickets")
@login_required
def tickets():
    """Liste des tickets de support"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    
    form = TicketSearchForm(request.args)
    
    # Construction de la requête de base
    query = SupportTicket.query.filter_by(business_id_fk=current_user.business_id_fk)
    
    # Filtres de recherche
    if form.search.data:
        search_filter = or_(
            SupportTicket.ticket_number.ilike(f'%{form.search.data}%'),
            SupportTicket.title.ilike(f'%{form.search.data}%'),
            SupportTicket.description.ilike(f'%{form.search.data}%')
        )
        query = query.filter(search_filter)
    
    if form.status.data and form.status.data != 'all':
        query = query.filter(SupportTicket.status == TicketStatus(form.status.data))
    
    if form.category.data and form.category.data != 'all':
        query = query.filter(SupportTicket.category == TicketCategory(form.category.data))
    
    if form.priority.data and form.priority.data != 'all':
        query = query.filter(SupportTicket.priority == TicketPriority(form.priority.data))
    
    if form.assigned_to.data:
        query = query.filter(SupportTicket.assigned_to_id == form.assigned_to.data)
    
    if form.created_by.data:
        query = query.filter(SupportTicket.created_by_id == form.created_by.data)
    
    # Tri par défaut: priorité puis date de création
    query = query.order_by(
        desc(SupportTicket.priority.case([
            (TicketPriority.URGENT, 4),
            (TicketPriority.HIGH, 3),
            (TicketPriority.MEDIUM, 2),
            (TicketPriority.LOW, 1)
        ])),
        desc(SupportTicket.created_at)
    )
    
    # Pagination
    tickets = query.paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return render_template(
        'support/tickets.html',
        tickets=tickets,
        form=form
    )


@bp.route("/tickets/new", methods=['GET', 'POST'])
@login_required
def new_ticket():
    """Créer un nouveau ticket de support"""
    form = SupportTicketForm()
    
    if form.validate_on_submit():
        ticket = SupportTicket(
            business_id_fk=current_user.business_id_fk,
            created_by_id=current_user.id,
            ticket_number=generate_ticket_number(),
            title=form.title.data,
            description=form.description.data,
            category=TicketCategory(form.category.data),
            priority=TicketPriority(form.priority.data),
            steps_to_reproduce=form.steps_to_reproduce.data,
            browser_info=form.browser_info.data,
            error_details=form.error_details.data
        )
        
        # Définir la date d'échéance selon la priorité
        if ticket.priority == TicketPriority.URGENT:
            ticket.due_date = datetime.utcnow() + timedelta(hours=4)
        elif ticket.priority == TicketPriority.HIGH:
            ticket.due_date = datetime.utcnow() + timedelta(days=1)
        elif ticket.priority == TicketPriority.MEDIUM:
            ticket.due_date = datetime.utcnow() + timedelta(days=3)
        else:  # LOW
            ticket.due_date = datetime.utcnow() + timedelta(days=7)
        
        db.session.add(ticket)
        db.session.commit()
        
        # Enregistrer dans l'historique
        history = TicketHistory(
            ticket_id_fk=ticket.id,
            user_id_fk=current_user.id,
            action_type="created",
            comment="Ticket créé"
        )
        db.session.add(history)
        db.session.commit()
        
        flash(f'Ticket {ticket.ticket_number} créé avec succès!', 'success')
        return redirect(url_for('support.view_ticket', id=ticket.id))
    
    return render_template('support/new_ticket.html', form=form)


@bp.route("/tickets/<int:id>")
@login_required
def view_ticket(id):
    """Voir les détails d'un ticket"""
    ticket = SupportTicket.query.filter_by(
        id=id,
        business_id_fk=current_user.business_id_fk
    ).first_or_404()
    
    # Commentaires du ticket
    comments = TicketComment.query.filter_by(
        ticket_id_fk=id
    ).order_by(TicketComment.created_at).all()
    
    # Pièces jointes
    attachments = TicketAttachment.query.filter_by(
        ticket_id_fk=id
    ).all()
    
    # Historique
    history = TicketHistory.query.filter_by(
        ticket_id_fk=id
    ).order_by(desc(TicketHistory.created_at)).all()
    
    # Formulaire pour nouveau commentaire
    comment_form = TicketCommentForm()
    
    return render_template(
        'support/view_ticket.html',
        ticket=ticket,
        comments=comments,
        attachments=attachments,
        history=history,
        comment_form=comment_form
    )


@bp.route("/tickets/<int:id>/edit", methods=['GET', 'POST'])
@login_required
def edit_ticket(id):
    """Modifier un ticket (admin seulement)"""
    ticket = SupportTicket.query.filter_by(
        id=id,
        business_id_fk=current_user.business_id_fk
    ).first_or_404()
    
    form = EditTicketForm(obj=ticket)
    
    # Peupler les choix d'assignation (ici on devrait récupérer les utilisateurs admin/support)
    # form.assigned_to_id.choices = [(0, 'Non assigné')] + [(u.id, u.name) for u in support_users]
    
    if form.validate_on_submit():
        old_status = ticket.status
        old_priority = ticket.priority
        old_assigned = ticket.assigned_to_id
        
        ticket.title = form.title.data
        ticket.description = form.description.data
        ticket.category = TicketCategory(form.category.data)
        ticket.priority = TicketPriority(form.priority.data)
        ticket.status = TicketStatus(form.status.data)
        ticket.assigned_to_id = form.assigned_to_id.data if form.assigned_to_id.data else None
        ticket.estimated_hours = form.estimated_hours.data
        ticket.due_date = form.due_date.data
        ticket.resolution = form.resolution.data
        ticket.is_public = form.is_public.data
        ticket.updated_at = datetime.utcnow()
        
        # Gestion du changement de statut
        if old_status != ticket.status:
            if ticket.status == TicketStatus.RESOLVED:
                ticket.resolved_at = datetime.utcnow()
                if ticket.created_at:
                    ticket.resolution_time_minutes = int(
                        (ticket.resolved_at - ticket.created_at).total_seconds() / 60
                    )
            elif ticket.status == TicketStatus.CLOSED:
                ticket.closed_at = datetime.utcnow()
        
        db.session.commit()
        
        # Enregistrer les changements dans l'historique
        if old_status != ticket.status:
            history = TicketHistory(
                ticket_id_fk=ticket.id,
                user_id_fk=current_user.id,
                action_type="status_change",
                old_value=old_status.value if old_status else None,
                new_value=ticket.status.value
            )
            db.session.add(history)
        
        if old_priority != ticket.priority:
            history = TicketHistory(
                ticket_id_fk=ticket.id,
                user_id_fk=current_user.id,
                action_type="priority_change",
                old_value=old_priority.value if old_priority else None,
                new_value=ticket.priority.value
            )
            db.session.add(history)
        
        if old_assigned != ticket.assigned_to_id:
            history = TicketHistory(
                ticket_id_fk=ticket.id,
                user_id_fk=current_user.id,
                action_type="assignment_change",
                old_value=str(old_assigned) if old_assigned else None,
                new_value=str(ticket.assigned_to_id) if ticket.assigned_to_id else None
            )
            db.session.add(history)
        
        db.session.commit()
        
        flash('Ticket mis à jour avec succès!', 'success')
        return redirect(url_for('support.view_ticket', id=ticket.id))
    
    return render_template('support/edit_ticket.html', form=form, ticket=ticket)


@bp.route("/tickets/<int:id>/comment", methods=['POST'])
@login_required
def add_comment(id):
    """Ajouter un commentaire à un ticket"""
    ticket = SupportTicket.query.filter_by(
        id=id,
        business_id_fk=current_user.business_id_fk
    ).first_or_404()
    
    form = TicketCommentForm()
    
    if form.validate_on_submit():
        comment = TicketComment(
            ticket_id_fk=id,
            user_id_fk=current_user.id,
            comment=form.comment.data,
            is_internal=form.is_internal.data,
            is_solution=form.is_solution.data,
            time_spent_minutes=form.time_spent_minutes.data
        )
        
        db.session.add(comment)
        
        # Mettre à jour le ticket
        ticket.updated_at = datetime.utcnow()
        
        # Si c'est marqué comme solution et le ticket n'est pas résolu
        if form.is_solution.data and ticket.status not in [TicketStatus.RESOLVED, TicketStatus.CLOSED]:
            ticket.status = TicketStatus.RESOLVED
            ticket.resolved_at = datetime.utcnow()
            ticket.resolution = form.comment.data
        
        db.session.commit()
        
        flash('Commentaire ajouté avec succès!', 'success')
    else:
        for field, errors in form.errors.items():
            for error in errors:
                flash(f'Erreur dans {field}: {error}', 'error')
    
    return redirect(url_for('support.view_ticket', id=id))


@bp.route("/faq")
def faq():
    """Page FAQ publique"""
    # FAQ publique pour l'entreprise ou FAQ globale
    faqs = FAQ.query.filter(
        or_(
            FAQ.business_id_fk == getattr(current_user, 'business_id_fk', None),
            FAQ.business_id_fk.is_(None)
        ),
        FAQ.is_published == True
    ).order_by(FAQ.is_featured.desc(), FAQ.display_order, FAQ.title).all()
    
    # Grouper par catégorie
    faq_categories = {}
    for faq in faqs:
        if faq.category not in faq_categories:
            faq_categories[faq.category] = []
        faq_categories[faq.category].append(faq)
    
    return render_template('support/faq.html', faq_categories=faq_categories)


@bp.route("/faq/manage")
@login_required
def manage_faq():
    """Gestion des FAQ (admin)"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    
    faqs = FAQ.query.filter_by(
        business_id_fk=current_user.business_id_fk
    ).order_by(FAQ.category, FAQ.display_order, FAQ.title).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return render_template('support/manage_faq.html', faqs=faqs)


@bp.route("/faq/new", methods=['GET', 'POST'])
@login_required
def new_faq():
    """Créer une nouvelle FAQ"""
    form = FAQForm()
    
    if form.validate_on_submit():
        # Traitement des tags
        tags = []
        if form.tags.data:
            tags = [tag.strip() for tag in form.tags.data.split(',') if tag.strip()]
        
        faq = FAQ(
            business_id_fk=current_user.business_id_fk,
            created_by_id=current_user.id,
            title=form.title.data,
            question=form.question.data,
            answer=form.answer.data,
            category=form.category.data,
            subcategory=form.subcategory.data,
            difficulty_level=form.difficulty_level.data,
            tags=tags,
            display_order=form.display_order.data,
            is_published=form.is_published.data,
            is_featured=form.is_featured.data
        )
        
        db.session.add(faq)
        db.session.commit()
        
        flash('FAQ créée avec succès!', 'success')
        return redirect(url_for('support.manage_faq'))
    
    return render_template('support/new_faq.html', form=form)


@bp.route("/knowledge")
def knowledge_base():
    """Base de connaissances publique"""
    articles = KnowledgeArticle.query.filter(
        or_(
            KnowledgeArticle.business_id_fk == getattr(current_user, 'business_id_fk', None),
            KnowledgeArticle.business_id_fk.is_(None)
        ),
        KnowledgeArticle.is_published == True
    ).order_by(KnowledgeArticle.is_featured.desc(), KnowledgeArticle.created_at.desc()).all()
    
    # Grouper par catégorie
    article_categories = {}
    for article in articles:
        if article.category not in article_categories:
            article_categories[article.category] = []
        article_categories[article.category].append(article)
    
    return render_template('support/knowledge_base.html', article_categories=article_categories)


@bp.route("/reports")
@login_required
def reports():
    """Rapports de support"""
    form = SupportReportForm()
    
    # Données pour le rapport par défaut (résumé)
    today = date.today()
    start_of_month = today.replace(day=1)
    
    summary_data = {
        'total_tickets': SupportTicket.query.filter_by(business_id_fk=current_user.business_id_fk).count(),
        'open_tickets': SupportTicket.query.filter_by(
            business_id_fk=current_user.business_id_fk,
            status=TicketStatus.OPEN
        ).count(),
        'monthly_created': SupportTicket.query.filter(
            SupportTicket.business_id_fk == current_user.business_id_fk,
            func.date(SupportTicket.created_at) >= start_of_month
        ).count(),
        'monthly_resolved': SupportTicket.query.filter(
            SupportTicket.business_id_fk == current_user.business_id_fk,
            func.date(SupportTicket.resolved_at) >= start_of_month
        ).count()
    }
    
    # Tickets par catégorie
    category_stats = db.session.query(
        SupportTicket.category,
        func.count(SupportTicket.id).label('count')
    ).filter(
        SupportTicket.business_id_fk == current_user.business_id_fk
    ).group_by(SupportTicket.category).all()
    
    # Temps de réponse moyen (derniers 30 jours)
    avg_response_time = db.session.query(
        func.avg(
            func.timestampdiff(
                'MINUTE',
                SupportTicket.created_at,
                TicketComment.created_at
            )
        )
    ).join(TicketComment).filter(
        SupportTicket.business_id_fk == current_user.business_id_fk,
        SupportTicket.created_at >= today - timedelta(days=30)
    ).scalar()
    
    return render_template(
        'support/reports.html',
        form=form,
        summary_data=summary_data,
        category_stats=category_stats,
        avg_response_time=avg_response_time
    )


@bp.route("/api/ticket/<int:id>/status", methods=['POST'])
@login_required
def api_change_ticket_status(id):
    """API pour changer le statut d'un ticket"""
    ticket = SupportTicket.query.filter_by(
        id=id,
        business_id_fk=current_user.business_id_fk
    ).first_or_404()
    
    new_status = request.json.get('status')
    comment = request.json.get('comment', '')
    
    if not new_status or new_status not in [s.value for s in TicketStatus]:
        return jsonify({'error': 'Statut invalide'}), 400
    
    try:
        ticket.change_status(TicketStatus(new_status), current_user.id, comment)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': f'Statut changé vers {new_status}',
            'new_status': new_status
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@bp.route("/search")
def search():
    """Recherche globale dans le support"""
    query = request.args.get('q', '')
    
    if not query or len(query) < 3:
        return render_template('support/search.html', query=query, results={})
    
    # Recherche dans les FAQ
    faq_results = FAQ.query.filter(
        or_(
            FAQ.business_id_fk == getattr(current_user, 'business_id_fk', None),
            FAQ.business_id_fk.is_(None)
        ),
        FAQ.is_published == True,
        or_(
            FAQ.title.ilike(f'%{query}%'),
            FAQ.question.ilike(f'%{query}%'),
            FAQ.answer.ilike(f'%{query}%')
        )
    ).all()
    
    # Recherche dans les articles
    article_results = KnowledgeArticle.query.filter(
        or_(
            KnowledgeArticle.business_id_fk == getattr(current_user, 'business_id_fk', None),
            KnowledgeArticle.business_id_fk.is_(None)
        ),
        KnowledgeArticle.is_published == True,
        or_(
            KnowledgeArticle.title.ilike(f'%{query}%'),
            KnowledgeArticle.content.ilike(f'%{query}%')
        )
    ).all()
    
    results = {
        'faq': faq_results,
        'articles': article_results
    }
    
    return render_template('support/search.html', query=query, results=results)