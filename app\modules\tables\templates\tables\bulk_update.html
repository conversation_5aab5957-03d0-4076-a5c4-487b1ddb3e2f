{% extends 'base.html' %}
{% block title %}Mise à jour en lot - Tables{% endblock %}
{% block content %}
<div class="max-w-4xl mx-auto">
  <div class="flex items-center justify-between mb-6">
    <div>
      <h1 class="text-3xl font-bold">Mise à jour en lot</h1>
      <p class="text-slate-400 mt-2">Modifiez plusieurs tables simultanément</p>
    </div>
    <div class="flex space-x-3">
      <a href="{{ url_for('tables.index') }}" class="px-4 py-2 bg-slate-700 hover:bg-slate-600 text-white rounded-lg">
        Retour à la gestion
      </a>
    </div>
  </div>

  <div class="grid grid-cols-12 gap-6">
    <!-- Formulaire de mise à jour --><div class="col-span-12 lg:col-span-6">
      <div class="rounded-xl bg-slate-900 border border-slate-700 p-6">
        <h2 class="text-lg font-semibold mb-4">⚙️ Configuration</h2>
        <form method="post" class="space-y-4">
          {{ form.csrf_token }}
          
          <div>
            <label class="block text-sm font-medium text-slate-300 mb-2">{{ form.table_ids.label.text }}</label>
            {{ form.table_ids(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-4 py-3 text-white", placeholder="Ex: 1,2,3,4") }}
            <p class="mt-1 text-sm text-slate-400">Séparez les IDs par des virgules</p>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-slate-300 mb-2">{{ form.action.label.text }}</label>
            {{ form.action(id="action-select", class="w-full bg-slate-800 border border-slate-700 rounded-lg px-4 py-3 text-white") }}
          </div>
          
          <!-- Champs conditionnels -->
          <div id="target-room-field" class="hidden">
            <label class="block text-sm font-medium text-slate-300 mb-2">{{ form.target_room_id.label.text }}</label>
            {{ form.target_room_id(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-4 py-3 text-white") }}
          </div>
          
          <div id="new-seats-field" class="hidden">
            <label class="block text-sm font-medium text-slate-300 mb-2">{{ form.new_seats.label.text }}</label>
            {{ form.new_seats(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-4 py-3 text-white") }}
          </div>
          
          <div class="flex justify-end space-x-3 pt-6 border-t border-slate-700">
            <a href="{{ url_for('tables.index') }}" class="px-6 py-3 bg-slate-700 hover:bg-slate-600 text-white rounded-lg">
              Annuler
            </a>
            {{ form.submit(class="px-6 py-3 bg-purple-600 hover:bg-purple-500 text-white rounded-lg font-medium") }}
          </div>
        </form>
      </div>
    </div>

    <!-- Guide et exemples -->
    <div class="col-span-12 lg:col-span-6">
      <div class="rounded-xl bg-slate-900 border border-slate-700 p-6">
        <h2 class="text-lg font-semibold mb-4">💡 Guide d'utilisation</h2>
        
        <div class="space-y-4">
          <div class="bg-slate-800 rounded-lg p-4">
            <h3 class="font-medium text-green-400 mb-2">✅ Activer des tables</h3>
            <p class="text-sm text-slate-300">Rend les tables sélectionnées disponibles pour les réservations.</p>
          </div>
          
          <div class="bg-slate-800 rounded-lg p-4">
            <h3 class="font-medium text-red-400 mb-2">❌ Désactiver des tables</h3>
            <p class="text-sm text-slate-300">Met les tables sélectionnées hors service temporairement.</p>
          </div>
          
          <div class="bg-slate-800 rounded-lg p-4">
            <h3 class="font-medium text-blue-400 mb-2">🏠 Déplacer vers une salle</h3>
            <p class="text-sm text-slate-300">Change l'affectation des tables vers une autre salle.</p>
          </div>
          
          <div class="bg-slate-800 rounded-lg p-4">
            <h3 class="font-medium text-purple-400 mb-2">👥 Modifier le nombre de places</h3>
            <p class="text-sm text-slate-300">Met à jour la capacité de toutes les tables sélectionnées.</p>
          </div>
        </div>
        
        <!-- Exemples -->
        <div class="mt-6 p-4 bg-blue-900 border border-blue-700 rounded-lg">
          <h4 class="font-medium text-blue-300 mb-2">🔍 Exemples d'IDs</h4>
          <div class="text-sm text-blue-200 space-y-1">
            <p><code>1,2,3</code> - Tables 1, 2 et 3</p>
            <p><code>5,7,9,12</code> - Tables 5, 7, 9 et 12</p>
            <p><code>1</code> - Seulement la table 1</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const actionSelect = document.getElementById('action-select');
    const targetRoomField = document.getElementById('target-room-field');
    const newSeatsField = document.getElementById('new-seats-field');
    
    function updateFormFields() {
        const action = actionSelect.value;
        
        // Masquer tous les champs conditionnels
        targetRoomField.classList.add('hidden');
        newSeatsField.classList.add('hidden');
        
        // Afficher les champs nécessaires selon l'action
        switch (action) {
            case 'move_room':
                targetRoomField.classList.remove('hidden');
                break;
            case 'update_seats':
                newSeatsField.classList.remove('hidden');
                break;
        }
    }
    
    // Écouteur d'événement pour le changement d'action
    actionSelect.addEventListener('change', updateFormFields);
    
    // Configuration initiale
    updateFormFields();
});
</script>
{% endblock %}