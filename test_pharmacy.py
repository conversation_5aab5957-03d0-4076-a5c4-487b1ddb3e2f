#!/usr/bin/env python3
"""
Script de test pour le module pharmacy
"""

import sys
import os

# Ajouter le répertoire racine au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Tester les imports du module pharmacy"""
    print("🧪 Test des imports du module pharmacy...")
    
    try:
        # Test import des modèles
        from app.modules.pharmacy.models import (
            Medication, Prescription, PrescriptionItem, DrugInteraction,
            ComplianceCheck, MedicationStockMovement, PatientAllergy,
            MedicationClass, PrescriptionStatus, InteractionSeverity
        )
        print("✅ Modèles importés avec succès")
        
        # Test import des formulaires
        from app.modules.pharmacy.forms import (
            MedicationForm, PrescriptionForm, PrescriptionItemForm, DrugInteractionForm,
            ComplianceCheckForm, MedicationStockMovementForm, PatientAllergyForm,
            PharmacySearchForm, QuickDispenseForm
        )
        print("✅ Formulaires importés avec succès")
        
        # Test import des utilitaires
        from app.modules.pharmacy.utils import (
            PharmacyInteractionChecker, PharmacyComplianceChecker, 
            PharmacyStockManager, PharmacyReportGenerator
        )
        print("✅ Utilitaires importés avec succès")
        
        # Test import du blueprint
        from app.modules.pharmacy import bp
        print("✅ Blueprint importé avec succès")
        
        return True
        
    except ImportError as e:
        print(f"❌ Erreur d'import: {e}")
        return False

def test_enums():
    """Tester les énumérations"""
    print("🧪 Test des énumérations...")
    
    try:
        from app.modules.pharmacy.models import MedicationClass, PrescriptionStatus, InteractionSeverity
        
        # Test MedicationClass
        assert MedicationClass.PRESCRIPTION.value == "prescription"
        assert MedicationClass.OTC.value == "otc"
        assert MedicationClass.CONTROLLED.value == "controlled"
        assert MedicationClass.HOSPITAL_ONLY.value == "hospital_only"
        print("✅ MedicationClass OK")
        
        # Test PrescriptionStatus
        assert PrescriptionStatus.PENDING.value == "pending"
        assert PrescriptionStatus.VALIDATED.value == "validated"
        assert PrescriptionStatus.DISPENSED.value == "dispensed"
        print("✅ PrescriptionStatus OK")
        
        # Test InteractionSeverity
        assert InteractionSeverity.MINOR.value == "minor"
        assert InteractionSeverity.MODERATE.value == "moderate"
        assert InteractionSeverity.MAJOR.value == "major"
        assert InteractionSeverity.CONTRAINDICATED.value == "contraindicated"
        print("✅ InteractionSeverity OK")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur dans les énumérations: {e}")
        return False

def test_utils_classes():
    """Tester les classes utilitaires"""
    print("🧪 Test des classes utilitaires...")
    
    try:
        from app.modules.pharmacy.utils import (
            PharmacyInteractionChecker, PharmacyComplianceChecker, 
            PharmacyStockManager, PharmacyReportGenerator,
            InteractionResult, ComplianceResult
        )
        
        # Test d'instanciation
        business_id = 1
        
        interaction_checker = PharmacyInteractionChecker(business_id)
        assert interaction_checker.business_id == business_id
        print("✅ PharmacyInteractionChecker OK")
        
        compliance_checker = PharmacyComplianceChecker(business_id)
        assert compliance_checker.business_id == business_id
        print("✅ PharmacyComplianceChecker OK")
        
        stock_manager = PharmacyStockManager(business_id)
        assert stock_manager.business_id == business_id
        print("✅ PharmacyStockManager OK")
        
        report_generator = PharmacyReportGenerator(business_id)
        assert report_generator.business_id == business_id
        print("✅ PharmacyReportGenerator OK")
        
        # Test des dataclasses
        from app.modules.pharmacy.models import InteractionSeverity
        interaction_result = InteractionResult(
            has_interaction=True,
            severity=InteractionSeverity.MODERATE,
            description="Test interaction",
            recommendations="Test recommendations",
            contraindicated=False,
            monitoring_required=True
        )
        assert interaction_result.has_interaction == True
        print("✅ InteractionResult OK")
        
        compliance_result = ComplianceResult(
            passed=False,
            check_type="test_check",
            severity_level="warning",
            description="Test compliance"
        )
        assert compliance_result.passed == False
        print("✅ ComplianceResult OK")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur dans les classes utilitaires: {e}")
        return False

def main():
    """Fonction principale de test"""
    print("🏥 Tests du module Pharmacy")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_enums,
        test_utils_classes
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"📊 Résultats: {passed}/{total} tests réussis")
    
    if passed == total:
        print("🎉 Tous les tests sont passés ! Le module pharmacy est prêt.")
        return 0
    else:
        print("❌ Certains tests ont échoué. Veuillez vérifier les erreurs.")
        return 1

if __name__ == "__main__":
    sys.exit(main())