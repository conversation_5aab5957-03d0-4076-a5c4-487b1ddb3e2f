{% extends 'base.html' %}
{% block title %}Commande {{ purchase_order.order_number }}{% endblock %}

{% block content %}
<div class=\"max-w-6xl mx-auto p-6\">
    <!-- En-tête -->
    <div class=\"flex items-center justify-between mb-6\">
        <div>
            <h1 class=\"text-3xl font-bold text-gray-900\">
                Bon de Commande {{ purchase_order.order_number }}
            </h1>
            <p class=\"text-gray-600 mt-2\">
                <span class=\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                     {% if purchase_order.status.value == 'draft' %}bg-yellow-100 text-yellow-800
                     {% elif purchase_order.status.value == 'approved' %}bg-green-100 text-green-800
                     {% elif purchase_order.status.value == 'sent' %}bg-blue-100 text-blue-800
                     {% elif purchase_order.status.value == 'received' %}bg-purple-100 text-purple-800
                     {% else %}bg-gray-100 text-gray-800{% endif %}\">
                    {{ purchase_order.status.value.title() }}
                </span>
            </p>
        </div>
        
        <div class=\"flex space-x-3\">
            {% if purchase_order.status.value == 'draft' %}
            <form method=\"post\" action=\"{{ url_for('purchasing.approve_purchase_order', id=purchase_order.id) }}\" class=\"inline\">
                <button type=\"submit\" class=\"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg\">
                    <i class=\"fas fa-check mr-2\"></i>Approuver
                </button>
            </form>
            {% endif %}
            
            <a href=\"{{ url_for('purchasing.edit_purchase_order', id=purchase_order.id) }}\" 
               class=\"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg\">
                <i class=\"fas fa-edit mr-2\"></i>Modifier
            </a>
            
            <a href=\"{{ url_for('purchasing.purchase_orders') }}\" 
               class=\"bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg\">
                <i class=\"fas fa-arrow-left mr-2\"></i>Retour
            </a>
        </div>
    </div>

    <div class=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">
        <!-- Informations principales -->
        <div class=\"lg:col-span-2 space-y-6\">
            <!-- Détails de la commande -->
            <div class=\"bg-white rounded-lg shadow p-6\">
                <h2 class=\"text-lg font-semibold mb-4\">Détails de la commande</h2>
                
                <div class=\"grid grid-cols-2 gap-4\">
                    <div>
                        <label class=\"block text-sm font-medium text-gray-700\">Fournisseur</label>
                        <p class=\"mt-1 text-sm text-gray-900\">{{ purchase_order.supplier.name if purchase_order.supplier else 'N/A' }}</p>
                    </div>
                    
                    <div>
                        <label class=\"block text-sm font-medium text-gray-700\">Date de commande</label>
                        <p class=\"mt-1 text-sm text-gray-900\">{{ purchase_order.order_date.strftime('%d/%m/%Y') if purchase_order.order_date else 'N/A' }}</p>
                    </div>
                    
                    <div>
                        <label class=\"block text-sm font-medium text-gray-700\">Livraison prévue</label>
                        <p class=\"mt-1 text-sm text-gray-900\">{{ purchase_order.expected_delivery_date.strftime('%d/%m/%Y') if purchase_order.expected_delivery_date else 'Non définie' }}</p>
                    </div>
                    
                    <div>
                        <label class=\"block text-sm font-medium text-gray-700\">Référence</label>
                        <p class=\"mt-1 text-sm text-gray-900\">{{ purchase_order.reference or 'N/A' }}</p>
                    </div>
                </div>
                
                {% if purchase_order.notes %}
                <div class=\"mt-4\">
                    <label class=\"block text-sm font-medium text-gray-700\">Notes</label>
                    <p class=\"mt-1 text-sm text-gray-900\">{{ purchase_order.notes }}</p>
                </div>
                {% endif %}
            </div>

            <!-- Articles -->
            <div class=\"bg-white rounded-lg shadow p-6\">
                <h2 class=\"text-lg font-semibold mb-4\">Articles commandés</h2>
                
                {% if purchase_order.items %}
                <div class=\"overflow-x-auto\">
                    <table class=\"min-w-full divide-y divide-gray-200\">
                        <thead class=\"bg-gray-50\">
                            <tr>
                                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">
                                    Produit
                                </th>
                                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">
                                    Quantité
                                </th>
                                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">
                                    Prix unitaire
                                </th>
                                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">
                                    Total
                                </th>
                                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">
                                    Statut
                                </th>
                            </tr>
                        </thead>
                        <tbody class=\"bg-white divide-y divide-gray-200\">
                            {% for item in purchase_order.items %}
                            <tr>
                                <td class=\"px-6 py-4 whitespace-nowrap\">
                                    <div class=\"text-sm font-medium text-gray-900\">{{ item.product_name }}</div>
                                    {% if item.supplier_reference %}
                                    <div class=\"text-sm text-gray-500\">Réf: {{ item.supplier_reference }}</div>
                                    {% endif %}
                                </td>
                                <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">
                                    {{ item.quantity }} {{ item.unit_of_measure }}
                                </td>
                                <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">
                                    {{ \"%.2f\"|format(item.unit_cost) }}€
                                </td>
                                <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">
                                    {{ \"%.2f\"|format(item.total) }}€
                                </td>
                                <td class=\"px-6 py-4 whitespace-nowrap\">
                                    {% if item.quantity_received >= item.quantity %}
                                    <span class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\">
                                        Reçu
                                    </span>
                                    {% elif item.quantity_received > 0 %}
                                    <span class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800\">
                                        Partiel ({{ item.quantity_received }}/{{ item.quantity }})
                                    </span>
                                    {% else %}
                                    <span class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800\">
                                        En attente
                                    </span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class=\"text-gray-500 text-center py-4\">Aucun article dans cette commande</p>
                {% endif %}
            </div>
        </div>

        <!-- Résumé financier et actions -->
        <div class=\"space-y-6\">
            <!-- Totaux -->
            <div class=\"bg-white rounded-lg shadow p-6\">
                <h2 class=\"text-lg font-semibold mb-4\">Résumé financier</h2>
                
                <div class=\"space-y-3\">
                    <div class=\"flex justify-between\">
                        <span class=\"text-sm text-gray-600\">Sous-total:</span>
                        <span class=\"text-sm font-medium\">{{ \"%.2f\"|format(purchase_order.subtotal) }}€</span>
                    </div>
                    
                    {% if purchase_order.discount > 0 %}
                    <div class=\"flex justify-between\">
                        <span class=\"text-sm text-gray-600\">Remise:</span>
                        <span class=\"text-sm font-medium text-green-600\">-{{ \"%.2f\"|format(purchase_order.discount) }}€</span>
                    </div>
                    {% endif %}
                    
                    {% if purchase_order.tax > 0 %}
                    <div class=\"flex justify-between\">
                        <span class=\"text-sm text-gray-600\">Taxes:</span>
                        <span class=\"text-sm font-medium\">{{ \"%.2f\"|format(purchase_order.tax) }}€</span>
                    </div>
                    {% endif %}
                    
                    {% if purchase_order.shipping > 0 %}
                    <div class=\"flex justify-between\">
                        <span class=\"text-sm text-gray-600\">Frais de port:</span>
                        <span class=\"text-sm font-medium\">{{ \"%.2f\"|format(purchase_order.shipping) }}€</span>
                    </div>
                    {% endif %}
                    
                    <div class=\"border-t pt-3\">
                        <div class=\"flex justify-between\">
                            <span class=\"text-base font-semibold\">Total:</span>
                            <span class=\"text-base font-bold text-blue-600\">{{ \"%.2f\"|format(purchase_order.total) }}€</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Progression -->
            <div class=\"bg-white rounded-lg shadow p-6\">
                <h2 class=\"text-lg font-semibold mb-4\">Progression</h2>
                
                <div class=\"space-y-4\">
                    <div>
                        <div class=\"flex justify-between text-sm\">
                            <span>Articles reçus</span>
                            <span>{{ \"%.1f\"|format(purchase_order.received_percentage) }}%</span>
                        </div>
                        <div class=\"w-full bg-gray-200 rounded-full h-2 mt-1\">
                            <div class=\"bg-blue-600 h-2 rounded-full\" style=\"width: {{ purchase_order.received_percentage }}%\"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Réceptions -->
            {% if purchase_order.receipts %}
            <div class=\"bg-white rounded-lg shadow p-6\">
                <h2 class=\"text-lg font-semibold mb-4\">Réceptions</h2>
                
                <div class=\"space-y-3\">
                    {% for receipt in purchase_order.receipts %}
                    <div class=\"border rounded-lg p-3\">
                        <div class=\"flex items-center justify-between\">
                            <div>
                                <div class=\"font-medium text-sm\">{{ receipt.receipt_number }}</div>
                                <div class=\"text-xs text-gray-500\">{{ receipt.receipt_date.strftime('%d/%m/%Y') if receipt.receipt_date else 'N/A' }}</div>
                            </div>
                            <a href=\"{{ url_for('purchasing.view_purchase_receipt', id=receipt.id) }}\" 
                               class=\"text-blue-600 hover:text-blue-800\">
                                <i class=\"fas fa-eye\"></i>
                            </a>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}