{% extends 'base.html' %}
{% block title %}Paiements - Dashboard{% endblock %}

{% block head %}
<style>
    .payments-dashboard {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        min-height: 100vh;
        padding: 20px 0;
    }
    
    .stat-card {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 15px;
        padding: 30px;
        text-align: center;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        transition: transform 0.3s ease;
    }
    
    .stat-card:hover {
        transform: translateY(-5px);
    }
    
    .stat-number {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 10px;
    }
    
    .stat-label {
        color: #6c757d;
        font-size: 1.1rem;
        margin-bottom: 15px;
    }
    
    .stat-icon {
        font-size: 3rem;
        margin-bottom: 15px;
        opacity: 0.7;
    }
    
    .text-revenue { color: #28a745; }
    .text-methods { color: #17a2b8; }
    .text-pending { color: #ffc107; }
    .text-cash { color: #6f42c1; }
    
    .payments-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        margin-bottom: 25px;
    }
    
    .quick-actions {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-top: 20px;
    }
    
    .action-btn {
        background: linear-gradient(45deg, #28a745, #20c997);
        color: white;
        padding: 15px 20px;
        border-radius: 10px;
        text-decoration: none;
        text-align: center;
        transition: all 0.3s ease;
        border: none;
        font-size: 1rem;
    }
    
    .action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        color: white;
        text-decoration: none;
    }
    
    .payment-item {
        border-left: 4px solid #28a745;
        padding: 15px;
        margin-bottom: 10px;
        background: #f8f9fa;
        border-radius: 0 8px 8px 0;
    }
    
    .cash-session-card {
        background: linear-gradient(45deg, #6f42c1, #e83e8c);
        color: white;
        padding: 20px;
        border-radius: 12px;
        margin-bottom: 20px;
    }
    
    .cash-session-inactive {
        background: #6c757d;
    }
    
    .status-badge {
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 500;
    }
    
    .status-completed { background: #d4edda; color: #155724; }
    .status-pending { background: #fff3cd; color: #856404; }
    .status-failed { background: #f8d7da; color: #721c24; }
    .status-refunded { background: #d1ecf1; color: #0c5460; }
</style>
{% endblock %}

{% block content %}
<div class="payments-dashboard">
    <div class="grid grid-cols-12 gap-6">
        <!-- En-tête -->
        <div class="col-span-12">
            <div class="payments-card">
                <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                    <div>
                        <h1 class="text-2xl font-bold text-slate-800 mb-1">💳 Paiements - Dashboard</h1>
                        <p class="text-slate-600">Gestion des paiements et encaissements</p>
                    </div>
                    <div class="text-right">
                        <div class="text-slate-600" id="current-datetime"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Session de caisse -->
        <div class="col-span-12">
            {% if active_session %}
            <div class="cash-session-card">
                <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                    <div>
                        <h5 class="text-lg font-semibold mb-1">💰 Session de caisse active</h5>
                        <p class="mb-1">{{ active_session.session_number }} - Ouverte le {{ active_session.opened_at.strftime('%d/%m/%Y à %H:%M') }}</p>
                        <small class="text-sm">Solde d'ouverture: {{ "%.2f"|format(active_session.opening_balance_euros) }}€</small>
                    </div>
                    <div class="text-right">
                        <a href="{{ url_for('payments.close_cash_session', session_id=active_session.id) }}" class="bg-white text-gray-800 hover:bg-gray-100 rounded-lg px-4 py-2 text-sm transition-colors">
                            Fermer session
                        </a>
                    </div>
                </div>
            </div>
            {% else %}
            <div class="cash-session-card cash-session-inactive">
                <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                    <div>
                        <h5 class="text-lg font-semibold mb-1">💰 Aucune session de caisse active</h5>
                        <p class="mb-1">Ouvrez une session pour commencer à encaisser</p>
                    </div>
                    <div>
                        <a href="{{ url_for('payments.new_cash_session') }}" class="bg-white text-gray-800 hover:bg-gray-100 rounded-lg px-4 py-2 text-sm transition-colors">
                            Ouvrir session
                        </a>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Statistiques principales -->
        <div class="col-span-12">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                <div class="stat-card">
                    <div class="stat-icon text-revenue">💰</div>
                    <div class="stat-number text-revenue">{{ "%.2f"|format(daily_total / 100) }}€</div>
                    <div class="stat-label">Chiffre d'affaires du jour</div>
                    <a href="{{ url_for('payments.payments') }}" class="bg-green-600 hover:bg-green-500 rounded-lg px-4 py-2 text-white text-sm transition-colors inline-block">
                        Voir détail
                    </a>
                </div>
                <div class="stat-card">
                    <div class="stat-icon text-methods">💳</div>
                    <div class="stat-number text-methods">{{ active_methods }}</div>
                    <div class="stat-label">Méthodes actives</div>
                    <a href="{{ url_for('payments.payment_methods') }}" class="bg-cyan-600 hover:bg-cyan-500 rounded-lg px-4 py-2 text-white text-sm transition-colors inline-block">
                        Gérer
                    </a>
                </div>
                <div class="stat-card">
                    <div class="stat-icon text-pending">⏳</div>
                    <div class="stat-number text-pending">{{ pending_payments }}</div>
                    <div class="stat-label">Paiements en attente</div>
                    <a href="{{ url_for('payments.payments') }}?status=pending" class="bg-amber-600 hover:bg-amber-500 rounded-lg px-4 py-2 text-white text-sm transition-colors inline-block">
                        Traiter
                    </a>
                </div>
                <div class="stat-card">
                    <div class="stat-icon text-cash">🏪</div>
                    <div class="stat-number text-cash">{{ '✓' if active_session else '✗' }}</div>
                    <div class="stat-label">Session caisse</div>
                    <a href="{{ url_for('payments.cash_sessions') }}" class="bg-gray-600 hover:bg-gray-500 rounded-lg px-4 py-2 text-white text-sm transition-colors inline-block">
                        Historique
                    </a>
                </div>
            </div>
        </div>

        <!-- Actions rapides -->
        <div class="col-span-12">
            <div class="payments-card">
                <h4 class="text-xl font-bold text-slate-800 mb-3">🚀 Actions rapides</h4>
                <div class="quick-actions">
                    <a href="{{ url_for('payments.new_payment') }}" class="action-btn">
                        💳 Nouveau paiement
                    </a>
                    <a href="{{ url_for('payments.payment_methods') }}" class="action-btn">
                        🛠️ Méthodes de paiement
                    </a>
                    <a href="{{ url_for('payments.cash_sessions') }}" class="action-btn">
                        🏪 Sessions de caisse
                    </a>
                    <a href="{{ url_for('payments.refunds') }}" class="action-btn">
                        💸 Remboursements
                    </a>
                </div>
            </div>
        </div>

        <!-- Paiements récents -->
        <div class="col-span-12">
            <div class="payments-card">
                <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-4">
                    <h4 class="text-xl font-bold text-slate-800">🧾 Paiements récents</h4>
                    <a href="{{ url_for('payments.payments') }}" class="text-cyan-600 hover:text-cyan-500 text-sm">
                        Voir tout →
                    </a>
                </div>
                
                {% if recent_payments %}
                <div class="space-y-3">
                    {% for payment in recent_payments %}
                    <div class="payment-item">
                        <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
                            <div>
                                <div class="font-semibold">{{ payment.receipt_number }}</div>
                                <div class="text-sm text-slate-600">
                                    {{ payment.created_at.strftime('%d/%m/%Y %H:%M') }}
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="font-semibold">{{ "%.2f"|format(payment.amount_euros) }}€</div>
                                <div class="text-sm">
                                    <span class="status-badge status-{{ payment.status.value }}">
                                        {{ payment.status.value|capitalize }}
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-2 mt-2 text-sm">
                            <div>
                                <span class="text-slate-600">Méthode:</span> 
                                <span class="font-medium">{{ payment.payment_method.name }}</span>
                            </div>
                            {% if payment.order %}
                            <div>
                                <span class="text-slate-600">Commande:</span> 
                                <span class="font-medium">#{{ payment.order.id }}</span>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-8 text-slate-500">
                    <div class="text-4xl mb-2">📭</div>
                    <p>Aucun paiement récent</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Update datetime every second
    function updateDateTime() {
        const now = moment();
        document.getElementById('current-datetime').textContent = now.format('DD/MM/YYYY HH:mm:ss');
    }
    
    // Initial update
    updateDateTime();
    
    // Update every second
    setInterval(updateDateTime, 1000);
</script>
{% endblock %}