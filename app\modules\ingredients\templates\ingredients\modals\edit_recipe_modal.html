<!-- Modal Édition Recette -->
<div id="editRecipeModal" class="hidden fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <!-- Fond noir transparent -->
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
            <div class="absolute inset-0 bg-gray-900 opacity-75" onclick="document.getElementById('editRecipeModal').classList.add('hidden')"></div>
        </div>

        <!-- Contenu du modal -->
        <div class="inline-block align-bottom bg-slate-900 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-6xl sm:w-full border border-slate-700">
            <div class="bg-green-600 px-4 py-3 border-b border-slate-700">
                <h3 class="text-lg leading-6 font-medium text-white">
                    <i class="fas fa-utensils mr-2"></i>Modifier la Recette
                </h3>
            </div>
            <form id="editRecipeForm" method="POST" action="{{ url_for('ingredients.update_recipe', id=0) }}" class="px-4 py-5 sm:p-6">
                <input type="hidden" id="editRecipeId" name="id">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Informations de base de la recette -->
                    <div>
                        <h4 class="text-lg font-medium text-cyan-400 mb-4">Informations de la Recette</h4>
                        
                        <div class="mb-4">
                            <label for="editRecipeName" class="block text-sm font-medium text-slate-300 mb-1">Nom de la recette *</label>
                            <input type="text" id="editRecipeName" name="name" required
                                   class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 text-slate-100 focus:outline-none focus:ring-2 focus:ring-green-500">
                        </div>
                        
                        <div class="mb-4">
                            <label for="editRecipeProduct" class="block text-sm font-medium text-slate-300 mb-1">Produit associé *</label>
                            <select id="editRecipeProduct" name="product_id" required
                                    class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 text-slate-100 focus:outline-none focus:ring-2 focus:ring-green-500">
                                <option value="">Sélectionner un produit</option>
                                {% for product in products %}
                                <option value="{{ product.id }}">{{ product.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="mb-4">
                            <label for="editRecipeDescription" class="block text-sm font-medium text-slate-300 mb-1">Description</label>
                            <textarea id="editRecipeDescription" name="description" rows="3"
                                      class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 text-slate-100 focus:outline-none focus:ring-2 focus:ring-green-500"
                                      placeholder="Description de la recette, instructions spéciales..."></textarea>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label for="editRecipeServings" class="block text-sm font-medium text-slate-300 mb-1">Nombre de portions</label>
                                <input type="number" id="editRecipeServings" name="servings" min="1"
                                       class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 text-slate-100 focus:outline-none focus:ring-2 focus:ring-green-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-slate-300 mb-1">Statut</label>
                                <div class="flex items-center">
                                    <input type="checkbox" id="editRecipeActive" name="is_active"
                                           class="h-4 w-4 text-green-600 focus:ring-green-500 border-slate-700 rounded bg-slate-800">
                                    <label for="editRecipeActive" class="ml-2 block text-sm text-slate-300">
                                        Recette active
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Temps de préparation -->
                    <div>
                        <h4 class="text-lg font-medium text-cyan-400 mb-4">Temps de Préparation</h4>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label for="editRecipePrepTime" class="block text-sm font-medium text-slate-300 mb-1">Temps de préparation (min)</label>
                                <input type="number" id="editRecipePrepTime" name="preparation_time_minutes" min="0"
                                       class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 text-slate-100 focus:outline-none focus:ring-2 focus:ring-green-500">
                            </div>
                            <div>
                                <label for="editRecipeCookTime" class="block text-sm font-medium text-slate-300 mb-1">Temps de cuisson (min)</label>
                                <input type="number" id="editRecipeCookTime" name="cooking_time_minutes" min="0"
                                       class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 text-slate-100 focus:outline-none focus:ring-2 focus:ring-green-500">
                            </div>
                        </div>
                        
                        <div class="bg-blue-900/30 border border-blue-800 rounded-lg p-3 mb-4">
                            <div class="flex items-center">
                                <i class="fas fa-info-circle text-blue-400 mr-2"></i>
                                <div>
                                    <strong class="text-blue-300">Temps total estimé:</strong>
                                    <span id="editTotalTime" class="text-blue-200">0</span> minutes
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <label for="editRecipeNotes" class="block text-sm font-medium text-slate-300 mb-1">Notes de préparation</label>
                            <textarea id="editRecipeNotes" name="notes" rows="3"
                                      class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 text-slate-100 focus:outline-none focus:ring-2 focus:ring-green-500"
                                      placeholder="Notes spéciales, conseils de préparation..."></textarea>
                        </div>
                    </div>
                </div>
                
                <div class="mt-6 flex justify-end space-x-3">
                    <button type="button" 
                            class="px-4 py-2 border border-slate-700 rounded-lg text-slate-300 hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-green-500"
                            onclick="document.getElementById('editRecipeModal').classList.add('hidden')">
                        <i class="fas fa-times mr-1"></i>Annuler
                    </button>
                    <button type="submit"
                            class="px-4 py-2 bg-green-600 rounded-lg text-white hover:bg-green-500 focus:outline-none focus:ring-2 focus:ring-green-500">
                        <i class="fas fa-save mr-1"></i>Enregistrer la Recette
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Calcul du temps total
    function updateTotalTime() {
        const prepTime = parseInt(document.getElementById('editRecipePrepTime').value) || 0;
        const cookTime = parseInt(document.getElementById('editRecipeCookTime').value) || 0;
        const totalTime = prepTime + cookTime;
        document.getElementById('editTotalTime').textContent = totalTime;
    }
    
    // Ajouter les event listeners
    document.getElementById('editRecipePrepTime').addEventListener('input', updateTotalTime);
    document.getElementById('editRecipeCookTime').addEventListener('input', updateTotalTime);
    
    // Validation du formulaire
    const form = document.getElementById('editRecipeForm');
    form.addEventListener('submit', function(e) {
        if (!form.checkValidity()) {
            e.preventDefault();
            e.stopPropagation();
        }
        form.classList.add('was-validated');
    });
});
</script>