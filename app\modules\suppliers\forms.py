from flask_wtf import FlaskForm
from wtforms import StringField, IntegerField, BooleanField, SelectField, SubmitField, DateField, TextAreaField, DecimalField, HiddenField, FloatField
from wtforms.validators import DataRequired, Length, Optional, Email, NumberRange, Regexp, URL
from datetime import date


class SupplierForm(FlaskForm):
    """Formulaire pour créer un fournisseur"""
    supplier_type = SelectField("Type de fournisseur", choices=[
        ("company", "Entreprise"),
        ("individual", "Particulier")
    ], validators=[DataRequired()])
    
    name = StringField("Nom du fournisseur", validators=[
        DataRequired(message='Le nom du fournisseur est requis'),
        Length(min=1, max=255, message='Le nom doit contenir entre 1 et 255 caractères')
    ])
    legal_name = StringField("Raison sociale", validators=[
        Optional(),
        Length(max=255, message='La raison sociale ne peut pas dépasser 255 caractères')
    ])
    supplier_code = StringField("Code fournisseur", validators=[
        Optional(),
        Length(max=20, message='Le code fournisseur ne peut pas dépasser 20 caractères'),
        Regexp(r'^[A-Z0-9]*$', message='Le code fournisseur ne peut contenir que des lettres majuscules et des chiffres')
    ])
    industry = StringField("Secteur d'activité", validators=[
        Optional(),
        Length(max=100, message='Le secteur d\'activité ne peut pas dépasser 100 caractères')
    ])
    
    # Informations légales
    siret = StringField("SIRET", validators=[
        Optional(),
        Length(min=14, max=14, message='Le SIRET doit contenir exactement 14 chiffres'),
        Regexp(r'^\d{14}$', message='Le SIRET ne peut contenir que des chiffres')
    ])
    vat_number = StringField("Numéro de TVA", validators=[
        Optional(),
        Length(max=20, message='Le numéro de TVA ne peut pas dépasser 20 caractères')
    ])
    registration_number = StringField("Numéro d'immatriculation", validators=[
        Optional(),
        Length(max=50, message='Le numéro d\'immatriculation ne peut pas dépasser 50 caractères')
    ])
    
    # Contact
    contact_email = StringField("Email", validators=[
        Optional(),
        Email(message='Format d\'email invalide'),
        Length(max=255, message='L\'email ne peut pas dépasser 255 caractères')
    ])
    contact_phone = StringField("Téléphone", validators=[
        Optional(),
        Length(max=20, message='Le téléphone ne peut pas dépasser 20 caractères')
    ])
    contact_mobile = StringField("Mobile", validators=[
        Optional(),
        Length(max=20, message='Le mobile ne peut pas dépasser 20 caractères')
    ])
    website = StringField("Site web", validators=[
        Optional(),
        URL(message='Format d\'URL invalide'),
        Length(max=255, message='L\'URL ne peut pas dépasser 255 caractères')
    ])
    
    # Adresse
    street = StringField("Adresse", validators=[
        Optional(),
        Length(max=255, message='L\'adresse ne peut pas dépasser 255 caractères')
    ])
    street2 = StringField("Complément d'adresse", validators=[
        Optional(),
        Length(max=255, message='Le complément d\'adresse ne peut pas dépasser 255 caractères')
    ])
    city = StringField("Ville", validators=[
        Optional(),
        Length(max=100, message='La ville ne peut pas dépasser 100 caractères')
    ])
    postal_code = StringField("Code postal", validators=[
        Optional(),
        Length(max=20, message='Le code postal ne peut pas dépasser 20 caractères')
    ])
    state = StringField("État/Région", validators=[
        Optional(),
        Length(max=100, message='L\'état/région ne peut pas dépasser 100 caractères')
    ])
    country = StringField("Pays", validators=[
        Optional(),
        Length(max=100, message='Le pays ne peut pas dépasser 100 caractères')
    ], default="France")
    
    # Conditions commerciales
    payment_terms_days = IntegerField("Délai de paiement (jours)", validators=[
        Optional(),
        NumberRange(min=0, max=365, message='Le délai de paiement doit être entre 0 et 365 jours')
    ], default=30)
    discount_percentage = DecimalField("Remise habituelle (%)", validators=[
        Optional(),
        NumberRange(min=0, max=100, message='La remise doit être entre 0 et 100%')
    ], places=2, default=0.0)
    minimum_order = DecimalField("Commande minimum (€)", validators=[
        Optional(),
        NumberRange(min=0, message='La commande minimum ne peut pas être négative')
    ], places=2, default=0.0)
    delivery_delay_days = IntegerField("Délai de livraison (jours)", validators=[
        Optional(),
        NumberRange(min=0, max=365, message='Le délai de livraison doit être entre 0 et 365 jours')
    ], default=7)
    credit_limit = DecimalField("Limite de crédit (€)", validators=[
        Optional(),
        NumberRange(min=0, message='La limite de crédit ne peut pas être négative')
    ], places=2, default=0.0)
    
    # Statut
    is_active = BooleanField("Fournisseur actif", default=True)
    is_preferred = BooleanField("Fournisseur préféré")
    is_certified = BooleanField("Certifié (bio, qualité, etc.)")
    
    # Notes
    notes = TextAreaField("Notes", validators=[
        Optional(),
        Length(max=1000, message='Les notes ne peuvent pas dépasser 1000 caractères')
    ])
    internal_notes = TextAreaField("Notes internes", validators=[
        Optional(),
        Length(max=1000, message='Les notes internes ne peuvent pas dépasser 1000 caractères')
    ])
    
    submit = SubmitField('Enregistrer')


class EditSupplierForm(SupplierForm):
    """Formulaire pour modifier un fournisseur"""
    submit = SubmitField('Modifier')


class SupplierContactForm(FlaskForm):
    """Formulaire pour les contacts des fournisseurs"""
    first_name = StringField("Prénom", validators=[
        DataRequired(message='Le prénom est requis'),
        Length(min=1, max=100, message='Le prénom doit contenir entre 1 et 100 caractères')
    ])
    last_name = StringField("Nom", validators=[
        DataRequired(message='Le nom est requis'),
        Length(min=1, max=100, message='Le nom doit contenir entre 1 et 100 caractères')
    ])
    position = StringField("Fonction", validators=[
        Optional(),
        Length(max=100, message='La fonction ne peut pas dépasser 100 caractères')
    ])
    department = StringField("Département", validators=[
        Optional(),
        Length(max=100, message='Le département ne peut pas dépasser 100 caractères')
    ])
    
    email = StringField("Email", validators=[
        Optional(),
        Email(message='Format d\'email invalide'),
        Length(max=255, message='L\'email ne peut pas dépasser 255 caractères')
    ])
    phone = StringField("Téléphone", validators=[
        Optional(),
        Length(max=20, message='Le téléphone ne peut pas dépasser 20 caractères')
    ])
    mobile = StringField("Mobile", validators=[
        Optional(),
        Length(max=20, message='Le mobile ne peut pas dépasser 20 caractères')
    ])
    
    contact_type = SelectField("Type de contact", choices=[
        ("general", "Général"),
        ("commercial", "Commercial"),
        ("technical", "Technique"),
        ("financial", "Financier")
    ], validators=[DataRequired()])
    
    is_primary = BooleanField("Contact principal")
    is_active = BooleanField("Contact actif", default=True)
    
    notes = TextAreaField("Notes", validators=[
        Optional(),
        Length(max=1000, message='Les notes ne peuvent pas dépasser 1000 caractères')
    ])
    
    submit = SubmitField('Enregistrer')


class SupplierProductForm(FlaskForm):
    """Formulaire pour les produits des fournisseurs"""
    supplier_reference = StringField("Référence fournisseur", validators=[
        DataRequired(message='La référence fournisseur est requise'),
        Length(min=1, max=100, message='La référence doit contenir entre 1 et 100 caractères')
    ])
    product_name = StringField("Nom du produit", validators=[
        DataRequired(message='Le nom du produit est requis'),
        Length(min=1, max=255, message='Le nom doit contenir entre 1 et 255 caractères')
    ])
    description = TextAreaField("Description", validators=[
        Optional(),
        Length(max=1000, message='La description ne peut pas dépasser 1000 caractères')
    ])
    category = StringField("Catégorie", validators=[
        Optional(),
        Length(max=100, message='La catégorie ne peut pas dépasser 100 caractères')
    ])
    
    # Prix et conditions
    unit_price = DecimalField("Prix unitaire (€)", validators=[
        DataRequired(message='Le prix unitaire est requis'),
        NumberRange(min=0.01, message='Le prix unitaire doit être supérieur à 0')
    ], places=2)
    currency = SelectField("Devise", choices=[
        ("EUR", "Euro (€)"),
        ("USD", "Dollar ($)"),
        ("GBP", "Livre (£)")
    ], default="EUR", validators=[DataRequired()])
    unit_of_measure = SelectField("Unité de mesure", choices=[
        ("unit", "Unité"),
        ("kg", "Kilogramme"),
        ("g", "Gramme"),
        ("liter", "Litre"),
        ("ml", "Millilitre"),
        ("meter", "Mètre"),
        ("cm", "Centimètre"),
        ("box", "Boîte"),
        ("pack", "Pack")
    ], default="unit", validators=[DataRequired()])
    minimum_quantity = IntegerField("Quantité minimum", validators=[
        Optional(),
        NumberRange(min=1, message='La quantité minimum doit être au moins 1')
    ], default=1)
    
    # Conditionnement
    package_size = StringField("Taille du conditionnement", validators=[
        Optional(),
        Length(max=50, message='La taille du conditionnement ne peut pas dépasser 50 caractères')
    ])
    package_quantity = IntegerField("Quantité par conditionnement", validators=[
        Optional(),
        NumberRange(min=1, message='La quantité par conditionnement doit être au moins 1')
    ], default=1)
    
    # Informations produit
    brand = StringField("Marque", validators=[
        Optional(),
        Length(max=100, message='La marque ne peut pas dépasser 100 caractères')
    ])
    model = StringField("Modèle", validators=[
        Optional(),
        Length(max=100, message='Le modèle ne peut pas dépasser 100 caractères')
    ])
    ean_code = StringField("Code EAN", validators=[
        Optional(),
        Length(max=20, message='Le code EAN ne peut pas dépasser 20 caractères'),
        Regexp(r'^\d*$', message='Le code EAN ne peut contenir que des chiffres')
    ])
    
    # Qualité et certifications
    is_organic = BooleanField("Produit bio")
    is_local = BooleanField("Produit local")
    certifications = TextAreaField("Certifications", validators=[
        Optional(),
        Length(max=500, message='Les certifications ne peuvent pas dépasser 500 caractères')
    ])
    
    # Délais et disponibilité
    lead_time_days = IntegerField("Délai de livraison (jours)", validators=[
        Optional(),
        NumberRange(min=0, max=365, message='Le délai de livraison doit être entre 0 et 365 jours')
    ], default=7)
    availability_status = SelectField("Statut de disponibilité", choices=[
        ("available", "Disponible"),
        ("limited", "Stock limité"),
        ("discontinued", "Arrêté")
    ], default="available", validators=[DataRequired()])
    
    is_active = BooleanField("Produit actif", default=True)
    is_preferred = BooleanField("Produit préféré")
    
    submit = SubmitField('Enregistrer')


class SupplierEvaluationForm(FlaskForm):
    """Formulaire pour l'évaluation des fournisseurs"""
    evaluation_date = DateField("Date d'évaluation", validators=[DataRequired()], default=date.today)
    period_start = DateField("Début de période", validators=[DataRequired()])
    period_end = DateField("Fin de période", validators=[DataRequired()])
    
    # Notes par critère (1-5)
    quality_rating = SelectField("Qualité", choices=[
        ("1", "1 - Très mauvais"),
        ("2", "2 - Mauvais"),
        ("3", "3 - Correct"),
        ("4", "4 - Bon"),
        ("5", "5 - Excellent")
    ], validators=[DataRequired()], coerce=int)
    
    delivery_rating = SelectField("Livraison", choices=[
        ("1", "1 - Très mauvais"),
        ("2", "2 - Mauvais"),
        ("3", "3 - Correct"),
        ("4", "4 - Bon"),
        ("5", "5 - Excellent")
    ], validators=[DataRequired()], coerce=int)
    
    price_rating = SelectField("Prix", choices=[
        ("1", "1 - Très mauvais"),
        ("2", "2 - Mauvais"),
        ("3", "3 - Correct"),
        ("4", "4 - Bon"),
        ("5", "5 - Excellent")
    ], validators=[DataRequired()], coerce=int)
    
    service_rating = SelectField("Service", choices=[
        ("1", "1 - Très mauvais"),
        ("2", "2 - Mauvais"),
        ("3", "3 - Correct"),
        ("4", "4 - Bon"),
        ("5", "5 - Excellent")
    ], validators=[DataRequired()], coerce=int)
    
    communication_rating = SelectField("Communication", choices=[
        ("1", "1 - Très mauvais"),
        ("2", "2 - Mauvais"),
        ("3", "3 - Correct"),
        ("4", "4 - Bon"),
        ("5", "5 - Excellent")
    ], validators=[DataRequired()], coerce=int)
    
    # Commentaires
    strengths = TextAreaField("Points forts", validators=[
        Optional(),
        Length(max=1000, message='Les points forts ne peuvent pas dépasser 1000 caractères')
    ])
    weaknesses = TextAreaField("Points faibles", validators=[
        Optional(),
        Length(max=1000, message='Les points faibles ne peuvent pas dépasser 1000 caractères')
    ])
    recommendations = TextAreaField("Recommandations", validators=[
        Optional(),
        Length(max=1000, message='Les recommandations ne peuvent pas dépasser 1000 caractères')
    ])
    
    evaluated_by = StringField("Évalué par", validators=[
        DataRequired(message='L\'évaluateur est requis'),
        Length(min=1, max=100, message='Le nom de l\'évaluateur doit contenir entre 1 et 100 caractères')
    ])
    
    submit = SubmitField('Enregistrer')


class SupplierSearchForm(FlaskForm):
    """Formulaire de recherche et filtrage des fournisseurs"""
    search = StringField("Recherche", validators=[Optional(), Length(max=100)])
    supplier_type = SelectField("Type", choices=[
        ("", "Tous les types"),
        ("company", "Entreprises"),
        ("individual", "Particuliers")
    ], validators=[Optional()])
    industry = SelectField("Secteur", choices=[], validators=[Optional()])
    status = SelectField("Statut", choices=[
        ("", "Tous les statuts"),
        ("active", "Actifs"),
        ("inactive", "Inactifs"),
        ("preferred", "Préférés"),
        ("certified", "Certifiés")
    ], validators=[Optional()])
    rating = SelectField("Note", choices=[
        ("", "Toutes les notes"),
        ("5", "5 étoiles"),
        ("4", "4+ étoiles"),
        ("3", "3+ étoiles"),
        ("2", "2+ étoiles"),
        ("1", "1+ étoiles")
    ], validators=[Optional()])
    sort_by = SelectField("Trier par", choices=[
        ("name_asc", "Nom (A-Z)"),
        ("name_desc", "Nom (Z-A)"),
        ("created_desc", "Plus récents"),
        ("created_asc", "Plus anciens"),
        ("rating_desc", "Mieux notés"),
        ("last_order_desc", "Dernière commande")
    ], default="name_asc", validators=[Optional()])
    
    submit = SubmitField('Filtrer')


class SupplierCategoryForm(FlaskForm):
    """Formulaire pour les catégories de fournisseurs"""
    name = StringField("Nom de la catégorie", validators=[
        DataRequired(message='Le nom de la catégorie est requis'),
        Length(min=1, max=100, message='Le nom doit contenir entre 1 et 100 caractères')
    ])
    description = TextAreaField("Description", validators=[
        Optional(),
        Length(max=1000, message='La description ne peut pas dépasser 1000 caractères')
    ])
    color = StringField("Couleur", validators=[
        Optional(),
        Regexp(r'^#[0-9A-Fa-f]{6}$', message='La couleur doit être au format hexadécimal (#RRGGBB)')
    ], default="#3B82F6")
    
    # Paramètres par défaut
    default_payment_terms = IntegerField("Délai de paiement par défaut (jours)", validators=[
        Optional(),
        NumberRange(min=0, max=365, message='Le délai doit être entre 0 et 365 jours')
    ], default=30)
    default_delivery_delay = IntegerField("Délai de livraison par défaut (jours)", validators=[
        Optional(),
        NumberRange(min=0, max=365, message='Le délai doit être entre 0 et 365 jours')
    ], default=7)
    
    is_active = BooleanField("Catégorie active", default=True)
    
    submit = SubmitField('Enregistrer')


class BulkSupplierUpdateForm(FlaskForm):
    """Formulaire pour les mises à jour en lot des fournisseurs"""
    supplier_ids = HiddenField("IDs des fournisseurs")
    action = SelectField("Action", choices=[
        ("activate", "Activer"),
        ("deactivate", "Désactiver"),
        ("mark_preferred", "Marquer comme préféré"),
        ("unmark_preferred", "Retirer le statut préféré"),
        ("mark_certified", "Marquer comme certifié"),
        ("unmark_certified", "Retirer la certification"),
        ("add_to_category", "Ajouter à une catégorie"),
        ("remove_from_category", "Retirer d'une catégorie"),
        ("export", "Exporter les données")
    ], validators=[DataRequired()])
    
    category_id = SelectField("Catégorie", coerce=int, validators=[Optional()])
    
    submit = SubmitField('Appliquer')


class SupplierImportForm(FlaskForm):
    """Formulaire pour l'import de fournisseurs"""
    import_file = StringField("Fichier CSV", validators=[DataRequired()])
    has_headers = BooleanField("Le fichier contient des en-têtes", default=True)
    update_existing = BooleanField("Mettre à jour les fournisseurs existants")
    create_categories = BooleanField("Créer automatiquement les catégories")
    
    submit = SubmitField('Importer')


class SupplierExportForm(FlaskForm):
    """Formulaire pour l'export de fournisseurs"""
    export_format = SelectField("Format", choices=[
        ("csv", "CSV"),
        ("excel", "Excel"),
        ("pdf", "PDF")
    ], validators=[DataRequired()])
    
    include_contacts = BooleanField("Inclure les contacts", default=True)
    include_products = BooleanField("Inclure les produits", default=True)
    include_evaluations = BooleanField("Inclure les évaluations")
    
    date_from = DateField("Fournisseurs créés à partir du", validators=[Optional()])
    date_to = DateField("Fournisseurs créés jusqu'au", validators=[Optional()])
    
    submit = SubmitField('Exporter')