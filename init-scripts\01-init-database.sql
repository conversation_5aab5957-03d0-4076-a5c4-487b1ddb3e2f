-- Script d'initialisation de la base de données PostgreSQL pour l'application POS System

-- C<PERSON>er les extensions nécessaires
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- C<PERSON>er les rôles si nécessaire
DO
$do$
BEGIN
   IF NOT EXISTS (
      SELECT FROM pg_catalog.pg_roles
      WHERE  rolname = 'pos_user') THEN

      CREATE ROLE pos_user LOGIN PASSWORD 'pos_password';
   END IF;
END
$do$;

-- Créer la base de données si elle n'existe pas
SELECT 'CREATE DATABASE pos_system OWNER pos_user'
WHERE NOT EXISTS (
    SELECT FROM pg_database
    WHERE datname = 'pos_system'
);

-- Donner les privilèges
GRANT ALL PRIVILEGES ON DATABASE pos_system TO pos_user;

-- Se connecter à la base de données
\connect pos_system;

-- <PERSON><PERSON><PERSON> les schémas si nécessaire
CREATE SCHEMA IF NOT EXISTS public;
CREATE SCHEMA IF NOT EXISTS pos;
CREATE SCHEMA IF NOT EXISTS catalog;
CREATE SCHEMA IF NOT EXISTS accounts;
CREATE SCHEMA IF NOT EXISTS inventory;

-- Donner les privilèges sur les schémas
GRANT ALL ON SCHEMA public TO pos_user;
GRANT ALL ON SCHEMA pos TO pos_user;
GRANT ALL ON SCHEMA catalog TO pos_user;
GRANT ALL ON SCHEMA accounts TO pos_user;
GRANT ALL ON SCHEMA inventory TO pos_user;