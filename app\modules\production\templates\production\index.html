{% extends 'base.html' %}
{% block title %}Production - Tableau de bord{% endblock %}

{% block content %}
<div class=\"max-w-7xl mx-auto p-6\">
    <!-- En-tête -->
    <div class=\"mb-8\">
        <h1 class=\"text-3xl font-bold text-white mb-2\">Module Production</h1>
        <p class=\"text-slate-400\">Gestion de la production industrielle et manufacturing</p>
    </div>
    
    <!-- Statistiques rapides -->
    <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">
        <!-- Total des ordres -->
        <div class=\"bg-slate-800 rounded-lg p-6 border border-slate-700\">
            <div class=\"flex items-center justify-between\">
                <div>
                    <p class=\"text-sm font-medium text-slate-400\">Total des ordres</p>
                    <p class=\"text-2xl font-bold text-white\">{{ total_orders }}</p>
                </div>
                <div class=\"w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center\">
                    <svg class=\"w-6 h-6 text-blue-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">
                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"></path>
                    </svg>
                </div>
            </div>
        </div>
        
        <!-- Ordres actifs -->
        <div class=\"bg-slate-800 rounded-lg p-6 border border-slate-700\">
            <div class=\"flex items-center justify-between\">
                <div>
                    <p class=\"text-sm font-medium text-slate-400\">Ordres actifs</p>
                    <p class=\"text-2xl font-bold text-green-400\">{{ active_orders }}</p>
                </div>
                <div class=\"w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center\">
                    <svg class=\"w-6 h-6 text-green-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">
                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 10V3L4 14h7v7l9-11h-7z\"></path>
                    </svg>
                </div>
            </div>
        </div>
        
        <!-- Ordres en retard -->
        <div class=\"bg-slate-800 rounded-lg p-6 border border-slate-700\">
            <div class=\"flex items-center justify-between\">
                <div>
                    <p class=\"text-sm font-medium text-slate-400\">En retard</p>
                    <p class=\"text-2xl font-bold text-red-400\">{{ overdue_orders }}</p>
                </div>
                <div class=\"w-12 h-12 bg-red-500/20 rounded-lg flex items-center justify-center\">
                    <svg class=\"w-6 h-6 text-red-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">
                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"></path>
                    </svg>
                </div>
            </div>
        </div>
        
        <!-- Centres de travail -->
        <div class=\"bg-slate-800 rounded-lg p-6 border border-slate-700\">
            <div class=\"flex items-center justify-between\">
                <div>
                    <p class=\"text-sm font-medium text-slate-400\">Centres de travail</p>
                    <p class=\"text-2xl font-bold text-yellow-400\">{{ work_centers }}</p>
                </div>
                <div class=\"w-12 h-12 bg-yellow-500/20 rounded-lg flex items-center justify-center\">
                    <svg class=\"w-6 h-6 text-yellow-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">
                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z\"></path>
                    </svg>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Navigation rapide -->
    <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8\">
        <!-- Ordres de production -->
        <a href=\"{{ url_for('production.production_orders') }}\" 
           class=\"block bg-slate-800 rounded-lg p-6 border border-slate-700 hover:border-blue-500 transition-colors group\">
            <div class=\"flex items-center justify-between mb-4\">
                <h3 class=\"text-lg font-semibold text-white group-hover:text-blue-400\">Ordres de production</h3>
                <svg class=\"w-5 h-5 text-slate-400 group-hover:text-blue-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">
                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5l7 7-7 7\"></path>
                </svg>
            </div>
            <p class=\"text-slate-400 text-sm\">Créer, planifier et suivre les ordres de production</p>
        </a>
        
        <!-- Nomenclatures (BOM) -->
        <a href=\"{{ url_for('production.boms') }}\" 
           class=\"block bg-slate-800 rounded-lg p-6 border border-slate-700 hover:border-green-500 transition-colors group\">
            <div class=\"flex items-center justify-between mb-4\">
                <h3 class=\"text-lg font-semibold text-white group-hover:text-green-400\">Nomenclatures (BOM)</h3>
                <svg class=\"w-5 h-5 text-slate-400 group-hover:text-green-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">
                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5l7 7-7 7\"></path>
                </svg>
            </div>
            <p class=\"text-slate-400 text-sm\">Gérer les nomenclatures et gammes de fabrication</p>
        </a>
        
        <!-- Centres de travail -->
        <a href=\"{{ url_for('production.work_centers') }}\" 
           class=\"block bg-slate-800 rounded-lg p-6 border border-slate-700 hover:border-yellow-500 transition-colors group\">
            <div class=\"flex items-center justify-between mb-4\">
                <h3 class=\"text-lg font-semibold text-white group-hover:text-yellow-400\">Centres de travail</h3>
                <svg class=\"w-5 h-5 text-slate-400 group-hover:text-yellow-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">
                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5l7 7-7 7\"></path>
                </svg>
            </div>
            <p class=\"text-slate-400 text-sm\">Configurer les postes et centres de production</p>
        </a>
    </div>
    
    <!-- Ordres récents -->
    <div class=\"bg-slate-800 rounded-lg border border-slate-700\">
        <div class=\"px-6 py-4 border-b border-slate-700\">
            <h2 class=\"text-lg font-semibold text-white\">Ordres récents</h2>
        </div>
        
        {% if recent_orders %}
        <div class=\"divide-y divide-slate-700\">
            {% for order in recent_orders %}
            <div class=\"px-6 py-4 hover:bg-slate-750\">
                <div class=\"flex items-center justify-between\">
                    <div class=\"flex-1\">
                        <div class=\"flex items-center gap-3\">
                            <a href=\"{{ url_for('production.order_details', order_id=order.id) }}\" 
                               class=\"text-white font-medium hover:text-blue-400\">
                                #{{ order.order_number }}
                            </a>
                            
                            <span class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    {% if order.status == 'draft' %}bg-gray-100 text-gray-800
                                    {% elif order.status == 'planned' %}bg-blue-100 text-blue-800
                                    {% elif order.status == 'released' %}bg-yellow-100 text-yellow-800
                                    {% elif order.status == 'in_progress' %}bg-green-100 text-green-800
                                    {% elif order.status == 'completed' %}bg-green-100 text-green-800
                                    {% elif order.status == 'cancelled' %}bg-red-100 text-red-800
                                    {% endif %}\">
                                {% if order.status == 'draft' %}Brouillon
                                {% elif order.status == 'planned' %}Planifié
                                {% elif order.status == 'released' %}Libéré
                                {% elif order.status == 'in_progress' %}En cours
                                {% elif order.status == 'completed' %}Terminé
                                {% elif order.status == 'cancelled' %}Annulé
                                {% endif %}
                            </span>
                            
                            {% if order.priority == 'urgent' %}
                            <span class=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800\">
                                Urgent
                            </span>
                            {% elif order.priority == 'high' %}
                            <span class=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800\">
                                Élevée
                            </span>
                            {% endif %}
                        </div>
                        
                        <div class=\"mt-1 text-sm text-slate-400\">
                            {% if order.bom and order.bom.product %}
                                {{ order.bom.product.name }} - {{ order.quantity_to_produce }} unités
                            {% endif %}
                            • Créé le {{ order.created_at.strftime('%d/%m/%Y à %H:%M') }}
                        </div>
                    </div>
                    
                    <div class=\"text-right\">
                        {% if order.quantity_to_produce > 0 %}
                        <div class=\"text-sm text-slate-400\">
                            Progression: {{ \"%.1f\"|format(order.progress_percentage) }}%
                        </div>
                        <div class=\"w-24 bg-slate-700 rounded-full h-2 mt-1\">
                            <div class=\"bg-blue-500 h-2 rounded-full\" 
                                 style=\"width: {{ order.progress_percentage }}%\"></div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        
        <div class=\"px-6 py-4 border-t border-slate-700\">
            <a href=\"{{ url_for('production.production_orders') }}\" 
               class=\"text-blue-400 hover:text-blue-300 text-sm font-medium\">
                Voir tous les ordres →
            </a>
        </div>
        {% else %}
        <div class=\"px-6 py-8 text-center\">
            <div class=\"text-slate-400 mb-4\">
                <svg class=\"w-12 h-12 mx-auto\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">
                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"></path>
                </svg>
            </div>
            <h3 class=\"text-lg font-medium text-white mb-2\">Aucun ordre de production</h3>
            <p class=\"text-slate-400 mb-4\">Commencez par créer votre premier ordre de production</p>
            <a href=\"{{ url_for('production.create_production_order') }}\" 
               class=\"inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md text-sm font-medium\">
                Créer un ordre
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}