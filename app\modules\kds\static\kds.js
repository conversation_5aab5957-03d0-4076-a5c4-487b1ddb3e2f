/**
 * KDS (Kitchen Display System) JavaScript
 * Gestion des WebSockets et interactions en temps réel
 */

class KDSManager {
    constructor() {
        this.socket = null;
        this.ticketsContainer = document.getElementById('tickets-container');
        this.connectionStatus = document.getElementById('connection-status');
        this.refreshButton = document.getElementById('refresh-tickets');
        this.filters = document.getElementById('kds-filters');
        
        this.init();
    }
    
    /**
     * Initialiser le gestionnaire KDS
     */
    init() {
        this.initializeWebSocket();
        this.bindEvents();
        this.startTimers();
        
        // Charger les tickets initiaux
        this.loadTickets();
    }
    
    /**
     * Initialiser la connexion WebSocket
     */
    initializeWebSocket() {
        if (typeof io === 'undefined') {
            console.warn('Socket.IO non disponible');
            return;
        }
        
        this.socket = io();
        
        this.socket.on('connect', () => {
            console.log('Connecté au serveur WebSocket');
            this.updateConnectionStatus(true);
            
            // Rejoindre la room de l'entreprise
            this.socket.emit('join_business');
        });
        
        this.socket.on('disconnect', () => {
            console.log('Déconnecté du serveur WebSocket');
            this.updateConnectionStatus(false);
        });
        
        // Écouter les événements de mise à jour des tickets
        this.socket.on('ticket_status_updated', (data) => {
            this.handleTicketStatusUpdate(data);
        });
        
        // Écouter les nouvelles commandes
        this.socket.on('new_order', (data) => {
            this.handleNewOrder(data);
        });
        
        // Écouter les mises à jour d'écrans
        this.socket.on('screen_created', (data) => {
            this.showNotification(`Nouvel écran créé: ${data.screen_name}`, 'success');
        });
        
        this.socket.on('screen_updated', (data) => {
            this.showNotification(`Écran mis à jour: ${data.screen_name}`, 'info');
        });
        
        this.socket.on('screen_deleted', (data) => {
            this.showNotification('Écran supprimé', 'warning');
        });
    }
    
    /**
     * Lier les événements DOM
     */
    bindEvents() {
        // Bouton de rafraîchissement
        if (this.refreshButton) {
            this.refreshButton.addEventListener('click', () => {
                this.loadTickets();
            });
        }
        
        // Filtres automatiques
        if (this.filters) {
            this.filters.addEventListener('change', (e) => {
                if (e.target.tagName === 'SELECT') {
                    this.loadTickets();
                }
            });
        }
        
        // Gestion des clics sur les boutons de statut
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('update-status-btn')) {
                this.handleStatusUpdate(e.target);
            }
        });
        
        // Raccourcis clavier
        document.addEventListener('keydown', (e) => {
            if (e.key === 'F5' || (e.ctrlKey && e.key === 'r')) {
                e.preventDefault();
                this.loadTickets();
            }
        });
    }
    
    /**
     * Charger les tickets depuis l'API
     */
    async loadTickets() {
        try {
            const formData = new FormData(this.filters);
            const params = new URLSearchParams();
            
            for (let [key, value] of formData.entries()) {
                if (value) {
                    params.append(key, value);
                }
            }
            
            const response = await fetch(`/kds/api/tickets?${params.toString()}`);
            const data = await response.json();
            
            this.displayTickets(data.tickets);
            
        } catch (error) {
            console.error('Erreur lors du chargement des tickets:', error);
            this.showNotification('Erreur lors du chargement des tickets', 'error');
        }
    }
    
    /**
     * Afficher les tickets dans le conteneur
     */
    displayTickets(tickets) {
        if (!this.ticketsContainer) return;
        
        if (tickets.length === 0) {
            this.ticketsContainer.innerHTML = `
                <div class=\"col-span-full text-center py-12\">
                    <div class=\"bg-slate-800 rounded-lg p-8 max-w-md mx-auto\">
                        <div class=\"text-slate-400 mb-4\">
                            <svg class=\"w-16 h-16 mx-auto\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">
                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"></path>
                            </svg>
                        </div>
                        <h3 class=\"text-xl font-semibold text-white mb-2\">Aucune commande en attente</h3>
                        <p class=\"text-slate-400\">Les nouvelles commandes apparaîtront ici automatiquement</p>
                    </div>
                </div>
            `;
            return;
        }
        
        this.ticketsContainer.innerHTML = tickets.map(ticket => 
            this.createTicketHTML(ticket)
        ).join('');
        
        // Réinitialiser les timers
        this.updateTimers();
    }
    
    /**
     * Créer le HTML d'un ticket
     */
    createTicketHTML(ticket) {
        const statusColors = {
            'pending': 'border-yellow-500',
            'preparing': 'border-blue-500',
            'ready': 'border-green-500',
            'completed': 'border-gray-500'
        };
        
        const statusBadges = {
            'pending': 'bg-yellow-500',
            'preparing': 'bg-blue-500',
            'ready': 'bg-green-500',
            'completed': 'bg-gray-500'
        };
        
        const statusTexts = {
            'pending': 'En attente',
            'preparing': 'En préparation',
            'ready': 'Prêt',
            'completed': 'Terminé'
        };
        
        const itemsHTML = ticket.items.map(item => `
            <div class=\"bg-slate-700 rounded p-3\">
                <div class=\"flex justify-between items-center\">
                    <div>
                        <p class=\"text-white font-medium\">${item.product_name}</p>
                        <p class=\"text-slate-300 text-sm\">${item.quantity}x ${item.price.toFixed(2)}€</p>
                    </div>
                    <div class=\"text-right\">
                        <span class=\"text-lg font-bold text-white\">${item.quantity}</span>
                    </div>
                </div>
            </div>
        `).join('');
        
        let actionButton = '';
        if (ticket.status === 'pending') {
            actionButton = `
                <button class=\"update-status-btn flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 px-3 rounded-md text-sm font-medium\"
                        data-ticket-id=\"${ticket.id}\" 
                        data-status=\"preparing\">
                    Commencer
                </button>
            `;
        } else if (ticket.status === 'preparing') {
            actionButton = `
                <button class=\"update-status-btn flex-1 bg-green-600 hover:bg-green-700 text-white py-2 px-3 rounded-md text-sm font-medium\"
                        data-ticket-id=\"${ticket.id}\" 
                        data-status=\"ready\">
                    Terminer
                </button>
            `;
        } else if (ticket.status === 'ready') {
            actionButton = `
                <button class=\"update-status-btn flex-1 bg-gray-600 hover:bg-gray-700 text-white py-2 px-3 rounded-md text-sm font-medium\"
                        data-ticket-id=\"${ticket.id}\" 
                        data-status=\"completed\">
                    Servir
                </button>
            `;
        }
        
        return `
            <div class=\"kds-ticket bg-slate-800 rounded-lg border-l-4 ${statusColors[ticket.status] || 'border-gray-500'} p-4 shadow-lg\" 
                 data-ticket-id=\"${ticket.id}\" 
                 data-order-id=\"${ticket.order_id}\">
                
                <div class=\"flex justify-between items-start mb-4\">
                    <div>
                        <h3 class=\"text-lg font-semibold text-white\">Commande #${ticket.order_id}</h3>
                        <p class=\"text-sm text-slate-400\">${new Date(ticket.created_at).toLocaleTimeString('fr-FR', {hour: '2-digit', minute: '2-digit'})}</p>
                    </div>
                    
                    <div class=\"ticket-status-badge ${statusBadges[ticket.status] || 'bg-gray-500'} text-white px-2 py-1 rounded-full text-xs font-medium\">
                        ${statusTexts[ticket.status] || ticket.status}
                    </div>
                </div>
                
                <div class=\"space-y-2 mb-4\">
                    ${itemsHTML}
                </div>
                
                <div class=\"flex gap-2\">
                    ${actionButton}
                    
                    <div class=\"text-xs text-slate-400 self-center\">
                        <span class=\"timer\" data-created=\"${ticket.created_at}\">--:--</span>
                    </div>
                </div>
            </div>
        `;
    }
    
    /**
     * Gérer la mise à jour de statut d'un ticket
     */
    async handleStatusUpdate(button) {
        const ticketId = button.dataset.ticketId;
        const newStatus = button.dataset.status;
        
        // Désactiver le bouton pendant la requête
        button.disabled = true;
        button.textContent = 'Mise à jour...';
        
        try {
            const response = await fetch(`/kds/tickets/${ticketId}/status`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken()
                },
                body: JSON.stringify({ status: newStatus })
            });
            
            const data = await response.json();
            
            if (data.success) {
                // Recharger les tickets pour avoir la mise à jour
                this.loadTickets();
                this.showNotification('Statut mis à jour avec succès', 'success');
            } else {
                throw new Error(data.error || 'Erreur inconnue');
            }
            
        } catch (error) {
            console.error('Erreur lors de la mise à jour:', error);
            this.showNotification('Erreur lors de la mise à jour du statut', 'error');
            
            // Réactiver le bouton
            button.disabled = false;
            this.resetButtonText(button, newStatus);
        }
    }
    
    /**
     * Remettre le texte original du bouton
     */
    resetButtonText(button, status) {
        const texts = {
            'preparing': 'Commencer',
            'ready': 'Terminer',
            'completed': 'Servir'
        };
        button.textContent = texts[status] || 'Action';
    }
    
    /**
     * Gérer les mises à jour de statut via WebSocket
     */
    handleTicketStatusUpdate(data) {
        console.log('Mise à jour de ticket reçue:', data);
        
        // Recharger les tickets pour avoir les dernières données
        this.loadTickets();
        
        // Afficher une notification
        this.showNotification(
            `Commande #${data.order_id} : ${data.old_status} → ${data.new_status}`,
            'info'
        );
    }
    
    /**
     * Gérer les nouvelles commandes
     */
    handleNewOrder(data) {
        console.log('Nouvelle commande reçue:', data);
        
        // Recharger les tickets
        this.loadTickets();
        
        // Notification sonore et visuelle
        this.playNotificationSound();
        this.showNotification(
            `Nouvelle commande #${data.order_id} reçue !`,
            'success'
        );
    }
    
    /**
     * Démarrer les timers
     */
    startTimers() {
        // Mettre à jour les timers toutes les secondes
        setInterval(() => {
            this.updateTimers();
        }, 1000);
        
        // Recharger automatiquement toutes les 30 secondes
        setInterval(() => {
            this.loadTickets();
        }, 30000);
    }
    
    /**
     * Mettre à jour les timers d'attente
     */
    updateTimers() {
        document.querySelectorAll('.timer').forEach(timer => {
            const createdAt = new Date(timer.dataset.created);
            const now = new Date();
            const diff = Math.floor((now - createdAt) / 1000);
            
            const minutes = Math.floor(diff / 60);
            const seconds = diff % 60;
            
            timer.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            
            // Marquer comme urgent après 10 minutes
            if (minutes >= 10) {
                timer.classList.add('urgent');
            }
        });
    }
    
    /**
     * Mettre à jour l'indicateur de connexion
     */
    updateConnectionStatus(connected) {
        if (!this.connectionStatus) return;
        
        const dot = this.connectionStatus.querySelector('div');
        const text = this.connectionStatus.querySelector('span');
        
        if (connected) {
            dot.className = 'w-3 h-3 rounded-full bg-green-500';
            text.textContent = 'Connecté';
        } else {
            dot.className = 'w-3 h-3 rounded-full bg-red-500';
            text.textContent = 'Déconnecté';
        }
    }
    
    /**
     * Afficher une notification
     */
    showNotification(message, type = 'info') {
        // Créer l'élément de notification
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg text-white max-w-sm
            ${type === 'success' ? 'bg-green-600' : ''}
            ${type === 'error' ? 'bg-red-600' : ''}
            ${type === 'warning' ? 'bg-yellow-600' : ''}
            ${type === 'info' ? 'bg-blue-600' : ''}
        `;
        
        notification.innerHTML = `
            <div class=\"flex justify-between items-start\">
                <p class=\"text-sm\">${message}</p>
                <button class=\"ml-2 text-white opacity-70 hover:opacity-100\" onclick=\"this.parentElement.parentElement.remove()\">
                    ✕
                </button>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Supprimer automatiquement après 5 secondes
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }
    
    /**
     * Jouer un son de notification
     */
    playNotificationSound() {
        try {
            // Créer un son simple avec l'API Web Audio
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);
            
            oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
            oscillator.type = 'sine';
            
            gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);
            
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.5);
        } catch (error) {
            console.log('Impossible de jouer le son de notification:', error);
        }
    }
    
    /**
     * Obtenir le token CSRF
     */
    getCSRFToken() {
        const metaTag = document.querySelector('meta[name=\"csrf-token\"]');
        if (metaTag) {
            return metaTag.getAttribute('content');
        }
        
        // Fallback: chercher dans les formulaires
        const csrfInput = document.querySelector('input[name=\"csrf_token\"]');
        return csrfInput ? csrfInput.value : '';
    }
}

// Initialiser le gestionnaire KDS quand le DOM est prêt
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('tickets-container')) {
        window.kdsManager = new KDSManager();
    }
});"