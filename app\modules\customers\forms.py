from flask_wtf import <PERSON>laskForm
from wtforms import StringField, IntegerField, BooleanField, SelectField, SubmitField, DateField, TextAreaField, DecimalField, HiddenField
from wtforms.validators import DataRequired, Length, Optional, Email, NumberRange, Regexp
from datetime import date


class CustomerForm(FlaskForm):
    """Formulaire pour créer un client"""
    customer_type = SelectField("Type de client", choices=[
        ("individual", "Particulier"),
        ("company", "Entreprise")
    ], validators=[DataRequired()])
    
    first_name = StringField("Prénom", validators=[
        DataRequired(message='Le prénom est requis'),
        Length(min=1, max=100, message='Le prénom doit contenir entre 1 et 100 caractères')
    ])
    last_name = StringField("Nom", validators=[
        DataRequired(message='Le nom est requis'),
        Length(min=1, max=100, message='Le nom doit contenir entre 1 et 100 caractères')
    ])
    company_name = StringField("Nom de l'entreprise", validators=[
        Optional(),
        Length(max=255, message='Le nom de l\'entreprise ne peut pas dépasser 255 caractères')
    ])
    customer_code = StringField("Code client", validators=[
        Optional(),
        Length(max=20, message='Le code client ne peut pas dépasser 20 caractères'),
        Regexp(r'^[A-Z0-9]*$', message='Le code client ne peut contenir que des lettres majuscules et des chiffres')
    ])
    
    email = StringField("Email", validators=[
        Optional(),
        Email(message='Format d\'email invalide'),
        Length(max=255, message='L\'email ne peut pas dépasser 255 caractères')
    ])
    phone = StringField("Téléphone", validators=[
        Optional(),
        Length(max=20, message='Le téléphone ne peut pas dépasser 20 caractères')
    ])
    mobile = StringField("Mobile", validators=[
        Optional(),
        Length(max=20, message='Le mobile ne peut pas dépasser 20 caractères')
    ])
    
    birth_date = DateField("Date de naissance", validators=[Optional()])
    gender = SelectField("Genre", choices=[
        ("", "Non spécifié"),
        ("M", "Masculin"),
        ("F", "Féminin"),
        ("Other", "Autre")
    ], validators=[Optional()])
    
    preferred_language = SelectField("Langue préférée", choices=[
        ("fr", "Français"),
        ("en", "Anglais"),
        ("es", "Espagnol"),
        ("de", "Allemand"),
        ("it", "Italien")
    ], default="fr", validators=[Optional()])
    
    credit_limit = DecimalField("Limite de crédit (€)", validators=[
        Optional(),
        NumberRange(min=0, message='La limite de crédit ne peut pas être négative')
    ], places=2)
    
    is_active = BooleanField("Client actif", default=True)
    is_vip = BooleanField("Client VIP")
    marketing_consent = BooleanField("Consent marketing", default=True)
    
    notes = TextAreaField("Notes", validators=[
        Optional(),
        Length(max=1000, message='Les notes ne peuvent pas dépasser 1000 caractères')
    ])
    
    submit = SubmitField('Enregistrer')


class EditCustomerForm(CustomerForm):
    """Formulaire pour modifier un client"""
    submit = SubmitField('Modifier')


class CustomerAddressForm(FlaskForm):
    """Formulaire pour les adresses des clients"""
    address_type = SelectField("Type d'adresse", choices=[
        ("billing", "Facturation"),
        ("shipping", "Livraison"),
        ("other", "Autre")
    ], validators=[DataRequired()])
    
    label = StringField("Libellé", validators=[
        Optional(),
        Length(max=50, message='Le libellé ne peut pas dépasser 50 caractères')
    ])
    
    street = StringField("Adresse", validators=[
        DataRequired(message='L\'adresse est requise'),
        Length(min=1, max=255, message='L\'adresse doit contenir entre 1 et 255 caractères')
    ])
    street2 = StringField("Complément d'adresse", validators=[
        Optional(),
        Length(max=255, message='Le complément d\'adresse ne peut pas dépasser 255 caractères')
    ])
    city = StringField("Ville", validators=[
        DataRequired(message='La ville est requise'),
        Length(min=1, max=100, message='La ville doit contenir entre 1 et 100 caractères')
    ])
    postal_code = StringField("Code postal", validators=[
        DataRequired(message='Le code postal est requis'),
        Length(min=1, max=20, message='Le code postal doit contenir entre 1 et 20 caractères')
    ])
    state = StringField("État/Région", validators=[
        Optional(),
        Length(max=100, message='L\'état/région ne peut pas dépasser 100 caractères')
    ])
    country = StringField("Pays", validators=[
        DataRequired(message='Le pays est requis'),
        Length(min=1, max=100, message='Le pays doit contenir entre 1 et 100 caractères')
    ], default="France")
    
    is_default = BooleanField("Adresse par défaut")
    is_active = BooleanField("Adresse active", default=True)
    
    submit = SubmitField('Enregistrer')


class CustomerLoyaltyPointForm(FlaskForm):
    """Formulaire pour ajuster les points de fidélité"""
    points = IntegerField("Points", validators=[
        DataRequired(message='Le nombre de points est requis'),
        NumberRange(min=-10000, max=10000, message='Les points doivent être entre -10000 et 10000')
    ])
    transaction_type = SelectField("Type de transaction", choices=[
        ("earned", "Gagnés"),
        ("redeemed", "Utilisés"),
        ("adjusted", "Ajustement"),
        ("expired", "Expirés")
    ], validators=[DataRequired()])
    description = StringField("Description", validators=[
        Optional(),
        Length(max=255, message='La description ne peut pas dépasser 255 caractères')
    ])
    expires_at = DateField("Date d'expiration", validators=[Optional()])
    
    submit = SubmitField('Enregistrer')


class CustomerVisitForm(FlaskForm):
    """Formulaire pour enregistrer une visite"""
    visit_type = SelectField("Type de visite", choices=[
        ("in_store", "En magasin"),
        ("delivery", "Livraison"),
        ("takeaway", "À emporter"),
        ("online", "En ligne")
    ], validators=[DataRequired()])
    
    table_number = StringField("Numéro de table", validators=[
        Optional(),
        Length(max=10, message='Le numéro de table ne peut pas dépasser 10 caractères')
    ])
    party_size = IntegerField("Nombre de personnes", validators=[
        Optional(),
        NumberRange(min=1, max=20, message='Le nombre de personnes doit être entre 1 et 20')
    ])
    duration_minutes = IntegerField("Durée (minutes)", validators=[
        Optional(),
        NumberRange(min=0, max=1440, message='La durée doit être entre 0 et 1440 minutes')
    ])
    satisfaction_rating = SelectField("Note de satisfaction", choices=[
        ("", "Non évalué"),
        ("1", "1 - Très insatisfait"),
        ("2", "2 - Insatisfait"),
        ("3", "3 - Neutre"),
        ("4", "4 - Satisfait"),
        ("5", "5 - Très satisfait")
    ], validators=[Optional()])
    
    notes = TextAreaField("Notes", validators=[
        Optional(),
        Length(max=1000, message='Les notes ne peuvent pas dépasser 1000 caractères')
    ])
    
    submit = SubmitField('Enregistrer')


class CustomerSearchForm(FlaskForm):
    """Formulaire de recherche et filtrage des clients"""
    search = StringField("Recherche", validators=[Optional(), Length(max=100)])
    customer_type = SelectField("Type", choices=[
        ("", "Tous les types"),
        ("individual", "Particuliers"),
        ("company", "Entreprises")
    ], validators=[Optional()])
    status = SelectField("Statut", choices=[
        ("", "Tous les statuts"),
        ("active", "Actifs"),
        ("inactive", "Inactifs"),
        ("vip", "VIP")
    ], validators=[Optional()])
    sort_by = SelectField("Trier par", choices=[
        ("name_asc", "Nom (A-Z)"),
        ("name_desc", "Nom (Z-A)"),
        ("created_desc", "Plus récents"),
        ("created_asc", "Plus anciens"),
        ("last_visit_desc", "Dernière visite"),
        ("total_spent_desc", "Plus gros dépenseurs")
    ], default="name_asc", validators=[Optional()])
    
    submit = SubmitField('Filtrer')


class CustomerGroupForm(FlaskForm):
    """Formulaire pour les groupes de clients"""
    name = StringField("Nom du groupe", validators=[
        DataRequired(message='Le nom du groupe est requis'),
        Length(min=1, max=100, message='Le nom doit contenir entre 1 et 100 caractères')
    ])
    description = TextAreaField("Description", validators=[
        Optional(),
        Length(max=1000, message='La description ne peut pas dépasser 1000 caractères')
    ])
    color = StringField("Couleur", validators=[
        Optional(),
        Regexp(r'^#[0-9A-Fa-f]{6}$', message='La couleur doit être au format hexadécimal (#RRGGBB)')
    ], default="#3B82F6")
    
    # Critères d'appartenance
    min_orders = IntegerField("Nombre minimum de commandes", validators=[
        Optional(),
        NumberRange(min=0, message='Le nombre minimum ne peut pas être négatif')
    ])
    min_spent = DecimalField("Montant minimum dépensé (€)", validators=[
        Optional(),
        NumberRange(min=0, message='Le montant minimum ne peut pas être négatif')
    ], places=2)
    is_vip_only = BooleanField("Réservé aux clients VIP")
    
    # Avantages
    discount_percentage = DecimalField("Pourcentage de remise (%)", validators=[
        Optional(),
        NumberRange(min=0, max=100, message='Le pourcentage doit être entre 0 et 100')
    ], places=2)
    loyalty_multiplier = DecimalField("Multiplicateur de points fidélité", validators=[
        Optional(),
        NumberRange(min=0.1, max=10, message='Le multiplicateur doit être entre 0.1 et 10')
    ], places=2, default=1.0)
    
    is_active = BooleanField("Groupe actif", default=True)
    
    submit = SubmitField('Enregistrer')


class BulkCustomerUpdateForm(FlaskForm):
    """Formulaire pour les mises à jour en lot des clients"""
    customer_ids = HiddenField("IDs des clients")
    action = SelectField("Action", choices=[
        ("activate", "Activer"),
        ("deactivate", "Désactiver"),
        ("mark_vip", "Marquer comme VIP"),
        ("unmark_vip", "Retirer le statut VIP"),
        ("add_to_group", "Ajouter à un groupe"),
        ("remove_from_group", "Retirer d'un groupe"),
        ("export", "Exporter les données")
    ], validators=[DataRequired()])
    
    group_id = SelectField("Groupe", coerce=int, validators=[Optional()])
    
    submit = SubmitField('Appliquer')


class CustomerImportForm(FlaskForm):
    """Formulaire pour l'import de clients"""
    import_file = StringField("Fichier CSV", validators=[DataRequired()])
    has_headers = BooleanField("Le fichier contient des en-têtes", default=True)
    update_existing = BooleanField("Mettre à jour les clients existants")
    create_groups = BooleanField("Créer automatiquement les groupes")
    
    submit = SubmitField('Importer')


class CustomerExportForm(FlaskForm):
    """Formulaire pour l'export de clients"""
    export_format = SelectField("Format", choices=[
        ("csv", "CSV"),
        ("excel", "Excel"),
        ("pdf", "PDF")
    ], validators=[DataRequired()])
    
    include_addresses = BooleanField("Inclure les adresses", default=True)
    include_loyalty = BooleanField("Inclure les points de fidélité", default=True)
    include_visits = BooleanField("Inclure l'historique des visites")
    
    date_from = DateField("Clients créés à partir du", validators=[Optional()])
    date_to = DateField("Clients créés jusqu'au", validators=[Optional()])
    
    submit = SubmitField('Exporter')