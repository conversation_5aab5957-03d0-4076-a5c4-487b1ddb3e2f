<!DOCTYPE html>
<html lang="fr" class="h-full">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ screen.name }} - KDS</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
    <meta http-equiv="refresh" content="30"> <!-- Auto-refresh toutes les 30 secondes -->
</head>
<body class="h-full bg-black text-white overflow-hidden">
    <div class="h-full flex flex-col">
        <!-- En-tête de l'écran -->
        <div class="bg-slate-900 border-b border-slate-700 p-4 flex justify-between items-center">
            <div class="flex items-center gap-4">
                <h1 class="text-2xl font-bold">{{ screen.name }}</h1>
                <div id="connection-status" class="flex items-center gap-2">
                    <div class="w-3 h-3 rounded-full bg-green-500"></div>
                    <span class="text-sm text-slate-400">Connecté</span>
                </div>
            </div>
            
            <div class="flex items-center gap-4">
                <div id="current-time" class="text-xl font-mono"></div>
                <div class="text-sm text-slate-400">
                    <span id="ticket-count">0</span> commandes actives
                </div>
            </div>
        </div>
        
        <!-- Zone des tickets en plein écran -->
        <div class="flex-1 p-4 overflow-auto">
            <div id="tickets-display" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 h-full">
                <!-- Les tickets seront chargés ici dynamiquement -->
            </div>
            
            <!-- Message si aucun ticket -->
            <div id="no-tickets" class="hidden h-full flex items-center justify-center">
                <div class="text-center">
                    <div class="text-6xl mb-4">🍽️</div>
                    <h2 class="text-3xl font-bold mb-2">Aucune commande en cours</h2>
                    <p class="text-xl text-slate-400">En attente de nouvelles commandes...</p>
                </div>
            </div>
        </div>
    </div>
    
    <style>
        /* Styles pour l'affichage plein écran */
        .ticket-card {
            transition: all 0.3s ease;
            animation: slideInUp 0.5s ease-out;
        }
        
        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .status-pending { border-left: 6px solid #eab308; }
        .status-preparing { border-left: 6px solid #3b82f6; }
        .status-ready { border-left: 6px solid #10b981; }
        
        .timer-urgent {
            color: #ef4444;
            animation: pulse 1s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        /* Responsive pour grands écrans */
        @media (min-width: 1920px) {
            #tickets-display {
                grid-template-columns: repeat(5, 1fr);
            }
        }
        
        @media (min-width: 2560px) {
            #tickets-display {
                grid-template-columns: repeat(6, 1fr);
            }
        }
    </style>
    
    <script>
        // Variables globales
        let socket;
        let currentBusinessId;
        
        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            initializeDisplay();
            updateClock();
            setInterval(updateClock, 1000);
            setInterval(updateTimers, 1000);
            loadTickets();
            
            // Recharger les données toutes les 30 secondes
            setInterval(loadTickets, 30000);
        });
        
        // Initialiser l'affichage
        function initializeDisplay() {
            // Configuration WebSocket si disponible
            if (typeof io !== 'undefined') {
                socket = io();
                
                socket.on('connect', function() {
                    updateConnectionStatus(true);
                    // Rejoindre la room de l'entreprise
                    socket.emit('join_business');
                });
                
                socket.on('disconnect', function() {
                    updateConnectionStatus(false);
                });
                
                socket.on('ticket_status_updated', function(data) {
                    handleTicketUpdate(data);
                });
                
                socket.on('new_order', function(data) {
                    loadTickets(); // Recharger tous les tickets
                });
            }
        }
        
        // Mettre à jour l'horloge
        function updateClock() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('fr-FR', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            document.getElementById('current-time').textContent = timeString;
        }
        
        // Charger les tickets depuis l'API
        function loadTickets() {
            fetch('/kds/api/tickets?status=pending&status=preparing&status=ready')
                .then(response => response.json())
                .then(data => {
                    displayTickets(data.tickets);
                    updateTicketCount(data.total);
                })
                .catch(error => {
                    console.error('Erreur lors du chargement des tickets:', error);
                });
        }
        
        // Afficher les tickets
        function displayTickets(tickets) {
            const container = document.getElementById('tickets-display');
            const noTicketsMsg = document.getElementById('no-tickets');
            
            if (tickets.length === 0) {
                container.style.display = 'none';
                noTicketsMsg.classList.remove('hidden');
                return;
            }
            
            container.style.display = 'grid';
            noTicketsMsg.classList.add('hidden');
            
            container.innerHTML = tickets.map(ticket => createTicketHTML(ticket)).join('');
            
            // Initialiser les timers
            updateTimers();
        }
        
        // Créer le HTML d'un ticket
        function createTicketHTML(ticket) {
            const statusClass = `status-${ticket.status}`;
            const statusText = {
                'pending': 'En attente',
                'preparing': 'En préparation',
                'ready': 'Prêt'
            }[ticket.status] || ticket.status;
            
            const itemsHTML = ticket.items.map(item => `
                <div class="bg-slate-700 rounded p-2 mb-2">
                    <div class="flex justify-between items-center">
                        <div>
                            <p class="font-medium text-sm">${item.product_name}</p>
                            <p class="text-xs text-slate-300">${item.price.toFixed(2)}€</p>
                        </div>
                        <span class="text-lg font-bold">${item.quantity}</span>
                    </div>
                </div>
            `).join('');
            
            return `
                <div class="ticket-card bg-slate-800 rounded-lg ${statusClass} p-4 shadow-xl" 
                     data-ticket-id="${ticket.id}">
                    <div class="mb-3">
                        <h3 class="text-lg font-bold">Commande #${ticket.order_id}</h3>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-slate-400 timer" data-created="${ticket.created_at}">--:--</span>
                            <span class="bg-slate-600 px-2 py-1 rounded text-xs font-medium">${statusText}</span>
                        </div>
                    </div>
                    
                    <div class="space-y-1">
                        ${itemsHTML}
                    </div>
                </div>
            `;
        }
        
        // Mettre à jour les timers
        function updateTimers() {
            document.querySelectorAll('.timer').forEach(timer => {
                const createdAt = new Date(timer.dataset.created);
                const now = new Date();
                const diff = Math.floor((now - createdAt) / 1000);
                
                const minutes = Math.floor(diff / 60);
                const seconds = diff % 60;
                
                timer.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                
                // Marquer comme urgent après 15 minutes
                if (minutes >= 15) {
                    timer.classList.add('timer-urgent');
                }
            });
        }
        
        // Mettre à jour le compteur de tickets
        function updateTicketCount(count) {
            document.getElementById('ticket-count').textContent = count;
        }
        
        // Mettre à jour le statut de connexion
        function updateConnectionStatus(connected) {
            const statusEl = document.getElementById('connection-status');
            const dot = statusEl.querySelector('div');
            const text = statusEl.querySelector('span');
            
            if (connected) {
                dot.className = 'w-3 h-3 rounded-full bg-green-500';
                text.textContent = 'Connecté';
            } else {
                dot.className = 'w-3 h-3 rounded-full bg-red-500';
                text.textContent = 'Déconnecté';
            }
        }
        
        // Gérer les mises à jour de tickets via WebSocket
        function handleTicketUpdate(data) {
            // Recharger les tickets pour avoir les dernières données
            loadTickets();
        }
    </script>
</body>
</html>