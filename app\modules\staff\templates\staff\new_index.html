{% extends 'base.html' %}
{% block title %}Gestion du Personnel{% endblock %}
{% block content %}
<div class="max-w-7xl mx-auto">
  <div class="mb-6">
    <h1 class="text-3xl font-bold">Gestion du Personnel</h1>
    <p class="text-slate-400 mt-2"><PERSON><PERSON>rez vos employés, planifiez les horaires et suivez les performances</p>
  </div>

  <!-- Statistiques rapides -->
  <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
    <div class="rounded-xl bg-slate-900 border border-slate-700 p-6">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-slate-400 text-sm">Employés totaux</p>
          <h3 class="text-2xl font-bold text-cyan-400">{{ stats.total_employees }}</h3>
        </div>
        <div class="text-3xl">👥</div>
      </div>
    </div>
    
    <div class="rounded-xl bg-slate-900 border border-slate-700 p-6">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-slate-400 text-sm">Employés actifs</p>
          <h3 class="text-2xl font-bold text-green-400">{{ stats.active_employees }}</h3>
        </div>
        <div class="text-3xl">✅</div>
      </div>
    </div>
    
    <div class="rounded-xl bg-slate-900 border border-slate-700 p-6">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-slate-400 text-sm">Départements</p>
          <h3 class="text-2xl font-bold text-purple-400">{{ stats.departments_count }}</h3>
        </div>
        <div class="text-3xl">🏢</div>
      </div>
    </div>
  </div>

  <div class="grid grid-cols-12 gap-6">
    <!-- Filtres et formulaires -->
    <div class="col-span-12 lg:col-span-4 space-y-6">
      <!-- Filtres de recherche -->
      <div class="rounded-xl bg-slate-900 border border-slate-700 p-6">
        <h2 class="text-lg font-semibold mb-4">🔍 Filtres</h2>
        <form method="get" class="space-y-3">
          <input type="text" name="search" value="{{ search_form.search.data or '' }}" class="w-full bg-slate-800 border border-slate-700 rounded px-3 py-2" placeholder="Rechercher employés..." />
          
          <select name="department" class="w-full bg-slate-800 border border-slate-700 rounded px-3 py-2">
            {% for value, label in search_form.department.choices %}
            <option value="{{ value }}" {% if search_form.department.data == value %}selected{% endif %}>{{ label }}</option>
            {% endfor %}
          </select>
          
          <select name="position" class="w-full bg-slate-800 border border-slate-700 rounded px-3 py-2">
            {% for value, label in search_form.position.choices %}
            <option value="{{ value }}" {% if search_form.position.data == value %}selected{% endif %}>{{ label }}</option>
            {% endfor %}
          </select>
          
          <select name="status" class="w-full bg-slate-800 border border-slate-700 rounded px-3 py-2">
            {% for value, label in search_form.status.choices %}
            <option value="{{ value }}" {% if search_form.status.data == value %}selected{% endif %}>{{ label }}</option>
            {% endfor %}
          </select>
          
          <select name="sort_by" class="w-full bg-slate-800 border border-slate-700 rounded px-3 py-2">
            {% for value, label in search_form.sort_by.choices %}
            <option value="{{ value }}" {% if search_form.sort_by.data == value %}selected{% endif %}>{{ label }}</option>
            {% endfor %}
          </select>
          
          <button type="submit" class="w-full bg-cyan-600 hover:bg-cyan-500 rounded px-3 py-2">Filtrer</button>
        </form>
      </div>

      <!-- Nouvel employé -->
      <div class="rounded-xl bg-slate-900 border border-slate-700 p-6">
        <h2 class="text-lg font-semibold mb-4">👤 Nouvel employé</h2>
        <form method="post" class="space-y-3">
          {{ employee_form.csrf_token }}
          <input type="hidden" name="_action" value="create_employee" />
          
          <div class="grid grid-cols-2 gap-2">
            {{ employee_form.first_name(class="w-full bg-slate-800 border border-slate-700 rounded px-3 py-2", placeholder="Prénom") }}
            {{ employee_form.last_name(class="w-full bg-slate-800 border border-slate-700 rounded px-3 py-2", placeholder="Nom") }}
          </div>
          
          {{ employee_form.employee_code(class="w-full bg-slate-800 border border-slate-700 rounded px-3 py-2", placeholder="Code employé") }}
          {{ employee_form.email(class="w-full bg-slate-800 border border-slate-700 rounded px-3 py-2", placeholder="Email") }}
          {{ employee_form.phone(class="w-full bg-slate-800 border border-slate-700 rounded px-3 py-2", placeholder="Téléphone") }}
          
          <div class="grid grid-cols-2 gap-2">
            {{ employee_form.position(class="w-full bg-slate-800 border border-slate-700 rounded px-3 py-2", placeholder="Poste") }}
            {{ employee_form.department(class="w-full bg-slate-800 border border-slate-700 rounded px-3 py-2", placeholder="Département") }}
          </div>
          
          {{ employee_form.hire_date(class="w-full bg-slate-800 border border-slate-700 rounded px-3 py-2") }}
          {{ employee_form.salary_cents(class="w-full bg-slate-800 border border-slate-700 rounded px-3 py-2", placeholder="Salaire (centimes)") }}
          
          <div class="flex items-center">
            {{ employee_form.is_active(class="w-4 h-4 text-cyan-600 bg-slate-800 border-slate-700 rounded focus:ring-cyan-500 focus:ring-2") }}
            <label class="ml-2 text-sm text-slate-300">Actif</label>
          </div>
          
          <button type="submit" class="w-full bg-cyan-600 hover:bg-cyan-500 rounded px-3 py-2">Créer employé</button>
        </form>
      </div>

      <!-- Actions rapides -->
      <div class="rounded-xl bg-slate-900 border border-slate-700 p-6">
        <h2 class="text-lg font-semibold mb-4">⚡ Actions rapides</h2>
        <div class="space-y-2">
          <a href="{{ url_for('staff.schedule') }}" class="w-full bg-indigo-600 hover:bg-indigo-500 rounded px-3 py-2 text-center block">
            📅 Planification
          </a>
          <a href="{{ url_for('staff.timeclock') }}" class="w-full bg-green-600 hover:bg-green-500 rounded px-3 py-2 text-center block">
            ⏰ Pointage
          </a>
          <a href="{{ url_for('staff.reports') }}" class="w-full bg-purple-600 hover:bg-purple-500 rounded px-3 py-2 text-center block">
            📊 Rapports
          </a>
          <a href="{{ url_for('staff.bulk_update') }}" class="w-full bg-orange-600 hover:bg-orange-500 rounded px-3 py-2 text-center block">
            📝 Mise à jour en lot
          </a>
        </div>
      </div>
    </div>

    <!-- Vue principale avec onglets -->
    <div class="col-span-12 lg:col-span-8">
      <div class="rounded-xl bg-slate-900 border border-slate-700 p-6">
        <div class="flex items-center justify-between mb-4">
          <h2 class="text-lg font-semibold">Personnel</h2>
          <div class="flex space-x-2">
            <button id="tab-employees" class="px-3 py-1 bg-cyan-600 text-white rounded text-sm font-medium">Employés</button>
            <button id="tab-shifts" class="px-3 py-1 bg-slate-700 text-slate-300 rounded text-sm font-medium">Shifts</button>
          </div>
        </div>
        
        <!-- Onglet Employés -->
        <div id="content-employees">
          {% if employees %}
            <div class="space-y-3">
              {% for emp in employees %}
              <div class="bg-slate-800 border border-slate-700 rounded-lg p-4 employee-item" data-status="{{ 'active' if emp.is_active else 'inactive' }}">
                <div class="flex items-center justify-between">
                  <div class="flex-1">
                    <div class="flex items-center space-x-3">
                      <h3 class="font-semibold text-lg">{{ emp.last_name }} {{ emp.first_name }}</h3>
                      <span class="px-2 py-1 text-xs rounded-full {% if emp.is_active %}bg-green-900 text-green-300{% else %}bg-red-900 text-red-300{% endif %}">
                        {{ 'Actif' if emp.is_active else 'Inactif' }}
                      </span>
                    </div>
                    <div class="text-sm text-slate-400 mt-1">
                      Code: <span class="font-mono">{{ emp.employee_code }}</span> • 
                      {% if emp.position %}{{ emp.position }}{% endif %}
                      {% if emp.department %} • {{ emp.department }}{% endif %}
                    </div>
                    {% if emp.email %}
                      <div class="text-sm text-slate-500">📧 {{ emp.email }}</div>
                    {% endif %}
                  </div>
                  <div class="flex space-x-2 ml-4">
                    <a href="{{ url_for('staff.edit_employee', id=emp.id) }}" class="px-3 py-1 bg-blue-600 hover:bg-blue-500 text-white text-xs rounded" title="Modifier">
                      ✏️
                    </a>
                    <button onclick="toggleEmployeeStatus({{ emp.id }}, {{ 'false' if emp.is_active else 'true' }})" class="px-3 py-1 {% if emp.is_active %}bg-yellow-600 hover:bg-yellow-500{% else %}bg-green-600 hover:bg-green-500{% endif %} text-white text-xs rounded" title="{{ 'Désactiver' if emp.is_active else 'Activer' }}">
                      {{ '🚫' if emp.is_active else '✅' }}
                    </button>
                    <form method="post" action="{{ url_for('staff.delete_employee', id=emp.id) }}" class="inline" onsubmit="return confirm('Supprimer cet employé ?')">
                      {{ csrf_token() }}
                      <button class="px-3 py-1 bg-red-600 hover:bg-red-500 text-white text-xs rounded" title="Supprimer">
                        🗑️
                      </button>
                    </form>
                  </div>
                </div>
              </div>
              {% endfor %}
            </div>
          {% else %}
            <div class="text-center py-8">
              <div class="text-6xl mb-4">👥</div>
              <h3 class="text-xl font-semibold text-slate-300 mb-2">Aucun employé</h3>
              <p class="text-slate-400">Ajoutez votre premier employé pour commencer.</p>
            </div>
          {% endif %}
        </div>
        
        <!-- Onglet Shifts -->
        <div id="content-shifts" class="hidden">
          {% if shifts %}
            <div class="space-y-3">
              {% for shift in shifts %}
              <div class="bg-slate-800 border border-slate-700 rounded-lg p-4">
                <div class="flex items-center justify-between">
                  <div class="flex-1">
                    <h3 class="font-semibold">{{ shift.employee.last_name }} {{ shift.employee.first_name }}</h3>
                    <div class="text-sm text-slate-400 mt-1">
                      📅 {{ shift.shift_date.strftime('%d/%m/%Y') }} • 
                      ⏰ {{ shift.start_time.strftime('%H:%M') }} - {{ shift.end_time.strftime('%H:%M') }}
                      {% if shift.position %} • {{ shift.position }}{% endif %}
                    </div>
                  </div>
                  <div class="flex space-x-2">
                    <a href="{{ url_for('staff.edit_shift', id=shift.id) }}" class="px-3 py-1 bg-blue-600 hover:bg-blue-500 text-white text-xs rounded">
                      Modifier
                    </a>
                    <form method="post" action="{{ url_for('staff.delete_shift', id=shift.id) }}" class="inline" onsubmit="return confirm('Supprimer ce shift ?')">
                      {{ csrf_token() }}
                      <button class="px-3 py-1 bg-red-600 hover:bg-red-500 text-white text-xs rounded">
                        Supprimer
                      </button>
                    </form>
                  </div>
                </div>
              </div>
              {% endfor %}
            </div>
          {% else %}
            <div class="text-center py-8">
              <div class="text-6xl mb-4">📅</div>
              <h3 class="text-xl font-semibold text-slate-300 mb-2">Aucun shift</h3>
              <p class="text-slate-400">Créez votre premier shift ci-dessous.</p>
            </div>
          {% endif %}
          
          <!-- Formulaire pour nouveau shift -->
          <div class="mt-6 p-4 bg-slate-800 border border-slate-700 rounded-lg">
            <h4 class="font-semibold mb-3">📅 Nouveau shift</h4>
            <form method="post" class="space-y-3">
              {{ shift_form.csrf_token }}
              <input type="hidden" name="_action" value="create_shift" />
              
              <select name="{{ shift_form.employee_id_fk.name }}" class="w-full bg-slate-800 border border-slate-700 rounded px-3 py-2" required>
                <option value="">Sélectionner un employé</option>
                {% for val, label in shift_form.employee_id_fk.choices %}
                <option value="{{ val }}">{{ label }}</option>
                {% endfor %}
              </select>
              
              <div class="grid grid-cols-3 gap-2">
                {{ shift_form.shift_date(class="w-full bg-slate-800 border border-slate-700 rounded px-3 py-2") }}
                {{ shift_form.start_time(class="w-full bg-slate-800 border border-slate-700 rounded px-3 py-2") }}
                {{ shift_form.end_time(class="w-full bg-slate-800 border border-slate-700 rounded px-3 py-2") }}
              </div>
              
              <div class="grid grid-cols-2 gap-2">
                {{ shift_form.position(class="w-full bg-slate-800 border border-slate-700 rounded px-3 py-2", placeholder="Poste pour ce shift") }}
                {{ shift_form.break_duration_minutes(class="w-full bg-slate-800 border border-slate-700 rounded px-3 py-2", placeholder="Pause (min)") }}
              </div>
              
              {{ shift_form.notes(rows="2", class="w-full bg-slate-800 border border-slate-700 rounded px-3 py-2", placeholder="Notes...") }}
              
              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  {{ shift_form.is_active(class="w-4 h-4 text-cyan-600 bg-slate-800 border-slate-700 rounded focus:ring-cyan-500 focus:ring-2") }}
                  <label class="ml-2 text-sm text-slate-300">Shift actif</label>
                </div>
                <button type="submit" class="bg-cyan-600 hover:bg-cyan-500 rounded px-4 py-2">Créer shift</button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
// Gestion des onglets
const tabs = ['employees', 'shifts'];
let currentTab = 'employees';

tabs.forEach(tab => {
    document.getElementById(`tab-${tab}`).addEventListener('click', () => {
        switchTab(tab);
    });
});

function switchTab(tab) {
    tabs.forEach(t => {
        const tabElement = document.getElementById(`tab-${t}`);
        const contentElement = document.getElementById(`content-${t}`);
        
        if (t === tab) {
            tabElement.className = 'px-3 py-1 bg-cyan-600 text-white rounded text-sm font-medium';
            contentElement.classList.remove('hidden');
        } else {
            tabElement.className = 'px-3 py-1 bg-slate-700 text-slate-300 rounded text-sm font-medium';
            contentElement.classList.add('hidden');
        }
    });
    
    currentTab = tab;
}

// Changement de statut d'employé
function toggleEmployeeStatus(employeeId, newStatus) {
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '⏳';
    button.disabled = true;
    
    // Simuler l'appel API
    setTimeout(() => {
        const employeeElement = button.closest('.employee-item');
        const statusBadge = employeeElement.querySelector('.rounded-full');
        
        if (newStatus === 'true') {
            statusBadge.className = 'px-2 py-1 text-xs rounded-full bg-green-900 text-green-300';
            statusBadge.textContent = 'Actif';
            button.className = 'px-3 py-1 bg-yellow-600 hover:bg-yellow-500 text-white text-xs rounded';
            button.innerHTML = '🚫';
            button.onclick = () => toggleEmployeeStatus(employeeId, 'false');
            employeeElement.dataset.status = 'active';
        } else {
            statusBadge.className = 'px-2 py-1 text-xs rounded-full bg-red-900 text-red-300';
            statusBadge.textContent = 'Inactif';
            button.className = 'px-3 py-1 bg-green-600 hover:bg-green-500 text-white text-xs rounded';
            button.innerHTML = '✅';
            button.onclick = () => toggleEmployeeStatus(employeeId, 'true');
            employeeElement.dataset.status = 'inactive';
        }
        
        button.disabled = false;
        showNotification(`Employé ${newStatus === 'true' ? 'activé' : 'désactivé'}`);
    }, 1000);
}

function showNotification(message) {
    const notification = document.createElement('div');
    notification.className = 'fixed top-4 right-4 bg-green-600 text-white px-4 py-2 rounded-lg shadow-lg z-50';
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, 3000);
}
</script>
{% endblock %}