"""Modèles d'Intelligence Artificielle pour l'application POS System"""
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import numpy as np
import pandas as pd
from sklearn.linear_model import LinearRegression
from sklearn.ensemble import RandomForestRegressor
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, r2_score
import joblib
import logging

from app.extensions import db
from app.modules.sales.models import Order as POSOrder
from app.modules.catalog.models import Product
from app.modules.inventory.models import StockMovement
from app.modules.customers.models import Customer

logger = logging.getLogger(__name__)

class SalesForecastModel:
    """Modèle de prévision des ventes"""
    
    def __init__(self):
        self.model = None
        self.scaler = StandardScaler()
        self.is_trained = False
    
    def prepare_features(self, orders: List[POSOrder]) -> pd.DataFrame:
        """Prépare les caractéristiques pour la prévision"""
        data = []
        for order in orders:
            # Extraire les caractéristiques pertinentes
            features = {
                'day_of_week': order.created_at.weekday(),
                'month': order.created_at.month,
                'hour': order.created_at.hour,
                'total_amount': order.total_cents / 100,  # Convertir en euros
                'is_weekend': 1 if order.created_at.weekday() >= 5 else 0
            }
            data.append(features)
        
        return pd.DataFrame(data)
    
    def train(self, orders: List[POSOrder]) -> Dict[str, Any]:
        """Entraîne le modèle de prévision des ventes"""
        if not orders:
            raise ValueError("Aucune donnée de vente disponible pour l'entraînement")
        
        # Préparer les données
        df = self.prepare_features(orders)
        
        # Créer la cible (montant total des ventes)
        y = df['total_amount'].values
        
        # Créer les caractéristiques
        X = df.drop(['total_amount'], axis=1).values
        
        # Normaliser les caractéristiques
        X_scaled = self.scaler.fit_transform(X)
        
        # Entraîner le modèle
        self.model = LinearRegression()
        self.model.fit(X_scaled, y)
        self.is_trained = True
        
        # Calculer les métriques
        y_pred = self.model.predict(X_scaled)
        mse = mean_squared_error(y, y_pred)
        r2 = r2_score(y, y_pred)
        
        logger.info(f"Modèle de prévision entraîné - MSE: {mse:.2f}, R²: {r2:.2f}")
        
        return {
            'mse': mse,
            'r2': r2,
            'samples': len(orders)
        }
    
    def predict(self, features: Dict[str, Any]) -> float:
        """Prédit le montant des ventes"""
        if not self.is_trained:
            raise ValueError("Le modèle n'est pas encore entraîné")
        
        # Convertir les caractéristiques en DataFrame
        df = pd.DataFrame([features])
        X = df.values
        X_scaled = self.scaler.transform(X)
        
        # Faire la prédiction
        prediction = self.model.predict(X_scaled)[0]
        return max(0, prediction)  # Assurer que la prédiction est positive
    
    def save_model(self, filepath: str):
        """Sauvegarde le modèle"""
        if self.is_trained:
            model_data = {
                'model': self.model,
                'scaler': self.scaler,
                'is_trained': self.is_trained
            }
            joblib.dump(model_data, filepath)
            logger.info(f"Modèle sauvegardé dans {filepath}")
    
    def load_model(self, filepath: str):
        """Charge le modèle"""
        try:
            model_data = joblib.load(filepath)
            self.model = model_data['model']
            self.scaler = model_data['scaler']
            self.is_trained = model_data['is_trained']
            logger.info(f"Modèle chargé depuis {filepath}")
        except Exception as e:
            logger.error(f"Erreur lors du chargement du modèle: {e}")

class ProductRecommendationModel:
    """Modèle de recommandation de produits"""
    
    def __init__(self):
        self.model = None
        self.product_associations = {}
        self.is_trained = False
    
    def train(self, orders: List[POSOrder]) -> Dict[str, Any]:
        """Entraîne le modèle de recommandation"""
        if not orders:
            raise ValueError("Aucune donnée de commande disponible pour l'entraînement")
        
        # Construire la matrice de co-occurrence des produits
        product_pairs = {}
        product_counts = {}
        
        for order in orders:
            product_ids = [item.product_id_fk for item in order.items]
            
            # Compter les occurrences individuelles
            for product_id in product_ids:
                product_counts[product_id] = product_counts.get(product_id, 0) + 1
            
            # Compter les paires de produits
            for i in range(len(product_ids)):
                for j in range(i + 1, len(product_ids)):
                    pair = tuple(sorted([product_ids[i], product_ids[j]]))
                    product_pairs[pair] = product_pairs.get(pair, 0) + 1
        
        # Calculer les associations
        self.product_associations = {}
        for pair, count in product_pairs.items():
            product1, product2 = pair
            # Calculer la confiance (probabilité conditionnelle)
            confidence_1_to_2 = count / product_counts[product1]
            confidence_2_to_1 = count / product_counts[product2]
            
            if product1 not in self.product_associations:
                self.product_associations[product1] = []
            if product2 not in self.product_associations:
                self.product_associations[product2] = []
            
            self.product_associations[product1].append({
                'product_id': product2,
                'confidence': confidence_1_to_2,
                'support': count
            })
            
            self.product_associations[product2].append({
                'product_id': product1,
                'confidence': confidence_2_to_1,
                'support': count
            })
        
        self.is_trained = True
        
        logger.info(f"Modèle de recommandation entraîné avec {len(self.product_associations)} produits")
        
        return {
            'associations': len(product_pairs),
            'products': len(product_counts)
        }
    
    def get_recommendations(self, product_id: int, limit: int = 5) -> List[Dict[str, Any]]:
        """Obtient les recommandations pour un produit"""
        if not self.is_trained:
            raise ValueError("Le modèle n'est pas encore entraîné")
        
        if product_id not in self.product_associations:
            return []
        
        # Trier par confiance et retourner les meilleures recommandations
        recommendations = sorted(
            self.product_associations[product_id],
            key=lambda x: x['confidence'],
            reverse=True
        )
        
        return recommendations[:limit]
    
    def get_user_recommendations(self, user_order_history: List[int], limit: int = 10) -> List[Dict[str, Any]]:
        """Obtient les recommandations basées sur l'historique de l'utilisateur"""
        if not self.is_trained:
            raise ValueError("Le modèle n'est pas encore entraîné")
        
        # Collecter toutes les recommandations possibles
        all_recommendations = {}
        
        for product_id in user_order_history:
            if product_id in self.product_associations:
                for rec in self.product_associations[product_id]:
                    rec_product_id = rec['product_id']
                    # Ne pas recommander des produits déjà achetés
                    if rec_product_id not in user_order_history:
                        if rec_product_id not in all_recommendations:
                            all_recommendations[rec_product_id] = {
                                'product_id': rec_product_id,
                                'confidence': rec['confidence'],
                                'support': rec['support'],
                                'count': 1
                            }
                        else:
                            # Combiner les scores
                            all_recommendations[rec_product_id]['confidence'] += rec['confidence']
                            all_recommendations[rec_product_id]['support'] += rec['support']
                            all_recommendations[rec_product_id]['count'] += 1
        
        # Calculer les scores moyens
        for rec in all_recommendations.values():
            rec['confidence'] /= rec['count']
        
        # Trier par confiance et retourner les meilleures recommandations
        sorted_recommendations = sorted(
            all_recommendations.values(),
            key=lambda x: x['confidence'],
            reverse=True
        )
        
        return sorted_recommendations[:limit]

class CustomerSegmentationModel:
    """Modèle de segmentation client"""
    
    def __init__(self):
        self.model = KMeans(n_clusters=5, random_state=42)
        self.scaler = StandardScaler()
        self.is_trained = False
    
    def prepare_customer_features(self, orders: List[POSOrder]) -> pd.DataFrame:
        """Prépare les caractéristiques des clients"""
        # Regrouper les commandes par client
        customer_data = {}
        
        for order in orders:
            customer_id = order.customer_id_fk or order.id  # Utiliser l'ID de commande si pas de client
            
            if customer_id not in customer_data:
                customer_data[customer_id] = {
                    'total_spent': 0,
                    'order_count': 0,
                    'avg_order_value': 0,
                    'days_since_last_order': 0,
                    'frequency': 0
                }
            
            customer_data[customer_id]['total_spent'] += order.total_cents / 100
            customer_data[customer_id]['order_count'] += 1
        
        # Calculer les caractéristiques dérivées
        for customer_id, data in customer_data.items():
            if data['order_count'] > 0:
                data['avg_order_value'] = data['total_spent'] / data['order_count']
                data['frequency'] = data['order_count']  # Simplification
        
        return pd.DataFrame.from_dict(customer_data, orient='index')
    
    def train(self, orders: List[POSOrder]) -> Dict[str, Any]:
        """Entraîne le modèle de segmentation client"""
        if not orders:
            raise ValueError("Aucune donnée de commande disponible pour l'entraînement")
        
        # Préparer les données
        df = self.prepare_customer_features(orders)
        
        if df.empty:
            raise ValueError("Aucune donnée client disponible")
        
        # Sélectionner les caractéristiques
        features = ['total_spent', 'order_count', 'avg_order_value', 'frequency']
        X = df[features].values
        
        # Normaliser les caractéristiques
        X_scaled = self.scaler.fit_transform(X)
        
        # Entraîner le modèle
        self.model.fit(X_scaled)
        self.is_trained = True
        
        # Calculer les centroïdes
        centroids = self.model.cluster_centers_
        
        logger.info(f"Modèle de segmentation client entraîné avec {len(centroids)} clusters")
        
        return {
            'clusters': len(centroids),
            'samples': len(df)
        }
    
    def predict_segment(self, customer_features: Dict[str, Any]) -> int:
        """Prédit le segment d'un client"""
        if not self.is_trained:
            raise ValueError("Le modèle n'est pas encore entraîné")
        
        # Convertir les caractéristiques
        features = ['total_spent', 'order_count', 'avg_order_value', 'frequency']
        values = [customer_features.get(f, 0) for f in features]
        
        # Normaliser
        X = np.array([values])
        X_scaled = self.scaler.transform(X)
        
        # Prédire le cluster
        cluster = self.model.predict(X_scaled)[0]
        return cluster
    
    def get_segment_characteristics(self) -> List[Dict[str, Any]]:
        """Obtient les caractéristiques des segments"""
        if not self.is_trained:
            raise ValueError("Le modèle n'est pas encore entraîné")
        
        # Inverse la normalisation des centroïdes
        centroids = self.scaler.inverse_transform(self.model.cluster_centers_)
        
        segments = []
        for i, centroid in enumerate(centroids):
            segments.append({
                'segment_id': i,
                'total_spent': centroid[0],
                'order_count': centroid[1],
                'avg_order_value': centroid[2],
                'frequency': centroid[3]
            })
        
        return segments

class InventoryOptimizationModel:
    """Modèle d'optimisation des stocks"""
    
    def __init__(self):
        self.forecast_model = SalesForecastModel()
        self.is_trained = False
    
    def calculate_eoq(self, demand_rate: float, ordering_cost: float, holding_cost: float) -> float:
        """Calcule la quantité économique de commande (EOQ)"""
        if holding_cost <= 0:
            return demand_rate  # Éviter la division par zéro
        
        eoq = np.sqrt((2 * demand_rate * ordering_cost) / holding_cost)
        return eoq
    
    def calculate_safety_stock(self, demand_std: float, lead_time: float, service_level: float = 1.65) -> float:
        """Calcule le stock de sécurité"""
        safety_stock = service_level * demand_std * np.sqrt(lead_time)
        return safety_stock
    
    def optimize_reorder_point(self, avg_daily_demand: float, lead_time: float, 
                             demand_std: float, service_level: float = 1.65) -> Dict[str, float]:
        """Optimise le point de commande"""
        # Calculer le point de commande
        reorder_point = avg_daily_demand * lead_time + self.calculate_safety_stock(
            demand_std, lead_time, service_level
        )
        
        return {
            'reorder_point': reorder_point,
            'safety_stock': self.calculate_safety_stock(demand_std, lead_time, service_level)
        }
    
    def analyze_inventory_performance(self, stock_movements: List[StockMovement]) -> Dict[str, Any]:
        """Analyse la performance des stocks"""
        if not stock_movements:
            return {}
        
        # Calculer les métriques de performance
        total_in = sum(m.quantity for m in stock_movements if m.movement_type == 'IN')
        total_out = sum(m.quantity for m in stock_movements if m.movement_type == 'OUT')
        
        # Taux de rotation des stocks
        if total_in > 0:
            turnover_rate = total_out / total_in
        else:
            turnover_rate = 0
        
        # Identifier les stocks à risque
        low_stock_items = []
        high_stock_items = []
        
        # Regrouper par produit
        product_stocks = {}
        for movement in stock_movements:
            product_id = movement.product_id_fk
            if product_id not in product_stocks:
                product_stocks[product_id] = {
                    'total_in': 0,
                    'total_out': 0,
                    'current_stock': 0
                }
            
            if movement.movement_type == 'IN':
                product_stocks[product_id]['total_in'] += movement.quantity
                product_stocks[product_id]['current_stock'] += movement.quantity
            else:
                product_stocks[product_id]['total_out'] += movement.quantity
                product_stocks[product_id]['current_stock'] -= movement.quantity
        
        # Analyser chaque produit
        for product_id, stock_data in product_stocks.items():
            if stock_data['current_stock'] < 10:  # Seuil arbitraire
                low_stock_items.append({
                    'product_id': product_id,
                    'current_stock': stock_data['current_stock']
                })
            elif stock_data['current_stock'] > 1000:  # Seuil arbitraire
                high_stock_items.append({
                    'product_id': product_id,
                    'current_stock': stock_data['current_stock']
                })
        
        return {
            'turnover_rate': turnover_rate,
            'total_in': total_in,
            'total_out': total_out,
            'low_stock_items': low_stock_items,
            'high_stock_items': high_stock_items
        }

# Nouveau modèle : Prédiction de la désertion client (Churn Prediction)
class ChurnPredictionModel:
    """Modèle de prédiction de la désertion client"""
    
    def __init__(self):
        self.model = RandomForestRegressor(n_estimators=100, random_state=42)
        self.scaler = StandardScaler()
        self.is_trained = False
        self.feature_names = [
            'days_since_last_order',
            'total_orders',
            'total_spent',
            'avg_order_value',
            'frequency',
            'recency_score',
            'monetary_score',
            'frequency_score'
        ]
    
    def prepare_features(self, customers: List[Customer], orders: List[POSOrder]) -> pd.DataFrame:
        """Prépare les caractéristiques pour la prédiction de churn"""
        # Créer un dictionnaire pour les commandes par client
        customer_orders = {}
        for order in orders:
            customer_id = order.customer_id_fk
            if customer_id:
                if customer_id not in customer_orders:
                    customer_orders[customer_id] = []
                customer_orders[customer_id].append(order)
        
        data = []
        for customer in customers:
            customer_id = customer.id
            
            # Initialiser les caractéristiques
            features = {
                'customer_id': customer_id,
                'days_since_last_order': 365,  # Valeur par défaut
                'total_orders': 0,
                'total_spent': 0,
                'avg_order_value': 0,
                'frequency': 0,
                'recency_score': 0,
                'monetary_score': 0,
                'frequency_score': 0
            }
            
            # Calculer les caractéristiques à partir de l'historique des commandes
            if customer_id in customer_orders:
                customer_order_list = customer_orders[customer_id]
                features['total_orders'] = len(customer_order_list)
                
                # Calculer le total dépensé
                total_spent = sum(order.total_cents / 100 for order in customer_order_list)
                features['total_spent'] = total_spent
                
                # Calculer la valeur moyenne des commandes
                if features['total_orders'] > 0:
                    features['avg_order_value'] = total_spent / features['total_orders']
                
                # Calculer la fréquence
                features['frequency'] = features['total_orders']
                
                # Calculer les jours depuis la dernière commande
                if customer_order_list:
                    last_order_date = max(order.created_at for order in customer_order_list)
                    days_since_last = (datetime.utcnow() - last_order_date).days
                    features['days_since_last_order'] = days_since_last
                
                # Calculer les scores RFM (Recency, Frequency, Monetary)
                # Ces scores sont simplifiés pour cet exemple
                features['recency_score'] = max(0, 100 - features['days_since_last_order'])
                features['monetary_score'] = features['total_spent']
                features['frequency_score'] = features['total_orders']
            
            data.append(features)
        
        return pd.DataFrame(data)
    
    def train(self, customers: List[Customer], orders: List[POSOrder]) -> Dict[str, Any]:
        """Entraîne le modèle de prédiction de churn"""
        if not customers:
            raise ValueError("Aucune donnée client disponible pour l'entraînement")
        
        # Préparer les données
        df = self.prepare_features(customers, orders)
        
        if df.empty:
            raise ValueError("Aucune donnée disponible pour l'entraînement")
        
        # Créer la cible (churn: 1 si le client n'a pas commandé depuis 90 jours, 0 sinon)
        df['churn'] = (df['days_since_last_order'] > 90).astype(int)
        
        # Sélectionner les caractéristiques
        X = df[self.feature_names].values
        y = df['churn'].values
        
        # Normaliser les caractéristiques
        X_scaled = self.scaler.fit_transform(X)
        
        # Entraîner le modèle
        self.model.fit(X_scaled, y)
        self.is_trained = True
        
        # Calculer l'importance des caractéristiques
        feature_importance = dict(zip(self.feature_names, self.model.feature_importances_))
        
        logger.info(f"Modèle de prédiction de churn entraîné")
        
        return {
            'samples': len(df),
            'churn_rate': df['churn'].mean(),
            'feature_importance': feature_importance
        }
    
    def predict_churn_probability(self, customer_features: Dict[str, Any]) -> float:
        """Prédit la probabilité de churn pour un client"""
        if not self.is_trained:
            raise ValueError("Le modèle n'est pas encore entraîné")
        
        # Convertir les caractéristiques
        values = [customer_features.get(f, 0) for f in self.feature_names]
        
        # Normaliser
        X = np.array([values])
        X_scaled = self.scaler.transform(X)
        
        # Prédire la probabilité de churn
        churn_probability = self.model.predict_proba(X_scaled)[0][1]  # Probabilité de la classe 1 (churn)
        return churn_probability
    
    def get_churn_risk_customers(self, customers: List[Customer], orders: List[POSOrder], 
                               risk_threshold: float = 0.7) -> List[Dict[str, Any]]:
        """Identifie les clients à risque de churn"""
        if not self.is_trained:
            raise ValueError("Le modèle n'est pas encore entraîné")
        
        # Préparer les données
        df = self.prepare_features(customers, orders)
        
        risk_customers = []
        for _, row in df.iterrows():
            customer_features = row[self.feature_names].to_dict()
            churn_probability = self.predict_churn_probability(customer_features)
            
            if churn_probability > risk_threshold:
                risk_customers.append({
                    'customer_id': int(row['customer_id']),
                    'churn_probability': churn_probability,
                    'days_since_last_order': int(row['days_since_last_order']),
                    'total_spent': float(row['total_spent'])
                })
        
        # Trier par probabilité de churn décroissante
        risk_customers.sort(key=lambda x: x['churn_probability'], reverse=True)
        
        return risk_customers

# Nouveau modèle : Analyse de sentiment des avis clients
class SentimentAnalysisModel:
    """Modèle d'analyse de sentiment des avis clients"""
    
    def __init__(self):
        # Pour cet exemple, nous utiliserons une approche basée sur des mots-clés
        # Dans une implémentation réelle, on utiliserait un modèle de NLP comme BERT
        self.positive_words = {
            'excellent', 'super', 'génial', 'parfait', 'merveilleux', 'fantastique',
            'bon', 'bien', 'satisfait', 'content', 'agréable', 'sympathique',
            'rapide', 'efficace', 'qualité', 'propre', 'accueillant'
        }
        
        self.negative_words = {
            'horrible', 'terrible', 'mauvais', 'déçu', 'médiocre', 'nul',
            'lent', 'lenteur', 'cher', 'problème', 'défaut', 'sale',
            'impoli', 'désagréable', 'inutile', 'inutiles'
        }
        
        self.is_trained = True  # Ce modèle n'a pas besoin d'entraînement
    
    def analyze_sentiment(self, text: str) -> Dict[str, Any]:
        """Analyse le sentiment d'un texte"""
        # Convertir en minuscules et diviser en mots
        words = text.lower().split()
        
        # Compter les mots positifs et négatifs
        positive_count = sum(1 for word in words if word in self.positive_words)
        negative_count = sum(1 for word in words if word in self.negative_words)
        
        # Calculer le score de sentiment
        total_sentiment_words = positive_count + negative_count
        if total_sentiment_words > 0:
            sentiment_score = (positive_count - negative_count) / total_sentiment_words
        else:
            sentiment_score = 0  # Neutre si aucun mot de sentiment trouvé
        
        # Déterminer le label de sentiment
        if sentiment_score > 0.1:
            sentiment_label = 'positive'
        elif sentiment_score < -0.1:
            sentiment_label = 'negative'
        else:
            sentiment_label = 'neutral'
        
        return {
            'sentiment_score': sentiment_score,
            'sentiment_label': sentiment_label,
            'positive_words_count': positive_count,
            'negative_words_count': negative_count
        }
    
    def analyze_reviews(self, reviews: List[str]) -> Dict[str, Any]:
        """Analyse les sentiments de plusieurs avis"""
        results = []
        for review in reviews:
            sentiment = self.analyze_sentiment(review)
            results.append(sentiment)
        
        # Calculer les statistiques globales
        total_reviews = len(results)
        if total_reviews > 0:
            positive_reviews = sum(1 for r in results if r['sentiment_label'] == 'positive')
            negative_reviews = sum(1 for r in results if r['sentiment_label'] == 'negative')
            neutral_reviews = total_reviews - positive_reviews - negative_reviews
            
            overall_sentiment_score = sum(r['sentiment_score'] for r in results) / total_reviews
            
            if overall_sentiment_score > 0.1:
                overall_sentiment_label = 'positive'
            elif overall_sentiment_score < -0.1:
                overall_sentiment_label = 'negative'
            else:
                overall_sentiment_label = 'neutral'
        else:
            positive_reviews = negative_reviews = neutral_reviews = 0
            overall_sentiment_score = 0
            overall_sentiment_label = 'neutral'
        
        return {
            'total_reviews': total_reviews,
            'positive_reviews': positive_reviews,
            'negative_reviews': negative_reviews,
            'neutral_reviews': neutral_reviews,
            'overall_sentiment_score': overall_sentiment_score,
            'overall_sentiment_label': overall_sentiment_label,
            'reviews_analysis': results
        }