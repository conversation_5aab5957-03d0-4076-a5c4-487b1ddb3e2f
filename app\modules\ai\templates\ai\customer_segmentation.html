{% extends "base.html" %}

{% block title %}Segmentation Client{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-users"></i> Segmentation Client</h1>
        <button id="trainModelBtn" class="btn btn-primary">
            <i class="fas fa-brain"></i> Entraîner le Modèle
        </button>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Analyse de Segment</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">ID du client</label>
                                <input type="number" class="form-control" id="customerId" placeholder="Entrez l'ID du client">
                            </div>
                            
                            <button id="analyzeCustomerBtn" class="btn btn-success" disabled>
                                <i class="fas fa-search"></i> Analyser le Client
                            </button>
                        </div>
                        
                        <div class="col-md-6">
                            <div id="customerSegmentResult" class="d-none">
                                <h6>Segment du Client: <span id="segmentId" class="badge bg-primary">-</span></h6>
                                <div class="mt-3">
                                    <p><strong>Caractéristiques:</strong></p>
                                    <ul class="list-unstyled">
                                        <li>Total dépensé: <span id="totalSpent">€0.00</span></li>
                                        <li>Nombre de commandes: <span id="orderCount">0</span></li>
                                        <li>Valeur moyenne: <span id="avgOrderValue">€0.00</span></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Visualisation des Segments</h5>
                </div>
                <div class="card-body">
                    <canvas id="segmentsChart" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Caractéristiques des Segments</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Segment</th>
                                    <th>Total Dépensé Moyen</th>
                                    <th>Nombre de Commandes Moyen</th>
                                    <th>Valeur Moyenne</th>
                                    <th>Fréquence</th>
                                </tr>
                            </thead>
                            <tbody id="segmentsTable">
                                <!-- Les caractéristiques des segments seront affichées ici -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Métriques du Modèle</h5>
                </div>
                <div class="card-body">
                    <div class="row" id="modelMetrics">
                        <div class="col-md-6">
                            <div class="text-center">
                                <h3 id="clustersValue">-</h3>
                                <p class="text-muted">Nombre de Segments</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="text-center">
                                <h3 id="samplesValue">-</h3>
                                <p class="text-muted">Clients Analysés</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Variables globales
    let segmentsChart = null;
    
    // Initialisation du graphique
    function initChart() {
        const ctx = document.getElementById('segmentsChart').getContext('2d');
        segmentsChart = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: ['Segment 0', 'Segment 1', 'Segment 2', 'Segment 3', 'Segment 4'],
                datasets: [{
                    data: [20, 20, 20, 20, 20],
                    backgroundColor: [
                        'rgb(255, 99, 132)',
                        'rgb(54, 162, 235)',
                        'rgb(255, 205, 86)',
                        'rgb(75, 192, 192)',
                        'rgb(153, 102, 255)'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom',
                    }
                }
            }
        });
    }
    
    // Activer le bouton quand un ID client est entré
    document.getElementById('customerId').addEventListener('input', function() {
        document.getElementById('analyzeCustomerBtn').disabled = !this.value;
    });
    
    // Entraîner le modèle
    document.getElementById('trainModelBtn').addEventListener('click', function() {
        fetch('/ai/api/customer_segmentation/train', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Modèle de segmentation client entraîné avec succès!');
                
                // Mettre à jour les métriques
                document.getElementById('clustersValue').textContent = data.metrics.clusters;
                document.getElementById('samplesValue').textContent = data.metrics.samples;
            } else {
                alert('Erreur: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur lors de l\'entraînement du modèle');
        });
    });
    
    // Analyser un client
    document.getElementById('analyzeCustomerBtn').addEventListener('click', function() {
        const customerId = document.getElementById('customerId').value;
        
        fetch(`/ai/api/customer_segmentation/analyze/${customerId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Afficher les résultats
                document.getElementById('segmentId').textContent = data.segment;
                document.getElementById('segmentId').className = 'badge bg-primary';
                document.getElementById('totalSpent').textContent = '€' + data.customer_features.total_spent.toFixed(2);
                document.getElementById('orderCount').textContent = data.customer_features.order_count;
                document.getElementById('avgOrderValue').textContent = '€' + data.customer_features.avg_order_value.toFixed(2);
                
                document.getElementById('customerSegmentResult').classList.remove('d-none');
                
                // Mettre à jour le tableau des segments
                const tableBody = document.getElementById('segmentsTable');
                tableBody.innerHTML = '';
                
                data.segment_characteristics.forEach(segment => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td><span class="badge bg-secondary">Segment ${segment.segment_id}</span></td>
                        <td>€${segment.total_spent.toFixed(2)}</td>
                        <td>${segment.order_count.toFixed(0)}</td>
                        <td>€${segment.avg_order_value.toFixed(2)}</td>
                        <td>${segment.frequency.toFixed(0)}</td>
                    `;
                    tableBody.appendChild(row);
                });
            } else {
                alert('Erreur: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur lors de l\'analyse du client');
        });
    });
    
    // Initialiser le graphique quand la page est chargée
    document.addEventListener('DOMContentLoaded', function() {
        initChart();
    });
</script>
{% endblock %}