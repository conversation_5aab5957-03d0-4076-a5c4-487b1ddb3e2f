{% extends 'base.html' %}
{% block title %}Achats - Dashboard{% endblock %}

{% block head %}
<style>
    .purchasing-dashboard {
        background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
        min-height: 100vh;
        padding: 20px 0;
    }
    
    .stats-card {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        padding: 20px;
        color: white;
        transition: transform 0.2s;
    }
    
    .stats-card:hover {
        transform: translateY(-2px);
    }
    
    .stats-value {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .quick-actions-card {
        background: white;
        border-radius: 12px;
        padding: 20px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }
    
    .action-btn {
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        color: white;
        border: none;
        border-radius: 8px;
        padding: 12px 20px;
        font-weight: 500;
        transition: all 0.2s;
        text-decoration: none;
        display: inline-block;
        text-align: center;
    }
    
    .action-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
        color: white;
        text-decoration: none;
    }
    
    .recent-item {
        border-left: 4px solid #3b82f6;
        background: #f8fafc;
        padding: 15px;
        border-radius: 0 8px 8px 0;
        margin-bottom: 10px;
    }
    
    .status-badge {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 500;
        text-transform: uppercase;
    }
    
    .status-draft { background: #fef3c7; color: #92400e; }
    .status-pending { background: #fecaca; color: #991b1b; }
    .status-approved { background: #d1fae5; color: #065f46; }
    .status-sent { background: #dbeafe; color: #1e40af; }
    .status-received { background: #e0e7ff; color: #3730a3; }
</style>
{% endblock %}

{% block content %}
<div class="purchasing-dashboard">
    <div class="container mx-auto px-4">
        <!-- Header -->
        <div class="flex items-center justify-between mb-8">
            <div>
                <h1 class="text-4xl font-bold text-white mb-2">
                    <i class="fas fa-shopping-cart mr-3"></i>Module Achats
                </h1>
                <p class="text-blue-100">Gestion des commandes, réceptions et fournisseurs</p>
            </div>
            <div class="flex space-x-3">
                <a href="{{ url_for('purchasing.new_purchase_order') }}" class="action-btn">
                    <i class="fas fa-plus mr-2"></i>Nouvelle Commande
                </a>
                <a href="{{ url_for('purchasing.new_purchase_receipt') }}" class="action-btn">
                    <i class="fas fa-truck mr-2"></i>Nouvelle Réception
                </a>
            </div>
        </div>

        <!-- Statistiques -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="stats-card">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="stats-value">{{ pending_orders }}</div>
                        <div class="text-sm opacity-90">Commandes en cours</div>
                    </div>
                    <i class="fas fa-clock text-3xl opacity-70"></i>
                </div>
            </div>
            
            <div class="stats-card">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="stats-value">{{ orders_to_receive }}</div>
                        <div class="text-sm opacity-90">À réceptionner</div>
                    </div>
                    <i class="fas fa-truck text-3xl opacity-70"></i>
                </div>
            </div>
            
            <div class="stats-card">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="stats-value">{{ "%.0f"|format(monthly_spending) }}€</div>
                        <div class="text-sm opacity-90">Dépenses du mois</div>
                    </div>
                    <i class="fas fa-euro-sign text-3xl opacity-70"></i>
                </div>
            </div>
            
            <div class="stats-card">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="stats-value">{{ active_suppliers }}</div>
                        <div class="text-sm opacity-90">Fournisseurs actifs</div>
                    </div>
                    <i class="fas fa-building text-3xl opacity-70"></i>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Actions rapides -->
            <div class="lg:col-span-1">
                <div class="quick-actions-card mb-6">
                    <h3 class="text-lg font-semibold mb-4">
                        <i class="fas fa-bolt text-blue-600 mr-2"></i>Commande Rapide
                    </h3>
                    
                    <form method="post" action="{{ url_for('purchasing.quick_order') }}" class="space-y-4">
                        {{ quick_order_form.csrf_token }}
                        
                        <div>
                            {{ quick_order_form.supplier_id(class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500") }}
                        </div>
                        
                        <div>
                            {{ quick_order_form.product_name(class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500", placeholder="Nom du produit") }}
                        </div>
                        
                        <div class="grid grid-cols-2 gap-3">
                            <div>
                                {{ quick_order_form.quantity(class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500", placeholder="Quantité") }}
                            </div>
                            <div>
                                {{ quick_order_form.unit_cost(class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500", placeholder="Prix unitaire") }}
                            </div>
                        </div>
                        
                        <div>
                            {{ quick_order_form.expected_delivery_date(class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500") }}
                        </div>
                        
                        <div>
                            {{ quick_order_form.notes(class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500", rows="2", placeholder="Notes (optionnel)") }}
                        </div>
                        
                        <button type="submit" class="w-full action-btn">
                            <i class="fas fa-shopping-cart mr-2"></i>Créer la Commande
                        </button>
                    </form>
                </div>
                
                <!-- Navigation rapide -->
                <div class="quick-actions-card">
                    <h3 class="text-lg font-semibold mb-4">
                        <i class="fas fa-compass text-blue-600 mr-2"></i>Navigation
                    </h3>
                    
                    <div class="space-y-3">
                        <a href="{{ url_for('purchasing.purchase_orders') }}" class="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                            <i class="fas fa-file-invoice text-blue-600 mr-3"></i>
                            <span class="font-medium">Bons de Commande</span>
                        </a>
                        
                        <a href="{{ url_for('purchasing.purchase_receipts') }}" class="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                            <i class="fas fa-truck-loading text-green-600 mr-3"></i>
                            <span class="font-medium">Réceptions</span>
                        </a>
                        
                        <a href="{{ url_for('suppliers.index') }}" class="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                            <i class="fas fa-building text-purple-600 mr-3"></i>
                            <span class="font-medium">Fournisseurs</span>
                        </a>
                        
                        <a href="#" class="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors" onclick="showAnalytics()">
                            <i class="fas fa-chart-line text-orange-600 mr-3"></i>
                            <span class="font-medium">Analytics</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Commandes récentes -->
            <div class="lg:col-span-2">
                <div class="quick-actions-card mb-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold">
                            <i class="fas fa-clock text-blue-600 mr-2"></i>Commandes Récentes
                        </h3>
                        <a href="{{ url_for('purchasing.purchase_orders') }}" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                            Voir toutes →
                        </a>
                    </div>
                    
                    {% if recent_orders %}
                        <div class="space-y-3">
                            {% for order in recent_orders %}
                            <div class="recent-item">
                                <div class="flex items-center justify-between">
                                    <div class="flex-1">
                                        <div class="flex items-center space-x-3 mb-2">
                                            <span class="font-semibold text-gray-900">{{ order.order_number }}</span>
                                            <span class="status-badge status-{{ order.status.value }}">{{ order.status.value }}</span>
                                        </div>
                                        
                                        <div class="text-sm text-gray-600 mb-1">
                                            <i class="fas fa-building mr-1"></i>
                                            Fournisseur: {{ order.supplier.name if order.supplier else 'N/A' }}
                                        </div>
                                        
                                        <div class="flex items-center space-x-4 text-xs text-gray-500">
                                            <span>
                                                <i class="fas fa-calendar mr-1"></i>
                                                {{ order.order_date.strftime('%d/%m/%Y') if order.order_date else 'N/A' }}
                                            </span>
                                            <span>
                                                <i class="fas fa-euro-sign mr-1"></i>
                                                {{ "%.2f"|format(order.total) }}€
                                            </span>
                                            <span>
                                                <i class="fas fa-boxes mr-1"></i>
                                                {{ order.get_items_count() }} articles
                                            </span>
                                        </div>
                                    </div>
                                    
                                    <div class="flex space-x-2">
                                        <a href="{{ url_for('purchasing.view_purchase_order', id=order.id) }}" 
                                           class="text-blue-600 hover:text-blue-800" title="Voir">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        {% if order.status.value in ['draft', 'pending'] %}
                                        <a href="{{ url_for('purchasing.edit_purchase_order', id=order.id) }}" 
                                           class="text-green-600 hover:text-green-800" title="Modifier">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-8 text-gray-500">
                            <i class="fas fa-shopping-cart text-4xl mb-3"></i>
                            <p>Aucune commande récente</p>
                            <a href="{{ url_for('purchasing.new_purchase_order') }}" class="text-blue-600 hover:text-blue-800 text-sm">
                                Créer votre première commande
                            </a>
                        </div>
                    {% endif %}
                </div>
                
                <!-- Réceptions récentes -->
                <div class="quick-actions-card">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold">
                            <i class="fas fa-truck text-green-600 mr-2"></i>Réceptions Récentes
                        </h3>
                        <a href="{{ url_for('purchasing.purchase_receipts') }}" class="text-green-600 hover:text-green-800 text-sm font-medium">
                            Voir toutes →
                        </a>
                    </div>
                    
                    {% if recent_receipts %}
                        <div class="space-y-3">
                            {% for receipt in recent_receipts %}
                            <div class="border-l-4 border-green-500 bg-green-50 p-3 rounded-r-lg">
                                <div class="flex items-center justify-between">
                                    <div class="flex-1">
                                        <div class="flex items-center space-x-3 mb-1">
                                            <span class="font-semibold text-gray-900">{{ receipt.receipt_number }}</span>
                                            <span class="status-badge bg-green-100 text-green-800">{{ receipt.status.value }}</span>
                                        </div>
                                        
                                        <div class="text-sm text-gray-600 mb-1">
                                            <i class="fas fa-file-invoice mr-1"></i>
                                            Commande: {{ receipt.purchase_order.order_number if receipt.purchase_order else 'N/A' }}
                                        </div>
                                        
                                        <div class="flex items-center space-x-4 text-xs text-gray-500">
                                            <span>
                                                <i class="fas fa-calendar mr-1"></i>
                                                {{ receipt.receipt_date.strftime('%d/%m/%Y') if receipt.receipt_date else 'N/A' }}
                                            </span>
                                            {% if receipt.quality_rating %}
                                            <span>
                                                <i class="fas fa-star mr-1"></i>
                                                Qualité: {{ receipt.quality_rating }}/5
                                            </span>
                                            {% endif %}
                                        </div>
                                    </div>
                                    
                                    <a href="{{ url_for('purchasing.view_purchase_receipt', id=receipt.id) }}" 
                                       class="text-green-600 hover:text-green-800" title="Voir">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-8 text-gray-500">
                            <i class="fas fa-truck text-4xl mb-3"></i>
                            <p>Aucune réception récente</p>
                            <a href="{{ url_for('purchasing.new_purchase_receipt') }}" class="text-green-600 hover:text-green-800 text-sm">
                                Créer votre première réception
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function showAnalytics() {
    // Simple analytics modal ou redirection
    alert('Module Analytics à venir - Affichage des statistiques d\'achat par fournisseur, tendances, etc.');
}

// Auto-refresh des statistiques toutes les 5 minutes
setInterval(function() {
    fetch(window.location.href, {
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        if (response.ok) {
            console.log('Dashboard data refreshed');
        }
    })
    .catch(error => console.log('Refresh failed:', error));
}, 300000);
</script>
{% endblock %}


