from __future__ import annotations

from datetime import datetime, date, timedelta
from flask import render_template, request, redirect, url_for, flash, jsonify, abort, send_file
from flask_login import login_required, current_user
from sqlalchemy.orm import joinedload
from sqlalchemy import and_, or_, desc, func, text
import json
import io

from app.extensions import db
from . import bp
from .models import (
    ReportTemplate, ReportSchedule, ReportExecution, ReportData, ReportWidget,
    ReportType, ReportFrequency, ReportFormat, ReportStatus
)
from .forms import (
    ReportTemplateForm, ReportScheduleForm, ReportFilterForm,
    QuickReportForm, ReportExportForm, ReportSearchForm
)
from .utils import ReportGenerator, ReportExporter

# Import conditionnel pour éviter les erreurs si les modules n'existent pas encore
try:
    from ..inventory.models import Product, Category
except ImportError:
    Product = Category = None

try:
    from ..sales.models import Sale, SaleItem
except ImportError:
    Sale = SaleItem = None

try:
    from ..suppliers.models import Supplier
except ImportError:
    Supplier = None

try:
    from ..purchasing.models import PurchaseOrder
except ImportError:
    PurchaseOrder = None

try:
    from ..customers.models import Customer
except ImportError:
    Customer = None


@bp.get("/")
@login_required
def index():
    """Dashboard principal des rapports"""
    # Statistiques rapides
    stats = {
        'total_templates': ReportTemplate.query.count(),
        'scheduled_reports': ReportSchedule.query.filter_by(is_active=True).count(),
        'executions_today': ReportExecution.query.filter(
            func.date(ReportExecution.executed_at) == date.today()
        ).count(),
        'successful_executions': ReportExecution.query.filter_by(
            status=ReportStatus.COMPLETED
        ).count()
    }
    
    # Dernières exécutions
    recent_executions = ReportExecution.query.options(
        joinedload(ReportExecution.template)
    ).order_by(desc(ReportExecution.executed_at)).limit(10).all()
    
    # Templates populaires
    popular_templates = db.session.query(
        ReportTemplate, func.count(ReportExecution.id).label('execution_count')
    ).outerjoin(ReportExecution).group_by(ReportTemplate.id).order_by(
        desc('execution_count')
    ).limit(5).all()
    
    # Rapports planifiés à venir
    upcoming_schedules = ReportSchedule.query.filter(
        ReportSchedule.is_active == True,
        ReportSchedule.next_run <= date.today() + timedelta(days=7)
    ).order_by(ReportSchedule.next_run).limit(5).all()
    
    return render_template(
        "reports/index.html",
        stats=stats,
        recent_executions=recent_executions,
        popular_templates=popular_templates,
        upcoming_schedules=upcoming_schedules
    )


@bp.get("/templates")
@login_required
def list_templates():
    """Liste des templates de rapports"""
    search_form = ReportSearchForm()
    page = request.args.get('page', 1, type=int)
    per_page = 10
    
    query = ReportTemplate.query
    
    # Filtres de recherche
    if request.args.get('search'):
        search_term = f"%{request.args.get('search')}%"
        query = query.filter(
            or_(
                ReportTemplate.name.ilike(search_term),
                ReportTemplate.description.ilike(search_term)
            )
        )
    
    if request.args.get('report_type'):
        query = query.filter_by(report_type=request.args.get('report_type'))
    
    templates = query.order_by(desc(ReportTemplate.created_at)).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return render_template(
        "reports/templates.html",
        templates=templates,
        search_form=search_form
    )


@bp.get("/templates/new")
@login_required
def new_template():
    """Formulaire de création d'un nouveau template"""
    form = ReportTemplateForm()
    return render_template("reports/new_template.html", form=form)


@bp.post("/templates")
@login_required
def create_template():
    """Créer un nouveau template de rapport"""
    form = ReportTemplateForm()
    
    if form.validate_on_submit():
        template = ReportTemplate(
            name=form.name.data,
            description=form.description.data,
            report_type=form.report_type.data,
            query_config=form.query_config.data,
            chart_config=form.chart_config.data,
            layout_config=form.layout_config.data,
            is_public=form.is_public.data,
            created_by_id=current_user.id
        )
        
        db.session.add(template)
        db.session.commit()
        
        flash("Template de rapport créé avec succès!", "success")
        return redirect(url_for('reports.view_template', id=template.id))
    
    return render_template("reports/new_template.html", form=form)


@bp.get("/templates/<int:id>")
@login_required
def view_template(id):
    """Voir les détails d'un template"""
    template = ReportTemplate.query.get_or_404(id)
    
    # Dernières exécutions de ce template
    recent_executions = ReportExecution.query.filter_by(
        template_id=id
    ).order_by(desc(ReportExecution.executed_at)).limit(5).all()
    
    # Statistiques d'exécution
    execution_stats = {
        'total_executions': ReportExecution.query.filter_by(template_id=id).count(),
        'successful_executions': ReportExecution.query.filter_by(
            template_id=id, status=ReportStatus.COMPLETED
        ).count(),
        'failed_executions': ReportExecution.query.filter_by(
            template_id=id, status=ReportStatus.FAILED
        ).count()
    }
    
    return render_template(
        "reports/view_template.html",
        template=template,
        recent_executions=recent_executions,
        execution_stats=execution_stats
    )


@bp.get("/templates/<int:id>/edit")
@login_required
def edit_template(id):
    """Formulaire d'édition d'un template"""
    template = ReportTemplate.query.get_or_404(id)
    
    if template.created_by_id != current_user.id and not current_user.is_admin:
        abort(403)
    
    form = ReportTemplateForm(obj=template)
    return render_template("reports/edit_template.html", form=form, template=template)


@bp.post("/templates/<int:id>")
@login_required
def update_template(id):
    """Mettre à jour un template"""
    template = ReportTemplate.query.get_or_404(id)
    
    if template.created_by_id != current_user.id and not current_user.is_admin:
        abort(403)
    
    form = ReportTemplateForm()
    
    if form.validate_on_submit():
        template.name = form.name.data
        template.description = form.description.data
        template.report_type = form.report_type.data
        template.query_config = form.query_config.data
        template.chart_config = form.chart_config.data
        template.layout_config = form.layout_config.data
        template.is_public = form.is_public.data
        template.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        flash("Template mis à jour avec succès!", "success")
        return redirect(url_for('reports.view_template', id=template.id))
    
    return render_template("reports/edit_template.html", form=form, template=template)


@bp.post("/templates/<int:id>/delete")
@login_required
def delete_template(id):
    """Supprimer un template"""
    template = ReportTemplate.query.get_or_404(id)
    
    if template.created_by_id != current_user.id and not current_user.is_admin:
        abort(403)
    
    # Vérifier s'il y a des planifications actives
    active_schedules = ReportSchedule.query.filter_by(
        template_id=id, is_active=True
    ).count()
    
    if active_schedules > 0:
        flash("Impossible de supprimer ce template car il a des planifications actives.", "error")
        return redirect(url_for('reports.view_template', id=id))
    
    db.session.delete(template)
    db.session.commit()
    
    flash("Template supprimé avec succès!", "success")
    return redirect(url_for('reports.list_templates'))


@bp.post("/templates/<int:id>/execute")
@login_required
def execute_template(id):
    """Exécuter un template de rapport"""
    template = ReportTemplate.query.get_or_404(id)
    
    try:
        # Créer une nouvelle exécution
        execution = ReportExecution(
            template_id=template.id,
            executed_by_id=current_user.id,
            status=ReportStatus.RUNNING,
            parameters=request.get_json() or {}
        )
        
        db.session.add(execution)
        db.session.flush()  # Pour obtenir l'ID
        
        # Générer les données du rapport
        if Sale and SaleItem:
            report_data = _generate_report_data_inline(template, execution.parameters)
        else:
            # Données factices si les modules ne sont pas disponibles
            report_data = [
                {'key': 'placeholder', 'value': 'Module non disponible', 'type': 'text'}
            ]
        
        # Sauvegarder les données
        for data_item in report_data:
            report_data_obj = ReportData(
                execution_id=execution.id,
                data_key=data_item['key'],
                data_value=data_item['value'],
                data_type=data_item['type']
            )
            db.session.add(report_data_obj)
        
        execution.status = ReportStatus.COMPLETED
        execution.completed_at = datetime.utcnow()
        
        db.session.commit()
        
        return jsonify({
            'status': 'success',
            'execution_id': execution.id,
            'message': 'Rapport exécuté avec succès!'
        })
        
    except Exception as e:
        db.session.rollback()
        execution.status = ReportStatus.FAILED
        execution.error_message = str(e)
        execution.completed_at = datetime.utcnow()
        db.session.commit()
        
        return jsonify({
            'status': 'error',
            'message': f'Erreur lors de l\'exécution du rapport: {str(e)}'
        }), 500


@bp.get("/executions")
@login_required
def list_executions():
    """Liste des exécutions de rapports"""
    page = request.args.get('page', 1, type=int)
    per_page = 20
    
    query = ReportExecution.query.options(
        joinedload(ReportExecution.template),
        joinedload(ReportExecution.executed_by)
    )
    
    # Filtres
    if request.args.get('status'):
        query = query.filter_by(status=request.args.get('status'))
    
    if request.args.get('template_id'):
        query = query.filter_by(template_id=request.args.get('template_id'))
    
    executions = query.order_by(desc(ReportExecution.executed_at)).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return render_template("reports/executions.html", executions=executions)


@bp.get("/executions/<int:id>")
@login_required
def view_execution(id):
    """Voir les détails d'une exécution"""
    execution = ReportExecution.query.options(
        joinedload(ReportExecution.template),
        joinedload(ReportExecution.executed_by),
        joinedload(ReportExecution.data)
    ).get_or_404(id)
    
    return render_template("reports/view_execution.html", execution=execution)


@bp.get("/executions/<int:id>/export/<format>")
@login_required
def export_execution(id, format):
    """Exporter une exécution de rapport"""
    execution = ReportExecution.query.options(
        joinedload(ReportExecution.template),
        joinedload(ReportExecution.data)
    ).get_or_404(id)
    
    try:
        exporter = ReportExporter()
        
        # Préparer les données pour l'export
        report_data = {
            'data': [{
                'key': item.data_key,
                'value': item.data_value,
                'type': item.data_type
            } for item in execution.data],
            'metadata': {
                'template_name': execution.template.name,
                'executed_at': execution.executed_at.isoformat(),
                'executed_by': execution.executed_by.username if execution.executed_by else 'Système'
            },
            'charts': []
        }
        
        if format.lower() == 'pdf':
            buffer = exporter.export_to_pdf(report_data)
            return send_file(
                buffer,
                as_attachment=True,
                download_name=f"rapport_{execution.id}.pdf",
                mimetype='application/pdf'
            )
        elif format.lower() == 'excel':
            buffer = exporter.export_to_excel(report_data)
            return send_file(
                buffer,
                as_attachment=True,
                download_name=f"rapport_{execution.id}.xlsx",
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
        elif format.lower() == 'csv':
            buffer = exporter.export_to_csv(report_data)
            output = buffer.getvalue()
            buffer_bytes = io.BytesIO(output.encode('utf-8'))
            return send_file(
                buffer_bytes,
                as_attachment=True,
                download_name=f"rapport_{execution.id}.csv",
                mimetype='text/csv'
            )
        else:
            abort(400, "Format d'export non supporté")
    
    except Exception as e:
        flash(f"Erreur lors de l'export: {str(e)}", "error")
        return redirect(url_for('reports.view_execution', id=id))


@bp.get("/schedules")
@login_required
def list_schedules():
    """Liste des planifications de rapports"""
    page = request.args.get('page', 1, type=int)
    per_page = 15
    
    schedules = ReportSchedule.query.options(
        joinedload(ReportSchedule.template)
    ).order_by(desc(ReportSchedule.created_at)).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return render_template("reports/schedules.html", schedules=schedules)


@bp.get("/schedules/new")
@login_required
def new_schedule():
    """Formulaire de nouvelle planification"""
    form = ReportScheduleForm()
    return render_template("reports/new_schedule.html", form=form)


@bp.post("/schedules")
@login_required
def create_schedule():
    """Créer une nouvelle planification"""
    form = ReportScheduleForm()
    
    if form.validate_on_submit():
        schedule = ReportSchedule(
            template_id=form.template_id.data,
            name=form.name.data,
            frequency=form.frequency.data,
            start_date=form.start_date.data,
            end_date=form.end_date.data,
            email_recipients=form.email_recipients.data,
            parameters=form.parameters.data or {},
            is_active=form.is_active.data,
            created_by_id=current_user.id
        )
        
        # Calculer la prochaine exécution
        schedule._calculate_next_run()
        
        db.session.add(schedule)
        db.session.commit()
        
        flash("Planification créée avec succès!", "success")
        return redirect(url_for('reports.view_schedule', id=schedule.id))
    
    return render_template("reports/new_schedule.html", form=form)


@bp.get("/schedules/<int:id>")
@login_required
def view_schedule(id):
    """Voir les détails d'une planification"""
    schedule = ReportSchedule.query.options(
        joinedload(ReportSchedule.template)
    ).get_or_404(id)
    
    return render_template("reports/view_schedule.html", schedule=schedule)


@bp.post("/schedules/<int:id>/toggle")
@login_required
def toggle_schedule(id):
    """Activer/désactiver une planification"""
    schedule = ReportSchedule.query.get_or_404(id)
    
    if schedule.created_by_id != current_user.id and not current_user.is_admin:
        abort(403)
    
    schedule.is_active = not schedule.is_active
    
    if schedule.is_active:
        schedule._calculate_next_run()
    
    db.session.commit()
    
    status = "activée" if schedule.is_active else "désactivée"
    flash(f"Planification {status} avec succès!", "success")
    
    return redirect(url_for('reports.view_schedule', id=id))


@bp.get("/quick-report")
@login_required
def quick_report():
    """Interface de génération rapide de rapport"""
    form = QuickReportForm()
    return render_template("reports/quick_report.html", form=form)


@bp.post("/quick-report")
@login_required
def generate_quick_report():
    """Générer un rapport rapide"""
    form = QuickReportForm()
    
    if form.validate_on_submit():
        try:
            # Générer les données selon le type de rapport
            report_data = _generate_quick_report_data_inline(
                form.report_type.data,
                form.date_from.data,
                form.date_to.data,
                form.filters.data or {}
            )
            
            return jsonify({
                'status': 'success',
                'data': report_data,
                'message': 'Rapport généré avec succès!'
            })
            
        except Exception as e:
            return jsonify({
                'status': 'error',
                'message': f'Erreur lors de la génération: {str(e)}'
            }), 500
    
    return render_template("reports/quick_report.html", form=form)


@bp.get("/analytics")
@login_required
def analytics_dashboard():
    """Dashboard d'analytics avancé"""
    # Récupérer les données pour les graphiques
    analytics_data = _get_analytics_data_inline()
    
    return render_template("reports/analytics.html", analytics_data=analytics_data)


@bp.get("/api/chart-data/<chart_type>")
@login_required
def get_chart_data(chart_type):
    """API pour récupérer les données de graphiques"""
    try:
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')
        
        if date_from:
            date_from = datetime.strptime(date_from, '%Y-%m-%d').date()
        if date_to:
            date_to = datetime.strptime(date_to, '%Y-%m-%d').date()
        
        data = _get_chart_data_inline(chart_type, date_from, date_to)
        
        return jsonify({
            'status': 'success',
            'data': data
        })
        
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500


# Fonctions utilitaires
def _generate_report_data(template, parameters):
    """Générer les données d'un rapport selon le template"""
    report_type = template.report_type
    query_config = template.query_config or {}
    
    if report_type == ReportType.SALES:
        return _generate_sales_report_data(query_config, parameters)
    elif report_type == ReportType.FINANCIAL:
        return _generate_financial_report_data(query_config, parameters)
    elif report_type == ReportType.INVENTORY:
        return _generate_inventory_report_data(query_config, parameters)
    elif report_type == ReportType.CUSTOMER:
        return _generate_customer_report_data(query_config, parameters)
    elif report_type == ReportType.SUPPLIER:
        return _generate_supplier_report_data(query_config, parameters)
    elif report_type == ReportType.PRODUCTION:
        return _generate_production_report_data(query_config, parameters)
    else:
        return []


def _generate_sales_report_data(query_config, parameters):
    """Générer des données de rapport de ventes"""
    data = []
    
    # Ventes totales
    total_sales = db.session.query(func.sum(Sale.total_amount)).scalar() or 0
    data.append({
        'key': 'total_sales',
        'value': float(total_sales),
        'type': 'numeric'
    })
    
    # Nombre de ventes
    sales_count = Sale.query.count()
    data.append({
        'key': 'sales_count',
        'value': sales_count,
        'type': 'numeric'
    })
    
    # Ventes par jour (dernier mois)
    daily_sales = db.session.query(
        func.date(Sale.created_at).label('date'),
        func.sum(Sale.total_amount).label('total')
    ).filter(
        Sale.created_at >= datetime.now() - timedelta(days=30)
    ).group_by(func.date(Sale.created_at)).all()
    
    data.append({
        'key': 'daily_sales',
        'value': [{'date': str(row.date), 'total': float(row.total)} for row in daily_sales],
        'type': 'chart_data'
    })
    
    # Top produits
    top_products = db.session.query(
        Product.name,
        func.sum(SaleItem.quantity).label('quantity_sold'),
        func.sum(SaleItem.subtotal).label('revenue')
    ).join(SaleItem).group_by(Product.id).order_by(
        desc('revenue')
    ).limit(10).all()
    
    data.append({
        'key': 'top_products',
        'value': [{
            'name': row.name,
            'quantity': int(row.quantity_sold),
            'revenue': float(row.revenue)
        } for row in top_products],
        'type': 'table_data'
    })
    
    return data


def _generate_financial_report_data(query_config, parameters):
    """Générer des données de rapport financier"""
    # Redirection vers la fonction inline pour éviter les erreurs d'import
    return _generate_financial_report_data_inline(query_config, parameters)


def _generate_inventory_report_data(query_config, parameters):
    """Générer des données de rapport d'inventaire"""
    # Redirection vers la fonction inline pour éviter les erreurs d'import
    return _generate_inventory_report_data_inline(query_config, parameters)


def _generate_customer_report_data(query_config, parameters):
    """Générer des données de rapport client"""
    # Redirection vers la fonction inline pour éviter les erreurs d'import
    return _generate_customer_report_data_inline(query_config, parameters)


def _generate_supplier_report_data(query_config, parameters):
    """Générer des données de rapport fournisseur"""
    # Redirection vers la fonction inline pour éviter les erreurs d'import
    return _generate_supplier_report_data_inline(query_config, parameters)


def _generate_production_report_data(query_config, parameters):
    """Générer des données de rapport de production"""
    # Redirection vers la fonction inline pour éviter les erreurs d'import
    return _generate_production_report_data_inline(query_config, parameters)


# Fonctions utilitaires inline pour éviter les erreurs d'import
def _generate_report_data_inline(template, parameters):
    """Générer les données d'un rapport selon le template"""
    report_type = template.report_type
    query_config = template.query_config or {}
    
    if report_type == ReportType.SALES and Sale and SaleItem:
        return _generate_sales_report_data_inline(query_config, parameters)
    elif report_type == ReportType.FINANCIAL and Sale and PurchaseOrder:
        return _generate_financial_report_data_inline(query_config, parameters)
    elif report_type == ReportType.INVENTORY and Product:
        return _generate_inventory_report_data_inline(query_config, parameters)
    elif report_type == ReportType.CUSTOMER and Customer:
        return _generate_customer_report_data_inline(query_config, parameters)
    elif report_type == ReportType.SUPPLIER and Supplier:
        return _generate_supplier_report_data_inline(query_config, parameters)
    elif report_type == ReportType.PRODUCTION:
        return _generate_production_report_data_inline(query_config, parameters)
    else:
        return [{'key': 'error', 'value': 'Type de rapport non supporté ou module manquant', 'type': 'text'}]


def _generate_sales_report_data_inline(query_config, parameters):
    """Générer des données de rapport de ventes"""
    if not Sale or not SaleItem:
        return [{'key': 'error', 'value': 'Module sales non disponible', 'type': 'text'}]
    
    data = []
    
    try:
        # Ventes totales
        total_sales = db.session.query(func.sum(Sale.total_amount)).scalar() or 0
        data.append({
            'key': 'total_sales',
            'value': float(total_sales),
            'type': 'numeric'
        })
        
        # Nombre de ventes
        sales_count = Sale.query.count()
        data.append({
            'key': 'sales_count',
            'value': sales_count,
            'type': 'numeric'
        })
        
    except Exception as e:
        data.append({
            'key': 'error',
            'value': f'Erreur lors de la génération: {str(e)}',
            'type': 'text'
        })
    
    return data


def _generate_financial_report_data_inline(query_config, parameters):
    """Générer des données de rapport financier"""
    data = []
    
    try:
        # Revenus totaux
        if Sale:
            total_revenue = db.session.query(func.sum(Sale.total_amount)).scalar() or 0
            data.append({
                'key': 'total_revenue',
                'value': float(total_revenue),
                'type': 'numeric'
            })
        
        # Coûts d'achat
        if PurchaseOrder:
            total_costs = db.session.query(func.sum(PurchaseOrder.total_amount)).scalar() or 0
            data.append({
                'key': 'total_costs',
                'value': float(total_costs),
                'type': 'numeric'
            })
            
            # Profit brut
            if Sale:
                gross_profit = (db.session.query(func.sum(Sale.total_amount)).scalar() or 0) - total_costs
                data.append({
                    'key': 'gross_profit',
                    'value': float(gross_profit),
                    'type': 'numeric'
                })
        
    except Exception as e:
        data.append({
            'key': 'error',
            'value': f'Erreur lors de la génération: {str(e)}',
            'type': 'text'
        })
    
    return data


def _generate_inventory_report_data_inline(query_config, parameters):
    """Générer des données de rapport d'inventaire"""
    if not Product:
        return [{'key': 'error', 'value': 'Module inventory non disponible', 'type': 'text'}]
    
    data = []
    
    try:
        # Nombre total de produits
        total_products = Product.query.count()
        data.append({
            'key': 'total_products',
            'value': total_products,
            'type': 'numeric'
        })
        
    except Exception as e:
        data.append({
            'key': 'error',
            'value': f'Erreur lors de la génération: {str(e)}',
            'type': 'text'
        })
    
    return data


def _generate_customer_report_data_inline(query_config, parameters):
    """Générer des données de rapport client"""
    if not Customer:
        return [{'key': 'error', 'value': 'Module customers non disponible', 'type': 'text'}]
    
    data = []
    
    try:
        # Nombre total de clients
        total_customers = Customer.query.count()
        data.append({
            'key': 'total_customers',
            'value': total_customers,
            'type': 'numeric'
        })
        
    except Exception as e:
        data.append({
            'key': 'error',
            'value': f'Erreur lors de la génération: {str(e)}',
            'type': 'text'
        })
    
    return data


def _generate_supplier_report_data_inline(query_config, parameters):
    """Générer des données de rapport fournisseur"""
    if not Supplier:
        return [{'key': 'error', 'value': 'Module suppliers non disponible', 'type': 'text'}]
    
    data = []
    
    try:
        # Nombre total de fournisseurs
        total_suppliers = Supplier.query.count()
        data.append({
            'key': 'total_suppliers',
            'value': total_suppliers,
            'type': 'numeric'
        })
        
    except Exception as e:
        data.append({
            'key': 'error',
            'value': f'Erreur lors de la génération: {str(e)}',
            'type': 'text'
        })
    
    return data


def _generate_production_report_data_inline(query_config, parameters):
    """Générer des données de rapport de production"""
    return [
        {
            'key': 'production_status',
            'value': 'Module production à implémenter',
            'type': 'text'
        }
    ]


def _generate_quick_report_data_inline(report_type, date_from, date_to, filters):
    """Générer des données pour un rapport rapide"""
    if report_type == 'sales' and Sale:
        return _get_sales_summary_inline(date_from, date_to)
    elif report_type == 'inventory' and Product:
        return _get_inventory_summary_inline()
    elif report_type == 'financial' and Sale:
        return _get_financial_summary_inline(date_from, date_to)
    else:
        return {'error': 'Type de rapport non supporté ou module manquant'}


def _get_sales_summary_inline(date_from, date_to):
    """Résumé des ventes"""
    if not Sale:
        return {'error': 'Module sales non disponible'}
    
    try:
        query = Sale.query
        
        if date_from:
            query = query.filter(Sale.created_at >= date_from)
        if date_to:
            query = query.filter(Sale.created_at <= date_to)
        
        sales = query.all()
        
        total_amount = sum(getattr(sale, 'total_amount', 0) for sale in sales)
        
        return {
            'total_sales': len(sales),
            'total_amount': float(total_amount),
            'average_sale': float(total_amount / len(sales)) if sales else 0
        }
    except Exception as e:
        return {'error': f'Erreur: {str(e)}'}


def _get_inventory_summary_inline():
    """Résumé de l'inventaire"""
    if not Product:
        return {'error': 'Module inventory non disponible'}
    
    try:
        products = Product.query.all()
        return {
            'total_products': len(products),
            'message': 'Données d\'inventaire disponibles'
        }
    except Exception as e:
        return {'error': f'Erreur: {str(e)}'}


def _get_financial_summary_inline(date_from, date_to):
    """Résumé financier"""
    try:
        total_revenue = 0
        total_costs = 0
        
        if Sale:
            revenue_query = db.session.query(func.sum(Sale.total_amount))
            if date_from:
                revenue_query = revenue_query.filter(Sale.created_at >= date_from)
            if date_to:
                revenue_query = revenue_query.filter(Sale.created_at <= date_to)
            
            total_revenue = revenue_query.scalar() or 0
        
        return {
            'total_revenue': float(total_revenue),
            'total_costs': float(total_costs),
            'gross_profit': float(total_revenue - total_costs)
        }
    except Exception as e:
        return {'error': f'Erreur: {str(e)}'}


def _get_analytics_data_inline():
    """Récupérer les données d'analytics"""
    try:
        return {
            'sales_trend': [],
            'top_products': [],
            'revenue_by_category': [],
            'monthly_comparison': {'current_month': 0, 'last_month': 0, 'growth': 0}
        }
    except Exception as e:
        return {
            'error': f'Erreur lors de la récupération des analytics: {str(e)}'
        }


def _get_chart_data_inline(chart_type, date_from, date_to):
    """Récupérer les données pour un type de graphique spécifique"""
    try:
        if chart_type == 'sales_trend':
            return []
        elif chart_type == 'product_performance':
            return []
        elif chart_type == 'revenue_breakdown':
            return []
        else:
            return []
    except Exception as e:
        return [{'error': str(e)}]


