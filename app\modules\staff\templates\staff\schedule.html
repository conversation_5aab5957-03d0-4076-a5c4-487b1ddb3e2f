{% extends 'base.html' %}
{% block title %}Planification des Horaires{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto">
  <!-- En-tête -->
  <div class="mb-6">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold">📅 Planification des Horaires</h1>
        <p class="text-slate-400 mt-2">Gérez les plannings et horaires de votre équipe</p>
      </div>
      <div class="flex space-x-3">
        <a href="{{ url_for('staff.index') }}" class="bg-slate-700 hover:bg-slate-600 px-4 py-2 rounded-lg">
          ← Retour au personnel
        </a>
        <button onclick="showCreateShiftModal()" class="bg-cyan-600 hover:bg-cyan-500 px-4 py-2 rounded-lg">
          ➕ Nouveau shift
        </button>
      </div>
    </div>
  </div>

  <!-- Navigation des semaines -->
  <div class="mb-6">
    <div class="flex items-center justify-between bg-slate-900 border border-slate-700 rounded-xl p-4">
      <button onclick="previousWeek()" class="bg-slate-700 hover:bg-slate-600 px-4 py-2 rounded-lg">
        ← Semaine précédente
      </button>
      
      <div class="text-center">
        <h2 class="text-xl font-semibold" id="week-title">
          Semaine du {{ week_start.strftime('%d/%m/%Y') }}
        </h2>
        <p class="text-slate-400 text-sm">
          {{ week_start.strftime('%d/%m') }} - {{ (week_start + timedelta(days=6)).strftime('%d/%m/%Y') }}
        </p>
      </div>
      
      <button onclick="nextWeek()" class="bg-slate-700 hover:bg-slate-600 px-4 py-2 rounded-lg">
        Semaine suivante →
      </button>
    </div>
  </div>

  <!-- Actions rapides -->
  <div class="mb-6 grid grid-cols-1 md:grid-cols-4 gap-4">
    <div class="rounded-xl bg-slate-900 border border-slate-700 p-4">
      <div class="text-center">
        <div class="text-2xl mb-2">👥</div>
        <h3 class="font-semibold">{{ employees|length }}</h3>
        <p class="text-sm text-slate-400">Employés actifs</p>
      </div>
    </div>
    
    <div class="rounded-xl bg-slate-900 border border-slate-700 p-4">
      <div class="text-center">
        <div class="text-2xl mb-2">📅</div>
        <h3 class="font-semibold">{{ week_shifts|length }}</h3>
        <p class="text-sm text-slate-400">Shifts cette semaine</p>
      </div>
    </div>
    
    <div class="rounded-xl bg-slate-900 border border-slate-700 p-4">
      <div class="text-center">
        <div class="text-2xl mb-2">⏱️</div>
        <h3 class="font-semibold" id="total-hours">-</h3>
        <p class="text-sm text-slate-400">Heures totales</p>
      </div>
    </div>
    
    <div class="rounded-xl bg-slate-900 border border-slate-700 p-4">
      <div class="text-center">
        <div class="text-2xl mb-2">💰</div>
        <h3 class="font-semibold" id="estimated-cost">-</h3>
        <p class="text-sm text-slate-400">Coût estimé</p>
      </div>
    </div>
  </div>

  <!-- Planning en grille -->
  <div class="rounded-xl bg-slate-900 border border-slate-700 p-6">
    <div class="flex items-center justify-between mb-6">
      <h2 class="text-xl font-semibold">📋 Planning de la semaine</h2>
      <div class="flex space-x-2">
        <button onclick="toggleView('grid')" id="grid-view-btn" class="px-3 py-1 bg-cyan-600 text-white rounded text-sm">
          📊 Grille
        </button>
        <button onclick="toggleView('list')" id="list-view-btn" class="px-3 py-1 bg-slate-700 text-slate-300 rounded text-sm">
          📋 Liste
        </button>
        <button onclick="printSchedule()" class="px-3 py-1 bg-purple-600 hover:bg-purple-500 text-white rounded text-sm">
          🖨️ Imprimer
        </button>
      </div>
    </div>

    <!-- Vue en grille -->
    <div id="grid-view" class="overflow-x-auto">
      <table class="w-full border-collapse">
        <thead>
          <tr class="border-b border-slate-700">
            <th class="text-left p-3 text-slate-300 font-medium">Employé</th>
            {% for i in range(7) %}
              {% set day = week_start + timedelta(days=i) %}
              <th class="text-center p-3 text-slate-300 font-medium min-w-32">
                <div>{{ day.strftime('%a') }}</div>
                <div class="text-sm text-slate-400">{{ day.strftime('%d/%m') }}</div>
              </th>
            {% endfor %}
          </tr>
        </thead>
        <tbody>
          {% for employee in employees %}
          <tr class="border-b border-slate-700 hover:bg-slate-800">
            <td class="p-3">
              <div class="font-medium">{{ employee.last_name }} {{ employee.first_name }}</div>
              <div class="text-sm text-slate-400">{{ employee.employee_code }}</div>
              <div class="text-xs text-slate-500">{{ employee.position or 'Non défini' }}</div>
            </td>
            {% for i in range(7) %}
              {% set day = week_start + timedelta(days=i) %}
              {% set day_shifts = week_shifts | selectattr('employee_id_fk', 'equalto', employee.id) | selectattr('shift_date', 'equalto', day) | list %}
              <td class="p-2 text-center">
                {% if day_shifts %}
                  {% for shift in day_shifts %}
                  <div class="bg-slate-700 border border-slate-600 rounded p-2 mb-1 text-xs cursor-pointer hover:bg-slate-600" 
                       onclick="editShift({{ shift.id }})"
                       title="Cliquer pour modifier">
                    <div class="font-mono">{{ shift.start_time.strftime('%H:%M') }}-{{ shift.end_time.strftime('%H:%M') }}</div>
                    {% if shift.position %}
                      <div class="text-slate-400">{{ shift.position[:8] }}{% if shift.position|length > 8 %}...{% endif %}</div>
                    {% endif %}
                    {% if shift.break_duration_minutes %}
                      <div class="text-yellow-400">⏸️ {{ shift.break_duration_minutes }}min</div>
                    {% endif %}
                  </div>
                  {% endfor %}
                {% else %}
                  <button onclick="createShiftForEmployee({{ employee.id }}, '{{ day.strftime('%Y-%m-%d') }}')" 
                          class="w-full h-12 border-2 border-dashed border-slate-600 rounded hover:border-cyan-500 hover:bg-slate-800 text-slate-500 hover:text-cyan-400 transition-colors">
                    +
                  </button>
                {% endif %}
              </td>
            {% endfor %}
          </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>

    <!-- Vue en liste -->
    <div id="list-view" class="hidden space-y-4">
      {% for day_index in range(7) %}
        {% set current_day = week_start + timedelta(days=day_index) %}
        {% set day_shifts = week_shifts | selectattr('shift_date', 'equalto', current_day) | list %}
        
        <div class="bg-slate-800 border border-slate-700 rounded-lg p-4">
          <h3 class="font-semibold mb-3 flex items-center justify-between">
            <span>{{ current_day.strftime('%A %d/%m/%Y') }}</span>
            <span class="text-sm text-slate-400">{{ day_shifts|length }} shift(s)</span>
          </h3>
          
          {% if day_shifts %}
            <div class="space-y-2">
              {% for shift in day_shifts | sort(attribute='start_time') %}
              <div class="bg-slate-700 border border-slate-600 rounded p-3 flex items-center justify-between">
                <div class="flex-1">
                  <div class="flex items-center space-x-3">
                    <h4 class="font-medium">{{ shift.employee.last_name }} {{ shift.employee.first_name }}</h4>
                    <span class="px-2 py-1 bg-slate-600 rounded text-xs">{{ shift.employee.employee_code }}</span>
                  </div>
                  <div class="mt-1 text-sm text-slate-400">
                    ⏰ {{ shift.start_time.strftime('%H:%M') }} - {{ shift.end_time.strftime('%H:%M') }}
                    {% if shift.position %} • 💼 {{ shift.position }}{% endif %}
                    {% if shift.break_duration_minutes %} • ⏸️ {{ shift.break_duration_minutes }}min pause{% endif %}
                  </div>
                </div>
                <div class="flex space-x-2">
                  <button onclick="editShift({{ shift.id }})" class="px-3 py-1 bg-blue-600 hover:bg-blue-500 rounded text-xs">
                    ✏️
                  </button>
                  <button onclick="deleteShift({{ shift.id }})" class="px-3 py-1 bg-red-600 hover:bg-red-500 rounded text-xs">
                    🗑️
                  </button>
                </div>
              </div>
              {% endfor %}
            </div>
          {% else %}
            <div class="text-center py-6 text-slate-400">
              <div class="text-3xl mb-2">📅</div>
              <p class="text-sm">Aucun shift programmé</p>
              <button onclick="createShiftForDay('{{ current_day.strftime('%Y-%m-%d') }}')" 
                      class="mt-2 px-4 py-2 bg-cyan-600 hover:bg-cyan-500 rounded text-sm">
                Ajouter un shift
              </button>
            </div>
          {% endif %}
        </div>
      {% endfor %}
    </div>
  </div>

  <!-- Résumé des heures par employé -->
  <div class="mt-6 rounded-xl bg-slate-900 border border-slate-700 p-6">
    <h2 class="text-xl font-semibold mb-4">⏱️ Résumé des heures</h2>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {% for employee in employees %}
        {% set emp_shifts = week_shifts | selectattr('employee_id_fk', 'equalto', employee.id) | list %}
        {% if emp_shifts %}
          <div class="bg-slate-800 border border-slate-700 rounded-lg p-4">
            <h3 class="font-medium mb-2">{{ employee.last_name }} {{ employee.first_name }}</h3>
            <div class="text-sm space-y-1">
              <div class="flex justify-between">
                <span class="text-slate-400">Shifts:</span>
                <span>{{ emp_shifts|length }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-slate-400">Heures:</span>
                <span class="font-mono">
                  {% set total_minutes = emp_shifts | map('calculate_duration') | sum %}
                  {{ (total_minutes // 60) }}h{{ (total_minutes % 60) }}min
                </span>
              </div>
              {% if employee.salary_cents %}
                <div class="flex justify-between">
                  <span class="text-slate-400">Coût estimé:</span>
                  <span class="text-green-400">{{ ((total_minutes * employee.salary_cents) / (60 * 100)) | round(2) }}€</span>
                </div>
              {% endif %}
            </div>
          </div>
        {% endif %}
      {% endfor %}
    </div>
  </div>
</div>

<!-- Modal de création de shift -->
<div id="createShiftModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
  <div class="bg-slate-900 border border-slate-700 rounded-xl p-6 max-w-md w-full mx-4">
    <h3 class="text-lg font-semibold mb-4">➕ Nouveau shift</h3>
    <form id="createShiftForm" method="post" action="{{ url_for('staff.index') }}" class="space-y-4">
      <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
      <input type="hidden" name="_action" value="create_shift">
      
      <div>
        <label class="block text-sm font-medium text-slate-300 mb-2">Employé</label>
        <select name="employee_id_fk" class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2" required>
          <option value="">Sélectionner un employé</option>
          {% for employee in employees %}
          <option value="{{ employee.id }}">{{ employee.last_name }} {{ employee.first_name }} ({{ employee.employee_code }})</option>
          {% endfor %}
        </select>
      </div>
      
      <div>
        <label class="block text-sm font-medium text-slate-300 mb-2">Date</label>
        <input type="date" name="shift_date" class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2" required>
      </div>
      
      <div class="grid grid-cols-2 gap-3">
        <div>
          <label class="block text-sm font-medium text-slate-300 mb-2">Début</label>
          <input type="time" name="start_time" class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2" required>
        </div>
        <div>
          <label class="block text-sm font-medium text-slate-300 mb-2">Fin</label>
          <input type="time" name="end_time" class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2" required>
        </div>
      </div>
      
      <div>
        <label class="block text-sm font-medium text-slate-300 mb-2">Poste</label>
        <input type="text" name="position" class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2" placeholder="Poste pour ce shift">
      </div>
      
      <div>
        <label class="block text-sm font-medium text-slate-300 mb-2">Pause (minutes)</label>
        <input type="number" name="break_duration_minutes" class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2" placeholder="0" min="0">
      </div>
      
      <div class="flex items-center">
        <input type="checkbox" name="is_active" checked class="w-4 h-4 text-cyan-600 bg-slate-800 border-slate-700 rounded">
        <label class="ml-3 text-sm font-medium text-slate-300">Shift actif</label>
      </div>
      
      <div class="flex justify-end space-x-3 pt-4">
        <button type="button" onclick="closeCreateShiftModal()" class="px-4 py-2 bg-slate-700 hover:bg-slate-600 rounded-lg">
          Annuler
        </button>
        <button type="submit" class="px-4 py-2 bg-cyan-600 hover:bg-cyan-500 rounded-lg">
          Créer shift
        </button>
      </div>
    </form>
  </div>
</div>

<script>
let currentWeekStart = new Date('{{ week_start.strftime('%Y-%m-%d') }}');

// Gestion des vues
function toggleView(view) {
  const gridView = document.getElementById('grid-view');
  const listView = document.getElementById('list-view');
  const gridBtn = document.getElementById('grid-view-btn');
  const listBtn = document.getElementById('list-view-btn');
  
  if (view === 'grid') {
    gridView.classList.remove('hidden');
    listView.classList.add('hidden');
    gridBtn.className = 'px-3 py-1 bg-cyan-600 text-white rounded text-sm';
    listBtn.className = 'px-3 py-1 bg-slate-700 text-slate-300 rounded text-sm';
  } else {
    gridView.classList.add('hidden');
    listView.classList.remove('hidden');
    gridBtn.className = 'px-3 py-1 bg-slate-700 text-slate-300 rounded text-sm';
    listBtn.className = 'px-3 py-1 bg-cyan-600 text-white rounded text-sm';
  }
}

// Navigation des semaines
function previousWeek() {
  currentWeekStart.setDate(currentWeekStart.getDate() - 7);
  updateWeek();
}

function nextWeek() {
  currentWeekStart.setDate(currentWeekStart.getDate() + 7);
  updateWeek();
}

function updateWeek() {
  const weekEnd = new Date(currentWeekStart);
  weekEnd.setDate(weekEnd.getDate() + 6);
  
  document.getElementById('week-title').textContent = 
    `Semaine du ${currentWeekStart.toLocaleDateString('fr-FR')}`;
  
  // Recharger la page avec la nouvelle semaine
  const url = new URL(window.location);
  url.searchParams.set('week_start', currentWeekStart.toISOString().split('T')[0]);
  window.location.href = url.toString();
}

// Modal de création de shift
function showCreateShiftModal() {
  document.getElementById('createShiftModal').classList.remove('hidden');
}

function closeCreateShiftModal() {
  document.getElementById('createShiftModal').classList.add('hidden');
}

function createShiftForEmployee(employeeId, date) {
  showCreateShiftModal();
  document.querySelector('select[name="employee_id_fk"]').value = employeeId;
  document.querySelector('input[name="shift_date"]').value = date;
}

function createShiftForDay(date) {
  showCreateShiftModal();
  document.querySelector('input[name="shift_date"]').value = date;
}

// Actions sur les shifts
function editShift(shiftId) {
  window.location.href = `/staff/shifts/${shiftId}/edit`;
}

function deleteShift(shiftId) {
  if (confirm('Supprimer ce shift ?')) {
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = `/staff/shifts/${shiftId}/delete`;
    
    const csrfToken = document.createElement('input');
    csrfToken.type = 'hidden';
    csrfToken.name = 'csrf_token';
    csrfToken.value = '{{ csrf_token() }}';
    
    form.appendChild(csrfToken);
    document.body.appendChild(form);
    form.submit();
  }
}

// Impression
function printSchedule() {
  window.print();
}

// Calcul des statistiques
function calculateStats() {
  // Cette fonction serait implémentée côté serveur
  // Ici on peut juste afficher des valeurs de base
}

// Fermeture du modal avec Escape
document.addEventListener('keydown', function(e) {
  if (e.key === 'Escape') {
    closeCreateShiftModal();
  }
});

// Initialisation
document.addEventListener('DOMContentLoaded', function() {
  calculateStats();
});
</script>

<style>
@media print {
  .no-print { display: none !important; }
  body { background: white !important; color: black !important; }
  .bg-slate-900, .bg-slate-800, .bg-slate-700 { background: white !important; }
  .border-slate-700, .border-slate-600 { border-color: #ccc !important; }
  .text-slate-300, .text-slate-400 { color: #333 !important; }
}
</style>
{% endblock %}