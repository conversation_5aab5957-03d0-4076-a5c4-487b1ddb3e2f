{% extends "base.html" %}

{% block title %}Interactions Médicamenteuses{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Interactions Médicamenteuses</h1>
                <a href="{{ url_for('pharmacy.new_interaction') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Nouvelle Interaction
                </a>
            </div>

            <!-- Statistiques -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-danger">{{ interactions.total }}</h5>
                            <p class="card-text">Total Interactions</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-danger">
                                {% set contraindicated = interactions.items | selectattr('contraindicated', 'equalto', True) | list | length %}
                                {{ contraindicated }}
                            </h5>
                            <p class="card-text">Contre-indiquées</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-warning">
                                {% set major = interactions.items | selectattr('severity.value', 'equalto', 'major') | list | length %}
                                {{ major }}
                            </h5>
                            <p class="card-text">Majeures</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-info">
                                {% set monitoring = interactions.items | selectattr('monitoring_required', 'equalto', True) | list | length %}
                                {{ monitoring }}
                            </h5>
                            <p class="card-text">Surveillance requise</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Liste des interactions -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Liste des Interactions</h5>
                </div>
                <div class="card-body">
                    {% if interactions.items %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Médicaments</th>
                                        <th>Sévérité</th>
                                        <th>Type</th>
                                        <th>Description</th>
                                        <th>Surveillance</th>
                                        <th>Niveau de preuve</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for interaction in interactions.items %}
                                    <tr {% if interaction.contraindicated %}class="table-danger"{% endif %}>
                                        <td>
                                            <div class="d-flex flex-column">
                                                <span class="fw-bold">{{ interaction.medication1.name }}</span>
                                                <span class="text-muted">+</span>
                                                <span class="fw-bold">{{ interaction.medication2.name }}</span>
                                            </div>
                                        </td>
                                        <td>
                                            {% set severity_colors = {
                                                'minor': 'success',
                                                'moderate': 'warning',
                                                'major': 'danger',
                                                'contraindicated': 'dark'
                                            } %}
                                            {% set severity_labels = {
                                                'minor': 'Mineure',
                                                'moderate': 'Modérée',
                                                'major': 'Majeure',
                                                'contraindicated': 'Contre-indiquée'
                                            } %}
                                            <span class="badge bg-{{ severity_colors.get(interaction.severity.value, 'secondary') }}">
                                                {{ severity_labels.get(interaction.severity.value, interaction.severity.value) }}
                                            </span>
                                            {% if interaction.contraindicated %}
                                                <span class="badge bg-danger ms-1">CI</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if interaction.interaction_type %}
                                                <span class="badge bg-info">{{ interaction.interaction_type }}</span>
                                            {% else %}
                                                <span class="text-muted">-</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div style="max-width: 300px;">
                                                {{ interaction.description[:100] }}
                                                {% if interaction.description|length > 100 %}...{% endif %}
                                            </div>
                                        </td>
                                        <td>
                                            {% if interaction.monitoring_required %}
                                                <span class="badge bg-warning">
                                                    <i class="fas fa-eye"></i> Requise
                                                </span>
                                            {% else %}
                                                <span class="text-muted">-</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if interaction.evidence_level %}
                                                <span class="badge bg-secondary">{{ interaction.evidence_level }}</span>
                                            {% else %}
                                                <span class="text-muted">-</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <a href="{{ url_for('pharmacy.interaction_detail', interaction_id=interaction.id) }}" 
                                                   class="btn btn-outline-primary" title="Voir détails">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ url_for('pharmacy.edit_interaction', interaction_id=interaction.id) }}" 
                                                   class="btn btn-outline-warning" title="Modifier">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button type="button" class="btn btn-outline-danger" 
                                                        onclick="deleteInteraction({{ interaction.id }})" title="Supprimer">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        {% if interactions.pages > 1 %}
                            <nav aria-label="Navigation des interactions">
                                <ul class="pagination justify-content-center">
                                    {% if interactions.has_prev %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('pharmacy.interactions', page=interactions.prev_num) }}">
                                                Précédent
                                            </a>
                                        </li>
                                    {% endif %}
                                    
                                    {% for page_num in interactions.iter_pages() %}
                                        {% if page_num %}
                                            {% if page_num != interactions.page %}
                                                <li class="page-item">
                                                    <a class="page-link" href="{{ url_for('pharmacy.interactions', page=page_num) }}">
                                                        {{ page_num }}
                                                    </a>
                                                </li>
                                            {% else %}
                                                <li class="page-item active">
                                                    <span class="page-link">{{ page_num }}</span>
                                                </li>
                                            {% endif %}
                                        {% else %}
                                            <li class="page-item disabled">
                                                <span class="page-link">…</span>
                                            </li>
                                        {% endif %}
                                    {% endfor %}
                                    
                                    {% if interactions.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('pharmacy.interactions', page=interactions.next_num) }}">
                                                Suivant
                                            </a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-exclamation-triangle fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Aucune interaction enregistrée</h5>
                            <p class="text-muted">
                                Commencez par ajouter des interactions médicamenteuses pour améliorer la sécurité de vos prescriptions.
                            </p>
                            <a href="{{ url_for('pharmacy.new_interaction') }}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Première Interaction
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de confirmation de suppression -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Supprimer l'interaction</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer cette interaction médicamenteuse ?</p>
                <p class="text-warning">
                    <i class="fas fa-exclamation-triangle"></i> 
                    Cette action désactivera l'interaction et ne pourra pas être annulée.
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">Supprimer</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function deleteInteraction(interactionId) {
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    const form = document.getElementById('deleteForm');
    form.action = `/pharmacy/interactions/${interactionId}/delete`;
    modal.show();
}

// Filtrage en temps réel
document.addEventListener('DOMContentLoaded', function() {
    // Ajouter des filtres de recherche si nécessaire
    const table = document.querySelector('table tbody');
    
    // Fonction de recherche simple
    function filterTable(searchTerm) {
        const rows = table.querySelectorAll('tr');
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(searchTerm.toLowerCase()) ? '' : 'none';
        });
    }
    
    // Ajouter un champ de recherche si nécessaire
    // Cette fonctionnalité peut être étendue
});
</script>

<style>
.table-danger {
    background-color: rgba(220, 53, 69, 0.1);
}

.badge {
    font-size: 0.75em;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}
</style>
{% endblock %}