"""Intégration Stripe pour l'application POS System"""
import stripe
import logging
from typing import Dict, Any, Optional
from datetime import datetime
from flask import current_app

from app.modules.payments.models import Payment
from app.extensions import db

logger = logging.getLogger(__name__)

class StripeIntegration:
    """Gestion de l'intégration Stripe"""
    
    def __init__(self):
        """Initialise l'intégration Stripe"""
        self.stripe_secret_key = current_app.config.get('STRIPE_SECRET_KEY')
        self.stripe_publishable_key = current_app.config.get('STRIPE_PUBLISHABLE_KEY')
        
        if self.stripe_secret_key:
            stripe.api_key = self.stripe_secret_key
    
    def create_payment_intent(self, amount_cents: int, currency: str = 'eur', 
                            metadata: Dict[str, Any] = None) -> Dict[str, Any]:
        """Crée un Payment Intent Stripe"""
        try:
            intent = stripe.PaymentIntent.create(
                amount=amount_cents,
                currency=currency,
                metadata=metadata or {},
                automatic_payment_methods={
                    'enabled': True,
                },
            )
            
            logger.info(f"Payment Intent créé: {intent.id}")
            return {
                'success': True,
                'client_secret': intent.client_secret,
                'payment_intent_id': intent.id
            }
            
        except stripe.error.StripeError as e:
            logger.error(f"Erreur Stripe lors de la création du Payment Intent: {e}")
            return {
                'success': False,
                'error': str(e)
            }
        except Exception as e:
            logger.error(f"Erreur inattendue lors de la création du Payment Intent: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def confirm_payment(self, payment_intent_id: str, payment_method_id: str) -> Dict[str, Any]:
        """Confirme un paiement Stripe"""
        try:
            intent = stripe.PaymentIntent.confirm(
                payment_intent_id,
                payment_method=payment_method_id,
            )
            
            logger.info(f"Payment Intent confirmé: {intent.id}")
            return {
                'success': True,
                'status': intent.status,
                'payment_intent_id': intent.id
            }
            
        except stripe.error.StripeError as e:
            logger.error(f"Erreur Stripe lors de la confirmation du paiement: {e}")
            return {
                'success': False,
                'error': str(e)
            }
        except Exception as e:
            logger.error(f"Erreur inattendue lors de la confirmation du paiement: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def create_refund(self, payment_intent_id: str, amount_cents: Optional[int] = None) -> Dict[str, Any]:
        """Crée un remboursement Stripe"""
        try:
            # Récupérer le paiement pour obtenir le charge ID
            payment = Payment.query.filter_by(transaction_id=payment_intent_id).first()
            if not payment:
                raise ValueError("Paiement non trouvé")
            
            refund_data = {
                'payment_intent': payment_intent_id
            }
            
            if amount_cents:
                refund_data['amount'] = amount_cents
            
            refund = stripe.Refund.create(**refund_data)
            
            logger.info(f"Remboursement créé: {refund.id}")
            return {
                'success': True,
                'refund_id': refund.id,
                'status': refund.status
            }
            
        except stripe.error.StripeError as e:
            logger.error(f"Erreur Stripe lors de la création du remboursement: {e}")
            return {
                'success': False,
                'error': str(e)
            }
        except Exception as e:
            logger.error(f"Erreur inattendue lors de la création du remboursement: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_payment_details(self, payment_intent_id: str) -> Dict[str, Any]:
        """Récupère les détails d'un paiement Stripe"""
        try:
            intent = stripe.PaymentIntent.retrieve(payment_intent_id)
            
            return {
                'success': True,
                'payment_intent_id': intent.id,
                'amount': intent.amount,
                'currency': intent.currency,
                'status': intent.status,
                'customer': intent.customer,
                'created': datetime.fromtimestamp(intent.created),
                'charges': intent.charges.data if intent.charges else []
            }
            
        except stripe.error.StripeError as e:
            logger.error(f"Erreur Stripe lors de la récupération des détails du paiement: {e}")
            return {
                'success': False,
                'error': str(e)
            }
        except Exception as e:
            logger.error(f"Erreur inattendue lors de la récupération des détails du paiement: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def create_customer(self, email: str, name: str = None, metadata: Dict[str, Any] = None) -> Dict[str, Any]:
        """Crée un client Stripe"""
        try:
            customer = stripe.Customer.create(
                email=email,
                name=name,
                metadata=metadata or {}
            )
            
            logger.info(f"Client Stripe créé: {customer.id}")
            return {
                'success': True,
                'customer_id': customer.id,
                'email': customer.email,
                'name': customer.name
            }
            
        except stripe.error.StripeError as e:
            logger.error(f"Erreur Stripe lors de la création du client: {e}")
            return {
                'success': False,
                'error': str(e)
            }
        except Exception as e:
            logger.error(f"Erreur inattendue lors de la création du client: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def create_subscription(self, customer_id: str, price_id: str, 
                          metadata: Dict[str, Any] = None) -> Dict[str, Any]:
        """Crée un abonnement Stripe"""
        try:
            subscription = stripe.Subscription.create(
                customer=customer_id,
                items=[{'price': price_id}],
                metadata=metadata or {}
            )
            
            logger.info(f"Abonnement Stripe créé: {subscription.id}")
            return {
                'success': True,
                'subscription_id': subscription.id,
                'status': subscription.status,
                'current_period_end': datetime.fromtimestamp(subscription.current_period_end)
            }
            
        except stripe.error.StripeError as e:
            logger.error(f"Erreur Stripe lors de la création de l'abonnement: {e}")
            return {
                'success': False,
                'error': str(e)
            }
        except Exception as e:
            logger.error(f"Erreur inattendue lors de la création de l'abonnement: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def create_webhook_endpoint(self, url: str, events: list) -> Dict[str, Any]:
        """Crée un endpoint webhook Stripe"""
        try:
            webhook = stripe.WebhookEndpoint.create(
                url=url,
                enabled_events=events,
                api_version='2020-08-27'
            )
            
            logger.info(f"Webhook Stripe créé: {webhook.id}")
            return {
                'success': True,
                'webhook_id': webhook.id,
                'secret': webhook.secret
            }
            
        except stripe.error.StripeError as e:
            logger.error(f"Erreur Stripe lors de la création du webhook: {e}")
            return {
                'success': False,
                'error': str(e)
            }
        except Exception as e:
            logger.error(f"Erreur inattendue lors de la création du webhook: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def verify_webhook_signature(self, payload: str, sig_header: str, secret: str) -> bool:
        """Vérifie la signature d'un webhook Stripe"""
        try:
            stripe.Webhook.construct_event(payload, sig_header, secret)
            return True
        except ValueError:
            # Signature invalide
            return False
        except stripe.error.SignatureVerificationError:
            # Signature invalide
            return False
    
    def get_balance(self) -> Dict[str, Any]:
        """Récupère le solde du compte Stripe"""
        try:
            balance = stripe.Balance.retrieve()
            
            return {
                'success': True,
                'balance': balance
            }
            
        except stripe.error.StripeError as e:
            logger.error(f"Erreur Stripe lors de la récupération du solde: {e}")
            return {
                'success': False,
                'error': str(e)
            }
        except Exception as e:
            logger.error(f"Erreur inattendue lors de la récupération du solde: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def create_payout(self, amount_cents: int, currency: str = 'eur', 
                     destination: str = None) -> Dict[str, Any]:
        """Crée un paiement sortant (payout) Stripe"""
        try:
            payout_data = {
                'amount': amount_cents,
                'currency': currency
            }
            
            if destination:
                payout_data['destination'] = destination
            
            payout = stripe.Payout.create(**payout_data)
            
            logger.info(f"Payout Stripe créé: {payout.id}")
            return {
                'success': True,
                'payout_id': payout.id,
                'status': payout.status
            }
            
        except stripe.error.StripeError as e:
            logger.error(f"Erreur Stripe lors de la création du payout: {e}")
            return {
                'success': False,
                'error': str(e)
            }
        except Exception as e:
            logger.error(f"Erreur inattendue lors de la création du payout: {e}")
            return {
                'success': False,
                'error': str(e)
            }