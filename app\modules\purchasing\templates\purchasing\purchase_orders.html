{% extends 'base.html' %}
{% block title %}Bons de Commande{% endblock %}

{% block content %}
<div class=\"max-w-7xl mx-auto p-6\">
    <!-- En-tête -->
    <div class=\"flex items-center justify-between mb-6\">
        <div>
            <h1 class=\"text-3xl font-bold text-gray-900\">
                <i class=\"fas fa-file-invoice mr-3 text-blue-600\"></i>Bons de Commande
            </h1>
            <p class=\"text-gray-600 mt-2\">Gestion des commandes fournisseurs</p>
        </div>
        
        <div class=\"flex space-x-3\">
            <a href=\"{{ url_for('purchasing.new_purchase_order') }}\" 
               class=\"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium\">
                <i class=\"fas fa-plus mr-2\"></i>Nouvelle Commande
            </a>
        </div>
    </div>

    <!-- Filtres de recherche -->
    <div class=\"bg-white rounded-lg shadow p-6 mb-6\">
        <form method=\"get\" class=\"grid grid-cols-1 md:grid-cols-5 gap-4\">
            {{ search_form.csrf_token }}
            
            <div>
                {{ search_form.search(class=\"w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500\", placeholder=\"Rechercher...\") }}
            </div>
            
            <div>
                {{ search_form.status(class=\"w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500\") }}
            </div>
            
            <div>
                {{ search_form.supplier_id(class=\"w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500\") }}
            </div>
            
            <div>
                {{ search_form.date_from(class=\"w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500\", placeholder=\"Du\") }}
            </div>
            
            <div class=\"flex space-x-2\">
                {{ search_form.date_to(class=\"flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500\", placeholder=\"Au\") }}
                <button type=\"submit\" class=\"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg\">
                    <i class=\"fas fa-search\"></i>
                </button>
                <a href=\"{{ url_for('purchasing.purchase_orders') }}\" class=\"bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg\">
                    <i class=\"fas fa-times\"></i>
                </a>
            </div>
        </form>
    </div>

    <!-- Liste des commandes -->
    <div class=\"bg-white rounded-lg shadow overflow-hidden\">
        {% if orders.items %}
        <div class=\"overflow-x-auto\">
            <table class=\"min-w-full divide-y divide-gray-200\">
                <thead class=\"bg-gray-50\">
                    <tr>
                        <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">
                            N° Commande
                        </th>
                        <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">
                            Fournisseur
                        </th>
                        <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">
                            Date
                        </th>
                        <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">
                            Statut
                        </th>
                        <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">
                            Total
                        </th>
                        <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">
                            Progression
                        </th>
                        <th class=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class=\"bg-white divide-y divide-gray-200\">
                    {% for order in orders.items %}
                    <tr class=\"hover:bg-gray-50\">
                        <td class=\"px-6 py-4 whitespace-nowrap\">
                            <div class=\"text-sm font-medium text-gray-900\">{{ order.order_number }}</div>
                            {% if order.reference %}
                            <div class=\"text-sm text-gray-500\">Réf: {{ order.reference }}</div>
                            {% endif %}
                        </td>
                        <td class=\"px-6 py-4 whitespace-nowrap\">
                            <div class=\"text-sm text-gray-900\">{{ order.supplier.name if order.supplier else 'N/A' }}</div>
                        </td>
                        <td class=\"px-6 py-4 whitespace-nowrap\">
                            <div class=\"text-sm text-gray-900\">{{ order.order_date.strftime('%d/%m/%Y') if order.order_date else 'N/A' }}</div>
                            {% if order.expected_delivery_date %}
                            <div class=\"text-sm text-gray-500\">Livraison: {{ order.expected_delivery_date.strftime('%d/%m/%Y') }}</div>
                            {% endif %}
                        </td>
                        <td class=\"px-6 py-4 whitespace-nowrap\">
                            <span class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                 {% if order.status.value == 'draft' %}bg-yellow-100 text-yellow-800
                                 {% elif order.status.value == 'pending' %}bg-orange-100 text-orange-800
                                 {% elif order.status.value == 'approved' %}bg-green-100 text-green-800
                                 {% elif order.status.value == 'sent' %}bg-blue-100 text-blue-800
                                 {% elif order.status.value == 'confirmed' %}bg-indigo-100 text-indigo-800
                                 {% elif order.status.value == 'partial' %}bg-purple-100 text-purple-800
                                 {% elif order.status.value == 'received' %}bg-gray-100 text-gray-800
                                 {% elif order.status.value == 'cancelled' %}bg-red-100 text-red-800
                                 {% else %}bg-gray-100 text-gray-800{% endif %}\">
                                {{ order.status.value.title() }}
                            </span>
                        </td>
                        <td class=\"px-6 py-4 whitespace-nowrap\">
                            <div class=\"text-sm font-medium text-gray-900\">{{ \"%.2f\"|format(order.total) }}€</div>
                            <div class=\"text-sm text-gray-500\">{{ order.get_items_count() }} articles</div>
                        </td>
                        <td class=\"px-6 py-4 whitespace-nowrap\">
                            <div class=\"flex items-center\">
                                <div class=\"flex-1\">
                                    <div class=\"text-xs text-gray-500 mb-1\">{{ \"%.0f\"|format(order.received_percentage) }}% reçu</div>
                                    <div class=\"w-full bg-gray-200 rounded-full h-2\">
                                        <div class=\"bg-blue-600 h-2 rounded-full\" style=\"width: {{ order.received_percentage }}%\"></div>
                                    </div>
                                </div>
                            </div>
                        </td>
                        <td class=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">
                            <div class=\"flex items-center justify-end space-x-2\">
                                <a href=\"{{ url_for('purchasing.view_purchase_order', id=order.id) }}\" 
                                   class=\"text-blue-600 hover:text-blue-900\" title=\"Voir\">
                                    <i class=\"fas fa-eye\"></i>
                                </a>
                                
                                {% if order.status.value in ['draft', 'pending', 'approved'] %}
                                <a href=\"{{ url_for('purchasing.edit_purchase_order', id=order.id) }}\" 
                                   class=\"text-green-600 hover:text-green-900\" title=\"Modifier\">
                                    <i class=\"fas fa-edit\"></i>
                                </a>
                                {% endif %}
                                
                                {% if order.status.value == 'draft' %}
                                <form method=\"post\" action=\"{{ url_for('purchasing.approve_purchase_order', id=order.id) }}\" class=\"inline\">
                                    <button type=\"submit\" class=\"text-green-600 hover:text-green-900\" title=\"Approuver\"
                                            onclick=\"return confirm('Approuver cette commande ?')\">
                                        <i class=\"fas fa-check\"></i>
                                    </button>
                                </form>
                                {% endif %}
                                
                                {% if order.status.value in ['confirmed', 'sent', 'partial'] %}
                                <a href=\"{{ url_for('purchasing.new_purchase_receipt') }}?order_id={{ order.id }}\" 
                                   class=\"text-purple-600 hover:text-purple-900\" title=\"Réceptionner\">
                                    <i class=\"fas fa-truck\"></i>
                                </a>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if orders.pages > 1 %}
        <div class=\"bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6\">
            <div class=\"flex-1 flex justify-between sm:hidden\">
                {% if orders.has_prev %}
                <a href=\"{{ url_for('purchasing.purchase_orders', page=orders.prev_num, **request.args) }}\" 
                   class=\"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50\">
                    Précédent
                </a>
                {% endif %}
                {% if orders.has_next %}
                <a href=\"{{ url_for('purchasing.purchase_orders', page=orders.next_num, **request.args) }}\" 
                   class=\"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50\">
                    Suivant
                </a>
                {% endif %}
            </div>
            <div class=\"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between\">
                <div>
                    <p class=\"text-sm text-gray-700\">
                        Affichage de
                        <span class=\"font-medium\">{{ (orders.page - 1) * orders.per_page + 1 }}</span>
                        à
                        <span class=\"font-medium\">{{ min(orders.page * orders.per_page, orders.total) }}</span>
                        sur
                        <span class=\"font-medium\">{{ orders.total }}</span>
                        résultats
                    </p>
                </div>
                <div>
                    <nav class=\"relative z-0 inline-flex rounded-md shadow-sm -space-x-px\" aria-label=\"Pagination\">
                        {% if orders.has_prev %}
                        <a href=\"{{ url_for('purchasing.purchase_orders', page=orders.prev_num, **request.args) }}\" 
                           class=\"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50\">
                            <i class=\"fas fa-chevron-left\"></i>
                        </a>
                        {% endif %}
                        
                        {% for page_num in orders.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != orders.page %}
                                <a href=\"{{ url_for('purchasing.purchase_orders', page=page_num, **request.args) }}\" 
                                   class=\"relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50\">
                                    {{ page_num }}
                                </a>
                                {% else %}
                                <span class=\"relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600\">
                                    {{ page_num }}
                                </span>
                                {% endif %}
                            {% else %}
                            <span class=\"relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700\">
                                …
                            </span>
                            {% endif %}
                        {% endfor %}
                        
                        {% if orders.has_next %}
                        <a href=\"{{ url_for('purchasing.purchase_orders', page=orders.next_num, **request.args) }}\" 
                           class=\"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50\">
                            <i class=\"fas fa-chevron-right\"></i>
                        </a>
                        {% endif %}
                    </nav>
                </div>
            </div>
        </div>
        {% endif %}
        
        {% else %}
        <div class=\"text-center py-12\">
            <div class=\"text-gray-400 text-6xl mb-4\">
                <i class=\"fas fa-file-invoice\"></i>
            </div>
            <h3 class=\"text-lg font-medium text-gray-900 mb-2\">Aucun bon de commande</h3>
            <p class=\"text-gray-500 mb-6\">Commencez par créer votre premier bon de commande.</p>
            <a href=\"{{ url_for('purchasing.new_purchase_order') }}\" 
               class=\"bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium\">
                <i class=\"fas fa-plus mr-2\"></i>Créer une commande
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}