from __future__ import annotations

from datetime import datetime
from app.extensions import db


class KdsScreen(db.Model):
    __tablename__ = "kds_screens"
    id = db.Column(db.Integer, primary_key=True)
    business_id_fk = db.Column(db.Integer, db.<PERSON>Key("businesses.id"), nullable=False, index=True)
    name = db.Column(db.String(120), nullable=False)
    
    # Relations
    business = db.relationship("Business", backref="kds_screens")


class KdsTicket(db.Model):
    __tablename__ = "kds_tickets"
    id = db.Column(db.Integer, primary_key=True)
    order_id_fk = db.Column(db.Integer, db.ForeignKey("orders.id"), nullable=False, index=True)
    status = db.Column(db.String(40), nullable=False, default="pending", index=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    
    # Relations
    order = db.relationship("Order", backref="kds_tickets")
    
    __table_args__ = (
        db.Index("idx_kds_ticket_status", "status"),
        db.Index("idx_kds_ticket_order", "order_id_fk"),
    )


