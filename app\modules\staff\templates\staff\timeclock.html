{% extends 'base.html' %}
{% block title %}Pointage - Gestion du Temps{% endblock %}

{% block content %}
<div class="max-w-6xl mx-auto">
  <!-- En-tête -->
  <div class="mb-6">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold">⏰ Système de Pointage</h1>
        <p class="text-slate-400 mt-2">Gestion des entrées et sorties du personnel</p>
      </div>
      <a href="{{ url_for('staff.index') }}" class="bg-slate-700 hover:bg-slate-600 px-4 py-2 rounded-lg">
        ← Retour au personnel
      </a>
    </div>
  </div>

  <!-- Heure actuelle -->
  <div class="mb-6 text-center">
    <div class="inline-block bg-slate-900 border border-slate-700 rounded-xl p-6">
      <div class="text-2xl font-mono text-cyan-400" id="current-time">--:--:--</div>
      <div class="text-sm text-slate-400" id="current-date">-- ------ ----</div>
    </div>
  </div>

  <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <!-- Formulaire de pointage -->
    <div class="lg:col-span-1">
      <div class="rounded-xl bg-slate-900 border border-slate-700 p-6">
        <h2 class="text-xl font-semibold mb-6">📝 Nouveau pointage</h2>
        
        <form method="post" class="space-y-4">
          {{ form.csrf_token }}
          
          <!-- Messages -->
          {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
              {% for category, message in messages %}
                <div class="rounded-lg p-4 {% if category == 'error' %}bg-red-900 border border-red-700 text-red-100{% else %}bg-green-900 border border-green-700 text-green-100{% endif %}">
                  {{ message }}
                </div>
              {% endfor %}
            {% endif %}
          {% endwith %}

          <div>
            <label class="block text-sm font-medium text-slate-300 mb-2">
              {{ form.employee_id_fk.label.text }} <span class="text-red-400">*</span>
            </label>
            {{ form.employee_id_fk(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 focus:ring-2 focus:ring-cyan-500 focus:border-transparent") }}
            {% if form.employee_id_fk.errors %}
              <p class="text-red-400 text-sm mt-1">{{ form.employee_id_fk.errors[0] }}</p>
            {% endif %}
          </div>
          
          <div>
            <label class="block text-sm font-medium text-slate-300 mb-2">
              {{ form.shift_id_fk.label.text }}
            </label>
            {{ form.shift_id_fk(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 focus:ring-2 focus:ring-cyan-500 focus:border-transparent") }}
            {% if form.shift_id_fk.errors %}
              <p class="text-red-400 text-sm mt-1">{{ form.shift_id_fk.errors[0] }}</p>
            {% endif %}
          </div>
          
          <div>
            <label class="block text-sm font-medium text-slate-300 mb-2">
              {{ form.clock_type.label.text }} <span class="text-red-400">*</span>
            </label>
            {{ form.clock_type(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 focus:ring-2 focus:ring-cyan-500 focus:border-transparent") }}
            {% if form.clock_type.errors %}
              <p class="text-red-400 text-sm mt-1">{{ form.clock_type.errors[0] }}</p>
            {% endif %}
          </div>
          
          <div>
            <label class="block text-sm font-medium text-slate-300 mb-2">
              {{ form.notes.label.text }}
            </label>
            {{ form.notes(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 focus:ring-2 focus:ring-cyan-500 focus:border-transparent", rows="3") }}
            {% if form.notes.errors %}
              <p class="text-red-400 text-sm mt-1">{{ form.notes.errors[0] }}</p>
            {% endif %}
          </div>
          
          {{ form.submit(class="w-full bg-cyan-600 hover:bg-cyan-500 rounded-lg px-4 py-3 font-medium text-lg") }}
        </form>
      </div>
      
      <!-- Pointages en attente de sortie -->
      <div class="mt-6 rounded-xl bg-slate-900 border border-slate-700 p-6">
        <h3 class="text-lg font-semibold mb-4">⏳ En cours</h3>
        {% if recent_clocks %}
          {% set pending_clocks = recent_clocks|selectattr('clock_out', 'none')|list %}
          {% if pending_clocks %}
            <div class="space-y-3">
              {% for clock in pending_clocks %}
              <div class="bg-slate-800 border border-slate-700 rounded-lg p-3">
                <div class="flex items-center justify-between">
                  <div>
                    <h4 class="font-medium">{{ clock.employee.last_name }} {{ clock.employee.first_name }}</h4>
                    <p class="text-sm text-slate-400">
                      {{ clock.clock_type|title }} depuis {{ clock.clock_in.strftime('%H:%M') }}
                    </p>
                  </div>
                  <form method="post" action="{{ url_for('staff.clock_out', id=clock.id) }}" class="inline">
                    {{ csrf_token() }}
                    <button class="bg-green-600 hover:bg-green-500 px-3 py-1 rounded text-sm">
                      Sortie
                    </button>
                  </form>
                </div>
              </div>
              {% endfor %}
            </div>
          {% else %}
            <div class="text-center py-4 text-slate-400">
              <div class="text-3xl mb-2">✅</div>
              <p class="text-sm">Aucun pointage en cours</p>
            </div>
          {% endif %}
        {% else %}
          <div class="text-center py-4 text-slate-400">
            <div class="text-3xl mb-2">⏰</div>
            <p class="text-sm">Aucun pointage</p>
          </div>
        {% endif %}
      </div>
    </div>

    <!-- Historique des pointages -->
    <div class="lg:col-span-2">
      <div class="rounded-xl bg-slate-900 border border-slate-700 p-6">
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-xl font-semibold">📋 Historique des pointages</h2>
          <div class="flex space-x-2">
            <button onclick="filterToday()" class="px-3 py-1 bg-cyan-600 text-white rounded text-sm">
              Aujourd'hui
            </button>
            <button onclick="filterWeek()" class="px-3 py-1 bg-slate-700 text-slate-300 rounded text-sm">
              Cette semaine
            </button>
            <button onclick="filterAll()" class="px-3 py-1 bg-slate-700 text-slate-300 rounded text-sm">
              Tout
            </button>
          </div>
        </div>
        
        {% if recent_clocks %}
          <div class="space-y-3 max-h-96 overflow-y-auto">
            {% for clock in recent_clocks %}
            <div class="bg-slate-800 border border-slate-700 rounded-lg p-4 timeclock-entry" 
                 data-date="{{ clock.clock_in.strftime('%Y-%m-%d') }}">
              <div class="flex items-center justify-between">
                <div class="flex-1">
                  <div class="flex items-center space-x-3">
                    <h3 class="font-semibold">{{ clock.employee.last_name }} {{ clock.employee.first_name }}</h3>
                    <span class="px-2 py-1 text-xs rounded-full 
                      {% if clock.clock_type == 'shift' %}bg-blue-900 text-blue-300
                      {% elif clock.clock_type == 'break' %}bg-yellow-900 text-yellow-300
                      {% else %}bg-purple-900 text-purple-300{% endif %}">
                      {{ clock.clock_type|title }}
                    </span>
                  </div>
                  
                  <div class="mt-2 grid grid-cols-2 md:grid-cols-4 gap-2 text-sm">
                    <div>
                      <span class="text-slate-400">Entrée:</span>
                      <div class="font-mono">{{ clock.clock_in.strftime('%H:%M') }}</div>
                    </div>
                    <div>
                      <span class="text-slate-400">Sortie:</span>
                      <div class="font-mono">
                        {% if clock.clock_out %}
                          {{ clock.clock_out.strftime('%H:%M') }}
                        {% else %}
                          <span class="text-yellow-400">En cours</span>
                        {% endif %}
                      </div>
                    </div>
                    <div>
                      <span class="text-slate-400">Durée:</span>
                      <div class="font-mono">
                        {% if clock.clock_out %}
                          {% set duration = clock.clock_out - clock.clock_in %}
                          {{ (duration.total_seconds() // 3600)|int }}h{{ ((duration.total_seconds() % 3600) // 60)|int }}min
                        {% else %}
                          <span class="text-yellow-400">-</span>
                        {% endif %}
                      </div>
                    </div>
                    <div>
                      <span class="text-slate-400">Date:</span>
                      <div>{{ clock.clock_in.strftime('%d/%m') }}</div>
                    </div>
                  </div>
                  
                  {% if clock.notes %}
                    <div class="mt-2 text-sm text-slate-400">
                      📝 {{ clock.notes }}
                    </div>
                  {% endif %}
                </div>
                
                <div class="ml-4">
                  {% if not clock.clock_out %}
                    <form method="post" action="{{ url_for('staff.clock_out', id=clock.id) }}" class="inline">
                      {{ csrf_token() }}
                      <button class="bg-green-600 hover:bg-green-500 px-3 py-2 rounded text-sm">
                        🏁 Sortie
                      </button>
                    </form>
                  {% else %}
                    <div class="text-center">
                      <div class="text-2xl">✅</div>
                      <div class="text-xs text-slate-400">Terminé</div>
                    </div>
                  {% endif %}
                </div>
              </div>
            </div>
            {% endfor %}
          </div>
        {% else %}
          <div class="text-center py-12">
            <div class="text-6xl mb-4">⏰</div>
            <h3 class="text-xl font-semibold text-slate-300 mb-2">Aucun pointage</h3>
            <p class="text-slate-400">Commencez par enregistrer un premier pointage.</p>
          </div>
        {% endif %}
      </div>
      
      <!-- Statistiques du jour -->
      <div class="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="rounded-xl bg-slate-900 border border-slate-700 p-4">
          <div class="text-center">
            <div class="text-2xl mb-2">👥</div>
            <h3 class="font-semibold">Personnel présent</h3>
            <p class="text-lg text-cyan-400" id="staff-present">-</p>
          </div>
        </div>
        
        <div class="rounded-xl bg-slate-900 border border-slate-700 p-4">
          <div class="text-center">
            <div class="text-2xl mb-2">⏱️</div>
            <h3 class="font-semibold">Pointages aujourd'hui</h3>
            <p class="text-lg text-green-400" id="todays-punches">-</p>
          </div>
        </div>
        
        <div class="rounded-xl bg-slate-900 border border-slate-700 p-4">
          <div class="text-center">
            <div class="text-2xl mb-2">⏳</div>
            <h3 class="font-semibold">En pause</h3>
            <p class="text-lg text-yellow-400" id="on-break">-</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
// Mise à jour de l'heure en temps réel
function updateTime() {
  const now = new Date();
  const timeString = now.toLocaleTimeString('fr-FR');
  const dateString = now.toLocaleDateString('fr-FR', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
  
  document.getElementById('current-time').textContent = timeString;
  document.getElementById('current-date').textContent = dateString;
}

// Filtres pour l'historique
function filterToday() {
  const today = new Date().toISOString().split('T')[0];
  filterByDate(today);
  updateFilterButtons('today');
}

function filterWeek() {
  const today = new Date();
  const weekStart = new Date(today.setDate(today.getDate() - today.getDay()));
  const weekStartStr = weekStart.toISOString().split('T')[0];
  filterByDateRange(weekStartStr);
  updateFilterButtons('week');
}

function filterAll() {
  const entries = document.querySelectorAll('.timeclock-entry');
  entries.forEach(entry => entry.style.display = 'block');
  updateFilterButtons('all');
}

function filterByDate(targetDate) {
  const entries = document.querySelectorAll('.timeclock-entry');
  entries.forEach(entry => {
    const entryDate = entry.getAttribute('data-date');
    entry.style.display = entryDate === targetDate ? 'block' : 'none';
  });
}

function filterByDateRange(startDate) {
  const entries = document.querySelectorAll('.timeclock-entry');
  const start = new Date(startDate);
  
  entries.forEach(entry => {
    const entryDate = new Date(entry.getAttribute('data-date'));
    entry.style.display = entryDate >= start ? 'block' : 'none';
  });
}

function updateFilterButtons(active) {
  const buttons = {
    'today': document.querySelector('button[onclick="filterToday()"]'),
    'week': document.querySelector('button[onclick="filterWeek()"]'),
    'all': document.querySelector('button[onclick="filterAll()"]')
  };
  
  Object.entries(buttons).forEach(([key, button]) => {
    if (key === active) {
      button.className = 'px-3 py-1 bg-cyan-600 text-white rounded text-sm';
    } else {
      button.className = 'px-3 py-1 bg-slate-700 text-slate-300 rounded text-sm';
    }
  });
}

// Calcul des statistiques
function calculateStats() {
  const today = new Date().toISOString().split('T')[0];
  const entries = document.querySelectorAll('.timeclock-entry');
  
  let staffPresent = 0;
  let todaysPunches = 0;
  let onBreak = 0;
  
  entries.forEach(entry => {
    const entryDate = entry.getAttribute('data-date');
    if (entryDate === today) {
      todaysPunches++;
      
      // Vérifier si en cours
      const inProgress = entry.querySelector('.text-yellow-400:not(.bg-yellow-900)');
      if (inProgress) {
        const type = entry.querySelector('.rounded-full').textContent.trim().toLowerCase();
        if (type === 'shift') {
          staffPresent++;
        } else if (type === 'break' || type === 'lunch') {
          onBreak++;
        }
      }
    }
  });
  
  document.getElementById('staff-present').textContent = staffPresent;
  document.getElementById('todays-punches').textContent = todaysPunches;
  document.getElementById('on-break').textContent = onBreak;
}

// Initialisation
document.addEventListener('DOMContentLoaded', function() {
  updateTime();
  setInterval(updateTime, 1000);
  calculateStats();
  filterToday(); // Afficher les pointages du jour par défaut
});

// Auto-sélection de l'employé si passé en paramètre URL
const urlParams = new URLSearchParams(window.location.search);
const employeeId = urlParams.get('employee');
if (employeeId) {
  const employeeSelect = document.querySelector('select[name="employee_id_fk"]');
  if (employeeSelect) {
    employeeSelect.value = employeeId;
  }
}
</script>
{% endblock %}