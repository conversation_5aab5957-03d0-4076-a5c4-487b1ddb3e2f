from __future__ import annotations

from datetime import datetime, timedelta
from decimal import Decimal
from typing import Optional
from sqlalchemy import Column, Integer, String, Text, DateTime, Numeric, ForeignKey, Boolean, Enum
from sqlalchemy.orm import relationship

from app.extensions import db


class WorkCenter(db.Model):
    """Centre de travail dans l'usine"""
    __tablename__ = "work_centers"
    
    id = Column(Integer, primary_key=True)
    business_id_fk = Column(Integer, ForeignKey("businesses.id"), nullable=False, index=True)
    name = Column(String(120), nullable=False)
    description = Column(Text)
    capacity_per_hour = Column(Numeric(10, 2), default=1.0)  # Capacité par heure
    cost_per_hour = Column(Integer, default=0)  # Coût par heure en centimes
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    # Relations
    business = relationship("Business", backref="work_centers")
    production_steps = relationship("ProductionStep", back_populates="work_center")
    
    def __repr__(self):
        return f"<WorkCenter {self.name}>"


class BOM(db.Model):
    """Bill of Materials - Nomenclature des produits"""
    __tablename__ = "boms"
    
    id = Column(Integer, primary_key=True)
    business_id_fk = Column(Integer, ForeignKey("businesses.id"), nullable=False, index=True)
    product_id_fk = Column(Integer, ForeignKey("products.id"), nullable=False, index=True)
    name = Column(String(120), nullable=False)
    description = Column(Text)
    version = Column(String(20), default="1.0")
    quantity_produced = Column(Numeric(10, 3), default=1.0)  # Quantité produite par ce BOM
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relations
    business = relationship("Business", backref="boms")
    product = relationship("Product", backref="boms")
    bom_items = relationship("BOMItem", back_populates="bom", cascade="all, delete-orphan")
    production_steps = relationship("ProductionStep", back_populates="bom", cascade="all, delete-orphan")
    production_orders = relationship("ProductionOrder", back_populates="bom")
    
    def __repr__(self):
        return f"<BOM {self.name} v{self.version}>"
    
    @property
    def total_material_cost(self) -> int:
        """Coût total des matériaux en centimes"""
        return sum(item.total_cost_cents for item in self.bom_items)
    
    @property
    def total_labor_cost(self) -> int:
        """Coût total de la main d'œuvre en centimes"""
        return sum(step.total_cost_cents for step in self.production_steps)
    
    @property
    def total_cost(self) -> int:
        """Coût total de production en centimes"""
        return self.total_material_cost + self.total_labor_cost


class BOMItem(db.Model):
    """Article composant un BOM"""
    __tablename__ = "bom_items"
    
    id = Column(Integer, primary_key=True)
    bom_id_fk = Column(Integer, ForeignKey("boms.id"), nullable=False, index=True)
    product_id_fk = Column(Integer, ForeignKey("products.id"), nullable=False, index=True)
    quantity = Column(Numeric(10, 3), nullable=False)
    unit_cost_cents = Column(Integer, default=0)  # Coût unitaire en centimes
    notes = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    # Relations
    bom = relationship("BOM", back_populates="bom_items")
    product = relationship("Product")
    
    def __repr__(self):
        return f"<BOMItem {self.product.name if self.product else 'Unknown'} x{self.quantity}>"
    
    @property
    def total_cost_cents(self) -> int:
        """Coût total de cet item en centimes"""
        return int(float(self.quantity) * self.unit_cost_cents)


class ProductionStep(db.Model):
    """Étape de production dans un BOM"""
    __tablename__ = "production_steps"
    
    id = Column(Integer, primary_key=True)
    bom_id_fk = Column(Integer, ForeignKey("boms.id"), nullable=False, index=True)
    work_center_id_fk = Column(Integer, ForeignKey("work_centers.id"), nullable=False, index=True)
    step_number = Column(Integer, nullable=False)
    name = Column(String(120), nullable=False)
    description = Column(Text)
    setup_time_minutes = Column(Integer, default=0)  # Temps de préparation en minutes
    run_time_minutes = Column(Integer, default=0)    # Temps d'exécution en minutes
    is_optional = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    # Relations
    bom = relationship("BOM", back_populates="production_steps")
    work_center = relationship("WorkCenter", back_populates="production_steps")
    order_steps = relationship("ProductionOrderStep", back_populates="production_step")
    
    def __repr__(self):
        return f"<ProductionStep {self.step_number}: {self.name}>"
    
    @property
    def total_time_minutes(self) -> int:
        """Temps total de cette étape en minutes"""
        return self.setup_time_minutes + self.run_time_minutes
    
    @property
    def total_cost_cents(self) -> int:
        """Coût total de cette étape en centimes"""
        if not self.work_center:
            return 0
        
        total_hours = self.total_time_minutes / 60.0
        return int(total_hours * self.work_center.cost_per_hour)


class ProductionOrder(db.Model):
    """Ordre de production"""
    __tablename__ = "production_orders"
    
    id = Column(Integer, primary_key=True)
    business_id_fk = Column(Integer, ForeignKey("businesses.id"), nullable=False, index=True)
    bom_id_fk = Column(Integer, ForeignKey("boms.id"), nullable=False, index=True)
    order_number = Column(String(50), nullable=False, unique=True)
    quantity_to_produce = Column(Numeric(10, 3), nullable=False)
    quantity_produced = Column(Numeric(10, 3), default=0.0)
    
    status = Column(String(20), default="draft", index=True)
    # Statuts: draft, planned, released, in_progress, completed, cancelled
    
    priority = Column(String(10), default="normal")  # low, normal, high, urgent
    
    # Dates
    planned_start_date = Column(DateTime)
    planned_end_date = Column(DateTime)
    actual_start_date = Column(DateTime)
    actual_end_date = Column(DateTime)
    
    # Coûts
    estimated_cost_cents = Column(Integer, default=0)
    actual_cost_cents = Column(Integer, default=0)
    
    notes = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relations
    business = relationship("Business", backref="production_orders")
    bom = relationship("BOM", back_populates="production_orders")
    order_steps = relationship("ProductionOrderStep", back_populates="production_order", cascade="all, delete-orphan")
    material_consumptions = relationship("MaterialConsumption", back_populates="production_order", cascade="all, delete-orphan")
    
    __table_args__ = (
        db.Index("idx_production_order_status", "status"),
        db.Index("idx_production_order_business", "business_id_fk"),
        db.Index("idx_production_order_dates", "planned_start_date", "planned_end_date"),
    )
    
    def __repr__(self):
        return f"<ProductionOrder {self.order_number}>"
    
    @property
    def progress_percentage(self) -> float:
        """Pourcentage de progression de l'ordre"""
        if self.quantity_to_produce == 0:
            return 0.0
        return float(self.quantity_produced) / float(self.quantity_to_produce) * 100
    
    @property
    def is_overdue(self) -> bool:
        """Vérifie si l'ordre est en retard"""
        if not self.planned_end_date or self.status in ["completed", "cancelled"]:
            return False
        return datetime.utcnow() > self.planned_end_date
    
    def generate_order_number(self) -> str:
        """Génère un numéro d'ordre unique"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
        return f"PO-{timestamp}"
    
    def calculate_estimated_cost(self):
        """Calcule le coût estimé basé sur le BOM et la quantité"""
        if self.bom:
            unit_cost = self.bom.total_cost
            self.estimated_cost_cents = int(float(self.quantity_to_produce) * unit_cost)


class ProductionOrderStep(db.Model):
    """Étape d'un ordre de production"""
    __tablename__ = "production_order_steps"
    
    id = Column(Integer, primary_key=True)
    production_order_id_fk = Column(Integer, ForeignKey("production_orders.id"), nullable=False, index=True)
    production_step_id_fk = Column(Integer, ForeignKey("production_steps.id"), nullable=False, index=True)
    
    status = Column(String(20), default="pending", index=True)
    # Statuts: pending, in_progress, completed, skipped
    
    # Temps
    planned_start_time = Column(DateTime)
    planned_end_time = Column(DateTime)
    actual_start_time = Column(DateTime)
    actual_end_time = Column(DateTime)
    
    # Personnel assigné
    assigned_user_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    
    # Résultats
    quantity_processed = Column(Numeric(10, 3), default=0.0)
    quality_check_passed = Column(Boolean, default=True)
    notes = Column(Text)
    
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relations
    production_order = relationship("ProductionOrder", back_populates="order_steps")
    production_step = relationship("ProductionStep", back_populates="order_steps")
    assigned_user = relationship("User")
    
    def __repr__(self):
        return f"<ProductionOrderStep {self.production_order.order_number}-{self.production_step.step_number}>"
    
    @property
    def duration_minutes(self) -> Optional[int]:
        """Durée réelle de l'étape en minutes"""
        if self.actual_start_time and self.actual_end_time:
            delta = self.actual_end_time - self.actual_start_time
            return int(delta.total_seconds() / 60)
        return None


class MaterialConsumption(db.Model):
    """Consommation de matériaux pour un ordre de production"""
    __tablename__ = "material_consumptions"
    
    id = Column(Integer, primary_key=True)
    production_order_id_fk = Column(Integer, ForeignKey("production_orders.id"), nullable=False, index=True)
    product_id_fk = Column(Integer, ForeignKey("products.id"), nullable=False, index=True)
    
    planned_quantity = Column(Numeric(10, 3), nullable=False)
    actual_quantity = Column(Numeric(10, 3), default=0.0)
    unit_cost_cents = Column(Integer, default=0)
    
    consumed_at = Column(DateTime, default=datetime.utcnow)
    notes = Column(Text)
    
    # Relations
    production_order = relationship("ProductionOrder", back_populates="material_consumptions")
    product = relationship("Product")
    
    def __repr__(self):
        return f"<MaterialConsumption {self.product.name if self.product else 'Unknown'} x{self.actual_quantity}>"
    
    @property
    def variance_quantity(self) -> float:
        """Écart entre quantité prévue et réelle"""
        return float(self.actual_quantity) - float(self.planned_quantity)
    
    @property
    def total_cost_cents(self) -> int:
        """Coût total de cette consommation en centimes"""
        return int(float(self.actual_quantity) * self.unit_cost_cents)


class QualityCheck(db.Model):
    """Contrôle qualité sur un ordre de production"""
    __tablename__ = "quality_checks"
    
    id = Column(Integer, primary_key=True)
    production_order_id_fk = Column(Integer, ForeignKey("production_orders.id"), nullable=False, index=True)
    step_id_fk = Column(Integer, ForeignKey("production_order_steps.id"), nullable=True, index=True)
    
    check_type = Column(String(50), nullable=False)  # visual, measurement, functional, etc.
    description = Column(Text, nullable=False)
    
    # Résultats
    passed = Column(Boolean, nullable=False)
    measured_value = Column(String(100))
    expected_value = Column(String(100))
    tolerance = Column(String(50))
    
    # Personnel
    checked_by_user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    checked_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    notes = Column(Text)
    
    # Relations
    production_order = relationship("ProductionOrder")
    step = relationship("ProductionOrderStep")
    checked_by = relationship("User")
    
    def __repr__(self):
        return f"<QualityCheck {self.check_type} - {'PASS' if self.passed else 'FAIL'}>"