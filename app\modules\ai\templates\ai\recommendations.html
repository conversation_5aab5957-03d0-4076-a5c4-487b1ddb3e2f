{% extends "base.html" %}

{% block title %}Recommandations de Produits{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-thumbs-up"></i> Recommandations de Produits</h1>
        <button id="trainModelBtn" class="btn btn-primary">
            <i class="fas fa-brain"></i> Entraîner le Modèle
        </button>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Recommandations par Produit</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">Sélectionner un produit</label>
                        <select class="form-select" id="productSelect">
                            <option value="">Choisir un produit...</option>
                            <!-- Les produits seront chargés dynamiquement -->
                        </select>
                    </div>
                    
                    <button id="getRecommendationsBtn" class="btn btn-success" disabled>
                        <i class="fas fa-search"></i> Obtenir les Recommandations
                    </button>
                    
                    <div id="recommendationsResult" class="mt-4 d-none">
                        <h6>Produits Recommandés:</h6>
                        <div id="recommendationsList">
                            <!-- Les recommandations seront affichées ici -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Recommandations par Client</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">ID du client</label>
                        <input type="number" class="form-control" id="customerId" placeholder="Entrez l'ID du client">
                    </div>
                    
                    <button id="getClientRecommendationsBtn" class="btn btn-info" disabled>
                        <i class="fas fa-user"></i> Recommandations Personnalisées
                    </button>
                    
                    <div id="clientRecommendationsResult" class="mt-4 d-none">
                        <h6>Recommandations Personnalisées:</h6>
                        <div id="clientRecommendationsList">
                            <!-- Les recommandations personnalisées seront affichées ici -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Métriques du Modèle</h5>
                </div>
                <div class="card-body">
                    <div class="row" id="modelMetrics">
                        <div class="col-md-6">
                            <div class="text-center">
                                <h3 id="associationsValue">-</h3>
                                <p class="text-muted">Associations de Produits</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="text-center">
                                <h3 id="productsValue">-</h3>
                                <p class="text-muted">Produits Analysés</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Charger la liste des produits
    function loadProducts() {
        // Dans une vraie application, cela viendrait de l'API
        // Pour l'instant, nous simulons quelques produits
        const products = [
            {id: 1, name: 'Burger Classique', sku: 'BURG001'},
            {id: 2, name: 'Frites', sku: 'FRIT001'},
            {id: 3, name: 'Coca-Cola', sku: 'COKE001'},
            {id: 4, name: 'Salade César', sku: 'SALA001'},
            {id: 5, name: 'Pizza Margherita', sku: 'PIZZ001'}
        ];
        
        const select = document.getElementById('productSelect');
        products.forEach(product => {
            const option = document.createElement('option');
            option.value = product.id;
            option.textContent = `${product.name} (${product.sku})`;
            select.appendChild(option);
        });
    }
    
    // Activer le bouton quand un produit est sélectionné
    document.getElementById('productSelect').addEventListener('change', function() {
        document.getElementById('getRecommendationsBtn').disabled = !this.value;
    });
    
    // Activer le bouton quand un ID client est entré
    document.getElementById('customerId').addEventListener('input', function() {
        document.getElementById('getClientRecommendationsBtn').disabled = !this.value;
    });
    
    // Entraîner le modèle
    document.getElementById('trainModelBtn').addEventListener('click', function() {
        fetch('/ai/api/recommendations/train', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Modèle de recommandation entraîné avec succès!');
                
                // Mettre à jour les métriques
                document.getElementById('associationsValue').textContent = data.metrics.associations;
                document.getElementById('productsValue').textContent = data.metrics.products;
            } else {
                alert('Erreur: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur lors de l\'entraînement du modèle');
        });
    });
    
    // Obtenir les recommandations pour un produit
    document.getElementById('getRecommendationsBtn').addEventListener('click', function() {
        const productId = document.getElementById('productSelect').value;
        
        fetch(`/ai/api/recommendations/product/${productId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const list = document.getElementById('recommendationsList');
                list.innerHTML = '';
                
                if (data.recommendations.length === 0) {
                    list.innerHTML = '<p class="text-muted">Aucune recommandation disponible pour ce produit.</p>';
                } else {
                    data.recommendations.forEach(rec => {
                        const item = document.createElement('div');
                        item.className = 'recommendation-item mb-2 p-2 border rounded';
                        item.innerHTML = `
                            <strong>${rec.name}</strong> (${rec.sku})<br>
                            <small class="text-muted">Confiance: ${(rec.confidence * 100).toFixed(1)}%</small>
                        `;
                        list.appendChild(item);
                    });
                }
                
                document.getElementById('recommendationsResult').classList.remove('d-none');
            } else {
                alert('Erreur: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur lors de l\'obtention des recommandations');
        });
    });
    
    // Obtenir les recommandations personnalisées pour un client
    document.getElementById('getClientRecommendationsBtn').addEventListener('click', function() {
        const customerId = document.getElementById('customerId').value;
        
        // Dans une vraie application, cela viendrait de l'API
        // Pour l'instant, nous simulons des recommandations
        const list = document.getElementById('clientRecommendationsList');
        list.innerHTML = `
            <div class="recommendation-item mb-2 p-2 border rounded">
                <strong>Pizza Pepperoni</strong> (PIZZ002)<br>
                <small class="text-muted">Basé sur vos achats précédents</small>
            </div>
            <div class="recommendation-item mb-2 p-2 border rounded">
                <strong>Chicken Wings</strong> (WING001)<br>
                <small class="text-muted">Populaire avec les clients similaires</small>
            </div>
        `;
        
        document.getElementById('clientRecommendationsResult').classList.remove('d-none');
    });
    
    // Charger les produits quand la page est prête
    document.addEventListener('DOMContentLoaded', function() {
        loadProducts();
    });
</script>
{% endblock %}