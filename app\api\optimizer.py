"""Optimisations spécifiques à l'API"""
from typing import Dict, Any, List, Optional
from flask import request, jsonify
from flask_restx import Resource
import logging
from app.utils.cache import cache_manager
from app.utils.performance_monitor import timing_decorator
from app.utils.response_optimizer import ResponseOptimizer

logger = logging.getLogger(__name__)

class APIOptimizer:
    """Optimise les performances de l'API"""
    
    @staticmethod
    def optimize_fields_selection(data: Dict[str, Any], allowed_fields: List[str]) -> Dict[str, Any]:
        """
        Filtre les champs retournés selon les paramètres de requête
        """
        # Vérifier si des champs spécifiques sont demandés
        fields_param = request.args.get('fields')
        if not fields_param:
            return data
            
        requested_fields = fields_param.split(',')
        filtered_data = {}
        
        # Filtrer les champs demandés
        for field in requested_fields:
            field = field.strip()
            if field in allowed_fields and field in data:
                filtered_data[field] = data[field]
                
        return filtered_data
    
    @staticmethod
    def add_pagination_links(base_url: str, page: int, per_page: int, total: int) -> Dict[str, str]:
        """
        Ajoute les liens de pagination aux réponses API
        """
        links = {}
        total_pages = (total + per_page - 1) // per_page
        
        # Lien vers la première page
        links['first'] = f"{base_url}?page=1&per_page={per_page}"
        
        # Lien vers la page précédente
        if page > 1:
            links['prev'] = f"{base_url}?page={page-1}&per_page={per_page}"
            
        # Lien vers la page suivante
        if page < total_pages:
            links['next'] = f"{base_url}?page={page+1}&per_page={per_page}"
            
        # Lien vers la dernière page
        links['last'] = f"{base_url}?page={total_pages}&per_page={per_page}"
        
        return links
    
    @staticmethod
    def optimize_response_data(data: Any, resource_type: str) -> Dict[str, Any]:
        """
        Optimise les données de réponse selon le type de ressource
        """
        response = {
            'success': True,
            'data': data,
            'meta': {
                'resource_type': resource_type,
                'timestamp': int(time.time())
            }
        }
        
        # Ajouter des métriques de performance si disponibles
        try:
            from app.utils.performance_monitor import performance_monitor
            response['meta']['performance'] = {
                'request_time_ms': getattr(request, 'start_time', 0)
            }
        except:
            pass
            
        return response

class OptimizedResource(Resource):
    """
    Classe de base pour les ressources API optimisées
    """
    
    method_decorators = []
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.optimizer = APIOptimizer()
    
    def optimize_json_output(self, data: Dict[str, Any]) -> str:
        """
        Optimise la sortie JSON
        """
        return ResponseOptimizer.optimize_json_response(data)
    
    def create_paginated_response(self, data: List[Any], page: int, per_page: int, total: int, 
                                base_url: str, resource_type: str) -> Dict[str, Any]:
        """
        Crée une réponse paginée optimisée
        """
        response = self.optimizer.optimize_response_data(data, resource_type)
        response['meta'].update({
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': total,
                'pages': (total + per_page - 1) // per_page
            }
        })
        
        # Ajouter les liens de pagination
        links = self.optimizer.add_pagination_links(base_url, page, per_page, total)
        response['links'] = links
        
        return response

def rate_limit_headers(max_requests: int = 1000, window_seconds: int = 3600):
    """
    Décorateur pour ajouter les en-têtes de rate limiting
    """
    def decorator(f):
        @timing_decorator(f"rate_limit.{f.__name__}")
        def decorated_function(*args, **kwargs):
            # Cette implémentation serait connectée au système de rate limiting
            # dans auth.py
            response = f(*args, **kwargs)
            
            # Ajouter les en-têtes de rate limiting
            if hasattr(request, 'rate_limit'):
                response.headers['X-RateLimit-Limit'] = max_requests
                response.headers['X-RateLimit-Remaining'] = getattr(request, 'remaining', 0)
                response.headers['X-RateLimit-Reset'] = getattr(request, 'reset_time', 0)
                
            return response
        return decorated_function
    return decorator

# Middleware pour l'optimisation des requêtes API
def api_optimization_middleware():
    """
    Middleware qui applique les optimisations API
    """
    # Cette fonction serait intégrée dans le pipeline de traitement des requêtes
    pass

import time