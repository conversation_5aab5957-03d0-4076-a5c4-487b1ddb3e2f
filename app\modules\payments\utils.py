from __future__ import annotations

import json
import hashlib
import hmac
from datetime import datetime, date, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import secrets
import uuid

from app.extensions import db
from app.modules.payments.models import (
    Payment, PaymentMethod, PaymentTransaction, PaymentRefund, Receipt,
    CashMovement, CashSession, PaymentStatus, PaymentMethodType
)


@dataclass
class PaymentResult:
    """Résultat d'un traitement de paiement"""
    success: bool
    payment_id: Optional[int] = None
    transaction_id: Optional[str] = None
    error_message: Optional[str] = None
    gateway_response: Optional[Dict] = None
    fees_cents: int = 0
    net_amount_cents: int = 0


@dataclass
class RefundResult:
    """Résultat d'un remboursement"""
    success: bool
    refund_id: Optional[int] = None
    transaction_id: Optional[str] = None
    error_message: Optional[str] = None
    gateway_response: Optional[Dict] = None


class PaymentProcessor:
    """Processeur de paiements centralisé"""
    
    def __init__(self, business_id: int):
        self.business_id = business_id
    
    def process_payment(self, payment_data: Dict[str, Any]) -> PaymentResult:
        """
        Traiter un paiement selon la méthode choisie
        
        Args:
            payment_data: {
                'payment_method_id': int,
                'amount_cents': int,
                'order_id': int,
                'customer_data': dict (optionnel),
                'metadata': dict (optionnel)
            }
        """
        try:
            # Récupérer la méthode de paiement
            payment_method = PaymentMethod.query.filter_by(
                id=payment_data['payment_method_id'],
                business_id_fk=self.business_id,
                is_active=True
            ).first()
            
            if not payment_method:
                return PaymentResult(
                    success=False,
                    error_message="Méthode de paiement invalide"
                )
            
            # Valider le montant
            amount_cents = payment_data['amount_cents']
            if not self._validate_amount(payment_method, amount_cents):
                return PaymentResult(
                    success=False,
                    error_message="Montant invalide pour cette méthode de paiement"
                )
            
            # Calculer les frais
            fees_cents = payment_method.calculate_fees(amount_cents)
            net_amount_cents = amount_cents - fees_cents
            
            # Traiter selon le type de méthode
            if payment_method.method_type == PaymentMethodType.CASH:
                return self._process_cash_payment(payment_method, payment_data, fees_cents, net_amount_cents)
            elif payment_method.method_type in [PaymentMethodType.CARD, PaymentMethodType.CREDIT_CARD, PaymentMethodType.DEBIT_CARD]:
                return self._process_card_payment(payment_method, payment_data, fees_cents, net_amount_cents)
            elif payment_method.method_type == PaymentMethodType.BANK_TRANSFER:
                return self._process_bank_transfer(payment_method, payment_data, fees_cents, net_amount_cents)
            else:
                return self._process_generic_payment(payment_method, payment_data, fees_cents, net_amount_cents)
                
        except Exception as e:
            return PaymentResult(
                success=False,
                error_message=f"Erreur lors du traitement: {str(e)}"
            )
    
    def process_refund(self, payment_id: int, refund_data: Dict[str, Any]) -> RefundResult:
        """
        Traiter un remboursement
        
        Args:
            payment_id: ID du paiement à rembourser
            refund_data: {
                'amount_cents': int,
                'reason': str,
                'reason_description': str (optionnel)
            }
        """
        try:
            payment = Payment.query.filter_by(
                id=payment_id,
                business_id_fk=self.business_id
            ).first()
            
            if not payment:
                return RefundResult(
                    success=False,
                    error_message="Paiement non trouvé"
                )
            
            if not payment.is_refundable:
                return RefundResult(
                    success=False,
                    error_message="Ce paiement ne peut pas être remboursé"
                )
            
            amount_cents = refund_data['amount_cents']
            if amount_cents > payment.refundable_amount_cents:
                return RefundResult(
                    success=False,
                    error_message="Montant supérieur au montant remboursable"
                )
            
            # Traiter le remboursement selon la méthode
            if payment.payment_method.method_type == PaymentMethodType.CASH:
                return self._process_cash_refund(payment, refund_data)
            elif payment.payment_method.is_card:
                return self._process_card_refund(payment, refund_data)
            else:
                return self._process_generic_refund(payment, refund_data)
                
        except Exception as e:
            return RefundResult(
                success=False,
                error_message=f"Erreur lors du remboursement: {str(e)}"
            )
    
    def _validate_amount(self, payment_method: PaymentMethod, amount_cents: int) -> bool:
        """Valider un montant pour une méthode de paiement"""
        if amount_cents < payment_method.minimum_amount_cents:
            return False
        
        if payment_method.maximum_amount_cents and amount_cents > payment_method.maximum_amount_cents:
            return False
        
        return True
    
    def _process_cash_payment(self, payment_method: PaymentMethod, payment_data: Dict, 
                             fees_cents: int, net_amount_cents: int) -> PaymentResult:
        """Traiter un paiement en espèces"""
        # Générer un numéro de reçu
        receipt_number = self._generate_receipt_number()
        
        # Créer le paiement
        payment = Payment(
            business_id_fk=self.business_id,
            order_id_fk=payment_data['order_id'],
            payment_method_id_fk=payment_method.id,
            amount_cents=payment_data['amount_cents'],
            fees_cents=fees_cents,
            net_amount_cents=net_amount_cents,
            status=PaymentStatus.COMPLETED,
            receipt_number=receipt_number,
            processed_at=datetime.utcnow(),
            gateway_name="internal_cash",
            notes=payment_data.get('notes', ''),
            created_by_user_id=payment_data.get('user_id')
        )
        
        db.session.add(payment)
        db.session.flush()  # Pour obtenir l'ID
        
        # Créer un mouvement de caisse
        active_session = CashSession.query.filter_by(
            business_id_fk=self.business_id,
            is_active=True
        ).first()
        
        cash_movement = CashMovement(
            business_id_fk=self.business_id,
            movement_type="sale",
            direction="in",
            amount_cents=payment_data['amount_cents'],
            reference_type="payment",
            reference_id=payment.id,
            reason="Paiement en espèces",
            cash_session_id=active_session.id if active_session else None,
            created_by_user_id=payment_data.get('user_id')
        )
        
        db.session.add(cash_movement)
        db.session.commit()
        
        return PaymentResult(
            success=True,
            payment_id=payment.id,
            transaction_id=receipt_number,
            fees_cents=fees_cents,
            net_amount_cents=net_amount_cents
        )
    
    def _process_card_payment(self, payment_method: PaymentMethod, payment_data: Dict,
                             fees_cents: int, net_amount_cents: int) -> PaymentResult:
        """Traiter un paiement par carte"""
        # Dans un vrai système, ici on ferait appel à Stripe, PayPal, etc.
        # Pour la démo, on simule un paiement réussi
        
        receipt_number = self._generate_receipt_number()
        transaction_id = f"card_{uuid.uuid4().hex[:12]}"
        
        payment = Payment(
            business_id_fk=self.business_id,
            order_id_fk=payment_data['order_id'],
            payment_method_id_fk=payment_method.id,
            amount_cents=payment_data['amount_cents'],
            fees_cents=fees_cents,
            net_amount_cents=net_amount_cents,
            status=PaymentStatus.COMPLETED,
            receipt_number=receipt_number,
            external_transaction_id=transaction_id,
            processed_at=datetime.utcnow(),
            gateway_name="simulated_gateway",
            card_last_four=payment_data.get('card_last_four'),
            card_brand=payment_data.get('card_brand'),
            card_holder_name=payment_data.get('card_holder_name'),
            notes=payment_data.get('notes', ''),
            created_by_user_id=payment_data.get('user_id')
        )
        
        db.session.add(payment)
        db.session.commit()
        
        return PaymentResult(
            success=True,
            payment_id=payment.id,
            transaction_id=transaction_id,
            fees_cents=fees_cents,
            net_amount_cents=net_amount_cents,
            gateway_response={"status": "success", "simulation": True}
        )
    
    def _process_bank_transfer(self, payment_method: PaymentMethod, payment_data: Dict,
                              fees_cents: int, net_amount_cents: int) -> PaymentResult:
        """Traiter un virement bancaire"""
        receipt_number = self._generate_receipt_number()
        
        payment = Payment(
            business_id_fk=self.business_id,
            order_id_fk=payment_data['order_id'],
            payment_method_id_fk=payment_method.id,
            amount_cents=payment_data['amount_cents'],
            fees_cents=fees_cents,
            net_amount_cents=net_amount_cents,
            status=PaymentStatus.PENDING,  # Les virements sont en attente
            receipt_number=receipt_number,
            gateway_name="bank_transfer",
            notes=payment_data.get('notes', ''),
            created_by_user_id=payment_data.get('user_id')
        )
        
        db.session.add(payment)
        db.session.commit()
        
        return PaymentResult(
            success=True,
            payment_id=payment.id,
            transaction_id=receipt_number,
            fees_cents=fees_cents,
            net_amount_cents=net_amount_cents
        )
    
    def _process_generic_payment(self, payment_method: PaymentMethod, payment_data: Dict,
                                fees_cents: int, net_amount_cents: int) -> PaymentResult:
        """Traiter un paiement générique"""
        receipt_number = self._generate_receipt_number()
        
        payment = Payment(
            business_id_fk=self.business_id,
            order_id_fk=payment_data['order_id'],
            payment_method_id_fk=payment_method.id,
            amount_cents=payment_data['amount_cents'],
            fees_cents=fees_cents,
            net_amount_cents=net_amount_cents,
            status=PaymentStatus.COMPLETED,
            receipt_number=receipt_number,
            gateway_name="generic",
            notes=payment_data.get('notes', ''),
            created_by_user_id=payment_data.get('user_id')
        )
        
        db.session.add(payment)
        db.session.commit()
        
        return PaymentResult(
            success=True,
            payment_id=payment.id,
            transaction_id=receipt_number,
            fees_cents=fees_cents,
            net_amount_cents=net_amount_cents
        )
    
    def _process_cash_refund(self, payment: Payment, refund_data: Dict) -> RefundResult:
        """Traiter un remboursement en espèces"""
        from app.modules.payments.models import PaymentRefund, RefundReason
        
        refund = PaymentRefund(
            payment_id_fk=payment.id,
            amount_cents=refund_data['amount_cents'],
            reason=RefundReason(refund_data['reason']),
            reason_description=refund_data.get('reason_description'),
            status=PaymentStatus.COMPLETED,
            requested_by_user_id=refund_data.get('user_id'),
            processed_by_user_id=refund_data.get('user_id'),
            processed_at=datetime.utcnow()
        )
        
        db.session.add(refund)
        db.session.flush()
        
        # Créer un mouvement de caisse sortant
        active_session = CashSession.query.filter_by(
            business_id_fk=self.business_id,
            is_active=True
        ).first()
        
        cash_movement = CashMovement(
            business_id_fk=self.business_id,
            movement_type="refund",
            direction="out",
            amount_cents=refund_data['amount_cents'],
            reference_type="refund",
            reference_id=refund.id,
            reason="Remboursement en espèces",
            cash_session_id=active_session.id if active_session else None,
            created_by_user_id=refund_data.get('user_id')
        )
        
        db.session.add(cash_movement)
        
        # Mettre à jour le statut du paiement
        if payment.refundable_amount_cents - refund_data['amount_cents'] == 0:
            payment.status = PaymentStatus.REFUNDED
        else:
            payment.status = PaymentStatus.PARTIALLY_REFUNDED
        
        db.session.commit()
        
        return RefundResult(
            success=True,
            refund_id=refund.id,
            transaction_id=f"refund_{refund.id}"
        )
    
    def _process_card_refund(self, payment: Payment, refund_data: Dict) -> RefundResult:
        """Traiter un remboursement par carte"""
        # Simulation d'un remboursement carte
        from app.modules.payments.models import PaymentRefund, RefundReason
        
        refund = PaymentRefund(
            payment_id_fk=payment.id,
            amount_cents=refund_data['amount_cents'],
            reason=RefundReason(refund_data['reason']),
            reason_description=refund_data.get('reason_description'),
            status=PaymentStatus.COMPLETED,
            external_refund_id=f"ref_{uuid.uuid4().hex[:12]}",
            requested_by_user_id=refund_data.get('user_id'),
            processed_by_user_id=refund_data.get('user_id'),
            processed_at=datetime.utcnow()
        )
        
        db.session.add(refund)
        
        # Mettre à jour le statut du paiement
        if payment.refundable_amount_cents - refund_data['amount_cents'] == 0:
            payment.status = PaymentStatus.REFUNDED
        else:
            payment.status = PaymentStatus.PARTIALLY_REFUNDED
        
        db.session.commit()
        
        return RefundResult(
            success=True,
            refund_id=refund.id,
            transaction_id=refund.external_refund_id,
            gateway_response={"status": "success", "simulation": True}
        )
    
    def _process_generic_refund(self, payment: Payment, refund_data: Dict) -> RefundResult:
        """Traiter un remboursement générique"""
        from app.modules.payments.models import PaymentRefund, RefundReason
        
        refund = PaymentRefund(
            payment_id_fk=payment.id,
            amount_cents=refund_data['amount_cents'],
            reason=RefundReason(refund_data['reason']),
            reason_description=refund_data.get('reason_description'),
            status=PaymentStatus.COMPLETED,
            requested_by_user_id=refund_data.get('user_id'),
            processed_by_user_id=refund_data.get('user_id'),
            processed_at=datetime.utcnow()
        )
        
        db.session.add(refund)
        
        # Mettre à jour le statut du paiement
        if payment.refundable_amount_cents - refund_data['amount_cents'] == 0:
            payment.status = PaymentStatus.REFUNDED
        else:
            payment.status = PaymentStatus.PARTIALLY_REFUNDED
        
        db.session.commit()
        
        return RefundResult(
            success=True,
            refund_id=refund.id,
            transaction_id=f"refund_{refund.id}"
        )
    
    def _generate_receipt_number(self) -> str:
        """Générer un numéro de reçu unique"""
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        random_part = secrets.token_hex(4).upper()
        return f"PAY-{timestamp}-{random_part}"


class PaymentAnalytics:
    """Analyseur de données de paiement"""
    
    def __init__(self, business_id: int):
        self.business_id = business_id
    
    def get_payment_summary(self, date_from: date = None, date_to: date = None) -> Dict[str, Any]:
        """Obtenir un résumé des paiements"""
        if not date_from:
            date_from = date.today()
        if not date_to:
            date_to = date_from
        
        start_datetime = datetime.combine(date_from, datetime.min.time())
        end_datetime = datetime.combine(date_to, datetime.max.time())
        
        payments = Payment.query.filter(
            Payment.business_id_fk == self.business_id,
            Payment.created_at >= start_datetime,
            Payment.created_at <= end_datetime,
            Payment.status == PaymentStatus.COMPLETED
        ).all()
        
        total_amount = sum(p.amount_cents for p in payments)
        total_fees = sum(p.fees_cents for p in payments)
        net_amount = sum(p.net_amount_cents for p in payments)
        
        # Répartition par méthode
        by_method = {}
        for payment in payments:
            method_name = payment.payment_method.name
            if method_name not in by_method:
                by_method[method_name] = {"count": 0, "amount_cents": 0}
            by_method[method_name]["count"] += 1
            by_method[method_name]["amount_cents"] += payment.amount_cents
        
        return {
            "period_from": date_from,
            "period_to": date_to,
            "total_payments": len(payments),
            "total_amount_cents": total_amount,
            "total_fees_cents": total_fees,
            "net_amount_cents": net_amount,
            "average_amount_cents": total_amount // len(payments) if payments else 0,
            "by_method": by_method
        }
    
    def get_cash_flow(self, date_from: date = None, date_to: date = None) -> Dict[str, Any]:
        """Obtenir le flux de trésorerie"""
        if not date_from:
            date_from = date.today() - timedelta(days=30)
        if not date_to:
            date_to = date.today()
        
        start_datetime = datetime.combine(date_from, datetime.min.time())
        end_datetime = datetime.combine(date_to, datetime.max.time())
        
        movements = CashMovement.query.filter(
            CashMovement.business_id_fk == self.business_id,
            CashMovement.created_at >= start_datetime,
            CashMovement.created_at <= end_datetime
        ).all()
        
        cash_in = sum(m.amount_cents for m in movements if m.direction == "in")
        cash_out = sum(m.amount_cents for m in movements if m.direction == "out")
        net_cash_flow = cash_in - cash_out
        
        # Répartition par type
        by_type = {}
        for movement in movements:
            movement_type = movement.movement_type
            if movement_type not in by_type:
                by_type[movement_type] = {"in": 0, "out": 0}
            by_type[movement_type][movement.direction] += movement.amount_cents
        
        return {
            "period_from": date_from,
            "period_to": date_to,
            "cash_in_cents": cash_in,
            "cash_out_cents": cash_out,
            "net_cash_flow_cents": net_cash_flow,
            "by_type": by_type
        }


class ReceiptGenerator:
    """Générateur de reçus"""
    
    def __init__(self, business_id: int):
        self.business_id = business_id
    
    def generate_receipt(self, payment_id: int, customer_data: Dict = None) -> Receipt:
        """Générer un reçu pour un paiement"""
        payment = Payment.query.filter_by(
            id=payment_id,
            business_id_fk=self.business_id
        ).first()
        
        if not payment:
            raise ValueError("Paiement non trouvé")
        
        # Générer un numéro de reçu
        receipt_number = f"REC-{datetime.now().strftime('%Y%m%d%H%M%S')}-{payment.id}"
        
        # Préparer les données des articles (si c'est lié à une commande)
        items_data = {}
        if payment.order:
            items_data = {
                "order_id": payment.order.id,
                "items": []  # À remplir selon la structure de votre Order
            }
        
        # Données de paiement
        payment_data = {
            "payment_id": payment.id,
            "method": payment.payment_method.name,
            "amount_cents": payment.amount_cents,
            "fees_cents": payment.fees_cents,
            "net_amount_cents": payment.net_amount_cents,
            "receipt_number": payment.receipt_number
        }
        
        receipt = Receipt(
            business_id_fk=self.business_id,
            order_id_fk=payment.order_id_fk,
            receipt_number=receipt_number,
            customer_name=customer_data.get('name') if customer_data else None,
            customer_email=customer_data.get('email') if customer_data else None,
            customer_phone=customer_data.get('phone') if customer_data else None,
            subtotal_cents=payment.amount_cents,
            total_cents=payment.amount_cents,
            items_data=json.dumps(items_data),
            payment_data=json.dumps(payment_data),
            created_by_user_id=customer_data.get('user_id') if customer_data else None
        )
        
        db.session.add(receipt)
        db.session.commit()
        
        return receipt
    
    def format_receipt_text(self, receipt: Receipt) -> str:
        """Formater un reçu en texte pour impression"""
        business = receipt.business
        
        lines = []
        lines.append("=" * 40)
        lines.append(f"{business.name}".center(40))
        if hasattr(business, 'address'):
            lines.append(f"{business.address}".center(40))
        lines.append("=" * 40)
        lines.append("")
        
        lines.append(f"Reçu N°: {receipt.receipt_number}")
        lines.append(f"Date: {receipt.created_at.strftime('%d/%m/%Y %H:%M')}")
        
        if receipt.customer_name:
            lines.append(f"Client: {receipt.customer_name}")
        
        lines.append("")
        lines.append("-" * 40)
        
        # Articles (si disponibles)
        if receipt.items_data:
            try:
                items = json.loads(receipt.items_data)
                for item in items.get('items', []):
                    lines.append(f"{item['name']:<25} {item['price']:>10}")
            except (json.JSONDecodeError, KeyError):
                pass
        
        lines.append("-" * 40)
        lines.append(f"{'Total':<25} {receipt.total_cents/100:>10.2f} EUR")
        lines.append("")
        
        # Informations de paiement
        if receipt.payment_data:
            try:
                payment_info = json.loads(receipt.payment_data)
                lines.append(f"Paiement: {payment_info['method']}")
                if payment_info.get('fees_cents', 0) > 0:
                    lines.append(f"Frais: {payment_info['fees_cents']/100:.2f} EUR")
            except (json.JSONDecodeError, KeyError):
                pass
        
        lines.append("")
        lines.append("Merci de votre visite !".center(40))
        lines.append("=" * 40)
        
        return "\n".join(lines)


# Fonctions utilitaires
def validate_card_number(card_number: str) -> bool:
    """Valider un numéro de carte avec l'algorithme de Luhn"""
    def luhn_checksum(card_num):
        def digits_of(n):
            return [int(d) for d in str(n)]
        digits = digits_of(card_num)
        odd_digits = digits[-1::-2]
        even_digits = digits[-2::-2]
        checksum = sum(odd_digits)
        for d in even_digits:
            checksum += sum(digits_of(d*2))
        return checksum % 10
    
    return luhn_checksum(card_number) == 0


def mask_card_number(card_number: str) -> str:
    """Masquer un numéro de carte"""
    if len(card_number) < 4:
        return "*" * len(card_number)
    return "*" * (len(card_number) - 4) + card_number[-4:]


def generate_webhook_signature(payload: str, secret: str) -> str:
    """Générer une signature webhook"""
    return hmac.new(
        secret.encode('utf-8'),
        payload.encode('utf-8'),
        hashlib.sha256
    ).hexdigest()


def verify_webhook_signature(payload: str, signature: str, secret: str) -> bool:
    """Vérifier une signature webhook"""
    expected_signature = generate_webhook_signature(payload, secret)
    return hmac.compare_digest(signature, expected_signature)


def calculate_payment_fees(amount_cents: int, fee_percentage: float, fee_fixed_cents: int) -> int:
    """Calculer les frais de paiement"""
    percentage_fee = int(amount_cents * fee_percentage / 100)
    return percentage_fee + fee_fixed_cents