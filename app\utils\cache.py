import functools
import hashlib
import json
import logging
from typing import Any, Dict, Optional, Union
from datetime import datetime, timedelta

from flask import current_app, request
from flask_caching import Cache
import redis

from app.extensions import cache, redis_client


class CacheManager:
    """Gestionnaire de cache avancé pour l'application"""
    
    @staticmethod
    def generate_cache_key(prefix: str, *args, **kwargs) -> str:
        """Génère une clé de cache unique basée sur les arguments"""
        # Créer une chaîne à partir des arguments
        key_parts = [prefix]
        
        # Ajouter les arguments positionnels
        for arg in args:
            key_parts.append(str(arg))
        
        # Ajouter les arguments nommés (triés pour cohérence)
        for key in sorted(kwargs.keys()):
            key_parts.append(f"{key}:{kwargs[key]}")
        
        # Créer un hash pour éviter les clés trop longues
        key_string = "|".join(key_parts)
        return f"{prefix}:{hashlib.md5(key_string.encode()).hexdigest()}"
    
    @staticmethod
    def cache_result(timeout: int = 300, key_prefix: str = "result"):
        """Décorateur pour mettre en cache les résultats de fonctions"""
        def decorator(func):
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                # Générer la clé de cache
                cache_key = CacheManager.generate_cache_key(
                    f"{key_prefix}:{func.__name__}", *args, **kwargs
                )
                
                # Essayer de récupérer depuis le cache
                cached_result = cache.get(cache_key)
                if cached_result is not None:
                    return cached_result
                
                # Exécuter la fonction et mettre en cache
                result = func(*args, **kwargs)
                cache.set(cache_key, result, timeout=timeout)
                return result
            
            return wrapper
        return decorator
    
    @staticmethod
    def invalidate_cache_pattern(pattern: str):
        """Invalide toutes les clés de cache correspondant à un pattern"""
        if redis_client:
            try:
                keys = redis_client.keys(f"{pattern}*")
                if keys:
                    redis_client.delete(*keys)
            except Exception as e:
                current_app.logger.warning(f"Erreur lors de l'invalidation du cache: {e}")
    
    @staticmethod
    def get_cache_stats() -> Dict[str, Any]:
        """Récupère les statistiques du cache"""
        if redis_client:
            try:
                info = redis_client.info()
                return {
                    'used_memory': info.get('used_memory_human', 'N/A'),
                    'connected_clients': info.get('connected_clients', 0),
                    'total_commands_processed': info.get('total_commands_processed', 0),
                    'keyspace_hits': info.get('keyspace_hits', 0),
                    'keyspace_misses': info.get('keyspace_misses', 0),
                    'hit_rate': info.get('keyspace_hits', 0) / (info.get('keyspace_hits', 1) + info.get('keyspace_misses', 1)) * 100
                }
            except Exception as e:
                current_app.logger.warning(f"Erreur lors de la récupération des stats Redis: {e}")
        
        return {}


class QueryOptimizer:
    """Optimiseur de requêtes SQL"""
    
    @staticmethod
    def optimize_pagination(query, page: int = 1, per_page: int = 20, 
                          max_per_page: int = 100) -> dict:
        """Optimise la pagination avec des limites et des statistiques"""
        # Limiter le nombre d'éléments par page
        per_page = min(per_page, max_per_page)
        
        # Exécuter la requête paginée
        pagination = query.paginate(
            page=page, 
            per_page=per_page, 
            error_out=False
        )
        
        return {
            'items': pagination.items,
            'total': pagination.total,
            'pages': pagination.pages,
            'current_page': pagination.page,
            'per_page': pagination.per_page,
            'has_prev': pagination.has_prev,
            'has_next': pagination.has_next,
            'prev_num': pagination.prev_num,
            'next_num': pagination.next_num
        }
    
    @staticmethod
    def add_eager_loading(query, *relationships):
        """Ajoute le chargement anticipé pour optimiser les requêtes"""
        for relationship in relationships:
            query = query.options(relationship)
        return query
    
    @staticmethod
    def add_index_hints(query, model, *index_names):
        """Ajoute des hints d'index pour optimiser les requêtes (PostgreSQL/MySQL)"""
        # Cette méthode est plus complexe et dépend du dialecte SQL
        # Pour une implémentation simple, on retourne la requête inchangée
        return query


class ResponseCache:
    """Cache des réponses HTTP"""
    
    @staticmethod
    def cache_response(timeout: int = 300):
        """Décorateur pour mettre en cache les réponses HTTP"""
        def decorator(f):
            @functools.wraps(f)
            def decorated_function(*args, **kwargs):
                # Ne pas mettre en cache pour les requêtes POST/PUT/DELETE
                if request.method in ['POST', 'PUT', 'DELETE', 'PATCH']:
                    return f(*args, **kwargs)
                
                # Générer une clé de cache basée sur l'URL et les paramètres
                cache_key = f"response:{request.url}"
                
                # Essayer de récupérer depuis le cache
                cached_response = cache.get(cache_key)
                if cached_response:
                    return cached_response
                
                # Exécuter la fonction et mettre en cache
                response = f(*args, **kwargs)
                
                # Ne mettre en cache que les réponses réussies
                if hasattr(response, 'status_code') and response.status_code == 200:
                    cache.set(cache_key, response, timeout=timeout)
                
                return response
            
            return decorated_function
        return decorator


# Fonctions utilitaires globales
def clear_all_cache():
    """Efface tout le cache"""
    try:
        cache.clear()
        if redis_client:
            redis_client.flushdb()
        return True
    except Exception as e:
        current_app.logger.error(f"Erreur lors de l'effacement du cache: {e}")
        return False


def warmup_cache():
    """Pré-charge les données fréquemment utilisées dans le cache"""
    # Cette fonction peut être étendue pour pré-charger des données spécifiques
    pass


# Configuration du cache
def init_cache(app):
    """Initialise le système de cache"""
    global redis_client
    
    cache_config = {
        'CACHE_TYPE': app.config.get('CACHE_TYPE', 'redis'),
        'CACHE_REDIS_URL': app.config.get('CACHE_REDIS_URL', 'redis://localhost:6379/0'),
        'CACHE_DEFAULT_TIMEOUT': app.config.get('CACHE_DEFAULT_TIMEOUT', 300)
    }
    
    cache.init_app(app, config=cache_config)
    
    # Initialiser le client Redis direct
    try:
        redis_client = redis.from_url(cache_config['CACHE_REDIS_URL'])
        redis_client.ping()  # Tester la connexion
        app.logger.info("Cache Redis connecté avec succès")
    except Exception as e:
        app.logger.warning(f"Redis non disponible, utilisation du cache en mémoire: {e}")
        # Fallback sur le cache en mémoire
        cache_config['CACHE_TYPE'] = 'simple'
        cache.init_app(app, config=cache_config)