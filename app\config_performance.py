"""Configuration des paramètres de performance"""
import os

class PerformanceConfig:
    """Configuration des paramètres de performance"""
    
    # Paramètres de cache
    CACHE_TYPE = os.environ.get('CACHE_TYPE', 'redis')
    CACHE_REDIS_HOST = os.environ.get('CACHE_REDIS_HOST', 'localhost')
    CACHE_REDIS_PORT = int(os.environ.get('CACHE_REDIS_PORT', 6379))
    CACHE_REDIS_DB = int(os.environ.get('CACHE_REDIS_DB', 1))
    CACHE_DEFAULT_TIMEOUT = int(os.environ.get('CACHE_DEFAULT_TIMEOUT', 300))
    CACHE_KEY_PREFIX = os.environ.get('CACHE_KEY_PREFIX', 'pos_')
    
    # Paramètres de compression
    ENABLE_RESPONSE_COMPRESSION = os.environ.get('ENABLE_RESPONSE_COMPRESSION', 'True').lower() == 'true'
    
    # Paramètres de pagination
    DEFAULT_PAGE_SIZE = int(os.environ.get('DEFAULT_PAGE_SIZE', 20))
    MAX_PAGE_SIZE = int(os.environ.get('MAX_PAGE_SIZE', 100))
    
    # Paramètres de requêtes
    SQLALCHEMY_POOL_SIZE = int(os.environ.get('SQLALCHEMY_POOL_SIZE', 10))
    SQLALCHEMY_MAX_OVERFLOW = int(os.environ.get('SQLALCHEMY_MAX_OVERFLOW', 20))
    SQLALCHEMY_POOL_TIMEOUT = int(os.environ.get('SQLALCHEMY_POOL_TIMEOUT', 30))
    SQLALCHEMY_POOL_RECYCLE = int(os.environ.get('SQLALCHEMY_POOL_RECYCLE', 3600))
    
    # Paramètres d'optimisation mémoire
    ENABLE_MEMORY_OPTIMIZATION = os.environ.get('ENABLE_MEMORY_OPTIMIZATION', 'True').lower() == 'true'
    
    # Paramètres de monitoring
    ENABLE_PERFORMANCE_MONITORING = os.environ.get('ENABLE_PERFORMANCE_MONITORING', 'True').lower() == 'true'
    PERFORMANCE_METRICS_RETENTION_DAYS = int(os.environ.get('PERFORMANCE_METRICS_RETENTION_DAYS', 7))
    
    # Paramètres de rate limiting
    RATELIMIT_STORAGE_URL = os.environ.get('RATELIMIT_STORAGE_URL', 'redis://localhost:6379/2')
    DEFAULT_RATE_LIMIT = os.environ.get('DEFAULT_RATE_LIMIT', '1000 per hour')
    
    # Paramètres de lazy loading
    ENABLE_LAZY_LOADING = os.environ.get('ENABLE_LAZY_LOADING', 'True').lower() == 'true'
    
    # Paramètres de préfetching
    ENABLE_PREFETCHING = os.environ.get('ENABLE_PREFETCHING', 'True').lower() == 'true'
    
    # Paramètres de batch processing
    DEFAULT_BATCH_SIZE = int(os.environ.get('DEFAULT_BATCH_SIZE', 50))
    
    @classmethod
    def init_app(cls, app):
        """Initialise l'application avec les paramètres de performance"""
        # Appliquer les paramètres de cache
        app.config['CACHE_TYPE'] = cls.CACHE_TYPE
        app.config['CACHE_REDIS_HOST'] = cls.CACHE_REDIS_HOST
        app.config['CACHE_REDIS_PORT'] = cls.CACHE_REDIS_PORT
        app.config['CACHE_REDIS_DB'] = cls.CACHE_REDIS_DB
        app.config['CACHE_DEFAULT_TIMEOUT'] = cls.CACHE_DEFAULT_TIMEOUT
        app.config['CACHE_KEY_PREFIX'] = cls.CACHE_KEY_PREFIX
        
        # Appliquer les paramètres de compression
        app.config['ENABLE_RESPONSE_COMPRESSION'] = cls.ENABLE_RESPONSE_COMPRESSION
        
        # Appliquer les paramètres de pagination
        app.config['DEFAULT_PAGE_SIZE'] = cls.DEFAULT_PAGE_SIZE
        app.config['MAX_PAGE_SIZE'] = cls.MAX_PAGE_SIZE
        
        # Appliquer les paramètres de base de données
        app.config['SQLALCHEMY_POOL_SIZE'] = cls.SQLALCHEMY_POOL_SIZE
        app.config['SQLALCHEMY_MAX_OVERFLOW'] = cls.SQLALCHEMY_MAX_OVERFLOW
        app.config['SQLALCHEMY_POOL_TIMEOUT'] = cls.SQLALCHEMY_POOL_TIMEOUT
        app.config['SQLALCHEMY_POOL_RECYCLE'] = cls.SQLALCHEMY_POOL_RECYCLE
        
        # Appliquer les autres paramètres
        app.config['ENABLE_MEMORY_OPTIMIZATION'] = cls.ENABLE_MEMORY_OPTIMIZATION
        app.config['ENABLE_PERFORMANCE_MONITORING'] = cls.ENABLE_PERFORMANCE_MONITORING
        app.config['PERFORMANCE_METRICS_RETENTION_DAYS'] = cls.PERFORMANCE_METRICS_RETENTION_DAYS
        app.config['RATELIMIT_STORAGE_URL'] = cls.RATELIMIT_STORAGE_URL
        app.config['DEFAULT_RATE_LIMIT'] = cls.DEFAULT_RATE_LIMIT
        app.config['ENABLE_LAZY_LOADING'] = cls.ENABLE_LAZY_LOADING
        app.config['ENABLE_PREFETCHING'] = cls.ENABLE_PREFETCHING
        app.config['DEFAULT_BATCH_SIZE'] = cls.DEFAULT_BATCH_SIZE