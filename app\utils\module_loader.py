"""Optimisation du chargement des modules et dépendances"""
import importlib
import logging
from typing import Dict, List, Optional, Any
from functools import lru_cache
import time

logger = logging.getLogger(__name__)

class ModuleLoader:
    """Gère le chargement optimisé des modules"""
    
    def __init__(self):
        self.loaded_modules: Dict[str, Any] = {}
        self.load_times: Dict[str, float] = {}
    
    def load_module(self, module_name: str, cache_result: bool = True) -> Any:
        """
        Charge un module avec mise en cache optionnelle
        """
        if cache_result and module_name in self.loaded_modules:
            return self.loaded_modules[module_name]
        
        start_time = time.time()
        try:
            module = importlib.import_module(module_name)
            load_time = time.time() - start_time
            
            if cache_result:
                self.loaded_modules[module_name] = module
                
            self.load_times[module_name] = load_time
            logger.debug(f"Module {module_name} loaded in {load_time:.2f}ms")
            return module
        except ImportError as e:
            logger.error(f"Failed to load module {module_name}: {e}")
            raise
    
    def get_load_time(self, module_name: str) -> Optional[float]:
        """
        Retourne le temps de chargement d'un module
        """
        return self.load_times.get(module_name)
    
    def get_slow_modules(self, threshold_ms: float = 100) -> List[str]:
        """
        Retourne la liste des modules lents à charger
        """
        return [name for name, time in self.load_times.items() if time > threshold_ms]
    
    def preload_modules(self, module_names: List[str]):
        """
        Précharge une liste de modules
        """
        for module_name in module_names:
            try:
                self.load_module(module_name)
            except Exception as e:
                logger.warning(f"Failed to preload module {module_name}: {e}")

# Instance globale
module_loader = ModuleLoader()

@lru_cache(maxsize=128)
def cached_import(module_name: str) -> Any:
    """
    Importe un module avec mise en cache LRU
    """
    return importlib.import_module(module_name)

def lazy_import(module_name: str):
    """
    Import paresseux d'un module - chargé uniquement quand utilisé
    """
    class LazyModule:
        def __init__(self, name):
            self._module_name = name
            self._module = None
        
        def __getattr__(self, name):
            if self._module is None:
                self._module = module_loader.load_module(self._module_name)
            return getattr(self._module, name)
    
    return LazyModule(module_name)

def optimize_imports():
    """
    Optimise les imports de l'application
    """
    # Liste des modules fréquemment utilisés à précharger
    common_modules = [
        'app.modules.accounts.models',
        'app.modules.catalog.models',
        'app.modules.pos.models',
        'sqlalchemy',
        'flask',
        'flask_sqlalchemy',
        'flask_login'
    ]
    
    module_loader.preload_modules(common_modules)
    logger.info("Preloaded common modules")

# Decorator pour le lazy loading des modules
def lazy_module(module_name: str):
    """
    Décorateur pour le lazy loading des modules
    """
    def decorator(func):
        module = lazy_import(module_name)
        def wrapper(*args, **kwargs):
            return func(module, *args, **kwargs)
        return wrapper
    return decorator