{% extends "base.html" %}

{% block title %}
    {% if prescription %}
        Modifier Ordonnance - {{ prescription.prescription_number }}
    {% else %}
        Nouvelle Ordonnance
    {% endif %}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">{{ title }}</h1>
                <a href="{{ url_for('pharmacy.prescriptions') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> Retour à la liste
                </a>
            </div>

            <form method="POST" novalidate>
                {{ form.hidden_tag() }}
                
                <div class="row">
                    <!-- Informations de l'ordonnance -->
                    <div class="col-lg-8">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Informations de l'Ordonnance</h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form.prescription_number.label(class="form-label") }}
                                            {{ form.prescription_number(class="form-control" + (" is-invalid" if form.prescription_number.errors else "")) }}
                                            {% if form.prescription_number.errors %}
                                                <div class="invalid-feedback">
                                                    {{ form.prescription_number.errors[0] }}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            {{ form.prescription_date.label(class="form-label") }}
                                            {{ form.prescription_date(class="form-control" + (" is-invalid" if form.prescription_date.errors else "")) }}
                                            {% if form.prescription_date.errors %}
                                                <div class="invalid-feedback">
                                                    {{ form.prescription_date.errors[0] }}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            {{ form.validity_date.label(class="form-label") }}
                                            {{ form.validity_date(class="form-control" + (" is-invalid" if form.validity_date.errors else "")) }}
                                            {% if form.validity_date.errors %}
                                                <div class="invalid-feedback">
                                                    {{ form.validity_date.errors[0] }}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>

                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Patient *</label>
                                            {{ form.customer_id_fk(class="form-control", style="display: none;") }}
                                            <div class="input-group">
                                                <input type="text" class="form-control" id="customer-search" 
                                                       placeholder="Rechercher un patient..." autocomplete="off">
                                                <button type="button" class="btn btn-outline-primary" id="new-customer-btn">
                                                    <i class="fas fa-plus"></i>
                                                </button>
                                            </div>
                                            <div id="customer-results" class="list-group position-absolute w-100" style="z-index: 1000; display: none;"></div>
                                            <div id="selected-customer" class="mt-2" style="display: none;">
                                                <div class="alert alert-info">
                                                    <strong id="customer-name"></strong><br>
                                                    <small id="customer-details"></small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form.status.label(class="form-label") }}
                                            {{ form.status(class="form-select" + (" is-invalid" if form.status.errors else "")) }}
                                            {% if form.status.errors %}
                                                <div class="invalid-feedback">
                                                    {{ form.status.errors[0] }}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Informations du médecin -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Médecin Prescripteur</h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form.doctor_name.label(class="form-label") }}
                                            {{ form.doctor_name(class="form-control" + (" is-invalid" if form.doctor_name.errors else "")) }}
                                            {% if form.doctor_name.errors %}
                                                <div class="invalid-feedback">
                                                    {{ form.doctor_name.errors[0] }}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            {{ form.doctor_rpps.label(class="form-label") }}
                                            {{ form.doctor_rpps(class="form-control" + (" is-invalid" if form.doctor_rpps.errors else "")) }}
                                            {% if form.doctor_rpps.errors %}
                                                <div class="invalid-feedback">
                                                    {{ form.doctor_rpps.errors[0] }}
                                                </div>
                                            {% endif %}
                                            <small class="form-text text-muted">Requis pour les stupéfiants</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            {{ form.doctor_specialty.label(class="form-label") }}
                                            {{ form.doctor_specialty(class="form-control" + (" is-invalid" if form.doctor_specialty.errors else "")) }}
                                            {% if form.doctor_specialty.errors %}
                                                <div class="invalid-feedback">
                                                    {{ form.doctor_specialty.errors[0] }}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Notes -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Notes et Observations</h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-12">
                                        <div class="mb-3">
                                            {{ form.doctor_notes.label(class="form-label") }}
                                            {{ form.doctor_notes(class="form-control" + (" is-invalid" if form.doctor_notes.errors else "")) }}
                                            {% if form.doctor_notes.errors %}
                                                <div class="invalid-feedback">
                                                    {{ form.doctor_notes.errors[0] }}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="mb-3">
                                            {{ form.pharmacist_notes.label(class="form-label") }}
                                            {{ form.pharmacist_notes(class="form-control" + (" is-invalid" if form.pharmacist_notes.errors else "")) }}
                                            {% if form.pharmacist_notes.errors %}
                                                <div class="invalid-feedback">
                                                    {{ form.pharmacist_notes.errors[0] }}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Informations patient -->
                    <div class="col-lg-4">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Informations Patient</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    {{ form.patient_weight.label(class="form-label") }}
                                    <div class="input-group">
                                        {{ form.patient_weight(class="form-control" + (" is-invalid" if form.patient_weight.errors else "")) }}
                                        <span class="input-group-text">kg</span>
                                    </div>
                                    {% if form.patient_weight.errors %}
                                        <div class="invalid-feedback">
                                            {{ form.patient_weight.errors[0] }}
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <div class="mb-3">
                                    {{ form.patient_allergies.label(class="form-label") }}
                                    {{ form.patient_allergies(class="form-control" + (" is-invalid" if form.patient_allergies.errors else "")) }}
                                    {% if form.patient_allergies.errors %}
                                        <div class="invalid-feedback">
                                            {{ form.patient_allergies.errors[0] }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Aide et conseils -->
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Aide</h5>
                            </div>
                            <div class="card-body">
                                <h6>Conseils de saisie :</h6>
                                <ul class="small">
                                    <li>Le numéro d'ordonnance doit être unique</li>
                                    <li>La date de validité est généralement 1 an après la prescription</li>
                                    <li>Le numéro RPPS est obligatoire pour les stupéfiants</li>
                                    <li>Saisissez les allergies connues du patient</li>
                                </ul>
                                
                                <h6 class="mt-3">Raccourcis clavier :</h6>
                                <ul class="small">
                                    <li><kbd>Ctrl+S</kbd> : Sauvegarder</li>
                                    <li><kbd>Esc</kbd> : Annuler</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Boutons d'action -->
                <div class="d-flex justify-content-end gap-2 mb-4">
                    <a href="{{ url_for('pharmacy.prescriptions') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i> Annuler
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> 
                        {% if prescription %}Modifier{% else %}Créer{% endif %} l'Ordonnance
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Recherche de patients
    const customerSearch = document.getElementById('customer-search');
    const customerResults = document.getElementById('customer-results');
    const selectedCustomer = document.getElementById('selected-customer');
    const customerIdField = document.querySelector('input[name="customer_id_fk"]');
    
    let searchTimeout;
    
    customerSearch.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        const query = this.value.trim();
        
        if (query.length < 2) {
            customerResults.style.display = 'none';
            return;
        }
        
        searchTimeout = setTimeout(() => {
            fetch(`/pharmacy/api/customers/search?q=${encodeURIComponent(query)}`)
                .then(response => response.json())
                .then(customers => {
                    customerResults.innerHTML = '';
                    
                    if (customers.length > 0) {
                        customers.forEach(customer => {
                            const item = document.createElement('button');
                            item.type = 'button';
                            item.className = 'list-group-item list-group-item-action';
                            item.innerHTML = `
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">${customer.name}</h6>
                                </div>
                                <p class="mb-1">${customer.email || ''}</p>
                                <small>${customer.phone || ''}</small>
                            `;
                            
                            item.addEventListener('click', function() {
                                selectCustomer(customer);
                            });
                            
                            customerResults.appendChild(item);
                        });
                        
                        customerResults.style.display = 'block';
                    } else {
                        customerResults.style.display = 'none';
                    }
                })
                .catch(error => {
                    console.error('Erreur lors de la recherche:', error);
                    customerResults.style.display = 'none';
                });
        }, 300);
    });
    
    function selectCustomer(customer) {
        customerIdField.value = customer.id;
        customerSearch.value = customer.name;
        customerResults.style.display = 'none';
        
        document.getElementById('customer-name').textContent = customer.name;
        document.getElementById('customer-details').textContent = 
            `${customer.email || ''} ${customer.phone || ''}`.trim();
        selectedCustomer.style.display = 'block';
    }
    
    // Cacher les résultats lors du clic ailleurs
    document.addEventListener('click', function(e) {
        if (!customerSearch.contains(e.target) && !customerResults.contains(e.target)) {
            customerResults.style.display = 'none';
        }
    });
    
    // Raccourcis clavier
    document.addEventListener('keydown', function(e) {
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            document.querySelector('form').submit();
        } else if (e.key === 'Escape') {
            window.location.href = "{{ url_for('pharmacy.prescriptions') }}";
        }
    });
    
    // Nouvelle patient
    document.getElementById('new-customer-btn').addEventListener('click', function() {
        // Ouvrir modal de création de patient ou rediriger
        window.open("{{ url_for('customers.new_customer') }}", '_blank');
    });
});
</script>
{% endblock %}