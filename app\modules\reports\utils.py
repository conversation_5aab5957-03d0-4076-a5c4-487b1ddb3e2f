from __future__ import annotations

import io
import json
from datetime import datetime, date, timedelta
from typing import Dict, Any, List, Optional
from decimal import Decimal

try:
    import pandas as pd
except ImportError:
    pd = None

try:
    from reportlab.pdfgen import canvas
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.lib import colors
    from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
    from reportlab.lib.styles import getSampleStyleSheet
    from reportlab.lib.units import inch
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False

from app.extensions import db
from .models import ReportTemplate, ReportExecution, ReportData, ReportType

# Import conditionnel pour éviter les erreurs si les modules n'existent pas encore
try:
    from ..sales.models import Sale, SaleItem
except ImportError:
    Sale = SaleItem = None

try:
    from ..inventory.models import Product, Category
except ImportError:
    Product = Category = None

try:
    from ..customers.models import Customer
except ImportError:
    Customer = None

try:
    from ..suppliers.models import Supplier
except ImportError:
    Supplier = None

try:
    from ..purchasing.models import PurchaseOrder
except ImportError:
    PurchaseOrder = None


class ReportGenerator:
    """Générateur de rapports avancé"""
    
    def __init__(self):
        self.supported_formats = ['pdf', 'excel', 'csv', 'json']
    
    def generate_report(self, template: ReportTemplate, parameters: Dict[str, Any] = None) -> Dict[str, Any]:
        """Générer un rapport complet selon le template"""
        if parameters is None:
            parameters = {}
        
        # Récupérer les données
        data = self._get_report_data(template, parameters)
        
        # Appliquer les transformations
        processed_data = self._process_data(data, template.query_config or {})
        
        # Générer les graphiques si configurés
        charts = self._generate_charts(processed_data, template.chart_config or {})
        
        return {
            'data': processed_data,
            'charts': charts,
            'metadata': {
                'generated_at': datetime.utcnow().isoformat(),
                'template_name': template.name,
                'parameters': parameters
            }
        }
    
    def _get_report_data(self, template: ReportTemplate, parameters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Récupérer les données selon le type de rapport"""
        if template.report_type == ReportType.SALES:
            return self._get_sales_data(parameters)
        elif template.report_type == ReportType.FINANCIAL:
            return self._get_financial_data(parameters)
        elif template.report_type == ReportType.INVENTORY:
            return self._get_inventory_data(parameters)
        elif template.report_type == ReportType.CUSTOMER:
            return self._get_customer_data(parameters)
        elif template.report_type == ReportType.SUPPLIER:
            return self._get_supplier_data(parameters)
        elif template.report_type == ReportType.PRODUCTION:
            return self._get_production_data(parameters)
        else:
            return []
    
    def _get_sales_data(self, parameters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Récupérer les données de ventes"""
        query = Sale.query
        
        # Filtres de date
        if 'date_from' in parameters:
            query = query.filter(Sale.created_at >= parameters['date_from'])
        if 'date_to' in parameters:
            query = query.filter(Sale.created_at <= parameters['date_to'])
        
        # Filtres additionnels
        if 'customer_id' in parameters:
            query = query.filter(Sale.customer_id == parameters['customer_id'])
        
        sales = query.all()
        
        return [{
            'id': sale.id,
            'date': sale.created_at.strftime('%Y-%m-%d'),
            'total_amount': float(sale.total_amount),
            'items_count': len(sale.items),
            'customer_name': sale.customer.name if sale.customer else 'Client anonyme',
            'status': sale.status.value if hasattr(sale, 'status') else 'completed'
        } for sale in sales]
    
    def _get_financial_data(self, parameters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Récupérer les données financières"""
        data = []
        
        # Revenus par période
        revenue_query = db.session.query(
            func.date(Sale.created_at).label('date'),
            func.sum(Sale.total_amount).label('revenue')
        )
        
        if 'date_from' in parameters:
            revenue_query = revenue_query.filter(Sale.created_at >= parameters['date_from'])
        if 'date_to' in parameters:
            revenue_query = revenue_query.filter(Sale.created_at <= parameters['date_to'])
        
        revenue_data = revenue_query.group_by(func.date(Sale.created_at)).all()
        
        for row in revenue_data:
            data.append({
                'date': str(row.date),
                'type': 'revenue',
                'amount': float(row.revenue),
                'description': 'Revenus des ventes'
            })
        
        # Coûts des achats
        cost_query = db.session.query(
            func.date(PurchaseOrder.created_at).label('date'),
            func.sum(PurchaseOrder.total_amount).label('cost')
        )
        
        if 'date_from' in parameters:
            cost_query = cost_query.filter(PurchaseOrder.created_at >= parameters['date_from'])
        if 'date_to' in parameters:
            cost_query = cost_query.filter(PurchaseOrder.created_at <= parameters['date_to'])
        
        cost_data = cost_query.group_by(func.date(PurchaseOrder.created_at)).all()
        
        for row in cost_data:
            data.append({
                'date': str(row.date),
                'type': 'cost',
                'amount': float(row.cost),
                'description': 'Coûts des achats'
            })
        
        return data
    
    def _get_inventory_data(self, parameters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Récupérer les données d'inventaire"""
        query = Product.query
        
        # Filtres
        if 'category_id' in parameters:
            query = query.filter(Product.category_id == parameters['category_id'])
        if 'low_stock_only' in parameters and parameters['low_stock_only']:
            query = query.filter(Product.quantity <= Product.min_quantity)
        
        products = query.all()
        
        return [{
            'id': product.id,
            'name': product.name,
            'sku': product.sku,
            'quantity': product.quantity,
            'min_quantity': product.min_quantity,
            'cost_price': float(product.cost_price),
            'selling_price': float(product.selling_price),
            'stock_value': float(product.quantity * product.cost_price),
            'category': product.category.name if product.category else 'Sans catégorie',
            'status': 'low_stock' if product.quantity <= product.min_quantity else 'normal'
        } for product in products]
    
    def _get_customer_data(self, parameters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Récupérer les données clients"""
        query = Customer.query
        
        # Filtres
        if 'date_from' in parameters:
            query = query.filter(Customer.created_at >= parameters['date_from'])
        if 'date_to' in parameters:
            query = query.filter(Customer.created_at <= parameters['date_to'])
        
        customers = query.all()
        
        return [{
            'id': customer.id,
            'name': customer.name,
            'email': customer.email,
            'phone': customer.phone,
            'created_at': customer.created_at.strftime('%Y-%m-%d'),
            'total_purchases': float(sum(sale.total_amount for sale in customer.sales)),
            'purchases_count': len(customer.sales),
            'last_purchase': max(sale.created_at for sale in customer.sales).strftime('%Y-%m-%d') if customer.sales else None
        } for customer in customers]
    
    def _get_supplier_data(self, parameters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Récupérer les données fournisseurs"""
        suppliers = Supplier.query.all()
        
        return [{
            'id': supplier.id,
            'name': supplier.name,
            'contact_person': supplier.contact_person,
            'email': supplier.email,
            'phone': supplier.phone,
            'total_orders': len(supplier.purchase_orders),
            'total_amount': float(sum(order.total_amount for order in supplier.purchase_orders)),
            'last_order': max(order.created_at for order in supplier.purchase_orders).strftime('%Y-%m-%d') if supplier.purchase_orders else None
        } for supplier in suppliers]
    
    def _get_production_data(self, parameters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Récupérer les données de production (placeholder)"""
        return [{
            'message': 'Données de production à implémenter selon les besoins spécifiques'
        }]
    
    def _process_data(self, data: List[Dict[str, Any]], config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Traiter et transformer les données"""
        if not config:
            return data
        
        processed_data = data.copy()
        
        # Tri
        if 'sort_by' in config:
            sort_field = config['sort_by']
            reverse = config.get('sort_order', 'asc') == 'desc'
            processed_data.sort(key=lambda x: x.get(sort_field, ''), reverse=reverse)
        
        # Limitation
        if 'limit' in config:
            processed_data = processed_data[:config['limit']]
        
        # Filtres personnalisés
        if 'filters' in config:
            for filter_config in config['filters']:
                field = filter_config.get('field')
                operator = filter_config.get('operator', 'equals')
                value = filter_config.get('value')
                
                if field and value is not None:
                    processed_data = self._apply_filter(processed_data, field, operator, value)
        
        return processed_data
    
    def _apply_filter(self, data: List[Dict[str, Any]], field: str, operator: str, value: Any) -> List[Dict[str, Any]]:
        """Appliquer un filtre sur les données"""
        filtered_data = []
        
        for item in data:
            item_value = item.get(field)
            
            if operator == 'equals' and item_value == value:
                filtered_data.append(item)
            elif operator == 'not_equals' and item_value != value:
                filtered_data.append(item)
            elif operator == 'greater_than' and isinstance(item_value, (int, float)) and item_value > value:
                filtered_data.append(item)
            elif operator == 'less_than' and isinstance(item_value, (int, float)) and item_value < value:
                filtered_data.append(item)
            elif operator == 'contains' and isinstance(item_value, str) and str(value).lower() in item_value.lower():
                filtered_data.append(item)
        
        return filtered_data
    
    def _generate_charts(self, data: List[Dict[str, Any]], config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Générer les configurations de graphiques"""
        if not config or not data:
            return []
        
        charts = []
        
        for chart_config in config.get('charts', []):
            chart_type = chart_config.get('type', 'line')
            x_field = chart_config.get('x_field')
            y_field = chart_config.get('y_field')
            title = chart_config.get('title', 'Graphique')
            
            if x_field and y_field:
                chart_data = self._prepare_chart_data(data, x_field, y_field, chart_type)
                
                charts.append({
                    'type': chart_type,
                    'title': title,
                    'data': chart_data,
                    'options': chart_config.get('options', {})
                })
        
        return charts
    
    def _prepare_chart_data(self, data: List[Dict[str, Any]], x_field: str, y_field: str, chart_type: str) -> Dict[str, Any]:
        """Préparer les données pour un graphique"""
        labels = []
        values = []
        
        # Grouper les données si nécessaire
        if chart_type in ['pie', 'doughnut']:
            # Grouper par x_field et sommer y_field
            groups = {}
            for item in data:
                x_value = str(item.get(x_field, ''))
                y_value = item.get(y_field, 0)
                
                if isinstance(y_value, (int, float)):
                    groups[x_value] = groups.get(x_value, 0) + y_value
            
            labels = list(groups.keys())
            values = list(groups.values())
        else:
            # Données séquentielles
            for item in data:
                labels.append(str(item.get(x_field, '')))
                values.append(item.get(y_field, 0))
        
        return {
            'labels': labels,
            'datasets': [{
                'data': values,
                'label': y_field.replace('_', ' ').title()
            }]
        }


class ReportExporter:
    """Exporteur de rapports en différents formats"""
    
    def __init__(self):
        self.supported_formats = ['pdf', 'excel', 'csv', 'json']
    
    def export_to_pdf(self, report_data: Dict[str, Any], filename: str = None) -> io.BytesIO:
        """Exporter un rapport en PDF avec fonctionnalités avancées"""
        if not REPORTLAB_AVAILABLE:
            raise ImportError("ReportLab n'est pas installé. Installez avec: pip install reportlab")
        
        buffer = io.BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=A4, topMargin=72, bottomMargin=72)
        styles = getSampleStyleSheet()
        story = []
        
        # Style personnalisé pour le titre
        title_style = styles['Title']
        title_style.alignment = 1  # Centré
        title_style.fontSize = 24
        title_style.textColor = colors.HexColor('#667eea')
        
        # Titre principal
        title = Paragraph(f"Rapport - {report_data['metadata']['template_name']}", title_style)
        story.append(title)
        story.append(Spacer(1, 24))
        
        # Métadonnées dans un tableau stylé
        meta_data = [
            ['Information', 'Valeur'],
            ['Généré le', report_data['metadata']['generated_at'][:19]],
            ['Template', report_data['metadata']['template_name']],
        ]
        
        # Ajouter les paramètres s'ils existent
        if report_data['metadata'].get('parameters'):
            params_str = ', '.join([f"{k}: {v}" for k, v in report_data['metadata']['parameters'].items()])
            meta_data.append(['Paramètres', params_str[:100]])  # Limiter la longueur
        
        meta_table = Table(meta_data, colWidths=[2*inch, 4*inch])
        meta_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#667eea')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.HexColor('#f8f9ff')),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'TOP')
        ]))
        
        story.append(meta_table)
        story.append(Spacer(1, 24))
        
        # Données du rapport
        if report_data['data']:
            data_title = Paragraph("Données du rapport", styles['Heading2'])
            story.append(data_title)
            story.append(Spacer(1, 12))
            
            # Créer un tableau avec les données
            if isinstance(report_data['data'], list) and report_data['data']:
                # Déterminer les colonnes
                if isinstance(report_data['data'][0], dict):
                    headers = list(report_data['data'][0].keys())
                    table_data = [headers]
                    
                    # Limiter à 50 lignes pour éviter les PDF trop volumineux
                    for item in report_data['data'][:50]:
                        row = []
                        for header in headers:
                            value = item.get(header, '')
                            # Formater les valeurs numériques
                            if isinstance(value, (int, float)):
                                if any(keyword in header.lower() for keyword in ['price', 'amount', 'revenue', 'cost', 'value']):
                                    value = f"€{value:,.2f}"
                                else:
                                    value = f"{value:,}"
                            row.append(str(value)[:50])  # Limiter la longueur
                        table_data.append(row)
                    
                    # Calculer les largeurs de colonnes
                    col_widths = [A4[0] / len(headers) * 0.8] * len(headers)
                    
                    data_table = Table(table_data, colWidths=col_widths)
                    data_table.setStyle(TableStyle([
                        ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#667eea')),
                        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                        ('FONTSIZE', (0, 0), (-1, 0), 10),
                        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                        ('BACKGROUND', (0, 1), (-1, -1), colors.HexColor('#f8f9ff')),
                        ('GRID', (0, 0), (-1, -1), 1, colors.black),
                        ('FONTSIZE', (0, 1), (-1, -1), 8),
                        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE')
                    ]))
                    
                    # Couleurs alternées pour les lignes
                    for i in range(1, len(table_data)):
                        if i % 2 == 0:
                            data_table.setStyle(TableStyle([
                                ('BACKGROUND', (0, i), (-1, i), colors.HexColor('#f0f0f0'))
                            ]))
                    
                    story.append(data_table)
        
        # Graphiques (si présents)
        if report_data.get('charts'):
            story.append(Spacer(1, 24))
            charts_title = Paragraph("Graphiques et Visualisations", styles['Heading2'])
            story.append(charts_title)
            story.append(Spacer(1, 12))
            
            for chart in report_data['charts']:
                chart_title = Paragraph(f"Graphique: {chart.get('title', 'Sans titre')}", styles['Heading3'])
                story.append(chart_title)
                
                # Afficher les informations du graphique
                chart_info = [
                    f"Type: {chart.get('type', 'N/A')}",
                    f"Nombre de points: {len(chart.get('data', {}).get('labels', []))}"
                ]
                
                for info in chart_info:
                    info_para = Paragraph(info, styles['Normal'])
                    story.append(info_para)
                
                story.append(Spacer(1, 12))
        
        # Pied de page avec informations
        story.append(Spacer(1, 24))
        footer_text = f"Rapport généré automatiquement le {datetime.now().strftime('%d/%m/%Y à %H:%M')}"
        footer_para = Paragraph(footer_text, styles['Normal'])
        footer_para.alignment = 1  # Centré
        story.append(footer_para)
        
        doc.build(story)
        buffer.seek(0)
        return buffer
    
    def export_to_excel(self, report_data: Dict[str, Any], filename: str = None) -> io.BytesIO:
        """Exporter un rapport en Excel avec fonctionnalités avancées"""
        if pd is None:
            raise ImportError("Pandas n'est pas installé. Installez avec: pip install pandas openpyxl")
        
        buffer = io.BytesIO()
        
        try:
            with pd.ExcelWriter(buffer, engine='openpyxl') as writer:
                # Feuille principale avec les données
                if report_data['data'] and isinstance(report_data['data'], list):
                    df = pd.DataFrame(report_data['data'])
                    
                    # Formatage des colonnes monétaires
                    for col in df.columns:
                        if any(keyword in col.lower() for keyword in ['price', 'amount', 'revenue', 'cost', 'value']):
                            if df[col].dtype in ['float64', 'int64']:
                                df[col] = df[col].apply(lambda x: f"€{x:,.2f}" if pd.notna(x) else '')
                    
                    # Écrire les données avec formatage
                    df.to_excel(writer, sheet_name='Données', index=False)
                    
                    # Formater la feuille
                    worksheet = writer.sheets['Données']
                    
                    # Ajuster la largeur des colonnes
                    for column in worksheet.columns:
                        max_length = 0
                        column_letter = column[0].column_letter
                        for cell in column:
                            try:
                                if len(str(cell.value)) > max_length:
                                    max_length = len(str(cell.value))
                            except:
                                pass
                        adjusted_width = min(max_length + 2, 50)
                        worksheet.column_dimensions[column_letter].width = adjusted_width
                    
                    # Styler l'en-tête
                    from openpyxl.styles import PatternFill, Font
                    header_fill = PatternFill(start_color='667eea', end_color='667eea', fill_type='solid')
                    header_font = Font(color='FFFFFF', bold=True)
                    
                    for cell in worksheet[1]:
                        cell.fill = header_fill
                        cell.font = header_font
                
                # Feuille métadonnées
                meta_df = pd.DataFrame([
                    {'Clé': 'Template', 'Valeur': report_data['metadata']['template_name']},
                    {'Clé': 'Généré le', 'Valeur': report_data['metadata']['generated_at']},
                    {'Clé': 'Paramètres', 'Valeur': str(report_data['metadata'].get('parameters', {}))}
                ])
                meta_df.to_excel(writer, sheet_name='Métadonnées', index=False)
                
                # Feuilles pour les graphiques (données uniquement)
                for i, chart in enumerate(report_data.get('charts', [])):
                    if chart.get('data'):
                        chart_data = chart['data']
                        if 'labels' in chart_data and 'datasets' in chart_data:
                            chart_df = pd.DataFrame({
                                'Labels': chart_data['labels'],
                                'Valeurs': chart_data['datasets'][0]['data'] if chart_data['datasets'] else []
                            })
                            sheet_name = f"Graphique_{i+1}"[:31]  # Limite Excel
                            chart_df.to_excel(writer, sheet_name=sheet_name, index=False)
                
                # Feuille résumé/statistiques
                if report_data['data']:
                    summary_data = self._generate_summary_stats(report_data['data'])
                    if summary_data:
                        summary_df = pd.DataFrame(summary_data)
                        summary_df.to_excel(writer, sheet_name='Résumé', index=False)
        
        except ImportError as e:
            if 'openpyxl' in str(e):
                raise ImportError("openpyxl n'est pas installé. Installez avec: pip install openpyxl")
            raise
        
        buffer.seek(0)
        return buffer
    
    def export_to_csv(self, report_data: Dict[str, Any], filename: str = None) -> io.StringIO:
        """Exporter un rapport en CSV"""
        buffer = io.StringIO()
        
        if report_data['data']:
            df = pd.DataFrame(report_data['data'])
            df.to_csv(buffer, index=False, encoding='utf-8')
        
        buffer.seek(0)
        return buffer
    
    def export_to_json(self, report_data: Dict[str, Any], filename: str = None) -> str:
        """Exporter un rapport en JSON"""
        return json.dumps(report_data, default=str, indent=2, ensure_ascii=False)


class ReportScheduler:
    """Planificateur de rapports automatiques"""
    
    def __init__(self):
        self.generator = ReportGenerator()
        self.exporter = ReportExporter()
    
    def execute_scheduled_reports(self):
        """Exécuter les rapports planifiés"""
        from .models import ReportSchedule
        
        today = date.today()
        
        # Récupérer les planifications à exécuter
        schedules = ReportSchedule.query.filter(
            ReportSchedule.is_active == True,
            ReportSchedule.next_run <= today
        ).all()
        
        for schedule in schedules:
            try:
                self._execute_schedule(schedule)
                schedule._calculate_next_run()  # Calculer la prochaine exécution
                db.session.commit()
            except Exception as e:
                print(f"Erreur lors de l'exécution de la planification {schedule.id}: {str(e)}")
                db.session.rollback()
    
    def _execute_schedule(self, schedule):
        """Exécuter une planification spécifique"""
        from .models import ReportExecution, ReportStatus
        
        # Créer une nouvelle exécution
        execution = ReportExecution(
            template_id=schedule.template_id,
            executed_by_id=None,  # Exécution automatique
            status=ReportStatus.RUNNING,
            parameters=schedule.parameters or {}
        )
        
        db.session.add(execution)
        db.session.flush()
        
        try:
            # Générer le rapport
            report_data = self.generator.generate_report(schedule.template, schedule.parameters)
            
            # Sauvegarder les données
            for item in report_data['data']:
                if isinstance(item, dict):
                    for key, value in item.items():
                        report_data_obj = ReportData(
                            execution_id=execution.id,
                            data_key=key,
                            data_value=str(value),
                            data_type=type(value).__name__
                        )
                        db.session.add(report_data_obj)
            
            execution.status = ReportStatus.COMPLETED
            execution.completed_at = datetime.utcnow()
            
            # Envoyer par email si configuré
            if schedule.email_recipients:
                self._send_report_email(schedule, execution, report_data)
        
        except Exception as e:
            execution.status = ReportStatus.FAILED
            execution.error_message = str(e)
            execution.completed_at = datetime.utcnow()
            raise
    
    def _send_report_email(self, schedule, execution, report_data):
        """Envoyer le rapport par email (placeholder)"""
        # Ici on pourrait intégrer un service d'email
        # Pour l'instant, on log juste l'action
        print(f"Envoi du rapport {schedule.name} aux destinataires: {schedule.email_recipients}")


class ReportAnalytics:
    """Analytiques pour les rapports"""
    
    def get_report_usage_stats(self) -> Dict[str, Any]:
        """Obtenir les statistiques d'utilisation des rapports"""
        from .models import ReportTemplate, ReportExecution
        
        # Templates les plus utilisés
        popular_templates = db.session.query(
            ReportTemplate.name,
            func.count(ReportExecution.id).label('usage_count')
        ).outerjoin(ReportExecution).group_by(ReportTemplate.id).order_by(
            desc('usage_count')
        ).limit(10).all()
        
        # Exécutions par statut
        status_stats = db.session.query(
            ReportExecution.status,
            func.count(ReportExecution.id).label('count')
        ).group_by(ReportExecution.status).all()
        
        # Tendance d'utilisation
        usage_trend = db.session.query(
            func.date(ReportExecution.executed_at).label('date'),
            func.count(ReportExecution.id).label('count')
        ).filter(
            ReportExecution.executed_at >= datetime.now() - timedelta(days=30)
        ).group_by(func.date(ReportExecution.executed_at)).all()
        
        return {
            'popular_templates': [{'name': t.name, 'usage_count': t.usage_count} for t in popular_templates],
            'status_distribution': [{'status': s.status.value, 'count': s.count} for s in status_stats],
            'usage_trend': [{'date': str(u.date), 'count': u.count} for u in usage_trend]
        }
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Obtenir les métriques de performance"""
        from .models import ReportExecution, ReportStatus
        
        # Temps d'exécution moyen
        avg_execution_time = db.session.query(
            func.avg(
                func.extract('epoch', ReportExecution.completed_at - ReportExecution.executed_at)
            ).label('avg_time')
        ).filter(
            ReportExecution.status == ReportStatus.COMPLETED,
            ReportExecution.completed_at.isnot(None)
        ).scalar()
        
        # Taux de succès
        total_executions = ReportExecution.query.count()
        successful_executions = ReportExecution.query.filter_by(status=ReportStatus.COMPLETED).count()
        success_rate = (successful_executions / total_executions * 100) if total_executions > 0 else 0
        
        return {
            'average_execution_time': avg_execution_time or 0,
            'success_rate': round(success_rate, 2),
            'total_executions': total_executions,
            'successful_executions': successful_executions
        }
    
    def _generate_summary_stats(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Générer des statistiques de résumé pour les données"""
        if not data or not isinstance(data, list):
            return []
        
        summary = []
        
        try:
            # Statistiques générales
            summary.append({'Statistique': 'Nombre total d\'enregistrements', 'Valeur': len(data)})
            
            # Analyser chaque colonne numérique
            if isinstance(data[0], dict):
                numeric_columns = []
                for key, value in data[0].items():
                    if isinstance(value, (int, float)):
                        numeric_columns.append(key)
                
                for col in numeric_columns:
                    values = [item.get(col, 0) for item in data if isinstance(item.get(col), (int, float))]
                    if values:
                        summary.extend([
                            {'Statistique': f'{col} - Somme', 'Valeur': sum(values)},
                            {'Statistique': f'{col} - Moyenne', 'Valeur': round(sum(values) / len(values), 2)},
                            {'Statistique': f'{col} - Maximum', 'Valeur': max(values)},
                            {'Statistique': f'{col} - Minimum', 'Valeur': min(values)}
                        ])
        
        except Exception as e:
            summary.append({'Statistique': 'Erreur de calcul', 'Valeur': str(e)})
        
        return summary


class AdvancedReportAnalytics:
    """Analytics avancées pour les rapports"""
    
    def __init__(self):
        self.cache = {}
        self.cache_ttl = 300  # 5 minutes
    
    def get_trend_analysis(self, data: List[Dict[str, Any]], date_field: str, value_field: str) -> Dict[str, Any]:
        """Analyser les tendances dans les données"""
        if not data or len(data) < 2:
            return {'trend': 'insufficient_data', 'slope': 0, 'r_squared': 0}
        
        try:
            # Préparer les données pour l'analyse
            dates = []
            values = []
            
            for item in data:
                if date_field in item and value_field in item:
                    date_str = item[date_field]
                    if isinstance(date_str, str):
                        try:
                            date_obj = datetime.strptime(date_str, '%Y-%m-%d')
                            dates.append(date_obj.timestamp())
                            values.append(float(item[value_field]))
                        except (ValueError, TypeError):
                            continue
            
            if len(dates) < 2:
                return {'trend': 'insufficient_data', 'slope': 0, 'r_squared': 0}
            
            # Calcul de la régression linéaire simple
            n = len(dates)
            sum_x = sum(dates)
            sum_y = sum(values)
            sum_xy = sum(x * y for x, y in zip(dates, values))
            sum_x2 = sum(x * x for x in dates)
            
            # Pente (slope)
            slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x * sum_x)
            
            # Intercept
            intercept = (sum_y - slope * sum_x) / n
            
            # R-squared
            y_mean = sum_y / n
            ss_tot = sum((y - y_mean) ** 2 for y in values)
            ss_res = sum((y - (slope * x + intercept)) ** 2 for x, y in zip(dates, values))
            r_squared = 1 - (ss_res / ss_tot) if ss_tot != 0 else 0
            
            # Déterminer la tendance
            if slope > 0.01:
                trend = 'increasing'
            elif slope < -0.01:
                trend = 'decreasing'
            else:
                trend = 'stable'
            
            return {
                'trend': trend,
                'slope': round(slope, 6),
                'r_squared': round(r_squared, 4),
                'confidence': 'high' if r_squared > 0.7 else 'medium' if r_squared > 0.4 else 'low'
            }
        
        except Exception as e:
            return {'trend': 'error', 'slope': 0, 'r_squared': 0, 'error': str(e)}
    
    def get_correlation_matrix(self, data: List[Dict[str, Any]], numeric_fields: List[str]) -> Dict[str, Any]:
        """Calculer la matrice de corrélation entre les champs numériques"""
        if len(numeric_fields) < 2 or len(data) < 3:
            return {'error': 'Insufficient data for correlation analysis'}
        
        try:
            # Extraire les valeurs pour chaque champ
            field_values = {field: [] for field in numeric_fields}
            
            for item in data:
                valid_item = True
                for field in numeric_fields:
                    if field not in item or not isinstance(item[field], (int, float)):
                        valid_item = False
                        break
                
                if valid_item:
                    for field in numeric_fields:
                        field_values[field].append(float(item[field]))
            
            # Vérifier que nous avons suffisamment de données
            min_length = min(len(values) for values in field_values.values())
            if min_length < 3:
                return {'error': 'Insufficient valid data points'}
            
            # Calculer les corrélations
            correlations = {}
            for i, field1 in enumerate(numeric_fields):
                for j, field2 in enumerate(numeric_fields):
                    if i <= j:
                        if field1 == field2:
                            correlation = 1.0
                        else:
                            correlation = self._calculate_correlation(
                                field_values[field1][:min_length],
                                field_values[field2][:min_length]
                            )
                        correlations[f'{field1}_{field2}'] = round(correlation, 4)
            
            return {'correlations': correlations, 'fields': numeric_fields}
        
        except Exception as e:
            return {'error': str(e)}
    
    def _calculate_correlation(self, x_values: List[float], y_values: List[float]) -> float:
        """Calculer le coefficient de corrélation de Pearson"""
        n = len(x_values)
        if n == 0:
            return 0
        
        sum_x = sum(x_values)
        sum_y = sum(y_values)
        sum_xy = sum(x * y for x, y in zip(x_values, y_values))
        sum_x2 = sum(x * x for x in x_values)
        sum_y2 = sum(y * y for y in y_values)
        
        numerator = n * sum_xy - sum_x * sum_y
        denominator = ((n * sum_x2 - sum_x * sum_x) * (n * sum_y2 - sum_y * sum_y)) ** 0.5
        
        return numerator / denominator if denominator != 0 else 0
    
    def detect_anomalies(self, data: List[Dict[str, Any]], field: str, threshold: float = 2.0) -> List[Dict[str, Any]]:
        """Détecter les anomalies dans un champ numérique using Z-score"""
        values = []
        valid_items = []
        
        for item in data:
            if field in item and isinstance(item[field], (int, float)):
                values.append(float(item[field]))
                valid_items.append(item)
        
        if len(values) < 3:
            return []
        
        # Calculer moyenne et écart-type
        mean_val = sum(values) / len(values)
        variance = sum((x - mean_val) ** 2 for x in values) / len(values)
        std_dev = variance ** 0.5
        
        if std_dev == 0:
            return []  # Pas de variation
        
        # Identifier les anomalies
        anomalies = []
        for item, value in zip(valid_items, values):
            z_score = abs(value - mean_val) / std_dev
            if z_score > threshold:
                anomaly_info = item.copy()
                anomaly_info['z_score'] = round(z_score, 3)
                anomaly_info['anomaly_field'] = field
                anomaly_info['anomaly_value'] = value
                anomalies.append(anomaly_info)
        
        return anomalies
    
    def generate_insights(self, data: List[Dict[str, Any]], report_type: str) -> List[str]:
        """Générer des insights automatisés sur les données"""
        insights = []
        
        if not data:
            return ['Aucune donnée disponible pour l\'analyse']
        
        try:
            if report_type == 'sales':
                insights.extend(self._generate_sales_insights(data))
            elif report_type == 'inventory':
                insights.extend(self._generate_inventory_insights(data))
            elif report_type == 'financial':
                insights.extend(self._generate_financial_insights(data))
            else:
                insights.extend(self._generate_general_insights(data))
        
        except Exception as e:
            insights.append(f'Erreur lors de la génération d\'insights: {str(e)}')
        
        return insights[:10]  # Limiter à 10 insights
    
    def _generate_sales_insights(self, data: List[Dict[str, Any]]) -> List[str]:
        """Générer des insights spécifiques aux ventes"""
        insights = []
        
        # Analyser les tendances de prix
        if 'total_amount' in data[0] and len(data) > 1:
            amounts = [item.get('total_amount', 0) for item in data if 'total_amount' in item]
            if amounts:
                avg_amount = sum(amounts) / len(amounts)
                max_amount = max(amounts)
                min_amount = min(amounts)
                
                insights.append(f'Panier moyen: €{avg_amount:.2f}')
                insights.append(f'Vente maximale: €{max_amount:.2f}')
                
                if max_amount > avg_amount * 3:
                    insights.append('Détection de ventes exceptionnellement élevées')
        
        return insights
    
    def _generate_inventory_insights(self, data: List[Dict[str, Any]]) -> List[str]:
        """Générer des insights spécifiques à l'inventaire"""
        insights = []
        
        # Analyser les niveaux de stock
        low_stock_items = [item for item in data if item.get('status') == 'low_stock']
        if low_stock_items:
            insights.append(f'{len(low_stock_items)} produits en rupture de stock')
        
        # Analyser la valeur du stock
        if 'stock_value' in data[0]:
            total_value = sum(item.get('stock_value', 0) for item in data)
            insights.append(f'Valeur totale du stock: €{total_value:,.2f}')
        
        return insights
    
    def _generate_financial_insights(self, data: List[Dict[str, Any]]) -> List[str]:
        """Générer des insights financiers"""
        insights = []
        
        # Analyser les revenus et coûts
        revenues = [item.get('amount', 0) for item in data if item.get('type') == 'revenue']
        costs = [item.get('amount', 0) for item in data if item.get('type') == 'cost']
        
        if revenues and costs:
            total_revenue = sum(revenues)
            total_costs = sum(costs)
            profit = total_revenue - total_costs
            margin = (profit / total_revenue * 100) if total_revenue > 0 else 0
            
            insights.append(f'Marge bénéficiaire: {margin:.1f}%')
            
            if margin < 10:
                insights.append('Marge bénéficiaire faible - optimisation recommandée')
            elif margin > 30:
                insights.append('Excellente marge bénéficiaire')
        
        return insights
    
    def _generate_general_insights(self, data: List[Dict[str, Any]]) -> List[str]:
        """Générer des insights généraux"""
        insights = []
        
        insights.append(f'Nombre total d\'enregistrements: {len(data)}')
        
        # Analyser la répartition temporelle
        if 'date' in data[0]:
            dates = [item.get('date') for item in data if 'date' in item]
            unique_dates = len(set(dates))
            insights.append(f'Période couverte: {unique_dates} jours')
        
        return insights