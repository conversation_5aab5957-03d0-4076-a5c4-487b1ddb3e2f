"""Configuration de production pour l'application POS System"""
import os
from .default import Config as DefaultConfig

class ProductionConfig(DefaultConfig):
    """Configuration de production"""
    
    # Mode debug désactivé
    DEBUG = False
    TESTING = False
    
    # Configuration de la base de données
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or \
        '************************************************/pos_system'
    
    # Configuration du secret key
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-production-secret-key-here'
    
    # Configuration de Redis
    REDIS_URL = os.environ.get('REDIS_URL') or 'redis://cache:6379/0'
    
    # Configuration du cache
    CACHE_TYPE = 'redis'
    CACHE_REDIS_URL = REDIS_URL
    CACHE_DEFAULT_TIMEOUT = 300
    
    # Configuration de l'authentification JWT
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY') or 'your-jwt-secret-key-here'
    JWT_ACCESS_TOKEN_EXPIRES = 3600  # 1 heure
    JWT_REFRESH_TOKEN_EXPIRES = 86400  # 24 heures
    
    # Configuration du logging
    LOG_LEVEL = 'INFO'
    LOG_FILE = '/var/log/pos-system/app.log'
    
    # Configuration de la sécurité
    WTF_CSRF_ENABLED = True
    SESSION_COOKIE_SECURE = True
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    
    # Configuration du rate limiting
    RATELIMIT_STORAGE_URL = REDIS_URL
    RATELIMIT_STRATEGY = 'fixed-window'
    DEFAULT_RATE_LIMIT = '1000/hour'
    
    # Configuration des performances
    SQLALCHEMY_POOL_SIZE = 20
    SQLALCHEMY_MAX_OVERFLOW = 30
    SQLALCHEMY_POOL_TIMEOUT = 30
    SQLALCHEMY_POOL_RECYCLE = 3600
    
    # Configuration du cache pour les templates
    TEMPLATE_CACHE_TIMEOUT = 300
    
    # Configuration des fichiers statiques
    SEND_FILE_MAX_AGE_DEFAULT = 31536000  # 1 an
    
    # Configuration de l'API
    API_RATE_LIMIT = '1000/hour'
    
    # Configuration des emails
    MAIL_SERVER = os.environ.get('MAIL_SERVER') or 'smtp.gmail.com'
    MAIL_PORT = int(os.environ.get('MAIL_PORT') or 587)
    MAIL_USE_TLS = os.environ.get('MAIL_USE_TLS', 'true').lower() in ['true', 'on', '1']
    MAIL_USERNAME = os.environ.get('MAIL_USERNAME')
    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD')
    
    # Configuration du monitoring
    ENABLE_MONITORING = True
    MONITORING_ENDPOINT = '/metrics'
    
    # Configuration des sauvegardes
    ENABLE_BACKUP = True
    BACKUP_SCHEDULE = '0 2 * * *'  # Tous les jours à 2h00
    
    @classmethod
    def init_app(cls, app):
        """Initialisation spécifique à la production"""
        DefaultConfig.init_app(app)
        
        # Configuration du logging pour la production
        import logging
        from logging.handlers import RotatingFileHandler
        
        if not app.debug and not app.testing:
            # Créer le répertoire de logs si nécessaire
            os.makedirs(os.path.dirname(cls.LOG_FILE), exist_ok=True)
            
            # Configurer le handler de fichiers rotatifs
            file_handler = RotatingFileHandler(
                cls.LOG_FILE, 
                maxBytes=10240000,  # 10MB
                backupCount=10
            )
            file_handler.setFormatter(logging.Formatter(
                '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
            ))
            file_handler.setLevel(logging.INFO)
            app.logger.addHandler(file_handler)
            
            app.logger.setLevel(logging.INFO)
            app.logger.info('POS System startup')