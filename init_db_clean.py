import os
import sys

# Ajouter le répertoire racine au chemin Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from sqlalchemy import MetaData

def init_database_clean():
    """Initialise la base de données sans les données de test"""
    print("Initialisation de la base de données...")
    
    # Créer l'application sans les données de test
    app = create_app(seed_data=False)
    
    with app.app_context():
        # Supprimer toutes les tables existantes
        print("Suppression des tables existantes...")
        # Utiliser une approche plus agressive pour supprimer toutes les contraintes
        try:
            # Obtenir toutes les tables
            meta = MetaData()
            meta.reflect(bind=db.engine)
            
            # Supprimer toutes les tables
            meta.drop_all(bind=db.engine)
            print("Toutes les tables ont été supprimées avec succès")
        except Exception as e:
            print(f"Erreur lors de la suppression des tables: {e}")
            # Continuer malgré l'erreur
        
        # Importer tous les modèles
        print("Importation des modèles...")
        from app.modules.accounts import models as accounts_models
        from app.modules.catalog import models as catalog_models
        from app.modules.sales import models as sales_models
        from app.modules.inventory import models as inventory_models
        from app.modules.ingredients import models as ingredients_models
        from app.modules.variants import models as variants_models
        from app.modules.expenses import models as expenses_models
        from app.modules.menu_planning import models as menu_planning_models
        from app.modules.loyalty import models as loyalty_models
        from app.modules.pharmacy import models as pharmacy_models
        from app.modules.reports import models as reports_models
        from app.modules.tables import models as tables_models
        from app.modules.kds import models as kds_models
        from app.modules.payments import models as payments_models
        from app.modules.customers import models as customers_models
        from app.modules.staff import models as staff_models
        from app.modules.cash import models as cash_models
        from app.modules.suppliers import models as suppliers_models
        from app.modules.purchasing import models as purchasing_models
        from app.modules.settings import models as settings_models
        from app.modules.audit import models as audit_models
        from app.modules.integrations import models as integrations_models
        from app.modules.notifications import models as notifications_models
        from app.modules.ai import models as ai_models
        from app.modules.delivery import models as delivery_models
        from app.modules.support import models as support_models
        
        # Créer toutes les tables
        print("Création des tables...")
        try:
            db.create_all()
            print("Toutes les tables ont été créées avec succès")
        except Exception as e:
            print(f"Erreur lors de la création des tables: {e}")
            return False
            
        # Initialiser les types d'entreprise par défaut
        print("Initialisation des types d'entreprise...")
        try:
            from app.modules.settings.utils import BusinessTypeConfigurator
            BusinessTypeConfigurator.create_default_business_types()
            print("Types d'entreprise initialisés avec succès")
        except Exception as e:
            print(f"Erreur lors de l'initialisation des types d'entreprise: {e}")
            
    return True

if __name__ == "__main__":
    success = init_database_clean()
    if success:
        print("Initialisation de la base de données terminée avec succès")
    else:
        print("Échec de l'initialisation de la base de données")
        sys.exit(1)