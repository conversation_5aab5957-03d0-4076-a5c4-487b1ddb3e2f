{"dashboard": {"id": null, "title": "POS System Dashboard", "tags": ["pos", "flask", "postgresql", "redis"], "timezone": "browser", "schemaVersion": 16, "version": 0, "refresh": "30s", "panels": [{"id": 1, "type": "graph", "title": "HTTP Requests Rate", "gridPos": {"x": 0, "y": 0, "w": 12, "h": 6}, "targets": [{"expr": "rate(http_requests_total[5m])", "legendFormat": "{{method}} {{status}}", "refId": "A"}], "yaxes": [{"format": "reqps", "label": "Requests/sec"}, {"format": "short"}]}, {"id": 2, "type": "graph", "title": "HTTP Request Duration", "gridPos": {"x": 12, "y": 0, "w": 12, "h": 6}, "targets": [{"expr": "rate(http_request_duration_seconds_sum[5m]) / rate(http_request_duration_seconds_count[5m])", "legendFormat": "Average", "refId": "A"}], "yaxes": [{"format": "s", "label": "Seconds"}, {"format": "short"}]}, {"id": 3, "type": "singlestat", "title": "Current Requests", "gridPos": {"x": 0, "y": 6, "w": 6, "h": 3}, "targets": [{"expr": "http_requests_total", "refId": "A"}], "format": "none"}, {"id": 4, "type": "singlestat", "title": "Error Rate", "gridPos": {"x": 6, "y": 6, "w": 6, "h": 3}, "targets": [{"expr": "rate(http_requests_total{status=~\"5..\"}[5m]) / rate(http_requests_total[5m]) * 100", "refId": "A"}], "format": "percent", "thresholds": "5,10", "colorBackground": true, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"]}, {"id": 5, "type": "graph", "title": "Database Connections", "gridPos": {"x": 12, "y": 6, "w": 12, "h": 6}, "targets": [{"expr": "pg_stat_database_numbackends", "legendFormat": "{{datname}}", "refId": "A"}], "yaxes": [{"format": "none", "label": "Connections"}, {"format": "short"}]}, {"id": 6, "type": "graph", "title": "CPU Usage", "gridPos": {"x": 0, "y": 9, "w": 12, "h": 6}, "targets": [{"expr": "rate(process_cpu_seconds_total[5m])", "legendFormat": "CPU", "refId": "A"}], "yaxes": [{"format": "percentunit", "label": "CPU %"}, {"format": "short"}]}, {"id": 7, "type": "graph", "title": "Memory Usage", "gridPos": {"x": 12, "y": 12, "w": 12, "h": 6}, "targets": [{"expr": "process_resident_memory_bytes / 1024 / 1024", "legendFormat": "Memory (MB)", "refId": "A"}], "yaxes": [{"format": "decmbytes", "label": "MB"}, {"format": "short"}]}, {"id": 8, "type": "graph", "title": "<PERSON><PERSON>", "gridPos": {"x": 0, "y": 15, "w": 12, "h": 6}, "targets": [{"expr": "redis_keyspace_hits_total / (redis_keyspace_hits_total + redis_keyspace_misses_total) * 100", "legendFormat": "Hit Ratio %", "refId": "A"}], "yaxes": [{"format": "percent", "label": "Percentage"}, {"format": "short"}]}]}}