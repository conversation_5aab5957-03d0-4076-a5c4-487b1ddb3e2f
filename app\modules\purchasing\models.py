from __future__ import annotations

from datetime import datetime, date
from decimal import Decimal
from typing import Optional, List
from enum import Enum as PyEnum
from sqlalchemy import Column, Integer, String, Text, DateTime, Date, Numeric, ForeignKey, <PERSON>olean, Enum
from sqlalchemy.orm import relationship

from app.extensions import db


class PurchaseOrderStatus(PyEnum):
    """Statut des bons de commande"""
    DRAFT = "draft"  # Brouillon
    PENDING = "pending"  # En attente
    APPROVED = "approved"  # Approuvé
    SENT = "sent"  # Envoyé
    CONFIRMED = "confirmed"  # Confirmé par le fournisseur
    PARTIAL = "partial"  # Partiellement reçu
    RECEIVED = "received"  # Complètement reçu
    CANCELLED = "cancelled"  # Annulé
    CLOSED = "closed"  # Fermé


class ReceiptStatus(PyEnum):
    """Statut des réceptions"""
    PENDING = "pending"  # En attente
    PARTIAL = "partial"  # Partielle
    COMPLETE = "complete"  # Complète
    CANCELLED = "cancelled"  # Annulée


class PurchaseOrder(db.Model):
    """Bon de commande d'achat"""
    __tablename__ = "purchase_orders"
    id = Column(Integer, primary_key=True)
    business_id_fk = Column(Integer, ForeignKey("businesses.id"), nullable=False, index=True)
    supplier_id_fk = Column(Integer, ForeignKey("suppliers.id"), nullable=False, index=True)
    
    # Informations de base
    order_number = Column(String(50), nullable=False, unique=True, index=True)
    reference = Column(String(100), nullable=True)  # Référence interne
    supplier_reference = Column(String(100), nullable=True)  # Référence fournisseur
    
    # Statut et dates
    status = Column(Enum(PurchaseOrderStatus), default=PurchaseOrderStatus.DRAFT, nullable=False, index=True)
    order_date = Column(Date, default=date.today, nullable=False)
    expected_delivery_date = Column(Date, nullable=True)
    delivery_date = Column(Date, nullable=True)
    
    # Montants en centimes
    subtotal_cents = Column(Integer, default=0, nullable=False)
    discount_cents = Column(Integer, default=0, nullable=False)
    tax_cents = Column(Integer, default=0, nullable=False)
    shipping_cents = Column(Integer, default=0, nullable=False)
    total_cents = Column(Integer, default=0, nullable=False)
    
    # Informations de livraison
    delivery_address = Column(Text, nullable=True)
    delivery_instructions = Column(Text, nullable=True)
    
    # Notes et commentaires
    notes = Column(Text, nullable=True)
    internal_notes = Column(Text, nullable=True)
    
    # Conditions commerciales
    payment_terms = Column(String(100), nullable=True)
    currency = Column(String(3), default='EUR', nullable=False)
    
    # Statut de réception
    is_fully_received = Column(Boolean, default=False, nullable=False)
    received_percentage = Column(Numeric(5, 2), default=0.0, nullable=False)
    
    # Dates système
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    approved_at = Column(DateTime, nullable=True)
    sent_at = Column(DateTime, nullable=True)
    
    # Relations
    items = relationship("PurchaseOrderItem", back_populates="purchase_order", lazy="dynamic", cascade="all, delete-orphan")
    receipts = relationship("PurchaseReceipt", back_populates="purchase_order", lazy="dynamic", cascade="all, delete-orphan")
    
    @property
    def subtotal(self):
        return self.subtotal_cents / 100 if self.subtotal_cents else 0
    
    @property
    def discount(self):
        return self.discount_cents / 100 if self.discount_cents else 0
    
    @property
    def tax(self):
        return self.tax_cents / 100 if self.tax_cents else 0
    
    @property
    def shipping(self):
        return self.shipping_cents / 100 if self.shipping_cents else 0
    
    @property
    def total(self):
        return self.total_cents / 100 if self.total_cents else 0
    
    def calculate_totals(self):
        """Calcule les totaux du bon de commande"""
        self.subtotal_cents = sum(item.total_cents for item in self.items.all())
        self.total_cents = self.subtotal_cents - self.discount_cents + self.tax_cents + self.shipping_cents
        return self.total_cents
    
    def get_items_count(self):
        """Retourne le nombre d'articles"""
        return self.items.count()
    
    def get_total_quantity(self):
        """Retourne la quantité totale commandée"""
        return sum(item.quantity for item in self.items.all())
    
    def update_received_percentage(self):
        """Met à jour le pourcentage de réception"""
        items = self.items.all()
        if not items:
            self.received_percentage = 0.0
            return
        
        total_ordered = sum(item.quantity for item in items)
        total_received = sum(item.quantity_received for item in items)
        
        if total_ordered > 0:
            self.received_percentage = (total_received / total_ordered) * 100
            self.is_fully_received = self.received_percentage >= 100.0
        else:
            self.received_percentage = 0.0
            self.is_fully_received = False
    
    __table_args__ = (
        db.Index("idx_po_business", "business_id_fk"),
        db.Index("idx_po_supplier", "supplier_id_fk"),
        db.Index("idx_po_number", "order_number"),
        db.Index("idx_po_status", "status"),
        db.Index("idx_po_dates", "order_date", "expected_delivery_date"),
    )


class PurchaseOrderItem(db.Model):
    """Article d'un bon de commande"""
    __tablename__ = "purchase_order_items"
    id = Column(Integer, primary_key=True)
    purchase_order_id_fk = Column(Integer, ForeignKey("purchase_orders.id"), nullable=False, index=True)
    
    # Référence produit (peut être externe ou interne)
    product_id_fk = Column(Integer, ForeignKey("products.id"), nullable=True, index=True)
    supplier_product_id_fk = Column(Integer, ForeignKey("supplier_products.id"), nullable=True, index=True)
    
    # Informations de l'article
    product_name = Column(String(255), nullable=False)  # Nom du produit
    supplier_reference = Column(String(100), nullable=True)  # Référence fournisseur
    description = Column(Text, nullable=True)
    
    # Quantités
    quantity = Column(Integer, nullable=False)
    quantity_received = Column(Integer, default=0, nullable=False)
    unit_of_measure = Column(String(20), default='unit', nullable=False)
    
    # Prix et coûts en centimes
    unit_cost_cents = Column(Integer, nullable=False)
    total_cents = Column(Integer, nullable=False)
    
    # Informations de réception
    expected_date = Column(Date, nullable=True)
    last_received_date = Column(Date, nullable=True)
    
    # Notes spécifiques à l'article
    notes = Column(Text, nullable=True)
    
    # Dates
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Relations
    purchase_order = relationship("PurchaseOrder", back_populates="items")
    receipt_items = relationship("PurchaseReceiptItem", back_populates="order_item", lazy="dynamic")
    
    @property
    def unit_cost(self):
        return self.unit_cost_cents / 100 if self.unit_cost_cents else 0
    
    @property
    def total(self):
        return self.total_cents / 100 if self.total_cents else 0
    
    @property
    def quantity_pending(self):
        """Quantité en attente de réception"""
        return self.quantity - self.quantity_received
    
    @property
    def is_fully_received(self):
        """Vérifie si l'article est complètement reçu"""
        return self.quantity_received >= self.quantity
    
    @property
    def received_percentage(self):
        """Pourcentage reçu"""
        if self.quantity > 0:
            return (self.quantity_received / self.quantity) * 100
        return 0
    
    def calculate_total(self):
        """Calcule le total de l'article"""
        self.total_cents = self.quantity * self.unit_cost_cents
        return self.total_cents
    
    __table_args__ = (
        db.Index("idx_po_item_order", "purchase_order_id_fk"),
        db.Index("idx_po_item_product", "product_id_fk"),
        db.Index("idx_po_item_supplier_product", "supplier_product_id_fk"),
    )


class PurchaseReceipt(db.Model):
    """Réception de marchandises"""
    __tablename__ = "purchase_receipts"
    id = Column(Integer, primary_key=True)
    business_id_fk = Column(Integer, ForeignKey("businesses.id"), nullable=False, index=True)
    purchase_order_id_fk = Column(Integer, ForeignKey("purchase_orders.id"), nullable=False, index=True)
    
    # Informations de réception
    receipt_number = Column(String(50), nullable=False, unique=True, index=True)
    delivery_note_number = Column(String(100), nullable=True)  # Numéro bon de livraison
    supplier_invoice_number = Column(String(100), nullable=True)  # Numéro facture fournisseur
    
    # Dates
    receipt_date = Column(Date, default=date.today, nullable=False)
    delivery_date = Column(Date, nullable=True)
    
    # Statut
    status = Column(Enum(ReceiptStatus), default=ReceiptStatus.PENDING, nullable=False, index=True)
    
    # Informations de livraison
    delivery_person = Column(String(255), nullable=True)
    delivery_company = Column(String(255), nullable=True)
    delivery_notes = Column(Text, nullable=True)
    
    # Contrôle qualité
    quality_check_done = Column(Boolean, default=False, nullable=False)
    quality_issues = Column(Text, nullable=True)
    quality_rating = Column(Integer, nullable=True)  # 1-5
    
    # Montants en centimes (si différents de la commande)
    total_received_cents = Column(Integer, default=0, nullable=False)
    
    # Notes
    notes = Column(Text, nullable=True)
    internal_notes = Column(Text, nullable=True)
    
    # Dates système
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    validated_at = Column(DateTime, nullable=True)
    validated_by = Column(String(255), nullable=True)
    
    # Relations
    purchase_order = relationship("PurchaseOrder", back_populates="receipts")
    items = relationship("PurchaseReceiptItem", back_populates="receipt", lazy="dynamic", cascade="all, delete-orphan")
    
    @property
    def total_received(self):
        return self.total_received_cents / 100 if self.total_received_cents else 0
    
    def calculate_total_received(self):
        """Calcule le total des articles reçus"""
        self.total_received_cents = sum(item.total_received_cents for item in self.items.all())
        return self.total_received_cents
    
    def get_items_count(self):
        """Retourne le nombre d'articles reçus"""
        return self.items.count()
    
    def get_total_quantity_received(self):
        """Retourne la quantité totale reçue"""
        return sum(item.quantity_received for item in self.items.all())
    
    __table_args__ = (
        db.Index("idx_purchase_receipt_business", "business_id_fk"),
        db.Index("idx_purchase_receipt_po", "purchase_order_id_fk"),
        db.Index("idx_purchase_receipt_number", "receipt_number"),
        db.Index("idx_purchase_receipt_date", "receipt_date"),
        db.Index("idx_purchase_receipt_status", "status"),
    )


class PurchaseReceiptItem(db.Model):
    """Article d'une réception"""
    __tablename__ = "purchase_receipt_items"
    id = Column(Integer, primary_key=True)
    receipt_id_fk = Column(Integer, ForeignKey("purchase_receipts.id"), nullable=False, index=True)
    order_item_id_fk = Column(Integer, ForeignKey("purchase_order_items.id"), nullable=False, index=True)
    
    # Quantités reçues
    quantity_received = Column(Integer, nullable=False)
    quantity_damaged = Column(Integer, default=0, nullable=False)
    quantity_accepted = Column(Integer, nullable=False)
    
    # Prix et coûts réels en centimes
    actual_unit_cost_cents = Column(Integer, nullable=True)  # Coût réel si différent
    total_received_cents = Column(Integer, nullable=False)
    
    # Contrôle qualité
    quality_check = Column(Boolean, default=False, nullable=False)
    quality_notes = Column(Text, nullable=True)
    expiry_date = Column(Date, nullable=True)
    batch_number = Column(String(100), nullable=True)
    
    # Notes
    notes = Column(Text, nullable=True)
    
    # Dates
    received_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    # Relations
    receipt = relationship("PurchaseReceipt", back_populates="items")
    order_item = relationship("PurchaseOrderItem", back_populates="receipt_items")
    
    @property
    def actual_unit_cost(self):
        if self.actual_unit_cost_cents:
            return self.actual_unit_cost_cents / 100
        return self.order_item.unit_cost if self.order_item else 0
    
    @property
    def total_received(self):
        return self.total_received_cents / 100 if self.total_received_cents else 0
    
    def calculate_total_received(self):
        """Calcule le total de l'article reçu"""
        unit_cost = self.actual_unit_cost_cents or self.order_item.unit_cost_cents
        self.total_received_cents = self.quantity_accepted * unit_cost
        return self.total_received_cents
    
    __table_args__ = (
        db.Index("idx_receipt_item_receipt", "receipt_id_fk"),
        db.Index("idx_receipt_item_order", "order_item_id_fk"),
    )


class PurchaseRequisition(db.Model):
    """Demande d'achat (requisition)"""
    __tablename__ = "purchase_requisitions"
    id = Column(Integer, primary_key=True)
    business_id_fk = Column(Integer, ForeignKey("businesses.id"), nullable=False, index=True)
    
    # Informations de base
    requisition_number = Column(String(50), nullable=False, unique=True, index=True)
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    
    # Demandeur
    requested_by = Column(String(255), nullable=False)
    department = Column(String(100), nullable=True)
    priority = Column(String(20), default='normal', nullable=False)  # low, normal, high, urgent
    
    # Dates
    request_date = Column(Date, default=date.today, nullable=False)
    needed_by_date = Column(Date, nullable=True)
    
    # Statut
    status = Column(String(20), default='pending', nullable=False, index=True)  # pending, approved, rejected, converted
    
    # Approbation
    approved_by = Column(String(255), nullable=True)
    approved_at = Column(DateTime, nullable=True)
    approval_notes = Column(Text, nullable=True)
    
    # Budget estimé en centimes
    estimated_total_cents = Column(Integer, default=0, nullable=False)
    budget_code = Column(String(50), nullable=True)
    
    # Notes
    notes = Column(Text, nullable=True)
    rejection_reason = Column(Text, nullable=True)
    
    # Conversion en bon de commande
    converted_to_po_id = Column(Integer, ForeignKey("purchase_orders.id"), nullable=True)
    converted_at = Column(DateTime, nullable=True)
    
    # Dates système
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Relations
    items = relationship("PurchaseRequisitionItem", back_populates="requisition", lazy="dynamic", cascade="all, delete-orphan")
    
    @property
    def estimated_total(self):
        return self.estimated_total_cents / 100 if self.estimated_total_cents else 0
    
    def calculate_estimated_total(self):
        """Calcule le total estimé"""
        self.estimated_total_cents = sum(item.estimated_total_cents for item in self.items.all())
        return self.estimated_total_cents
    
    __table_args__ = (
        db.Index("idx_requisition_business", "business_id_fk"),
        db.Index("idx_requisition_number", "requisition_number"),
        db.Index("idx_requisition_status", "status"),
        db.Index("idx_requisition_dates", "request_date", "needed_by_date"),
    )


class PurchaseRequisitionItem(db.Model):
    """Article d'une demande d'achat"""
    __tablename__ = "purchase_requisition_items"
    id = Column(Integer, primary_key=True)
    requisition_id_fk = Column(Integer, ForeignKey("purchase_requisitions.id"), nullable=False, index=True)
    
    # Informations du produit demandé
    product_name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    specifications = Column(Text, nullable=True)
    
    # Quantité
    quantity = Column(Integer, nullable=False)
    unit_of_measure = Column(String(20), default='unit', nullable=False)
    
    # Prix estimé en centimes
    estimated_unit_cost_cents = Column(Integer, nullable=True)
    estimated_total_cents = Column(Integer, default=0, nullable=False)
    
    # Fournisseur suggéré
    suggested_supplier_id = Column(Integer, ForeignKey("suppliers.id"), nullable=True)
    supplier_notes = Column(Text, nullable=True)
    
    # Justification
    justification = Column(Text, nullable=True)
    
    # Dates
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    # Relations
    requisition = relationship("PurchaseRequisition", back_populates="items")
    
    @property
    def estimated_unit_cost(self):
        return self.estimated_unit_cost_cents / 100 if self.estimated_unit_cost_cents else 0
    
    @property
    def estimated_total(self):
        return self.estimated_total_cents / 100 if self.estimated_total_cents else 0
    
    def calculate_estimated_total(self):
        """Calcule le total estimé"""
        if self.estimated_unit_cost_cents:
            self.estimated_total_cents = self.quantity * self.estimated_unit_cost_cents
        return self.estimated_total_cents
    
    __table_args__ = (
        db.Index("idx_requisition_item_req", "requisition_id_fk"),
        db.Index("idx_requisition_item_supplier", "suggested_supplier_id"),
    )


