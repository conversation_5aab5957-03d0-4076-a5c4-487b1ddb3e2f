{% extends "base.html" %}

{% block title %}Support & Maintenance{% endblock %}

{% block content %}
<div class="grid grid-cols-12 gap-6">
    <!-- En-tête -->
    <div class="col-span-12">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
            <div>
                <h1 class="text-2xl font-bold text-slate-100">
                    <i class="fas fa-headset text-blue-400 mr-2"></i>
                    Support & Maintenance
                </h1>
            </div>
            <div class="flex flex-col sm:flex-row gap-2">
                <a href="{{ url_for('support.new_ticket') }}" class="bg-blue-600 hover:bg-blue-500 rounded-lg px-4 py-2 text-center transition-colors text-sm">
                    <i class="fas fa-plus mr-1"></i>Nouveau Ticket
                </a>
                <a href="{{ url_for('support.tickets') }}" class="bg-slate-700 hover:bg-slate-600 rounded-lg px-4 py-2 text-center transition-colors text-sm">
                    <i class="fas fa-ticket-alt mr-1"></i>Tous les Tickets
                </a>
                <a href="{{ url_for('support.faq') }}" class="bg-cyan-600 hover:bg-cyan-500 rounded-lg px-4 py-2 text-center transition-colors text-sm">
                    <i class="fas fa-question-circle mr-1"></i>FAQ
                </a>
                <a href="{{ url_for('support.knowledge_base') }}" class="bg-green-600 hover:bg-green-500 rounded-lg px-4 py-2 text-center transition-colors text-sm">
                    <i class="fas fa-book mr-1"></i>Base de connaissances
                </a>
                <div class="relative group">
                    <button class="bg-slate-700 hover:bg-slate-600 rounded-lg px-4 py-2 text-center transition-colors text-sm">
                        <i class="fas fa-cog mr-1"></i>Gestion
                    </button>
                    <div class="absolute right-0 mt-1 w-48 bg-slate-800 border border-slate-700 rounded-lg shadow-lg py-2 hidden group-hover:block z-10">
                        <a href="{{ url_for('support.manage_faq') }}" class="block px-4 py-2 text-slate-300 hover:bg-slate-700 hover:text-white text-sm">
                            <i class="fas fa-edit mr-2"></i>Gérer FAQ
                        </a>
                        <a href="{{ url_for('support.reports') }}" class="block px-4 py-2 text-slate-300 hover:bg-slate-700 hover:text-white text-sm">
                            <i class="fas fa-chart-bar mr-2"></i>Rapports
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistiques rapides -->
    <div class="col-span-12">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div class="rounded-xl bg-slate-900 border border-slate-700 p-5">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-slate-400 text-sm">Total Tickets</p>
                        <h3 class="text-2xl font-bold text-blue-400">{{ total_tickets }}</h3>
                    </div>
                    <div class="text-2xl text-blue-400">
                        <i class="fas fa-ticket-alt"></i>
                    </div>
                </div>
            </div>
            <div class="rounded-xl bg-slate-900 border border-amber-500 p-5">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-slate-400 text-sm">Ouverts</p>
                        <h3 class="text-2xl font-bold text-amber-400">{{ open_tickets }}</h3>
                    </div>
                    <div class="text-2xl text-amber-400">
                        <i class="fas fa-folder-open"></i>
                    </div>
                </div>
            </div>
            <div class="rounded-xl bg-slate-900 border border-cyan-500 p-5">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-slate-400 text-sm">En cours</p>
                        <h3 class="text-2xl font-bold text-cyan-400">{{ in_progress_tickets }}</h3>
                    </div>
                    <div class="text-2xl text-cyan-400">
                        <i class="fas fa-cog"></i>
                    </div>
                </div>
            </div>
            <div class="rounded-xl bg-slate-900 border border-green-500 p-5">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-slate-400 text-sm">Résolus aujourd'hui</p>
                        <h3 class="text-2xl font-bold text-green-400">{{ resolved_today }}</h3>
                    </div>
                    <div class="text-2xl text-green-400">
                        <i class="fas fa-check-circle"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Métriques du mois -->
    <div class="col-span-12">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div class="rounded-xl bg-slate-900 border border-slate-700">
                <div class="border-b border-slate-700 px-5 py-4">
                    <h2 class="font-semibold text-slate-200">
                        <i class="fas fa-calendar-alt mr-2"></i>
                        Performance du mois
                    </h2>
                </div>
                <div class="p-5">
                    <div class="grid grid-cols-2 gap-4 text-center mb-4">
                        <div>
                            <h4 class="text-2xl font-bold text-blue-400">{{ monthly_metrics.created }}</h4>
                            <p class="text-slate-400 text-sm">Tickets créés</p>
                        </div>
                        <div>
                            <h4 class="text-2xl font-bold text-green-400">{{ monthly_metrics.resolved }}</h4>
                            <p class="text-slate-400 text-sm">Tickets résolus</p>
                        </div>
                    </div>
                    {% if monthly_metrics.created > 0 %}
                    <div>
                        <div class="w-full h-2 bg-slate-700 rounded-full overflow-hidden">
                            <div class="h-full bg-green-500 rounded-full" 
                                 style="width: {{ '%.1f'|format((monthly_metrics.resolved / monthly_metrics.created * 100) if monthly_metrics.created else 0) }}%">
                            </div>
                        </div>
                        <p class="text-slate-400 text-sm mt-2">
                            Taux de résolution: {{ "%.1f"|format((monthly_metrics.resolved / monthly_metrics.created * 100) if monthly_metrics.created else 0) }}%
                        </p>
                    </div>
                    {% endif %}
                </div>
            </div>
            <div class="rounded-xl bg-slate-900 border border-slate-700">
                <div class="border-b border-slate-700 px-5 py-4">
                    <h2 class="font-semibold text-slate-200">
                        <i class="fas fa-search mr-2"></i>
                        Recherche rapide
                    </h2>
                </div>
                <div class="p-5">
                    <form action="{{ url_for('support.search') }}" method="GET">
                        <div class="flex">
                            <input type="text" class="flex-1 bg-slate-800 border border-slate-700 rounded-l-lg px-3 py-2 text-slate-100 focus:outline-none focus:ring-2 focus:ring-blue-500" name="q" 
                                   placeholder="Rechercher dans la FAQ, articles..." required>
                            <button class="bg-blue-600 hover:bg-blue-500 rounded-r-lg px-4 py-2 text-white transition-colors" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                    <div class="mt-3">
                        <p class="text-slate-400 text-sm">
                            <i class="fas fa-lightbulb mr-1"></i>
                            Recherchez dans la FAQ et base de connaissances pour trouver des solutions rapidement.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-span-12">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            <!-- Tickets urgents -->
            <div class="rounded-xl bg-slate-900 border border-rose-500">
                <div class="border-b border-rose-500 px-5 py-4">
                    <h2 class="font-semibold text-rose-400">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        Tickets urgents ({{ urgent_tickets|length }})
                    </h2>
                </div>
                <div class="p-5">
                    {% if urgent_tickets %}
                    <div class="space-y-3">
                        {% for ticket in urgent_tickets %}
                        <div class="flex items-center justify-between p-3 bg-slate-800 rounded-lg">
                            <div>
                                <div class="font-medium">
                                    <a href="{{ url_for('support.view_ticket', id=ticket.id) }}" class="text-blue-400 hover:text-blue-300">
                                        {{ ticket.ticket_number }}
                                    </a>
                                </div>
                                <p class="text-slate-400 text-sm mt-1">
                                    {{ ticket.title[:50] }}{% if ticket.title|length > 50 %}...{% endif %}
                                </p>
                                <p class="text-slate-500 text-xs mt-1">
                                    <i class="fas fa-clock mr-1"></i>
                                    {{ ticket.created_at.strftime('%d/%m/%Y %H:%M') }}
                                </p>
                            </div>
                            <span class="bg-{{ 'rose-500' if ticket.priority.value == 'urgent' else 'amber-500' }} text-white text-xs px-2 py-1 rounded-full">
                                {{ ticket.priority.value.title() }}
                            </span>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center py-6">
                        <i class="fas fa-check-circle text-3xl text-green-500 mb-2"></i>
                        <p class="text-slate-400">Aucun ticket urgent</p>
                    </div>
                    {% endif %}
                </div>
                {% if urgent_tickets %}
                <div class="border-t border-slate-700 px-5 py-4">
                    <a href="{{ url_for('support.tickets', priority='urgent') }}" class="w-full bg-rose-600 hover:bg-rose-500 rounded-lg px-4 py-2 text-white text-center transition-colors text-sm block">
                        Voir tous les tickets urgents
                    </a>
                </div>
                {% endif %}
            </div>

            <!-- Tickets en retard -->
            <div class="rounded-xl bg-slate-900 border border-amber-500">
                <div class="border-b border-amber-500 px-5 py-4">
                    <h2 class="font-semibold text-amber-400">
                        <i class="fas fa-clock mr-2"></i>
                        Tickets en retard ({{ overdue_tickets|length }})
                    </h2>
                </div>
                <div class="p-5">
                    {% if overdue_tickets %}
                    <div class="space-y-3">
                        {% for ticket in overdue_tickets %}
                        <div class="flex items-center justify-between p-3 bg-slate-800 rounded-lg">
                            <div>
                                <div class="font-medium">
                                    <a href="{{ url_for('support.view_ticket', id=ticket.id) }}" class="text-blue-400 hover:text-blue-300">
                                        {{ ticket.ticket_number }}
                                    </a>
                                </div>
                                <p class="text-slate-400 text-sm mt-1">
                                    {{ ticket.title[:50] }}{% if ticket.title|length > 50 %}...{% endif %}
                                </p>
                                <p class="text-rose-400 text-xs mt-1">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    Échéance: {{ ticket.due_date.strftime('%d/%m/%Y %H:%M') }}
                                </p>
                            </div>
                            <span class="bg-{{ 'rose-500' if ticket.status.value == 'open' else 'amber-500' }} text-white text-xs px-2 py-1 rounded-full">
                                {{ ticket.status.value.title() }}
                            </span>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center py-6">
                        <i class="fas fa-check-circle text-3xl text-green-500 mb-2"></i>
                        <p class="text-slate-400">Aucun ticket en retard</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Tickets récents -->
    <div class="col-span-12">
        <div class="rounded-xl bg-slate-900 border border-slate-700">
            <div class="border-b border-slate-700 px-5 py-4 flex flex-row items-center justify-between">
                <h2 class="font-semibold text-slate-200">
                    <i class="fas fa-history mr-2"></i>
                    Tickets récents
                </h2>
                <a href="{{ url_for('support.tickets') }}" class="bg-slate-700 hover:bg-slate-600 rounded px-3 py-1 text-sm transition-colors">
                    Voir tous
                </a>
            </div>
            <div class="p-5">
                {% if recent_tickets %}
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-slate-700">
                        <thead class="bg-slate-800">
                            <tr>
                                <th class="px-4 py-3 text-left text-xs font-medium text-slate-300 uppercase tracking-wider">Numéro</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-slate-300 uppercase tracking-wider">Titre</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-slate-300 uppercase tracking-wider">Catégorie</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-slate-300 uppercase tracking-wider">Priorité</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-slate-300 uppercase tracking-wider">Statut</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-slate-300 uppercase tracking-wider">Créé</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-slate-300 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-slate-700">
                            {% for ticket in recent_tickets %}
                            <tr class="hover:bg-slate-800">
                                <td class="px-4 py-3 whitespace-nowrap">
                                    <code class="bg-slate-700 px-2 py-1 rounded text-slate-300">{{ ticket.ticket_number }}</code>
                                </td>
                                <td class="px-4 py-3">
                                    <a href="{{ url_for('support.view_ticket', id=ticket.id) }}" class="text-blue-400 hover:text-blue-300">
                                        {{ ticket.title[:40] }}{% if ticket.title|length > 40 %}...{% endif %}
                                    </a>
                                </td>
                                <td class="px-4 py-3 whitespace-nowrap">
                                    <span class="bg-slate-700 text-slate-300 text-xs px-2 py-1 rounded">{{ ticket.category.value.title() }}</span>
                                </td>
                                <td class="px-4 py-3 whitespace-nowrap">
                                    <span class="bg-{% if ticket.priority.value == 'urgent' %}rose-500{% elif ticket.priority.value == 'high' %}amber-500{% else %}slate-700{% endif %} text-white text-xs px-2 py-1 rounded">
                                        {{ ticket.priority.value.title() }}
                                    </span>
                                </td>
                                <td class="px-4 py-3 whitespace-nowrap">
                                    <span class="bg-{% if ticket.status.value == 'open' %}amber-500{% elif ticket.status.value == 'in_progress' %}cyan-500{% elif ticket.status.value == 'resolved' %}green-500{% else %}slate-700{% endif %} text-white text-xs px-2 py-1 rounded">
                                        {{ ticket.status.value.title() }}
                                    </span>
                                </td>
                                <td class="px-4 py-3 whitespace-nowrap text-slate-400 text-sm">
                                    {{ ticket.created_at.strftime('%d/%m/%Y') }}
                                </td>
                                <td class="px-4 py-3 whitespace-nowrap">
                                    <div class="flex gap-1">
                                        <a href="{{ url_for('support.view_ticket', id=ticket.id) }}" 
                                           class="bg-slate-700 hover:bg-slate-600 rounded px-2 py-1 text-white transition-colors text-xs"
                                           title="Voir">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ url_for('support.edit_ticket', id=ticket.id) }}" 
                                           class="bg-amber-600 hover:bg-amber-500 rounded px-2 py-1 text-white transition-colors text-xs"
                                           title="Modifier">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-8">
                    <i class="fas fa-ticket-alt text-4xl text-slate-500 mb-3"></i>
                    <h3 class="text-lg font-medium text-slate-300 mb-2">Aucun ticket créé</h3>
                    <p class="text-slate-400 mb-4">Commencez par créer votre premier ticket de support.</p>
                    <a href="{{ url_for('support.new_ticket') }}" class="bg-blue-600 hover:bg-blue-500 rounded-lg px-4 py-2 text-white transition-colors inline-flex items-center">
                        <i class="fas fa-plus mr-1"></i>Créer un ticket
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Actualisation automatique des statistiques toutes les 5 minutes
setInterval(function() {
    // On pourrait implémenter un refresh AJAX ici
}, 300000); // 5 minutes

// Fonction pour changer rapidement le statut d'un ticket
function quickStatusChange(ticketId, newStatus) {
    fetch(`/support/api/ticket/${ticketId}/status`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            status: newStatus
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            setTimeout(() => window.location.reload(), 1000);
        } else {
            showAlert('danger', data.error || 'Erreur lors du changement de statut');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'Erreur de communication');
    });
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `p-4 mb-4 text-sm rounded-lg ${
        type === 'success' ? 'bg-green-900 text-green-200' : 'bg-red-900 text-red-200'
    }`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="ml-2 float-right" onclick="this.parentElement.remove()">&times;</button>
    `;
    
    const container = document.querySelector('.grid');
    container.insertBefore(alertDiv, container.firstChild);
    
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>
{% endblock %}