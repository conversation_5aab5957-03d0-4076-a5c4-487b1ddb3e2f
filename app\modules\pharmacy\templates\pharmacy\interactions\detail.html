{% extends "base.html" %}

{% block title %}Détail Interaction - {{ interaction.medication1.name }} + {{ interaction.medication2.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Interaction Médicamenteuse</h1>
                <div class="btn-group" role="group">
                    <a href="{{ url_for('pharmacy.interactions') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Retour
                    </a>
                    <a href="{{ url_for('pharmacy.edit_interaction', interaction_id=interaction.id) }}" 
                       class="btn btn-outline-warning">
                        <i class="fas fa-edit"></i> Modifier
                    </a>
                    <button type="button" class="btn btn-outline-primary" onclick="window.print()">
                        <i class="fas fa-print"></i> Imprimer
                    </button>
                </div>
            </div>

            <div class="row">
                <!-- Informations principales -->
                <div class="col-lg-8">
                    <!-- Médicaments concernés -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">Médicaments Concernés</h5>
                                {% set severity_colors = {
                                    'minor': 'success',
                                    'moderate': 'warning', 
                                    'major': 'danger',
                                    'contraindicated': 'dark'
                                } %}
                                {% set severity_labels = {
                                    'minor': 'Mineure',
                                    'moderate': 'Modérée',
                                    'major': 'Majeure', 
                                    'contraindicated': 'Contre-indiquée'
                                } %}
                                <span class="badge bg-{{ severity_colors.get(interaction.severity.value, 'secondary') }} fs-6">
                                    {{ severity_labels.get(interaction.severity.value, interaction.severity.value) }}
                                </span>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card h-100">
                                        <div class="card-body">
                                            <h6 class="fw-bold text-primary">{{ interaction.medication1.name }}</h6>
                                            {% if interaction.medication1.generic_name %}
                                                <p class="mb-1"><strong>DCI :</strong> {{ interaction.medication1.generic_name }}</p>
                                            {% endif %}
                                            {% if interaction.medication1.dosage %}
                                                <p class="mb-1"><strong>Dosage :</strong> {{ interaction.medication1.dosage }}</p>
                                            {% endif %}
                                            {% if interaction.medication1.pharmaceutical_form %}
                                                <p class="mb-1"><strong>Forme :</strong> {{ interaction.medication1.pharmaceutical_form }}</p>
                                            {% endif %}
                                            <span class="badge bg-info">{{ interaction.medication1.medication_class.value }}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card h-100">
                                        <div class="card-body">
                                            <h6 class="fw-bold text-primary">{{ interaction.medication2.name }}</h6>
                                            {% if interaction.medication2.generic_name %}
                                                <p class="mb-1"><strong>DCI :</strong> {{ interaction.medication2.generic_name }}</p>
                                            {% endif %}
                                            {% if interaction.medication2.dosage %}
                                                <p class="mb-1"><strong>Dosage :</strong> {{ interaction.medication2.dosage }}</p>
                                            {% endif %}
                                            {% if interaction.medication2.pharmaceutical_form %}
                                                <p class="mb-1"><strong>Forme :</strong> {{ interaction.medication2.pharmaceutical_form }}</p>
                                            {% endif %}
                                            <span class="badge bg-info">{{ interaction.medication2.medication_class.value }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Description de l'interaction -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Description de l'Interaction</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-4">
                                <h6>Description générale :</h6>
                                <p>{{ interaction.description }}</p>
                            </div>
                            
                            {% if interaction.mechanism %}
                            <div class="mb-4">
                                <h6>Mécanisme d'action :</h6>
                                <p>{{ interaction.mechanism }}</p>
                            </div>
                            {% endif %}
                            
                            {% if interaction.clinical_effects %}
                            <div class="mb-4">
                                <h6>Effets cliniques :</h6>
                                <p>{{ interaction.clinical_effects }}</p>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Gestion et recommandations -->
                    {% if interaction.recommendations %}
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Gestion et Recommandations</h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-lightbulb"></i> Recommandations :</h6>
                                <p class="mb-0">{{ interaction.recommendations }}</p>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Références scientifiques -->
                    {% if interaction.references %}
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Références Scientifiques</h5>
                        </div>
                        <div class="card-body">
                            <p>{{ interaction.references }}</p>
                        </div>
                    </div>
                    {% endif %}
                </div>

                <!-- Panneau latéral -->
                <div class="col-lg-4">
                    <!-- Informations sur l'interaction -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Informations</h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-borderless table-sm">
                                <tr>
                                    <td><strong>Sévérité :</strong></td>
                                    <td>
                                        <span class="badge bg-{{ severity_colors.get(interaction.severity.value, 'secondary') }}">
                                            {{ severity_labels.get(interaction.severity.value, interaction.severity.value) }}
                                        </span>
                                    </td>
                                </tr>
                                {% if interaction.interaction_type %}
                                <tr>
                                    <td><strong>Type :</strong></td>
                                    <td><span class="badge bg-info">{{ interaction.interaction_type }}</span></td>
                                </tr>
                                {% endif %}
                                <tr>
                                    <td><strong>Contre-indication :</strong></td>
                                    <td>
                                        {% if interaction.contraindicated %}
                                            <span class="badge bg-danger">Oui</span>
                                        {% else %}
                                            <span class="badge bg-success">Non</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Surveillance :</strong></td>
                                    <td>
                                        {% if interaction.monitoring_required %}
                                            <span class="badge bg-warning">Requise</span>
                                        {% else %}
                                            <span class="badge bg-secondary">Non requise</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% if interaction.evidence_level %}
                                <tr>
                                    <td><strong>Niveau de preuve :</strong></td>
                                    <td><span class="badge bg-secondary">{{ interaction.evidence_level }}</span></td>
                                </tr>
                                {% endif %}
                                <tr>
                                    <td><strong>Statut :</strong></td>
                                    <td>
                                        {% if interaction.is_active %}
                                            <span class="badge bg-success">Active</span>
                                        {% else %}
                                            <span class="badge bg-secondary">Inactive</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Créée le :</strong></td>
                                    <td>{{ interaction.created_at.strftime('%d/%m/%Y') }}</td>
                                </tr>
                                {% if interaction.updated_at != interaction.created_at %}
                                <tr>
                                    <td><strong>Modifiée le :</strong></td>
                                    <td>{{ interaction.updated_at.strftime('%d/%m/%Y') }}</td>
                                </tr>
                                {% endif %}
                            </table>
                        </div>
                    </div>

                    <!-- Actions rapides -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Actions Rapides</h5>
                        </div>
                        <div class="card-body">
                            <a href="{{ url_for('pharmacy.edit_interaction', interaction_id=interaction.id) }}" 
                               class="btn btn-warning w-100 mb-2">
                                <i class="fas fa-edit"></i> Modifier l'interaction
                            </a>
                            
                            <button type="button" class="btn btn-outline-info w-100 mb-2" onclick="searchSimilar()">
                                <i class="fas fa-search"></i> Rechercher interactions similaires
                            </button>
                            
                            <button type="button" class="btn btn-outline-primary w-100 mb-2" onclick="exportInteraction()">
                                <i class="fas fa-download"></i> Exporter
                            </button>
                            
                            {% if interaction.is_active %}
                                <button type="button" class="btn btn-outline-danger w-100" 
                                        onclick="deactivateInteraction()" data-bs-toggle="modal" data-bs-target="#deactivateModal">
                                    <i class="fas fa-times"></i> Désactiver
                                </button>
                            {% else %}
                                <button type="button" class="btn btn-outline-success w-100" onclick="activateInteraction()">
                                    <i class="fas fa-check"></i> Réactiver
                                </button>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Alertes et conseils -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Conseils Pratiques</h5>
                        </div>
                        <div class="card-body">
                            {% if interaction.contraindicated %}
                                <div class="alert alert-danger">
                                    <strong><i class="fas fa-ban"></i> Contre-indication absolue</strong><br>
                                    Cette association ne doit jamais être prescrite.
                                </div>
                            {% elif interaction.severity.value == 'major' %}
                                <div class="alert alert-warning">
                                    <strong><i class="fas fa-exclamation-triangle"></i> Interaction majeure</strong><br>
                                    Éviter cette association ou surveiller étroitement.
                                </div>
                            {% elif interaction.monitoring_required %}
                                <div class="alert alert-info">
                                    <strong><i class="fas fa-eye"></i> Surveillance requise</strong><br>
                                    Monitoring clinique et/ou biologique nécessaire.
                                </div>
                            {% endif %}
                            
                            <h6 class="mt-3">Points clés :</h6>
                            <ul class="small">
                                <li>Vérifier systématiquement lors de nouvelles prescriptions</li>
                                <li>Informer le patient des signes à surveiller</li>
                                <li>Documenter dans le dossier patient</li>
                                {% if interaction.monitoring_required %}
                                <li>Planifier les contrôles nécessaires</li>
                                {% endif %}
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de désactivation -->
<div class="modal fade" id="deactivateModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Désactiver l'interaction</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir désactiver cette interaction médicamenteuse ?</p>
                <p class="text-warning">
                    <i class="fas fa-exclamation-triangle"></i> 
                    L'interaction ne sera plus vérifiée automatiquement lors des prescriptions.
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <form method="POST" action="{{ url_for('pharmacy.delete_interaction', interaction_id=interaction.id) }}" style="display: inline;">
                    <button type="submit" class="btn btn-danger">Désactiver</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function searchSimilar() {
    // Rechercher des interactions similaires
    const med1Name = "{{ interaction.medication1.generic_name or interaction.medication1.name }}";
    const med2Name = "{{ interaction.medication2.generic_name or interaction.medication2.name }}";
    
    alert(`Recherche d'interactions similaires impliquant ${med1Name} ou ${med2Name}...`);
    // Ici on pourrait faire une recherche plus avancée
}

function exportInteraction() {
    // Exporter les informations de l'interaction
    const data = {
        medication1: "{{ interaction.medication1.name }}",
        medication2: "{{ interaction.medication2.name }}",
        severity: "{{ interaction.severity.value }}",
        description: "{{ interaction.description }}",
        recommendations: "{{ interaction.recommendations or '' }}"
    };
    
    const dataStr = JSON.stringify(data, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    
    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `interaction_${data.medication1}_${data.medication2}.json`;
    link.click();
}

function activateInteraction() {
    if (confirm('Réactiver cette interaction ?')) {
        // Faire un appel pour réactiver
        window.location.reload();
    }
}

// Impression
window.addEventListener('beforeprint', function() {
    document.querySelectorAll('.btn, .card-header').forEach(el => {
        el.style.display = 'none';
    });
});

window.addEventListener('afterprint', function() {
    document.querySelectorAll('.btn, .card-header').forEach(el => {
        el.style.display = '';
    });
});
</script>
{% endblock %}