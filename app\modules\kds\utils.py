"""
Utilitaires pour le module KDS
"""

from app.extensions import db
from .models import KdsTicket
from .websocket_handlers import notify_new_order


def create_kds_ticket_for_order(order_id, business_id):
    """
    Créer automatiquement un ticket KDS pour une nouvelle commande
    
    Args:
        order_id: ID de la commande
        business_id: ID de l'entreprise
    
    Returns:
        KdsTicket: Le ticket créé ou None si erreur
    """
    try:
        # Vérifier si un ticket existe déjà pour cette commande
        existing_ticket = KdsTicket.query.filter_by(order_id_fk=order_id).first()
        if existing_ticket:
            return existing_ticket
        
        # Créer un nouveau ticket KDS
        ticket = KdsTicket(
            order_id_fk=order_id,
            status='pending'
        )
        
        db.session.add(ticket)
        db.session.commit()
        
        # Notifier les clients WebSocket de la nouvelle commande
        notify_new_order(order_id, business_id)
        
        return ticket
        
    except Exception as e:
        db.session.rollback()
        raise e


def update_kds_ticket_status(order_id, new_status):
    """
    Mettre à jour le statut d'un ticket KDS basé sur l'ID de commande
    
    Args:
        order_id: ID de la commande
        new_status: Nouveau statut ('pending', 'preparing', 'ready', 'completed')
    
    Returns:
        bool: True si mis à jour, False sinon
    """
    try:
        ticket = KdsTicket.query.filter_by(order_id_fk=order_id).first()
        if not ticket:
            return False
        
        old_status = ticket.status
        ticket.status = new_status
        db.session.commit()
        
        # Notifier les clients WebSocket si nécessaire
        from app.modules.sales.models import Order
        order = Order.query.get(order_id)
        if order:
            from .websocket_handlers import notify_ticket_update
            notify_ticket_update(
                ticket.id,
                order_id,
                old_status,
                new_status,
                order.business_id_fk
            )
        
        return True
        
    except Exception as e:
        db.session.rollback()
        raise e


def get_active_tickets_count(business_id):
    """
    Obtenir le nombre de tickets actifs pour une entreprise
    
    Args:
        business_id: ID de l'entreprise
    
    Returns:
        int: Nombre de tickets actifs
    """
    from sqlalchemy import and_
    from app.modules.sales.models import Order
    
    return KdsTicket.query.join(\n        Order, KdsTicket.order_id_fk == Order.id\n    ).filter(\n        and_(\n            Order.business_id_fk == business_id,\n            KdsTicket.status.in_(['pending', 'preparing', 'ready'])\n        )\n    ).count()


def cleanup_completed_tickets(business_id, hours_old=24):
    \"\"\"
    Nettoyer les anciens tickets terminés
    
    Args:
        business_id: ID de l'entreprise
        hours_old: Nombre d'heures après lesquelles supprimer les tickets terminés
    \"\"\"
    from datetime import datetime, timedelta
    from sqlalchemy import and_
    from app.modules.sales.models import Order
    
    cutoff_time = datetime.utcnow() - timedelta(hours=hours_old)
    
    old_tickets = KdsTicket.query.join(\n        Order, KdsTicket.order_id_fk == Order.id\n    ).filter(\n        and_(\n            Order.business_id_fk == business_id,\n            KdsTicket.status == 'completed',\n            KdsTicket.created_at < cutoff_time\n        )\n    ).all()
    
    for ticket in old_tickets:
        db.session.delete(ticket)
    
    db.session.commit()
    return len(old_tickets)