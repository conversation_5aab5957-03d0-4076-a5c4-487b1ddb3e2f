from flask import render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from sqlalchemy.orm import joinedload
from sqlalchemy import and_

from app.extensions import db, socketio
from app.modules.accounts.models import Business
from app.modules.sales.models import Order, OrderItem
from app.modules.catalog.models import Product

from . import bp
from .models import KdsScreen, KdsTicket
from .forms import KdsScreenForm, KdsTicketForm, KdsFilterForm
from .websocket_handlers import notify_screen_update, notify_ticket_update


@bp.get("/")
@login_required
def index():
    """Affichage principal du KDS avec les tickets en temps réel"""
    business = Business.query.get_or_404(current_user.business_id_fk)
    
    # Récupérer les écrans KDS
    screens = KdsScreen.query.filter_by(
        business_id_fk=business.id
    ).all()
    
    # Récupérer les tickets actifs (non terminés)
    tickets = db.session.query(KdsTicket).join(
        Order, KdsTicket.order_id_fk == Order.id
    ).filter(
        and_(
            Order.business_id_fk == business.id,
            KdsTicket.status.in_(['pending', 'preparing', 'ready'])
        )
    ).options(
        joinedload(KdsTicket.order)
    ).order_by(KdsTicket.created_at.asc()).all()
    
    # Ajouter les détails des commandes
    for ticket in tickets:
        ticket.order_items = OrderItem.query.filter_by(
            order_id_fk=ticket.order_id_fk
        ).options(joinedload(OrderItem.product)).all()
    
    form = KdsFilterForm()
    form.screen_filter.choices = [('', 'Tous les écrans')] + [
        (str(screen.id), screen.name) for screen in screens
    ]
    
    return render_template(
        "kds/index.html",
        tickets=tickets,
        screens=screens,
        form=form
    )


@bp.get("/screens")
@login_required
def screens():
    """Gestion des écrans KDS"""
    business = Business.query.get_or_404(current_user.business_id_fk)
    
    screens = KdsScreen.query.filter_by(
        business_id_fk=business.id
    ).all()
    
    form = KdsScreenForm()
    
    return render_template(
        "kds/screens.html",
        screens=screens,
        form=form
    )


@bp.post("/screens")
@login_required
def create_screen():
    """Créer un nouvel écran KDS"""
    business = Business.query.get_or_404(current_user.business_id_fk)
    form = KdsScreenForm()
    
    if form.validate_on_submit():
        screen = KdsScreen(
            business_id_fk=business.id,
            name=form.name.data
        )
        
        db.session.add(screen)
        db.session.commit()
        
        flash(f"Écran KDS '{screen.name}' créé avec succès!", "success")
        
        # Notifier les clients WebSocket
        notify_screen_update(
            screen.id, 
            screen.name, 
            'created', 
            business.id
        )
        
        return redirect(url_for('kds.screens'))
    
    # Si erreur, recharger la page avec les erreurs
    screens = KdsScreen.query.filter_by(
        business_id_fk=business.id
    ).all()
    
    return render_template(
        "kds/screens.html",
        screens=screens,
        form=form
    )


@bp.get("/screens/<int:screen_id>/edit")
@login_required
def edit_screen(screen_id):
    """Éditer un écran KDS"""
    business = Business.query.get_or_404(current_user.business_id_fk)
    screen = KdsScreen.query.filter_by(
        id=screen_id,
        business_id_fk=business.id
    ).first_or_404()
    
    form = KdsScreenForm(obj=screen)
    
    return render_template(
        "kds/edit_screen.html",
        screen=screen,
        form=form
    )


@bp.post("/screens/<int:screen_id>/edit")
@login_required
def update_screen(screen_id):
    """Mettre à jour un écran KDS"""
    business = Business.query.get_or_404(current_user.business_id_fk)
    screen = KdsScreen.query.filter_by(
        id=screen_id,
        business_id_fk=business.id
    ).first_or_404()
    
    form = KdsScreenForm()
    
    if form.validate_on_submit():
        screen.name = form.name.data
        
        db.session.commit()
        
        flash(f"Écran KDS '{screen.name}' mis à jour avec succès!", "success")
        
        # Notifier les clients WebSocket
        notify_screen_update(
            screen.id, 
            screen.name, 
            'updated', 
            business.id
        )
        
        return redirect(url_for('kds.screens'))
    
    return render_template(
        "kds/edit_screen.html",
        screen=screen,
        form=form
    )


@bp.post("/screens/<int:screen_id>/delete")
@login_required
def delete_screen(screen_id):
    """Supprimer un écran KDS"""
    business = Business.query.get_or_404(current_user.business_id_fk)
    screen = KdsScreen.query.filter_by(
        id=screen_id,
        business_id_fk=business.id
    ).first_or_404()
    
    screen_name = screen.name
    
    db.session.delete(screen)
    db.session.commit()
    
    flash(f"Écran KDS '{screen_name}' supprimé avec succès!", "success")
    
    # Notifier les clients WebSocket
    notify_screen_update(
        screen_id, 
        screen_name, 
        'deleted', 
        business.id
    )
    
    return redirect(url_for('kds.screens'))


@bp.post("/tickets/<int:ticket_id>/status")
@login_required
def update_ticket_status(ticket_id):
    """Mettre à jour le statut d'un ticket KDS"""
    business = Business.query.get_or_404(current_user.business_id_fk)
    
    ticket = db.session.query(KdsTicket).join(
        Order, KdsTicket.order_id_fk == Order.id
    ).filter(
        and_(
            KdsTicket.id == ticket_id,
            Order.business_id_fk == business.id
        )
    ).first_or_404()
    
    new_status = request.json.get('status')
    
    if new_status not in ['pending', 'preparing', 'ready', 'completed']:
        return jsonify({'error': 'Statut invalide'}), 400
    
    old_status = ticket.status
    ticket.status = new_status
    
    db.session.commit()
    
    # Notifier tous les clients connectés
    notify_ticket_update(
        ticket.id,
        ticket.order_id_fk,
        old_status,
        new_status,
        business.id
    )
    
    return jsonify({
        'success': True,
        'ticket_id': ticket.id,
        'new_status': new_status
    })


@bp.get("/api/tickets")
@login_required
def api_get_tickets():
    """API pour récupérer les tickets en temps réel"""
    business = Business.query.get_or_404(current_user.business_id_fk)
    
    status_filter = request.args.get('status')
    screen_filter = request.args.get('screen')
    
    query = db.session.query(KdsTicket).join(
        Order, KdsTicket.order_id_fk == Order.id
    ).filter(
        Order.business_id_fk == business.id
    )
    
    if status_filter:
        query = query.filter(KdsTicket.status == status_filter)
    
    tickets = query.order_by(KdsTicket.created_at.asc()).all()
    
    tickets_data = []
    for ticket in tickets:
        order_items = OrderItem.query.filter_by(
            order_id_fk=ticket.order_id_fk
        ).options(joinedload(OrderItem.product)).all()
        
        tickets_data.append({
            'id': ticket.id,
            'order_id': ticket.order_id_fk,
            'status': ticket.status,
            'created_at': ticket.created_at.isoformat(),
            'items': [{
                'id': item.id,
                'product_name': item.product.name if item.product else 'Produit supprimé',
                'quantity': item.qty,
                'price': item.price_cents / 100
            } for item in order_items]
        })
    
    return jsonify({
        'tickets': tickets_data,
        'total': len(tickets_data)
    })


@bp.get("/display/<int:screen_id>")
def display_screen(screen_id):
    """Affichage plein écran pour un écran KDS spécifique"""
    screen = KdsScreen.query.get_or_404(screen_id)
    
    return render_template(
        "kds/display.html",
        screen=screen
    )


