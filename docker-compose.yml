version: '3.8'

services:
  # Base de données PostgreSQL
  database:
    image: postgres:13
    environment:
      POSTGRES_DB: pos_system
      POSTGRES_USER: pos_user
      POSTGRES_PASSWORD: pos_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - pos-network
    restart: unless-stopped

  # Cache Redis
  cache:
    image: redis:6-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - pos-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # Application Flask
  app:
    build: .
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=************************************************/pos_system
      - REDIS_URL=redis://cache:6379/0
      - SECRET_KEY=your-secret-key-here
    volumes:
      - ./instance:/app/instance
    networks:
      - pos-network
    depends_on:
      - database
      - cache
    restart: unless-stopped

  # Nginx (reverse proxy)
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    networks:
      - pos-network
    depends_on:
      - app
    restart: unless-stopped

  # Celery Worker (pour les tâches asynchrones)
  celery:
    build: .
    command: celery -A app.celery worker --loglevel=info
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=************************************************/pos_system
      - REDIS_URL=redis://cache:6379/0
    volumes:
      - ./instance:/app/instance
    networks:
      - pos-network
    depends_on:
      - database
      - cache
    restart: unless-stopped

  # Celery Beat (pour les tâches planifiées)
  celery-beat:
    build: .
    command: celery -A app.celery beat --loglevel=info
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=************************************************/pos_system
      - REDIS_URL=redis://cache:6379/0
    volumes:
      - ./instance:/app/instance
    networks:
      - pos-network
    depends_on:
      - database
      - cache
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:

networks:
  pos-network:
    driver: bridge