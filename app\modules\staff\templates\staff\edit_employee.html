{% extends 'base.html' %}
{% block title %}Modifier Employé - {{ employee.last_name }} {{ employee.first_name }}{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto">
  <!-- En-tête -->
  <div class="mb-6">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold">Modifier Employé</h1>
        <p class="text-slate-400 mt-2">{{ employee.last_name }} {{ employee.first_name }} ({{ employee.employee_code }})</p>
      </div>
      <a href="{{ url_for('staff.index') }}" class="bg-slate-700 hover:bg-slate-600 px-4 py-2 rounded-lg">
        ← Retour à la liste
      </a>
    </div>
  </div>

  <!-- Informations actuelles -->
  <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
    <div class="rounded-xl bg-slate-900 border border-slate-700 p-4">
      <div class="text-center">
        <div class="text-3xl mb-2">👤</div>
        <h3 class="font-semibold">{{ employee.last_name }} {{ employee.first_name }}</h3>
        <p class="text-sm text-slate-400">{{ employee.employee_code }}</p>
      </div>
    </div>
    
    <div class="rounded-xl bg-slate-900 border border-slate-700 p-4">
      <div class="text-center">
        <div class="text-3xl mb-2">{% if employee.is_active %}✅{% else %}🚫{% endif %}</div>
        <h3 class="font-semibold">Statut</h3>
        <p class="text-sm {% if employee.is_active %}text-green-400{% else %}text-red-400{% endif %}">
          {{ 'Actif' if employee.is_active else 'Inactif' }}
        </p>
      </div>
    </div>
    
    <div class="rounded-xl bg-slate-900 border border-slate-700 p-4">
      <div class="text-center">
        <div class="text-3xl mb-2">🏢</div>
        <h3 class="font-semibold">Département</h3>
        <p class="text-sm text-slate-400">{{ employee.department or 'Non assigné' }}</p>
      </div>
    </div>
  </div>

  <!-- Formulaire de modification -->
  <div class="rounded-xl bg-slate-900 border border-slate-700 p-6">
    <h2 class="text-xl font-semibold mb-6">✏️ Informations de l'employé</h2>
    
    <form method="post" class="space-y-6">
      {{ form.csrf_token }}
      
      <!-- Messages d'erreur -->
      {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
          {% for category, message in messages %}
            <div class="rounded-lg p-4 {% if category == 'error' %}bg-red-900 border border-red-700 text-red-100{% else %}bg-green-900 border border-green-700 text-green-100{% endif %}">
              {{ message }}
            </div>
          {% endfor %}
        {% endif %}
      {% endwith %}

      <!-- Informations personnelles -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-slate-300 mb-2">
              {{ form.first_name.label.text }} <span class="text-red-400">*</span>
            </label>
            {{ form.first_name(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 focus:ring-2 focus:ring-cyan-500 focus:border-transparent") }}
            {% if form.first_name.errors %}
              <p class="text-red-400 text-sm mt-1">{{ form.first_name.errors[0] }}</p>
            {% endif %}
          </div>
          
          <div>
            <label class="block text-sm font-medium text-slate-300 mb-2">
              {{ form.last_name.label.text }} <span class="text-red-400">*</span>
            </label>
            {{ form.last_name(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 focus:ring-2 focus:ring-cyan-500 focus:border-transparent") }}
            {% if form.last_name.errors %}
              <p class="text-red-400 text-sm mt-1">{{ form.last_name.errors[0] }}</p>
            {% endif %}
          </div>
          
          <div>
            <label class="block text-sm font-medium text-slate-300 mb-2">
              {{ form.employee_code.label.text }} <span class="text-red-400">*</span>
            </label>
            {{ form.employee_code(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 focus:ring-2 focus:ring-cyan-500 focus:border-transparent") }}
            {% if form.employee_code.errors %}
              <p class="text-red-400 text-sm mt-1">{{ form.employee_code.errors[0] }}</p>
            {% endif %}
          </div>
        </div>
        
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-slate-300 mb-2">
              {{ form.email.label.text }}
            </label>
            {{ form.email(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 focus:ring-2 focus:ring-cyan-500 focus:border-transparent") }}
            {% if form.email.errors %}
              <p class="text-red-400 text-sm mt-1">{{ form.email.errors[0] }}</p>
            {% endif %}
          </div>
          
          <div>
            <label class="block text-sm font-medium text-slate-300 mb-2">
              {{ form.phone.label.text }}
            </label>
            {{ form.phone(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 focus:ring-2 focus:ring-cyan-500 focus:border-transparent") }}
            {% if form.phone.errors %}
              <p class="text-red-400 text-sm mt-1">{{ form.phone.errors[0] }}</p>
            {% endif %}
          </div>
          
          <div>
            <label class="block text-sm font-medium text-slate-300 mb-2">
              {{ form.hire_date.label.text }}
            </label>
            {{ form.hire_date(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 focus:ring-2 focus:ring-cyan-500 focus:border-transparent") }}
            {% if form.hire_date.errors %}
              <p class="text-red-400 text-sm mt-1">{{ form.hire_date.errors[0] }}</p>
            {% endif %}
          </div>
        </div>
      </div>

      <!-- Informations professionnelles -->
      <div class="border-t border-slate-700 pt-6">
        <h3 class="text-lg font-semibold mb-4">💼 Informations professionnelles</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium text-slate-300 mb-2">
              {{ form.position.label.text }}
            </label>
            {{ form.position(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 focus:ring-2 focus:ring-cyan-500 focus:border-transparent") }}
            {% if form.position.errors %}
              <p class="text-red-400 text-sm mt-1">{{ form.position.errors[0] }}</p>
            {% endif %}
          </div>
          
          <div>
            <label class="block text-sm font-medium text-slate-300 mb-2">
              {{ form.department.label.text }}
            </label>
            {{ form.department(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 focus:ring-2 focus:ring-cyan-500 focus:border-transparent") }}
            {% if form.department.errors %}
              <p class="text-red-400 text-sm mt-1">{{ form.department.errors[0] }}</p>
            {% endif %}
          </div>
          
          <div>
            <label class="block text-sm font-medium text-slate-300 mb-2">
              {{ form.salary_cents.label.text }}
            </label>
            {{ form.salary_cents(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 focus:ring-2 focus:ring-cyan-500 focus:border-transparent") }}
            {% if form.salary_cents.errors %}
              <p class="text-red-400 text-sm mt-1">{{ form.salary_cents.errors[0] }}</p>
            {% endif %}
            <p class="text-xs text-slate-400 mt-1">Montant en centimes (ex: 250000 = 2500€)</p>
          </div>
          
          <div class="flex items-center justify-center">
            <div class="flex items-center">
              {{ form.is_active(class="w-4 h-4 text-cyan-600 bg-slate-800 border-slate-700 rounded focus:ring-cyan-500 focus:ring-2") }}
              <label class="ml-3 text-sm font-medium text-slate-300">
                {{ form.is_active.label.text }}
              </label>
            </div>
          </div>
        </div>
      </div>

      <!-- Actions -->
      <div class="flex items-center justify-between pt-6 border-t border-slate-700">
        <a href="{{ url_for('staff.index') }}" class="bg-slate-700 hover:bg-slate-600 px-6 py-2 rounded-lg">
          Annuler
        </a>
        <div class="flex space-x-3">
          <button type="button" onclick="confirmDelete()" class="bg-red-600 hover:bg-red-500 px-6 py-2 rounded-lg">
            🗑️ Supprimer
          </button>
          {{ form.submit(class="bg-cyan-600 hover:bg-cyan-500 px-6 py-2 rounded-lg font-medium") }}
        </div>
      </div>
    </form>
  </div>

  <!-- Statistiques de l'employé -->
  <div class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
    <div class="rounded-xl bg-slate-900 border border-slate-700 p-6">
      <h3 class="text-lg font-semibold mb-4">📊 Statistiques</h3>
      <div class="space-y-3">
        <div class="flex justify-between">
          <span class="text-slate-400">Shifts totaux:</span>
          <span class="font-medium">{{ employee.shifts|length }}</span>
        </div>
        <div class="flex justify-between">
          <span class="text-slate-400">Date d'embauche:</span>
          <span class="font-medium">{{ employee.hire_date.strftime('%d/%m/%Y') if employee.hire_date else 'Non définie' }}</span>
        </div>
        <div class="flex justify-between">
          <span class="text-slate-400">Ancienneté:</span>
          <span class="font-medium">
            {% if employee.hire_date %}
              {{ ((now() - employee.hire_date).days // 365) }} ans
            {% else %}
              -
            {% endif %}
          </span>
        </div>
      </div>
    </div>
    
    <div class="rounded-xl bg-slate-900 border border-slate-700 p-6">
      <h3 class="text-lg font-semibold mb-4">⚡ Actions rapides</h3>
      <div class="space-y-2">
        <a href="{{ url_for('staff.schedule') }}?employee={{ employee.id }}" class="block w-full bg-indigo-600 hover:bg-indigo-500 rounded-lg px-4 py-2 text-center">
          📅 Voir planning
        </a>
        <a href="{{ url_for('staff.timeclock') }}?employee={{ employee.id }}" class="block w-full bg-green-600 hover:bg-green-500 rounded-lg px-4 py-2 text-center">
          ⏰ Pointages
        </a>
        <button onclick="toggleEmployeeStatus()" class="block w-full {% if employee.is_active %}bg-yellow-600 hover:bg-yellow-500{% else %}bg-green-600 hover:bg-green-500{% endif %} rounded-lg px-4 py-2 text-center">
          {{ '🚫 Désactiver' if employee.is_active else '✅ Activer' }}
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Modal de confirmation de suppression -->
<div id="deleteModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
  <div class="bg-slate-900 border border-slate-700 rounded-xl p-6 max-w-md w-full mx-4">
    <h3 class="text-lg font-semibold mb-4">⚠️ Confirmer la suppression</h3>
    <p class="text-slate-300 mb-6">
      Êtes-vous sûr de vouloir supprimer cet employé ? Cette action est irréversible.
      <br><br>
      <strong>{{ employee.last_name }} {{ employee.first_name }}</strong> ({{ employee.employee_code }})
    </p>
    <div class="flex justify-end space-x-3">
      <button onclick="closeDeleteModal()" class="px-4 py-2 bg-slate-700 hover:bg-slate-600 rounded-lg">
        Annuler
      </button>
      <form method="post" action="{{ url_for('staff.delete_employee', id=employee.id) }}" class="inline">
        {{ csrf_token() }}
        <button class="px-4 py-2 bg-red-600 hover:bg-red-500 rounded-lg">
          Supprimer définitivement
        </button>
      </form>
    </div>
  </div>
</div>

<script>
function confirmDelete() {
  document.getElementById('deleteModal').classList.remove('hidden');
}

function closeDeleteModal() {
  document.getElementById('deleteModal').classList.add('hidden');
}

function toggleEmployeeStatus() {
  const form = document.createElement('form');
  form.method = 'POST';
  form.action = "{{ url_for('staff.toggle_employee_status', id=employee.id) }}";
  
  const csrfToken = document.createElement('input');
  csrfToken.type = 'hidden';
  csrfToken.name = 'csrf_token';
  csrfToken.value = "{{ csrf_token() }}";
  
  form.appendChild(csrfToken);
  document.body.appendChild(form);
  form.submit();
}

// Fermeture du modal avec Escape
document.addEventListener('keydown', function(e) {
  if (e.key === 'Escape') {
    closeDeleteModal();
  }
});
</script>
{% endblock %}