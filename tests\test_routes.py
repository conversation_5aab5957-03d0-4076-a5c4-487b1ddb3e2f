"""Tests des routes et vues de l'application POS"""
import unittest
import json
from tests import BaseTestCase

class RouteTestCase(BaseTestCase):
    """Tests pour les routes de l'application"""
    
    def login_user(self, username, password):
        """Helper pour connecter un utilisateur"""
        return self.client.post('/accounts/login', data=dict(
            username=username,
            password=password
        ), follow_redirects=True)
    
    def test_home_page(self):
        """Test de la page d'accueil"""
        response = self.client.get('/', follow_redirects=True)
        self.assertEqual(response.status_code, 200)
    
    def test_pos_routes(self):
        """Test des routes du module POS"""
        # Test de la page de vente
        response = self.client.get('/pos/sale', follow_redirects=True)
        self.assertEqual(response.status_code, 200)
        
        # Test de la page des commandes
        response = self.client.get('/pos/orders', follow_redirects=True)
        self.assertEqual(response.status_code, 200)
    
    def test_accounts_routes(self):
        """Test des routes du module Accounts"""
        # Test de la page de login
        response = self.client.get('/accounts/login')
        self.assertEqual(response.status_code, 200)
        
        # Test de la page de register
        response = self.client.get('/accounts/register')
        self.assertEqual(response.status_code, 200)
    
    def test_catalog_routes(self):
        """Test des routes du module Catalog"""
        # Test de la page des produits
        response = self.client.get('/catalog/products', follow_redirects=True)
        self.assertEqual(response.status_code, 200)
        
        # Test de la page des catégories
        response = self.client.get('/catalog/categories', follow_redirects=True)
        self.assertEqual(response.status_code, 200)
    
    def test_inventory_routes(self):
        """Test des routes du module Inventory"""
        # Test de la page des stocks
        response = self.client.get('/inventory/stock', follow_redirects=True)
        self.assertEqual(response.status_code, 200)
        
        # Test de la page des mouvements de stock
        response = self.client.get('/inventory/movements', follow_redirects=True)
        self.assertEqual(response.status_code, 200)

class APIRouteTestCase(BaseTestCase):
    """Tests pour les routes API"""
    
    def get_auth_token(self):
        """Helper pour obtenir un token d'authentification"""
        # Cette méthode serait implémentée pour tester les routes API
        # avec authentification JWT
        return "test_token"
    
    def test_api_auth_routes(self):
        """Test des routes d'authentification API"""
        # Test de la route de login API
        response = self.client.post('/api/v1/auth/login', 
                                  data=json.dumps({
                                      'username': 'test',
                                      'password': 'test'
                                  }),
                                  content_type='application/json')
        # On s'attend à ce que cela échoue car l'utilisateur n'existe pas
        self.assertIn(response.status_code, [200, 401])
    
    def test_api_business_routes(self):
        """Test des routes API pour les entreprises"""
        token = self.get_auth_token()
        headers = {'Authorization': f'Bearer {token}'}
        
        # Test de la route pour lister les entreprises
        response = self.client.get('/api/v1/businesses', 
                                  headers=headers,
                                  content_type='application/json')
        self.assertEqual(response.status_code, 200)

if __name__ == '__main__':
    unittest.main()