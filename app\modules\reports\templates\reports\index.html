{% extends 'base.html' %}
{% block title %}Rapports - Dashboard{% endblock %}

{% block head %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/date-fns@2.29.3/index.min.js"></script>
<style>
    .reports-dashboard {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 20px 0;
    }
    
    .stat-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        padding: 25px;
        text-align: center;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        border: none;
    }
    
    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0,0,0,0.2);
    }
    
    .stat-number {
        font-size: 2.5rem;
        font-weight: bold;
        color: #667eea;
        margin-bottom: 10px;
    }
    
    .stat-label {
        color: #666;
        font-size: 1rem;
        text-transform: uppercase;
        letter-spacing: 1px;
    }
    
    .reports-content {
        background: white;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        padding: 30px;
        margin-top: 30px;
    }
    
    .section-title {
        color: #333;
        font-size: 1.8rem;
        font-weight: 600;
        margin-bottom: 25px;
        border-bottom: 3px solid #667eea;
        padding-bottom: 10px;
    }
    
    .chart-container {
        position: relative;
        height: 300px;
        margin-bottom: 30px;
    }
    
    .btn-reports {
        background: linear-gradient(45deg, #667eea, #764ba2);
        border: none;
        color: white;
        padding: 12px 25px;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
        margin: 5px;
    }
    
    .btn-reports:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }
    
    .quick-actions {
        background: #f8f9ff;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 30px;
    }
    
    .execution-item {
        background: #f8f9ff;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 15px;
        border-left: 4px solid #667eea;
        transition: all 0.3s ease;
    }
    
    .execution-item:hover {
        background: #e8ecff;
        transform: translateX(5px);
    }
    
    .status-badge {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
    }
    
    .status-completed {
        background: #d4edda;
        color: #155724;
    }
    
    .status-failed {
        background: #f8d7da;
        color: #721c24;
    }
    
    .status-running {
        background: #fff3cd;
        color: #856404;
    }
    
    .template-item {
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 15px;
        transition: all 0.3s ease;
    }
    
    .template-item:hover {
        border-color: #667eea;
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.1);
    }
</style>
{% endblock %}

{% block content %}
<div class="reports-dashboard">
    <div class="container-fluid">
        <!-- Header avec titre et actions rapides -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="text-white text-center mb-4">
                    <i class="fas fa-chart-bar me-3"></i>
                    Dashboard Rapports
                </h1>
                
                <div class="quick-actions text-center">
                    <a href="{{ url_for('reports.quick_report') }}" class="btn-reports">
                        <i class="fas fa-bolt me-2"></i>Rapport Rapide
                    </a>
                    <a href="{{ url_for('reports.new_template') }}" class="btn-reports">
                        <i class="fas fa-plus me-2"></i>Nouveau Template
                    </a>
                    <a href="{{ url_for('reports.analytics_dashboard') }}" class="btn-reports">
                        <i class="fas fa-analytics me-2"></i>Analytics
                    </a>
                    <a href="{{ url_for('reports.list_schedules') }}" class="btn-reports">
                        <i class="fas fa-clock me-2"></i>Planifications
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Statistiques -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card">
                    <div class="stat-number">{{ stats.total_templates }}</div>
                    <div class="stat-label">Templates</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card">
                    <div class="stat-number">{{ stats.scheduled_reports }}</div>
                    <div class="stat-label">Planifiés</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card">
                    <div class="stat-number">{{ stats.executions_today }}</div>
                    <div class="stat-label">Aujourd'hui</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card">
                    <div class="stat-number">{{ stats.successful_executions }}</div>
                    <div class="stat-label">Réussis</div>
                </div>
            </div>
        </div>
        
        <!-- Contenu principal -->
        <div class="row">
            <!-- Graphique d'utilisation -->
            <div class="col-lg-8 mb-4">
                <div class="reports-content">
                    <h3 class="section-title">
                        <i class="fas fa-chart-line me-2"></i>
                        Tendance d'utilisation
                    </h3>
                    <div class="chart-container">
                        <canvas id="usageChart"></canvas>
                    </div>
                </div>
            </div>
            
            <!-- Templates populaires -->
            <div class="col-lg-4 mb-4">
                <div class="reports-content">
                    <h3 class="section-title">
                        <i class="fas fa-star me-2"></i>
                        Templates Populaires
                    </h3>
                    {% for template, count in popular_templates %}
                    <div class="template-item">
                        <h6 class="mb-2">
                            <a href="{{ url_for('reports.view_template', id=template.id) }}" class="text-decoration-none">
                                {{ template.name }}
                            </a>
                        </h6>
                        <small class="text-muted">
                            <i class="fas fa-play me-1"></i>
                            {{ count }} exécutions
                        </small>
                    </div>
                    {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-file-alt fa-3x mb-3"></i>
                        <p>Aucun template utilisé</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        
        <div class="row">
            <!-- Dernières exécutions -->
            <div class="col-lg-6 mb-4">
                <div class="reports-content">
                    <h3 class="section-title">
                        <i class="fas fa-history me-2"></i>
                        Dernières Exécutions
                    </h3>
                    {% for execution in recent_executions %}
                    <div class="execution-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">
                                    <a href="{{ url_for('reports.view_execution', id=execution.id) }}" class="text-decoration-none">
                                        {{ execution.template.name if execution.template else 'Template supprimé' }}
                                    </a>
                                </h6>
                                <small class="text-muted">
                                    <i class="fas fa-clock me-1"></i>
                                    {{ execution.executed_at.strftime('%d/%m/%Y %H:%M') }}
                                </small>
                            </div>
                            <span class="status-badge status-{{ execution.status.value }}">
                                {{ execution.status.value }}
                            </span>
                        </div>
                    </div>
                    {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-clock fa-3x mb-3"></i>
                        <p>Aucune exécution récente</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
            
            <!-- Planifications à venir -->
            <div class="col-lg-6 mb-4">
                <div class="reports-content">
                    <h3 class="section-title">
                        <i class="fas fa-calendar-alt me-2"></i>
                        Planifications à Venir
                    </h3>
                    {% for schedule in upcoming_schedules %}
                    <div class="execution-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">
                                    <a href="{{ url_for('reports.view_schedule', id=schedule.id) }}" class="text-decoration-none">
                                        {{ schedule.name }}
                                    </a>
                                </h6>
                                <small class="text-muted">
                                    <i class="fas fa-calendar me-1"></i>
                                    {{ schedule.next_run.strftime('%d/%m/%Y') if schedule.next_run else 'Non planifié' }}
                                </small>
                            </div>
                            <span class="badge bg-primary">{{ schedule.frequency.value if schedule.frequency else 'Manual' }}</span>
                        </div>
                    </div>
                    {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-calendar-plus fa-3x mb-3"></i>
                        <p>Aucune planification à venir</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Graphique d'utilisation des rapports
const ctx = document.getElementById('usageChart').getContext('2d');
const usageChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'],
        datasets: [{
            label: 'Exécutions de rapports',
            data: [12, 19, 3, 5, 2, 3, 9],
            borderColor: '#667eea',
            backgroundColor: 'rgba(102, 126, 234, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                grid: {
                    color: 'rgba(0,0,0,0.05)'
                }
            },
            x: {
                grid: {
                    display: false
                }
            }
        }
    }
});

// Actualisation automatique des données
setInterval(() => {
    // Ici on pourrait faire un appel AJAX pour mettre à jour les données
    console.log('Actualisation des données...');
}, 30000); // Toutes les 30 secondes
</script>
{% endblock %}


