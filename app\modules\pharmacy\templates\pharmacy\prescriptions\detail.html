{% extends "base.html" %}

{% block title %}Ordonnance {{ prescription.prescription_number }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Ordonnance {{ prescription.prescription_number }}</h1>
                <div class="btn-group" role="group">
                    <a href="{{ url_for('pharmacy.prescriptions') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Retour
                    </a>
                    {% if prescription.status.value in ['pending', 'validated'] %}
                        <a href="{{ url_for('pharmacy.edit_prescription', prescription_id=prescription.id) }}" 
                           class="btn btn-outline-warning">
                            <i class="fas fa-edit"></i> Modifier
                        </a>
                    {% endif %}
                    {% if prescription.status.value in ['validated', 'partially_dispensed'] %}
                        <a href="{{ url_for('pharmacy.dispense_prescription', prescription_id=prescription.id) }}" 
                           class="btn btn-success">
                            <i class="fas fa-pills"></i> Délivrer
                        </a>
                    {% endif %}
                    <button type="button" class="btn btn-outline-primary" onclick="window.print()">
                        <i class="fas fa-print"></i> Imprimer
                    </button>
                </div>
            </div>

            <div class="row">
                <!-- Informations principales -->
                <div class="col-lg-8">
                    <!-- En-tête ordonnance -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">Informations de l'Ordonnance</h5>
                                {% set status_colors = {
                                    'pending': 'warning',
                                    'validated': 'info',
                                    'dispensed': 'success',
                                    'partially_dispensed': 'primary',
                                    'cancelled': 'danger',
                                    'expired': 'secondary'
                                } %}
                                {% set status_labels = {
                                    'pending': 'En attente',
                                    'validated': 'Validée',
                                    'dispensed': 'Délivrée',
                                    'partially_dispensed': 'Partiellement délivrée',
                                    'cancelled': 'Annulée',
                                    'expired': 'Expirée'
                                } %}
                                <span class="badge bg-{{ status_colors.get(prescription.status.value, 'secondary') }} fs-6">
                                    {{ status_labels.get(prescription.status.value, prescription.status.value) }}
                                </span>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-borderless table-sm">
                                        <tr>
                                            <td class="fw-bold">Numéro :</td>
                                            <td>{{ prescription.prescription_number }}</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Date de prescription :</td>
                                            <td>{{ prescription.prescription_date.strftime('%d/%m/%Y') }}</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Date limite :</td>
                                            <td>
                                                {{ prescription.validity_date.strftime('%d/%m/%Y') }}
                                                {% if prescription.is_expired %}
                                                    <span class="badge bg-danger ms-2">Expirée</span>
                                                {% else %}
                                                    <span class="text-muted ms-2">
                                                        ({{ prescription.days_until_expiry }} jours restants)
                                                    </span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-borderless table-sm">
                                        <tr>
                                            <td class="fw-bold">Créée le :</td>
                                            <td>{{ prescription.created_at.strftime('%d/%m/%Y à %H:%M') }}</td>
                                        </tr>
                                        {% if prescription.validated_at %}
                                        <tr>
                                            <td class="fw-bold">Validée le :</td>
                                            <td>
                                                {{ prescription.validated_at.strftime('%d/%m/%Y à %H:%M') }}
                                                {% if prescription.validated_by %}
                                                    <br><small class="text-muted">par {{ prescription.validated_by.username }}</small>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endif %}
                                        {% if prescription.dispensed_at %}
                                        <tr>
                                            <td class="fw-bold">Délivrée le :</td>
                                            <td>
                                                {{ prescription.dispensed_at.strftime('%d/%m/%Y à %H:%M') }}
                                                {% if prescription.dispensed_by %}
                                                    <br><small class="text-muted">par {{ prescription.dispensed_by.username }}</small>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endif %}
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Patient et médecin -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Patient</h5>
                                </div>
                                <div class="card-body">
                                    <h6 class="fw-bold">{{ prescription.customer.name }}</h6>
                                    {% if prescription.customer.email %}
                                        <p class="mb-1"><i class="fas fa-envelope text-muted me-2"></i>{{ prescription.customer.email }}</p>
                                    {% endif %}
                                    {% if prescription.customer.phone %}
                                        <p class="mb-1"><i class="fas fa-phone text-muted me-2"></i>{{ prescription.customer.phone }}</p>
                                    {% endif %}
                                    {% if prescription.patient_weight %}
                                        <p class="mb-1"><i class="fas fa-weight text-muted me-2"></i>{{ prescription.patient_weight }} kg</p>
                                    {% endif %}
                                    {% if prescription.patient_allergies %}
                                        <div class="alert alert-warning mt-3">
                                            <strong><i class="fas fa-exclamation-triangle me-2"></i>Allergies :</strong><br>
                                            {{ prescription.patient_allergies }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Médecin Prescripteur</h5>
                                </div>
                                <div class="card-body">
                                    <h6 class="fw-bold">{{ prescription.doctor_name }}</h6>
                                    {% if prescription.doctor_specialty %}
                                        <p class="mb-1"><i class="fas fa-stethoscope text-muted me-2"></i>{{ prescription.doctor_specialty }}</p>
                                    {% endif %}
                                    {% if prescription.doctor_rpps %}
                                        <p class="mb-1"><i class="fas fa-id-card text-muted me-2"></i>RPPS: {{ prescription.doctor_rpps }}</p>
                                    {% endif %}
                                    {% if prescription.doctor_notes %}
                                        <div class="mt-3">
                                            <strong>Notes du médecin :</strong><br>
                                            <em>{{ prescription.doctor_notes }}</em>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Articles de l'ordonnance -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Médicaments Prescrits</h5>
                        </div>
                        <div class="card-body">
                            {% if prescription.items %}
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Médicament</th>
                                                <th>Posologie</th>
                                                <th>Quantité</th>
                                                <th>Prix</th>
                                                <th>Statut</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for item in prescription.items %}
                                            <tr>
                                                <td>
                                                    <div class="d-flex flex-column">
                                                        <span class="fw-bold">{{ item.medication.name }}</span>
                                                        {% if item.medication.generic_name %}
                                                            <small class="text-muted">DCI: {{ item.medication.generic_name }}</small>
                                                        {% endif %}
                                                        {% if item.medication.dosage %}
                                                            <small class="text-muted">{{ item.medication.dosage }}</small>
                                                        {% endif %}
                                                        {% if item.medication.medication_class.value == 'controlled' %}
                                                            <span class="badge bg-warning text-dark mt-1">Stupéfiant</span>
                                                        {% elif item.medication.medication_class.value == 'prescription' %}
                                                            <span class="badge bg-info mt-1">Sur ordonnance</span>
                                                        {% endif %}
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="d-flex flex-column">
                                                        <span>{{ item.dosage_instructions }}</span>
                                                        {% if item.duration_days %}
                                                            <small class="text-muted">Durée: {{ item.duration_days }} jours</small>
                                                        {% endif %}
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="d-flex flex-column">
                                                        <span>{{ item.dispensed_quantity }}/{{ item.prescribed_quantity }}</span>
                                                        {% if item.remaining_quantity > 0 %}
                                                            <small class="text-warning">{{ item.remaining_quantity }} restant(s)</small>
                                                        {% endif %}
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="d-flex flex-column">
                                                        <span>{{ "%.2f"|format(item.total_price_cents / 100) }}€</span>
                                                        {% if item.reimbursed_amount_cents > 0 %}
                                                            <small class="text-success">
                                                                Remb: {{ "%.2f"|format(item.reimbursed_amount_cents / 100) }}€
                                                            </small>
                                                        {% endif %}
                                                    </div>
                                                </td>
                                                <td>
                                                    {% if item.is_fully_dispensed %}
                                                        <span class="badge bg-success">Délivré</span>
                                                    {% elif item.dispensed_quantity > 0 %}
                                                        <span class="badge bg-warning">Partiel</span>
                                                    {% else %}
                                                        <span class="badge bg-secondary">En attente</span>
                                                    {% endif %}
                                                    
                                                    {% if item.substitute_medication_id %}
                                                        <span class="badge bg-info mt-1">Substitué</span>
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    <button type="button" class="btn btn-sm btn-outline-primary" 
                                                            data-bs-toggle="modal" data-bs-target="#itemModal{{ item.id }}">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            {% else %}
                                <div class="text-center py-4">
                                    <i class="fas fa-pills fa-2x text-muted mb-3"></i>
                                    <p class="text-muted">Aucun médicament prescrit</p>
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Notes du pharmacien -->
                    {% if prescription.pharmacist_notes %}
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Notes du Pharmacien</h5>
                        </div>
                        <div class="card-body">
                            <p class="mb-0">{{ prescription.pharmacist_notes }}</p>
                        </div>
                    </div>
                    {% endif %}
                </div>

                <!-- Panneau latéral -->
                <div class="col-lg-4">
                    <!-- Résumé financier -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Résumé Financier</h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-borderless table-sm">
                                <tr>
                                    <td>Nombre d'articles :</td>
                                    <td class="text-end fw-bold">{{ prescription.total_items }}</td>
                                </tr>
                                <tr>
                                    <td>Montant total :</td>
                                    <td class="text-end fw-bold">{{ "%.2f"|format(prescription.total_amount_cents / 100) }}€</td>
                                </tr>
                                <tr>
                                    <td>Montant remboursé :</td>
                                    <td class="text-end text-success fw-bold">{{ "%.2f"|format(prescription.reimbursed_amount_cents / 100) }}€</td>
                                </tr>
                                <tr class="border-top">
                                    <td><strong>Reste à charge :</strong></td>
                                    <td class="text-end fw-bold text-primary">
                                        {{ "%.2f"|format((prescription.total_amount_cents - prescription.reimbursed_amount_cents) / 100) }}€
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- Actions rapides -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Actions Rapides</h5>
                        </div>
                        <div class="card-body">
                            {% if prescription.status.value == 'pending' %}
                                <button type="button" class="btn btn-info w-100 mb-2" onclick="validatePrescription()">
                                    <i class="fas fa-check"></i> Valider l'ordonnance
                                </button>
                            {% endif %}
                            
                            {% if prescription.status.value in ['validated', 'partially_dispensed'] %}
                                <a href="{{ url_for('pharmacy.dispense_prescription', prescription_id=prescription.id) }}" 
                                   class="btn btn-success w-100 mb-2">
                                    <i class="fas fa-pills"></i> Délivrer les médicaments
                                </a>
                            {% endif %}
                            
                            <button type="button" class="btn btn-outline-primary w-100 mb-2" onclick="checkInteractions()">
                                <i class="fas fa-exclamation-triangle"></i> Vérifier les interactions
                            </button>
                            
                            <button type="button" class="btn btn-outline-info w-100 mb-2" onclick="checkCompliance()">
                                <i class="fas fa-shield-alt"></i> Contrôle de conformité
                            </button>
                            
                            {% if prescription.status.value in ['pending', 'validated'] %}
                                <button type="button" class="btn btn-outline-danger w-100" 
                                        onclick="cancelPrescription()" data-bs-toggle="modal" data-bs-target="#cancelModal">
                                    <i class="fas fa-times"></i> Annuler l'ordonnance
                                </button>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Alertes et contrôles -->
                    <div id="alerts-panel" class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Alertes et Contrôles</h5>
                        </div>
                        <div class="card-body">
                            <div id="alerts-content">
                                <p class="text-muted text-center">Cliquez sur "Vérifier les interactions" ou "Contrôle de conformité" pour voir les alertes.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modals pour les détails des articles -->
{% for item in prescription.items %}
<div class="modal fade" id="itemModal{{ item.id }}" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ item.medication.name }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-6"><strong>Quantité prescrite :</strong></div>
                    <div class="col-6">{{ item.prescribed_quantity }}</div>
                </div>
                <div class="row">
                    <div class="col-6"><strong>Quantité délivrée :</strong></div>
                    <div class="col-6">{{ item.dispensed_quantity }}</div>
                </div>
                <div class="row">
                    <div class="col-6"><strong>Posologie :</strong></div>
                    <div class="col-6">{{ item.dosage_instructions }}</div>
                </div>
                {% if item.duration_days %}
                <div class="row">
                    <div class="col-6"><strong>Durée :</strong></div>
                    <div class="col-6">{{ item.duration_days }} jours</div>
                </div>
                {% endif %}
                {% if item.pharmacist_notes %}
                <div class="row">
                    <div class="col-12"><strong>Notes du pharmacien :</strong></div>
                    <div class="col-12">{{ item.pharmacist_notes }}</div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endfor %}

<!-- Modal d'annulation -->
<div class="modal fade" id="cancelModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Annuler l'ordonnance</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir annuler cette ordonnance ?</p>
                <p class="text-warning"><i class="fas fa-exclamation-triangle"></i> Cette action ne peut pas être annulée.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                <form method="POST" action="{{ url_for('pharmacy.delete_prescription', prescription_id=prescription.id) }}" style="display: inline;">
                    <button type="submit" class="btn btn-danger">Annuler l'ordonnance</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function validatePrescription() {
    if (confirm('Valider cette ordonnance ?')) {
        fetch(`/pharmacy/api/prescriptions/{{ prescription.id }}/validate`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.message) {
                alert(data.message);
                location.reload();
            } else {
                alert(data.error || 'Erreur lors de la validation');
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur lors de la validation');
        });
    }
}

function checkInteractions() {
    const medicationIds = [{% for item in prescription.items %}{{ item.medication_id_fk }}{% if not loop.last %},{% endif %}{% endfor %}];
    
    fetch('/pharmacy/api/interactions/check', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
        },
        body: JSON.stringify({ medication_ids: medicationIds })
    })
    .then(response => response.json())
    .then(data => {
        displayAlerts('interactions', data.interactions);
    })
    .catch(error => {
        console.error('Erreur:', error);
        alert('Erreur lors de la vérification des interactions');
    });
}

function checkCompliance() {
    fetch(`/pharmacy/api/compliance/check/{{ prescription.id }}`)
        .then(response => response.json())
        .then(data => {
            displayAlerts('compliance', data.compliance_results);
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur lors du contrôle de conformité');
        });
}

function displayAlerts(type, results) {
    const alertsContent = document.getElementById('alerts-content');
    
    if (results.length === 0) {
        alertsContent.innerHTML = `
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> 
                ${type === 'interactions' ? 'Aucune interaction détectée' : 'Ordonnance conforme'}
            </div>
        `;
        return;
    }
    
    let html = '';
    results.forEach(result => {
        const alertClass = type === 'interactions' 
            ? (result.contraindicated ? 'danger' : result.severity === 'major' ? 'warning' : 'info')
            : (result.passed ? 'success' : result.severity_level === 'critical' ? 'danger' : 'warning');
        
        html += `
            <div class="alert alert-${alertClass}">
                <strong>${result.description || result.severity}</strong><br>
                <small>${result.recommendations || result.failure_reason || ''}</small>
            </div>
        `;
    });
    
    alertsContent.innerHTML = html;
}
</script>
{% endblock %}