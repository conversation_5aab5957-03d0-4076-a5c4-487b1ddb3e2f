/**
 * Module Ingredients - Gestion des ingrédients et recettes
 * Fonctionnalités principales : CRUD, calculs de coûts, gestion des stocks
 */

class IngredientsManager {
    constructor() {
        this.initializeEventListeners();
        this.initializeSearch();
        this.initializeCharts();
    }

    /**
     * Initialise tous les event listeners
     */
    initializeEventListeners() {
        // Ne pas gérer les onglets ici car ils sont gérés dans le template
        // pour éviter les conflits

        // Gestion des formulaires
        this.setupFormHandlers();

        // Gestion des modals
        this.setupModalHandlers();

        // Gestion des actions (edit, delete, view)
        this.setupActionHandlers();
    }

    /**
     * Initialise la recherche
     */
    initializeSearch() {
        // Recherche d'ingrédients
        const ingredientSearch = document.getElementById('ingredientSearch');
        if (ingredientSearch) {
            ingredientSearch.addEventListener('input', (e) => {
                this.filterIngredients(e.target.value);
            });
        }

        // Recherche de recettes
        const recipeSearch = document.querySelector('input[placeholder*="Rechercher une recette"]');
        if (recipeSearch) {
            recipeSearch.addEventListener('input', (e) => {
                this.filterRecipes(e.target.value);
            });
        }
    }

    /**
     * Initialise les graphiques
     */
    initializeCharts() {
        // Les graphiques sont initialisés dans le template principal
        // Cette méthode peut être utilisée pour des mises à jour dynamiques
        // Ne pas initialiser ici pour éviter les conflits
    }

    /**
     * Configure les gestionnaires de formulaires
     */
    setupFormHandlers() {
        // Formulaire d'ajout d'ingrédient
        const addIngredientForm = document.getElementById('addIngredientForm');
        if (addIngredientForm) {
            addIngredientForm.addEventListener('submit', (e) => {
                this.handleAddIngredient(e);
            });
        }

        // Formulaire d'ajout de recette
        const addRecipeForm = document.getElementById('addRecipeForm');
        if (addRecipeForm) {
            addRecipeForm.addEventListener('submit', (e) => {
                this.handleAddRecipe(e);
            });
        }

        // Formulaire d'ajout de catégorie
        const addCategoryForm = document.getElementById('addCategoryForm');
        if (addCategoryForm) {
            addCategoryForm.addEventListener('submit', (e) => {
                this.handleAddCategory(e);
            });
        }
    }

    /**
     * Configure les gestionnaires de modals
     */
    setupModalHandlers() {
        // Gestion de la fermeture des modals en cliquant sur le fond
        const modals = document.querySelectorAll('[id$="Modal"]');
        modals.forEach(modal => {
            const backdrop = modal.querySelector('.absolute.inset-0');
            if (backdrop) {
                backdrop.addEventListener('click', () => {
                    this.closeModal(modal.id);
                });
            }
        });
    }

    /**
     * Configure les gestionnaires d'actions
     */
    setupActionHandlers() {
        // Les actions sont gérées par des fonctions globales pour la compatibilité
        // avec les onclick des boutons
    }

    /**
     * Gère le changement d'onglet
     */
    handleTabChange(event) {
        // La gestion des onglets est déjà implémentée dans le template
        // Cette méthode peut être utilisée pour des actions supplémentaires
        // Ne pas gérer ici pour éviter les conflits
    }

    /**
     * Filtre les ingrédients
     */
    filterIngredients(searchTerm) {
        const table = document.querySelector('#tab-ingredients table');
        if (!table) return;

        const rows = table.querySelectorAll('tbody tr');
        const searchLower = searchTerm.toLowerCase();

        rows.forEach(row => {
            const nameCell = row.querySelector('td:first-child .text-sm.font-medium');
            const skuCell = row.querySelector('td:nth-child(3) code');
            const categoryCell = row.querySelector('td:nth-child(2) .rounded-full');

            const name = nameCell ? nameCell.textContent.toLowerCase() : '';
            const sku = skuCell ? skuCell.textContent.toLowerCase() : '';
            const category = categoryCell ? categoryCell.textContent.toLowerCase() : '';

            if (name.includes(searchLower) || sku.includes(searchLower) || category.includes(searchLower)) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    }

    /**
     * Filtre les recettes
     */
    filterRecipes(searchTerm) {
        const grid = document.querySelector('#tab-recipes .grid.grid-cols-1');
        if (!grid) return;

        const cards = grid.querySelectorAll('.bg-slate-800');
        const searchLower = searchTerm.toLowerCase();

        cards.forEach(card => {
            const nameElement = card.querySelector('.font-semibold');
            const descriptionElement = card.querySelector('.text-sm.text-slate-400');
            
            const name = nameElement ? nameElement.textContent.toLowerCase() : '';
            const description = descriptionElement ? descriptionElement.textContent.toLowerCase() : '';

            if (name.includes(searchLower) || description.includes(searchLower)) {
                card.closest('.grid > div').style.display = '';
            } else {
                card.closest('.grid > div').style.display = 'none';
            }
        });
    }

    /**
     * Gère l'ajout d'un ingrédient
     */
    async handleAddIngredient(event) {
        event.preventDefault();
        
        const form = event.target;
        const formData = new FormData(form);
        
        try {
            const response = await fetch(form.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            });

            if (response.ok) {
                const result = await response.json();
                if (result.success) {
                    this.showNotification('Ingrédient ajouté avec succès!', 'success');
                    this.closeModal('addIngredientModal');
                    // Recharger la page pour afficher le nouvel ingrédient
                    location.reload();
                } else {
                    this.showNotification(result.message || 'Erreur lors de l\'ajout', 'error');
                }
            } else {
                // Gérer les erreurs de validation du formulaire
                const errorText = await response.text();
                console.error('Erreur de validation:', errorText);
                this.showNotification('Erreur lors de l\'ajout de l\'ingrédient: ' + errorText, 'error');
            }
        } catch (error) {
            console.error('Erreur:', error);
            this.showNotification('Erreur de connexion', 'error');
        }
    }

    /**
     * Gère l'ajout d'une recette
     */
    async handleAddRecipe(event) {
        event.preventDefault();
        
        const form = event.target;
        const formData = new FormData(form);
        
        try {
            const response = await fetch(form.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            });

            if (response.ok) {
                const result = await response.json();
                if (result.success) {
                    this.showNotification('Recette ajoutée avec succès!', 'success');
                    this.closeModal('addRecipeModal');
                    // Recharger la page pour afficher la nouvelle recette
                    location.reload();
                } else {
                    this.showNotification(result.message || 'Erreur lors de l\'ajout', 'error');
                }
            } else {
                // Gérer les erreurs de validation du formulaire
                const errorText = await response.text();
                console.error('Erreur de validation:', errorText);
                this.showNotification('Erreur lors de l\'ajout de la recette: ' + errorText, 'error');
            }
        } catch (error) {
            console.error('Erreur:', error);
            this.showNotification('Erreur de connexion', 'error');
        }
    }

    /**
     * Gère l'ajout d'une catégorie
     */
    async handleAddCategory(event) {
        event.preventDefault();
        
        const form = event.target;
        const formData = new FormData(form);
        
        try {
            // Utiliser l'API endpoint pour la création de catégorie
            const response = await fetch('/ingredients/api/categories', {
                method: 'POST',
                body: JSON.stringify(Object.fromEntries(formData)),
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            });

            if (response.ok) {
                const result = await response.json();
                if (result.success) {
                    this.showNotification('Catégorie ajoutée avec succès!', 'success');
                    this.closeModal('addCategoryModal');
                    // Recharger la page pour afficher la nouvelle catégorie
                    location.reload();
                } else {
                    this.showNotification(result.message || 'Erreur lors de l\'ajout', 'error');
                }
            } else {
                // Gérer les erreurs de validation du formulaire
                const errorResult = await response.json();
                console.error('Erreur de validation:', errorResult);
                const errorMessage = errorResult.errors ? 
                    Object.values(errorResult.errors).flat().join(', ') : 
                    errorResult.message;
                this.showNotification('Erreur lors de l\'ajout de la catégorie: ' + errorMessage, 'error');
            }
        } catch (error) {
            console.error('Erreur:', error);
            this.showNotification('Erreur de connexion', 'error');
        }
    }

    /**
     * Charge les données des coûts
     */
    async loadCostsData() {
        try {
            const response = await fetch('/ingredients/api/costs-data');
            if (response.ok) {
                const data = await response.json();
                this.updateCostsCharts(data);
            }
        } catch (error) {
            console.error('Erreur lors du chargement des données de coûts:', error);
        }
    }

    /**
     * Met à jour les graphiques de coûts
     */
    updateCostsCharts(data) {
        // Mise à jour des KPIs
        if (data.total_stock_value !== undefined) {
            const element = document.querySelector('[data-kpi="total-stock-value"]');
            if (element) {
                element.textContent = (data.total_stock_value / 100).toFixed(2) + ' €';
            }
        }

        // Mise à jour des graphiques Chart.js si disponibles
        if (window.categoryChart && data.category_data) {
            window.categoryChart.data.datasets[0].data = data.category_data;
            window.categoryChart.update();
        }

        if (window.costChart && data.cost_data) {
            window.costChart.data.datasets[0].data = data.cost_data;
            window.costChart.update();
        }
    }

    /**
     * Réinitialise les formulaires des modals
     */
    resetModalForms(modalId) {
        const modal = document.getElementById(modalId);
        if (!modal) return;

        const forms = modal.querySelectorAll('form');
        forms.forEach(form => {
            form.reset();
            form.classList.remove('was-validated');
        });

        // Réinitialiser les tableaux d'ingrédients dans les modals de recette
        const tbody = modal.querySelector('#recipeIngredientsBody');
        if (tbody) {
            tbody.innerHTML = '';
            if (typeof ingredientRowCounter !== 'undefined') {
                ingredientRowCounter = 0;
                if (typeof addIngredientRow === 'function') {
                    addIngredientRow();
                }
            }
        }
    }

    /**
     * Ferme un modal
     */
    closeModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('hidden');
            this.resetModalForms(modalId);
        }
    }

    /**
     * Affiche une notification
     */
    showNotification(message, type = 'info') {
        // Créer un élément de notification
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 px-4 py-2 rounded-lg shadow-lg text-white ${
            type === 'success' ? 'bg-green-600' : 
            type === 'error' ? 'bg-red-600' : 
            type === 'warning' ? 'bg-yellow-600' : 'bg-blue-600'
        }`;
        notification.textContent = message;
        notification.style.transition = 'opacity 0.3s ease';
        
        // Ajouter à la page
        document.body.appendChild(notification);
        
        // Supprimer après 5 secondes
        setTimeout(() => {
            notification.style.opacity = '0';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 5000);
    }
}

// Initialiser le gestionnaire d'ingrédients quand le DOM est chargé
document.addEventListener('DOMContentLoaded', function() {
    window.ingredientsManager = new IngredientsManager();
});