{% extends "base.html" %}

{% block title %}Délivrance - {{ prescription.prescription_number }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Délivrance - Ordonnance {{ prescription.prescription_number }}</h1>
                <a href="{{ url_for('pharmacy.prescription_detail', prescription_id=prescription.id) }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> Retour
                </a>
            </div>

            {% if critical_issues %}
            <div class="alert alert-danger">
                <h5><i class="fas fa-exclamation-triangle"></i> Problèmes critiques détectés</h5>
                <ul class="mb-0">
                    {% for issue in critical_issues %}
                    <li><strong>{{ issue.description }}</strong> - {{ issue.failure_reason }}</li>
                    {% endfor %}
                </ul>
                <p class="mt-2 mb-0"><strong>Recommandation :</strong> Résolvez ces problèmes avant de délivrer l'ordonnance.</p>
            </div>
            {% endif %}

            <div class="row">
                <!-- Formulaire de délivrance -->
                <div class="col-lg-8">
                    <form method="POST">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Médicaments à délivrer</h5>
                            </div>
                            <div class="card-body">
                                {% for item in prescription.items %}
                                <div class="card mb-3 {% if item.medication.is_low_stock %}border-warning{% endif %}">
                                    <div class="card-body">
                                        <div class="row align-items-center">
                                            <div class="col-md-4">
                                                <h6 class="fw-bold mb-1">{{ item.medication.name }}</h6>
                                                {% if item.medication.generic_name %}
                                                    <small class="text-muted">DCI: {{ item.medication.generic_name }}</small><br>
                                                {% endif %}
                                                {% if item.medication.dosage %}
                                                    <small class="text-muted">{{ item.medication.dosage }}</small>
                                                {% endif %}
                                                
                                                <!-- Badges de classification -->
                                                <div class="mt-2">
                                                    {% if item.medication.medication_class.value == 'controlled' %}
                                                        <span class="badge bg-warning text-dark">Stupéfiant</span>
                                                    {% elif item.medication.medication_class.value == 'prescription' %}
                                                        <span class="badge bg-info">Sur ordonnance</span>
                                                    {% endif %}
                                                    
                                                    {% if item.medication.is_low_stock %}
                                                        <span class="badge bg-warning">Stock faible</span>
                                                    {% endif %}
                                                </div>
                                            </div>
                                            
                                            <div class="col-md-3">
                                                <div class="text-center">
                                                    <label class="form-label small">Posologie</label>
                                                    <p class="mb-0 small">{{ item.dosage_instructions }}</p>
                                                    {% if item.duration_days %}
                                                        <small class="text-muted">{{ item.duration_days }} jours</small>
                                                    {% endif %}
                                                </div>
                                            </div>
                                            
                                            <div class="col-md-2">
                                                <div class="text-center">
                                                    <label class="form-label small">Stock disponible</label>
                                                    <p class="mb-0 fw-bold {% if item.medication.current_stock < item.remaining_quantity %}text-danger{% endif %}">
                                                        {{ item.medication.current_stock }}
                                                    </p>
                                                    <small class="text-muted">{{ item.medication.unit_of_measure }}</small>
                                                </div>
                                            </div>
                                            
                                            <div class="col-md-3">
                                                <label class="form-label small">Quantité à délivrer</label>
                                                <div class="input-group input-group-sm">
                                                    <span class="input-group-text">{{ item.dispensed_quantity }}/</span>
                                                    <input type="number" 
                                                           class="form-control" 
                                                           name="dispensed_{{ item.id }}"
                                                           value="{{ item.remaining_quantity }}"
                                                           min="0" 
                                                           max="{{ min(item.remaining_quantity, item.medication.current_stock) }}"
                                                           {% if item.medication.current_stock == 0 %}disabled{% endif %}>
                                                    <span class="input-group-text">{{ item.prescribed_quantity }}</span>
                                                </div>
                                                <small class="text-muted">
                                                    Prix: {{ "%.2f"|format(item.unit_price_cents / 100) }}€ / unité
                                                </small>
                                            </div>
                                        </div>
                                        
                                        <!-- Messages d'alerte pour cet article -->
                                        {% if item.medication.current_stock < item.remaining_quantity %}
                                            <div class="alert alert-warning mt-3 mb-0">
                                                <i class="fas fa-exclamation-triangle"></i>
                                                Stock insuffisant : {{ item.medication.current_stock }} disponible(s), 
                                                {{ item.remaining_quantity }} demandé(s)
                                            </div>
                                        {% endif %}
                                        
                                        {% if item.substitution_allowed and item.medication.current_stock == 0 %}
                                            <div class="alert alert-info mt-3 mb-0">
                                                <i class="fas fa-info-circle"></i>
                                                Substitution autorisée - Rechercher un médicament équivalent
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>

                        <!-- Résumé des contrôles -->
                        {% if interactions or compliance_results or allergy_results %}
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Résumé des Contrôles</h5>
                            </div>
                            <div class="card-body">
                                <!-- Interactions -->
                                {% if interactions %}
                                <div class="mb-3">
                                    <h6>Interactions médicamenteuses détectées :</h6>
                                    {% for interaction in interactions %}
                                    <div class="alert {% if interaction.contraindicated %}alert-danger{% elif interaction.severity.value == 'major' %}alert-warning{% else %}alert-info{% endif %} py-2">
                                        <strong>{{ interaction.severity.value|title }} :</strong> {{ interaction.description }}
                                        {% if interaction.recommendations %}
                                            <br><small><strong>Recommandation :</strong> {{ interaction.recommendations }}</small>
                                        {% endif %}
                                    </div>
                                    {% endfor %}
                                </div>
                                {% endif %}

                                <!-- Conformité -->
                                {% if compliance_results %}
                                <div class="mb-3">
                                    <h6>Contrôles de conformité :</h6>
                                    {% for result in compliance_results %}
                                    <div class="alert {% if result.passed %}alert-success{% elif result.severity_level == 'critical' %}alert-danger{% else %}alert-warning{% endif %} py-2">
                                        <strong>{{ result.check_type|replace('_', ' ')|title }} :</strong> 
                                        {% if result.passed %}✓ Conforme{% else %}✗ {{ result.failure_reason }}{% endif %}
                                        {% if result.recommendations %}
                                            <br><small><strong>Recommandation :</strong> {{ result.recommendations }}</small>
                                        {% endif %}
                                    </div>
                                    {% endfor %}
                                </div>
                                {% endif %}

                                <!-- Allergies -->
                                {% if allergy_results %}
                                <div class="mb-3">
                                    <h6>Alertes allergies :</h6>
                                    {% for result in allergy_results %}
                                    <div class="alert alert-danger py-2">
                                        <strong>⚠️ {{ result.description }} :</strong> {{ result.failure_reason }}
                                        <br><small><strong>Action requise :</strong> {{ result.recommendations }}</small>
                                    </div>
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        {% endif %}

                        <!-- Boutons d'action -->
                        <div class="d-flex justify-content-end gap-2 mb-4">
                            <a href="{{ url_for('pharmacy.prescription_detail', prescription_id=prescription.id) }}" 
                               class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i> Annuler
                            </a>
                            {% if not critical_issues %}
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-pills"></i> Délivrer les médicaments
                            </button>
                            {% else %}
                            <button type="button" class="btn btn-success" disabled title="Résolvez les problèmes critiques avant de délivrer">
                                <i class="fas fa-pills"></i> Délivrer les médicaments
                            </button>
                            {% endif %}
                        </div>
                    </form>
                </div>

                <!-- Panneau d'information -->
                <div class="col-lg-4">
                    <!-- Informations patient -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Patient</h5>
                        </div>
                        <div class="card-body">
                            <h6 class="fw-bold">{{ prescription.customer.name }}</h6>
                            {% if prescription.customer.phone %}
                                <p class="mb-1"><i class="fas fa-phone text-muted me-2"></i>{{ prescription.customer.phone }}</p>
                            {% endif %}
                            {% if prescription.patient_weight %}
                                <p class="mb-1"><i class="fas fa-weight text-muted me-2"></i>{{ prescription.patient_weight }} kg</p>
                            {% endif %}
                            
                            <!-- Allergies connues -->
                            {% if prescription.customer.allergies %}
                            <div class="alert alert-warning mt-3">
                                <strong><i class="fas fa-exclamation-triangle me-2"></i>Allergies connues :</strong>
                                <ul class="mb-0 mt-2">
                                    {% for allergy in prescription.customer.allergies %}
                                    <li>{{ allergy.allergen_name }} ({{ allergy.severity }})</li>
                                    {% endfor %}
                                </ul>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Résumé ordonnance -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Résumé Ordonnance</h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-borderless table-sm">
                                <tr>
                                    <td>Numéro :</td>
                                    <td class="fw-bold">{{ prescription.prescription_number }}</td>
                                </tr>
                                <tr>
                                    <td>Médecin :</td>
                                    <td>{{ prescription.doctor_name }}</td>
                                </tr>
                                <tr>
                                    <td>Date :</td>
                                    <td>{{ prescription.prescription_date.strftime('%d/%m/%Y') }}</td>
                                </tr>
                                <tr>
                                    <td>Expire le :</td>
                                    <td>{{ prescription.validity_date.strftime('%d/%m/%Y') }}</td>
                                </tr>
                                <tr>
                                    <td>Articles :</td>
                                    <td>{{ prescription.total_items }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- Actions rapides -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Actions Rapides</h5>
                        </div>
                        <div class="card-body">
                            <button type="button" class="btn btn-outline-primary w-100 mb-2" onclick="checkAllStock()">
                                <i class="fas fa-boxes"></i> Vérifier tous les stocks
                            </button>
                            <button type="button" class="btn btn-outline-info w-100 mb-2" onclick="suggestSubstitutes()">
                                <i class="fas fa-exchange-alt"></i> Suggérer des substituts
                            </button>
                            <button type="button" class="btn btn-outline-warning w-100 mb-2" onclick="contactDoctor()">
                                <i class="fas fa-phone"></i> Contacter le médecin
                            </button>
                            <button type="button" class="btn btn-outline-secondary w-100" onclick="printDispenseForm()">
                                <i class="fas fa-print"></i> Imprimer fiche de délivrance
                            </button>
                        </div>
                    </div>

                    <!-- Guide de délivrance -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Guide de Délivrance</h5>
                        </div>
                        <div class="card-body">
                            <h6>Étapes à suivre :</h6>
                            <ol class="small">
                                <li>Vérifier l'identité du patient</li>
                                <li>Contrôler la validité de l'ordonnance</li>
                                <li>Vérifier les interactions et allergies</li>
                                <li>Ajuster les quantités selon les stocks</li>
                                <li>Donner les conseils d'utilisation</li>
                                <li>Enregistrer la délivrance</li>
                            </ol>
                            
                            <h6 class="mt-3">Points d'attention :</h6>
                            <ul class="small">
                                <li>Stupéfiants : vérifier RPPS médecin</li>
                                <li>Substitution : informer le patient</li>
                                <li>Stock faible : commander si nécessaire</li>
                                <li>Interactions : surveiller le patient</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Calculer automatiquement les totaux lors du changement des quantités
    const quantityInputs = document.querySelectorAll('input[name^="dispensed_"]');
    quantityInputs.forEach(input => {
        input.addEventListener('change', calculateTotals);
    });
    
    function calculateTotals() {
        // Recalculer les totaux si nécessaire
        // Cette fonction peut être étendue pour calculer les montants en temps réel
    }
});

function checkAllStock() {
    const medicationIds = [{% for item in prescription.items %}{{ item.medication_id_fk }}{% if not loop.last %},{% endif %}{% endfor %}];
    
    // Afficher le statut des stocks pour tous les médicaments
    let stockInfo = '';
    {% for item in prescription.items %}
    stockInfo += `{{ item.medication.name }} : {{ item.medication.current_stock }} en stock\n`;
    {% endfor %}
    
    alert('État des stocks :\n\n' + stockInfo);
}

function suggestSubstitutes() {
    alert('Fonctionnalité de suggestion de substituts en cours de développement.');
}

function contactDoctor() {
    const doctorInfo = `
Médecin : {{ prescription.doctor_name }}
{% if prescription.doctor_rpps %}RPPS : {{ prescription.doctor_rpps }}{% endif %}
{% if prescription.doctor_specialty %}Spécialité : {{ prescription.doctor_specialty }}{% endif %}
    `;
    
    if (confirm('Informations du médecin :\n' + doctorInfo + '\n\nVoulez-vous copier ces informations ?')) {
        navigator.clipboard.writeText(doctorInfo.trim());
    }
}

function printDispenseForm() {
    window.print();
}

// Gestion des raccourcis clavier
document.addEventListener('keydown', function(e) {
    if (e.ctrlKey && e.key === 's') {
        e.preventDefault();
        document.querySelector('form').submit();
    }
});
</script>

<style>
@media print {
    .btn, .card-header, nav, .alert {
        display: none !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    
    .container-fluid {
        padding: 0 !important;
    }
}
</style>
{% endblock %}