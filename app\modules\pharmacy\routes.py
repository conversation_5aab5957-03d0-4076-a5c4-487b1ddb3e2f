from __future__ import annotations

from datetime import datetime, date
from flask import render_template, request, redirect, url_for, flash, jsonify, abort
from flask_login import login_required, current_user
from sqlalchemy.orm import joinedload
from sqlalchemy import and_, or_, desc, func

from app.extensions import db
from app.modules.accounts.models import Business, User
from app.modules.customers.models import Customer
from app.modules.pharmacy.models import (
    Medication, Prescription, PrescriptionItem, DrugInteraction,
    ComplianceCheck, MedicationStockMovement, PatientAllergy,
    MedicationClass, PrescriptionStatus, InteractionSeverity
)
from app.modules.pharmacy.forms import (
    MedicationForm, PrescriptionForm, PrescriptionItemForm, DrugInteractionForm,
    ComplianceCheckForm, MedicationStockMovementForm, PatientAllergyForm,
    PharmacySearchForm, QuickDispenseForm
)
from . import bp


@bp.route("/")
@login_required
def index():
    """Page d'accueil de la pharmacie"""
    business = current_user.business
    
    # Statistiques rapides
    total_medications = Medication.query.filter_by(business_id_fk=business.id, is_active=True).count()
    pending_prescriptions = Prescription.query.filter_by(
        business_id_fk=business.id, 
        status=PrescriptionStatus.PENDING
    ).count()
    low_stock_medications = Medication.query.filter(
        Medication.business_id_fk == business.id,
        Medication.current_stock <= Medication.minimum_stock,
        Medication.is_active == True
    ).count()
    
    # Ordonnances récentes
    recent_prescriptions = Prescription.query.filter_by(business_id_fk=business.id)\
        .order_by(desc(Prescription.created_at)).limit(5).all()
    
    # Médicaments en stock faible
    low_stock_meds = Medication.query.filter(
        Medication.business_id_fk == business.id,
        Medication.current_stock <= Medication.minimum_stock,
        Medication.is_active == True
    ).limit(10).all()
    
    return render_template("pharmacy/index.html",
                         total_medications=total_medications,
                         pending_prescriptions=pending_prescriptions,
                         low_stock_medications=low_stock_medications,
                         recent_prescriptions=recent_prescriptions,
                         low_stock_meds=low_stock_meds)


# =============================================================================
# ROUTES MÉDICAMENTS
# =============================================================================

@bp.route("/medications")
@login_required
def medications():
    """Liste des médicaments"""
    search_form = PharmacySearchForm()
    page = request.args.get("page", 1, type=int)
    
    query = Medication.query.filter_by(business_id_fk=current_user.business.id)
    
    # Filtrage par recherche
    if request.args.get("search_query"):
        search = f"%{request.args.get('search_query')}%"
        query = query.filter(or_(
            Medication.name.ilike(search),
            Medication.generic_name.ilike(search),
            Medication.cip_code.ilike(search)
        ))
    
    # Filtrage par classe
    if request.args.get("medication_class"):
        query = query.filter_by(medication_class=request.args.get("medication_class"))
    
    # Filtrage stock faible
    if request.args.get("low_stock_only"):
        query = query.filter(Medication.current_stock <= Medication.minimum_stock)
    
    medications = query.order_by(Medication.name).paginate(
        page=page, per_page=20, error_out=False
    )
    
    return render_template("pharmacy/medications/list.html", 
                         medications=medications, search_form=search_form)


@bp.route("/medications/new", methods=["GET", "POST"])
@login_required
def new_medication():
    """Nouveau médicament"""
    form = MedicationForm()
    
    if form.validate_on_submit():
        medication = Medication(
            business_id_fk=current_user.business.id,
            **{field.name: field.data for field in form if field.name not in ['csrf_token']}
        )
        
        db.session.add(medication)
        db.session.commit()
        
        flash(f"Médicament {medication.name} créé avec succès", "success")
        return redirect(url_for("pharmacy.medications"))
    
    return render_template("pharmacy/medications/form.html", form=form, title="Nouveau médicament")


@bp.route("/medications/<int:medication_id>")
@login_required
def medication_detail(medication_id):
    """Détail d'un médicament"""
    medication = Medication.query.filter_by(
        id=medication_id, business_id_fk=current_user.business.id
    ).first_or_404()
    
    # Mouvements de stock récents
    recent_movements = MedicationStockMovement.query.filter_by(
        medication_id_fk=medication_id
    ).order_by(desc(MedicationStockMovement.created_at)).limit(10).all()
    
    # Interactions avec ce médicament
    interactions = DrugInteraction.query.filter(
        or_(
            DrugInteraction.medication1_id_fk == medication_id,
            DrugInteraction.medication2_id_fk == medication_id
        ),
        DrugInteraction.business_id_fk == current_user.business.id,
        DrugInteraction.is_active == True
    ).all()
    
    return render_template("pharmacy/medications/detail.html",
                         medication=medication,
                         recent_movements=recent_movements,
                         interactions=interactions)


@bp.route("/medications/<int:medication_id>/edit", methods=["GET", "POST"])
@login_required
def edit_medication(medication_id):
    """Modifier un médicament"""
    medication = Medication.query.filter_by(
        id=medication_id, business_id_fk=current_user.business.id
    ).first_or_404()
    
    form = MedicationForm(obj=medication)
    
    if form.validate_on_submit():
        for field in form:
            if field.name not in ['csrf_token']:
                setattr(medication, field.name, field.data)
        
        medication.updated_at = datetime.utcnow()
        db.session.commit()
        
        flash(f"Médicament {medication.name} modifié avec succès", "success")
        return redirect(url_for("pharmacy.medication_detail", medication_id=medication.id))
    
    return render_template("pharmacy/medications/form.html", 
                         form=form, medication=medication, title="Modifier médicament")


# =============================================================================
# ROUTES ORDONNANCES
# =============================================================================

@bp.route("/prescriptions")
@login_required
def prescriptions():
    """Liste des ordonnances"""
    search_form = PharmacySearchForm()
    page = request.args.get("page", 1, type=int)
    
    query = Prescription.query.filter_by(business_id_fk=current_user.business.id)\
        .options(joinedload(Prescription.customer))
    
    # Filtrage par statut
    if request.args.get("prescription_status"):
        query = query.filter_by(status=request.args.get("prescription_status"))
    
    # Filtrage par dates
    if request.args.get("date_from"):
        query = query.filter(Prescription.prescription_date >= request.args.get("date_from"))
    if request.args.get("date_to"):
        query = query.filter(Prescription.prescription_date <= request.args.get("date_to"))
    
    prescriptions = query.order_by(desc(Prescription.created_at)).paginate(
        page=page, per_page=20, error_out=False
    )
    
    return render_template("pharmacy/prescriptions/list.html", 
                         prescriptions=prescriptions, search_form=search_form)


@bp.route("/prescriptions/new", methods=["GET", "POST"])
@login_required
def new_prescription():
    """Nouvelle ordonnance"""
    form = PrescriptionForm()
    
    if form.validate_on_submit():
        prescription = Prescription(
            business_id_fk=current_user.business.id,
            **{field.name: field.data for field in form if field.name not in ['csrf_token']}
        )
        
        db.session.add(prescription)
        db.session.commit()
        
        flash(f"Ordonnance {prescription.prescription_number} créée avec succès", "success")
        return redirect(url_for("pharmacy.prescription_detail", prescription_id=prescription.id))
    
    return render_template("pharmacy/prescriptions/form.html", form=form, title="Nouvelle ordonnance")


@bp.route("/prescriptions/<int:prescription_id>")
@login_required
def prescription_detail(prescription_id):
    """Détail d'une ordonnance"""
    prescription = Prescription.query.filter_by(
        id=prescription_id, business_id_fk=current_user.business.id
    ).options(
        joinedload(Prescription.customer),
        joinedload(Prescription.items).joinedload(PrescriptionItem.medication)
    ).first_or_404()
    
    return render_template("pharmacy/prescriptions/detail.html", prescription=prescription)


# =============================================================================
# ROUTES INTERACTIONS
# =============================================================================

@bp.route("/interactions")
@login_required
def interactions():
    """Liste des interactions médicamenteuses"""
    page = request.args.get("page", 1, type=int)
    
    interactions = DrugInteraction.query.filter_by(
        business_id_fk=current_user.business.id, is_active=True
    ).options(
        joinedload(DrugInteraction.medication1),
        joinedload(DrugInteraction.medication2)
    ).order_by(DrugInteraction.severity, DrugInteraction.created_at).paginate(
        page=page, per_page=20, error_out=False
    )
    
    return render_template("pharmacy/interactions/list.html", interactions=interactions)


@bp.route("/interactions/new", methods=["GET", "POST"])
@login_required
def new_interaction():
    """Nouvelle interaction"""
    form = DrugInteractionForm()
    
    if form.validate_on_submit():
        interaction = DrugInteraction(
            business_id_fk=current_user.business.id,
            **{field.name: field.data for field in form if field.name not in ['csrf_token']}
        )
        
        db.session.add(interaction)
        db.session.commit()
        
        flash("Interaction médicamenteuse créée avec succès", "success")
        return redirect(url_for("pharmacy.interactions"))
    
    return render_template("pharmacy/interactions/form.html", form=form, title="Nouvelle interaction")


# =============================================================================
# API ROUTES
# =============================================================================

@bp.route("/api/medications/search")
@login_required
def api_medications_search():
    """API de recherche de médicaments"""
    query = request.args.get("q", "").strip()
    if len(query) < 2:
        return jsonify([])
    
    medications = Medication.query.filter(
        Medication.business_id_fk == current_user.business.id,
        Medication.is_active == True,
        or_(
            Medication.name.ilike(f"%{query}%"),
            Medication.generic_name.ilike(f"%{query}%"),
            Medication.cip_code.ilike(f"%{query}%")
        )
    ).limit(10).all()
    
    return jsonify([{
        "id": med.id,
        "name": med.name,
        "generic_name": med.generic_name,
        "cip_code": med.cip_code,
        "price_euros": med.price_cents / 100,
        "current_stock": med.current_stock,
        "medication_class": med.medication_class.value
    } for med in medications])


@bp.route("/api/customers/search")
@login_required
def api_customers_search():
    """API de recherche de clients"""
    query = request.args.get("q", "").strip()
    if len(query) < 2:
        return jsonify([])
    
    customers = Customer.query.filter(
        Customer.business_id_fk == current_user.business.id,
        or_(
            Customer.name.ilike(f"%{query}%"),
            Customer.email.ilike(f"%{query}%"),
            Customer.phone.ilike(f"%{query}%")
        )
    ).limit(10).all()
    
    return jsonify([{
        "id": customer.id,
        "name": customer.name,
        "email": customer.email,
        "phone": customer.phone
    } for customer in customers])


@bp.route("/api/prescriptions/<int:prescription_id>/validate", methods=["POST"])
@login_required
def api_validate_prescription(prescription_id):
    """API de validation d'ordonnance"""
    prescription = Prescription.query.filter_by(
        id=prescription_id, business_id_fk=current_user.business.id
    ).first_or_404()
    
    if prescription.status != PrescriptionStatus.PENDING:
        return jsonify({"error": "L'ordonnance n'est pas en attente de validation"}), 400
    
    prescription.status = PrescriptionStatus.VALIDATED
    prescription.validated_by_pharmacist_id = current_user.id
    prescription.validated_at = datetime.utcnow()
    
    db.session.commit()
    
    return jsonify({"message": "Ordonnance validée avec succès"})


@bp.route("/api/stock/<int:medication_id>/movement", methods=["POST"])
@login_required
def api_stock_movement(medication_id):
    """API de mouvement de stock"""
    medication = Medication.query.filter_by(
        id=medication_id, business_id_fk=current_user.business.id
    ).first_or_404()
    
    data = request.get_json()
    movement_type = data.get("movement_type")
    quantity = int(data.get("quantity", 0))
    
    if not movement_type or quantity == 0:
        return jsonify({"error": "Type de mouvement et quantité requis"}), 400
    
    # Créer le mouvement
    movement = MedicationStockMovement(
        business_id_fk=current_user.business.id,
        medication_id_fk=medication_id,
        movement_type=movement_type,
        quantity=quantity,
        created_by_user_id=current_user.id,
        notes=data.get("notes", "")
    )
    
    # Mettre à jour le stock
    if movement_type in ["in", "adjustment"] and quantity > 0:
        medication.current_stock += quantity
    elif movement_type in ["out", "expired", "damaged"] and quantity > 0:
        medication.current_stock = max(0, medication.current_stock - quantity)
    
    db.session.add(movement)
    db.session.commit()
    
    return jsonify({
        "message": "Mouvement de stock enregistré",
        "new_stock": medication.current_stock
    })


@bp.route("/medications/<int:medication_id>/delete", methods=["POST"])
@login_required
def delete_medication(medication_id):
    """Supprimer un médicament"""
    medication = Medication.query.filter_by(
        id=medication_id, business_id_fk=current_user.business.id
    ).first_or_404()
    
    # Vérifier s'il y a des ordonnances en cours
    active_prescriptions = PrescriptionItem.query.join(Prescription).filter(
        PrescriptionItem.medication_id_fk == medication_id,
        Prescription.business_id_fk == current_user.business.id,
        Prescription.status.in_([PrescriptionStatus.PENDING, PrescriptionStatus.VALIDATED])
    ).count()
    
    if active_prescriptions > 0:
        flash(f"Impossible de supprimer {medication.name} : des ordonnances actives l'utilisent", "error")
        return redirect(url_for("pharmacy.medication_detail", medication_id=medication.id))
    
    medication.is_active = False
    db.session.commit()
    
    flash(f"Médicament {medication.name} désactivé avec succès", "success")
    return redirect(url_for("pharmacy.medications"))


# =============================================================================
# ROUTES ORDONNANCES COMPLÉMENTAIRES
# =============================================================================

@bp.route("/prescriptions/<int:prescription_id>/edit", methods=["GET", "POST"])
@login_required
def edit_prescription(prescription_id):
    """Modifier une ordonnance"""
    prescription = Prescription.query.filter_by(
        id=prescription_id, business_id_fk=current_user.business.id
    ).first_or_404()
    
    form = PrescriptionForm(obj=prescription)
    
    if form.validate_on_submit():
        for field in form:
            if field.name not in ['csrf_token']:
                setattr(prescription, field.name, field.data)
        
        prescription.updated_at = datetime.utcnow()
        db.session.commit()
        
        flash(f"Ordonnance {prescription.prescription_number} modifiée avec succès", "success")
        return redirect(url_for("pharmacy.prescription_detail", prescription_id=prescription.id))
    
    return render_template("pharmacy/prescriptions/form.html", 
                         form=form, prescription=prescription, title="Modifier ordonnance")


@bp.route("/prescriptions/<int:prescription_id>/delete", methods=["POST"])
@login_required
def delete_prescription(prescription_id):
    """Supprimer une ordonnance"""
    prescription = Prescription.query.filter_by(
        id=prescription_id, business_id_fk=current_user.business.id
    ).first_or_404()
    
    if prescription.status == PrescriptionStatus.DISPENSED:
        flash("Impossible de supprimer une ordonnance déjà délivrée", "error")
        return redirect(url_for("pharmacy.prescription_detail", prescription_id=prescription.id))
    
    prescription.status = PrescriptionStatus.CANCELLED
    db.session.commit()
    
    flash(f"Ordonnance {prescription.prescription_number} annulée avec succès", "success")
    return redirect(url_for("pharmacy.prescriptions"))


@bp.route("/prescriptions/<int:prescription_id>/dispense", methods=["GET", "POST"])
@login_required
def dispense_prescription(prescription_id):
    """Délivrer une ordonnance"""
    from app.modules.pharmacy.utils import (
        PharmacyInteractionChecker, PharmacyComplianceChecker, 
        PharmacyStockManager, calculate_prescription_totals
    )
    
    prescription = Prescription.query.filter_by(
        id=prescription_id, business_id_fk=current_user.business.id
    ).options(
        joinedload(Prescription.customer),
        joinedload(Prescription.items).joinedload(PrescriptionItem.medication)
    ).first_or_404()
    
    if prescription.status not in [PrescriptionStatus.VALIDATED, PrescriptionStatus.PARTIALLY_DISPENSED]:
        flash("Cette ordonnance ne peut pas être délivrée dans son état actuel", "error")
        return redirect(url_for("pharmacy.prescription_detail", prescription_id=prescription.id))
    
    # Vérifications de conformité
    interaction_checker = PharmacyInteractionChecker(current_user.business.id)
    compliance_checker = PharmacyComplianceChecker(current_user.business.id)
    
    interactions = interaction_checker.check_prescription_interactions(prescription)
    compliance_results = compliance_checker.check_prescription_compliance(prescription)
    allergy_results = interaction_checker.check_patient_allergies(
        prescription.customer, 
        [item.medication_id_fk for item in prescription.items]
    )
    
    # Vérifier s'il y a des problèmes critiques
    critical_issues = [
        issue for issue in compliance_results + allergy_results 
        if issue.severity_level == "critical" and not issue.passed
    ]
    
    if critical_issues and request.method == "GET":
        flash("Des problèmes critiques ont été détectés. Vérifiez avant de délivrer.", "error")
    
    if request.method == "POST":
        # Traiter la délivrance
        stock_manager = PharmacyStockManager(current_user.business.id)
        
        try:
            # Mettre à jour les quantités délivrées
            for item in prescription.items:
                requested_quantity = request.form.get(f"dispensed_{item.id}", type=int)
                if requested_quantity is not None:
                    item.dispensed_quantity = min(requested_quantity, item.prescribed_quantity)
            
            # Calculer les totaux
            calculate_prescription_totals(prescription)
            
            # Mettre à jour le stock
            stock_manager.update_stock_from_prescription(prescription, current_user.id)
            
            # Mettre à jour le statut
            fully_dispensed = all(item.is_fully_dispensed for item in prescription.items)
            prescription.status = PrescriptionStatus.DISPENSED if fully_dispensed else PrescriptionStatus.PARTIALLY_DISPENSED
            prescription.dispensed_by_pharmacist_id = current_user.id
            prescription.dispensed_at = datetime.utcnow()
            
            db.session.commit()
            
            flash("Ordonnance délivrée avec succès", "success")
            return redirect(url_for("pharmacy.prescription_detail", prescription_id=prescription.id))
            
        except Exception as e:
            db.session.rollback()
            flash(f"Erreur lors de la délivrance : {str(e)}", "error")
    
    return render_template("pharmacy/prescriptions/dispense.html",
                         prescription=prescription,
                         interactions=interactions,
                         compliance_results=compliance_results,
                         allergy_results=allergy_results,
                         critical_issues=critical_issues)


# =============================================================================
# ROUTES INTERACTIONS COMPLÈTES
# =============================================================================

@bp.route("/interactions/<int:interaction_id>")
@login_required
def interaction_detail(interaction_id):
    """Détail d'une interaction"""
    interaction = DrugInteraction.query.filter_by(
        id=interaction_id, business_id_fk=current_user.business.id
    ).options(
        joinedload(DrugInteraction.medication1),
        joinedload(DrugInteraction.medication2)
    ).first_or_404()
    
    return render_template("pharmacy/interactions/detail.html", interaction=interaction)


@bp.route("/interactions/<int:interaction_id>/edit", methods=["GET", "POST"])
@login_required
def edit_interaction(interaction_id):
    """Modifier une interaction"""
    interaction = DrugInteraction.query.filter_by(
        id=interaction_id, business_id_fk=current_user.business.id
    ).first_or_404()
    
    form = DrugInteractionForm(obj=interaction)
    
    if form.validate_on_submit():
        for field in form:
            if field.name not in ['csrf_token']:
                setattr(interaction, field.name, field.data)
        
        interaction.updated_at = datetime.utcnow()
        db.session.commit()
        
        flash("Interaction médicamenteuse modifiée avec succès", "success")
        return redirect(url_for("pharmacy.interaction_detail", interaction_id=interaction.id))
    
    return render_template("pharmacy/interactions/form.html", 
                         form=form, interaction=interaction, title="Modifier interaction")


@bp.route("/interactions/<int:interaction_id>/delete", methods=["POST"])
@login_required
def delete_interaction(interaction_id):
    """Supprimer une interaction"""
    interaction = DrugInteraction.query.filter_by(
        id=interaction_id, business_id_fk=current_user.business.id
    ).first_or_404()
    
    interaction.is_active = False
    db.session.commit()
    
    flash("Interaction médicamenteuse désactivée avec succès", "success")
    return redirect(url_for("pharmacy.interactions"))


# =============================================================================
# ROUTES ALLERGIES DES PATIENTS
# =============================================================================

@bp.route("/allergies")
@login_required
def allergies():
    """Liste des allergies des patients"""
    page = request.args.get("page", 1, type=int)
    
    allergies = PatientAllergy.query.filter_by(
        business_id_fk=current_user.business.id, is_active=True
    ).options(
        joinedload(PatientAllergy.customer)
    ).order_by(PatientAllergy.severity.desc(), PatientAllergy.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    
    return render_template("pharmacy/allergies/list.html", allergies=allergies)


@bp.route("/allergies/new", methods=["GET", "POST"])
@login_required
def new_allergy():
    """Nouvelle allergie patient"""
    form = PatientAllergyForm()
    
    if form.validate_on_submit():
        allergy = PatientAllergy(
            business_id_fk=current_user.business.id,
            **{field.name: field.data for field in form if field.name not in ['csrf_token']}
        )
        
        db.session.add(allergy)
        db.session.commit()
        
        flash("Allergie patient enregistrée avec succès", "success")
        return redirect(url_for("pharmacy.allergies"))
    
    return render_template("pharmacy/allergies/form.html", form=form, title="Nouvelle allergie")


@bp.route("/allergies/<int:allergy_id>/edit", methods=["GET", "POST"])
@login_required
def edit_allergy(allergy_id):
    """Modifier une allergie"""
    allergy = PatientAllergy.query.filter_by(
        id=allergy_id, business_id_fk=current_user.business.id
    ).first_or_404()
    
    form = PatientAllergyForm(obj=allergy)
    
    if form.validate_on_submit():
        for field in form:
            if field.name not in ['csrf_token']:
                setattr(allergy, field.name, field.data)
        
        allergy.updated_at = datetime.utcnow()
        db.session.commit()
        
        flash("Allergie patient modifiée avec succès", "success")
        return redirect(url_for("pharmacy.allergies"))
    
    return render_template("pharmacy/allergies/form.html", 
                         form=form, allergy=allergy, title="Modifier allergie")


@bp.route("/allergies/<int:allergy_id>/delete", methods=["POST"])
@login_required
def delete_allergy(allergy_id):
    """Supprimer une allergie"""
    allergy = PatientAllergy.query.filter_by(
        id=allergy_id, business_id_fk=current_user.business.id
    ).first_or_404()
    
    allergy.is_active = False
    db.session.commit()
    
    flash("Allergie patient supprimée avec succès", "success")
    return redirect(url_for("pharmacy.allergies"))


# =============================================================================
# ROUTES MOUVEMENTS DE STOCK
# =============================================================================

@bp.route("/stock")
@login_required
def stock():
    """Gestion des stocks"""
    from app.modules.pharmacy.utils import PharmacyStockManager
    
    stock_manager = PharmacyStockManager(current_user.business.id)
    
    # Médicaments en stock faible
    low_stock_meds = stock_manager.get_low_stock_medications()
    
    # Médicaments expirant bientôt
    expiring_meds = stock_manager.get_expiring_medications()
    
    # Mouvements récents
    recent_movements = MedicationStockMovement.query.filter_by(
        business_id_fk=current_user.business.id
    ).options(
        joinedload(MedicationStockMovement.medication),
        joinedload(MedicationStockMovement.created_by)
    ).order_by(desc(MedicationStockMovement.created_at)).limit(20).all()
    
    return render_template("pharmacy/stock/index.html",
                         low_stock_meds=low_stock_meds,
                         expiring_meds=expiring_meds,
                         recent_movements=recent_movements)


@bp.route("/stock/movements")
@login_required
def stock_movements():
    """Liste des mouvements de stock"""
    page = request.args.get("page", 1, type=int)
    
    movements = MedicationStockMovement.query.filter_by(
        business_id_fk=current_user.business.id
    ).options(
        joinedload(MedicationStockMovement.medication),
        joinedload(MedicationStockMovement.created_by)
    ).order_by(desc(MedicationStockMovement.created_at)).paginate(
        page=page, per_page=50, error_out=False
    )
    
    return render_template("pharmacy/stock/movements.html", movements=movements)


@bp.route("/stock/movement/new", methods=["GET", "POST"])
@login_required
def new_stock_movement():
    """Nouveau mouvement de stock"""
    form = MedicationStockMovementForm()
    
    if form.validate_on_submit():
        from app.modules.pharmacy.utils import PharmacyStockManager
        
        stock_manager = PharmacyStockManager(current_user.business.id)
        
        try:
            if form.movement_type.data == "in":
                stock_manager.add_stock(
                    medication_id=form.medication_id_fk.data,
                    quantity=form.quantity.data,
                    unit_cost_cents=form.unit_cost_cents.data,
                    batch_number=form.batch_number.data,
                    expiry_date=form.expiry_date.data,
                    supplier_name=form.supplier_name.data,
                    user_id=current_user.id,
                    notes=form.notes.data
                )
            else:
                # Pour les sorties, utiliser l'API de mouvement
                medication = Medication.query.get(form.medication_id_fk.data)
                stock_manager._create_stock_movement(
                    medication_id=form.medication_id_fk.data,
                    quantity=-abs(form.quantity.data),  # Négatif pour sortie
                    movement_type=form.movement_type.data,
                    user_id=current_user.id,
                    batch_number=form.batch_number.data,
                    notes=form.notes.data
                )
                medication.current_stock = max(0, medication.current_stock - abs(form.quantity.data))
                db.session.commit()
            
            flash("Mouvement de stock enregistré avec succès", "success")
            return redirect(url_for("pharmacy.stock"))
            
        except Exception as e:
            db.session.rollback()
            flash(f"Erreur lors de l'enregistrement : {str(e)}", "error")
    
    return render_template("pharmacy/stock/movement_form.html", form=form, title="Nouveau mouvement")


# =============================================================================
# ROUTES RAPPORTS
# =============================================================================

@bp.route("/reports")
@login_required
def reports():
    """Page des rapports"""
    from app.modules.pharmacy.utils import PharmacyReportGenerator
    
    report_generator = PharmacyReportGenerator(current_user.business.id)
    
    # Rapport quotidien
    daily_report = report_generator.generate_daily_sales_report()
    
    # Rapport de stock
    stock_report = report_generator.generate_stock_report()
    
    return render_template("pharmacy/reports/index.html",
                         daily_report=daily_report,
                         stock_report=stock_report)


@bp.route("/reports/daily/<string:report_date_str>")
@login_required
def daily_report(report_date_str):
    """Rapport quotidien détaillé"""
    from app.modules.pharmacy.utils import PharmacyReportGenerator
    from datetime import datetime
    
    try:
        # Convertir la chaîne de date en objet date
        report_date = datetime.strptime(report_date_str, "%Y-%m-%d").date()
    except ValueError:
        # Si la date n'est pas valide, utiliser la date d'aujourd'hui
        report_date = datetime.today().date()
    
    report_generator = PharmacyReportGenerator(current_user.business.id)
    report = report_generator.generate_daily_sales_report(report_date)
    
    return render_template("pharmacy/reports/daily.html", report=report, report_date=report_date)


# =============================================================================
# ROUTES API COMPLÉMENTAIRES
# =============================================================================

@bp.route("/api/interactions/check", methods=["POST"])
@login_required
def api_check_interactions():
    """API de vérification d'interactions"""
    from app.modules.pharmacy.utils import PharmacyInteractionChecker
    
    medication_ids = request.json.get("medication_ids", [])
    
    if not medication_ids or len(medication_ids) < 2:
        return jsonify({"interactions": []})
    
    checker = PharmacyInteractionChecker(current_user.business.id)
    interactions = checker.check_medication_interactions(medication_ids)
    
    return jsonify({
        "interactions": [
            {
                "severity": interaction.severity.value,
                "description": interaction.description,
                "recommendations": interaction.recommendations,
                "contraindicated": interaction.contraindicated,
                "monitoring_required": interaction.monitoring_required
            }
            for interaction in interactions
        ]
    })


@bp.route("/api/compliance/check/<int:prescription_id>")
@login_required
def api_check_compliance(prescription_id):
    """API de vérification de conformité"""
    from app.modules.pharmacy.utils import PharmacyComplianceChecker
    
    prescription = Prescription.query.filter_by(
        id=prescription_id, business_id_fk=current_user.business.id
    ).first_or_404()
    
    checker = PharmacyComplianceChecker(current_user.business.id)
    results = checker.check_prescription_compliance(prescription)
    
    return jsonify({
        "compliance_results": [
            {
                "check_type": result.check_type,
                "passed": result.passed,
                "severity_level": result.severity_level,
                "description": result.description,
                "failure_reason": result.failure_reason,
                "recommendations": result.recommendations,
                "action_required": result.action_required
            }
            for result in results
        ]
    })


@bp.route("/api/quick-dispense", methods=["POST"])
@login_required
def api_quick_dispense():
    """API de délivrance rapide (vente libre)"""
    form = QuickDispenseForm()
    
    if form.validate_on_submit():
        from app.modules.pharmacy.utils import PharmacyStockManager
        
        medication = Medication.query.filter_by(
            id=form.medication_id.data,
            business_id_fk=current_user.business.id
        ).first_or_404()
        
        # Vérifier le stock
        if medication.current_stock < form.quantity.data:
            return jsonify({
                "error": f"Stock insuffisant (disponible: {medication.current_stock})"
            }), 400
        
        # Vérifier que c'est un médicament en vente libre
        if medication.is_prescription_only:
            return jsonify({
                "error": "Ce médicament nécessite une ordonnance"
            }), 400
        
        try:
            # Créer le mouvement de stock
            stock_manager = PharmacyStockManager(current_user.business.id)
            stock_manager._create_stock_movement(
                medication_id=medication.id,
                quantity=-form.quantity.data,
                movement_type="out",
                reference_type="direct_sale",
                user_id=current_user.id,
                notes=form.notes.data or "Vente directe"
            )
            
            # Mettre à jour le stock
            medication.current_stock -= form.quantity.data
            db.session.commit()
            
            return jsonify({
                "message": "Vente enregistrée avec succès",
                "total_price_cents": medication.price_cents * form.quantity.data,
                "new_stock": medication.current_stock
            })
            
        except Exception as e:
            db.session.rollback()
            return jsonify({"error": str(e)}), 500
    
    return jsonify({"errors": form.errors}), 400