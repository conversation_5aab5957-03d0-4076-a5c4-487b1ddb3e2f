"""Optimisation des réponses HTTP"""
import gzip
import json
from typing import Any, Dict, Optional
from flask import Response, request, current_app
import logging

logger = logging.getLogger(__name__)

class ResponseOptimizer:
    """Optimise les réponses HTTP pour améliorer les performances"""
    
    @staticmethod
    def compress_response(response: Response) -> Response:
        """
        Compresse la réponse si elle est assez grande et si le client accepte gzip
        """
        if not current_app.config.get('ENABLE_RESPONSE_COMPRESSION', True):
            return response
            
        # Vérifier si la réponse est déjà compressée
        if 'Content-Encoding' in response.headers:
            return response
            
        # Vérifier si le client accepte gzip
        accept_encoding = request.headers.get('Accept-Encoding', '')
        if 'gzip' not in accept_encoding:
            return response
            
        # Vérifier la taille de la réponse (ne pas compresser les petites réponses)
        response_data = response.get_data()
        if len(response_data) < 1024:  # Moins de 1KB, pas la peine de compresser
            return response
            
        # Compresser la réponse
        try:
            compressed_data = gzip.compress(response_data)
            response.set_data(compressed_data)
            response.headers['Content-Encoding'] = 'gzip'
            response.headers['Content-Length'] = len(compressed_data)
            response.headers['Vary'] = 'Accept-Encoding'
            logger.debug(f"Compressed response from {len(response_data)} to {len(compressed_data)} bytes")
        except Exception as e:
            logger.warning(f"Failed to compress response: {e}")
            
        return response
    
    @staticmethod
    def optimize_json_response(data: Dict[str, Any], indent: Optional[int] = None) -> str:
        """
        Optimise la sérialisation JSON en supprimant les espaces inutiles en production
        """
        if current_app.config.get('DEBUG', False):
            # En mode debug, garder un format lisible
            return json.dumps(data, indent=indent or 2, ensure_ascii=False)
        else:
            # En production, minimiser la taille
            return json.dumps(data, separators=(',', ':'), ensure_ascii=False)
    
    @staticmethod
    def add_cache_headers(response: Response, cache_time: int = 300) -> Response:
        """
        Ajoute des en-têtes de cache pour les ressources statiques
        """
        if request.method in ['GET', 'HEAD']:
            response.cache_control.max_age = cache_time
            response.cache_control.public = True
        return response
    
    @staticmethod
    def add_security_headers(response: Response) -> Response:
        """
        Ajoute des en-têtes de sécurité pour améliorer la performance et la sécurité
        """
        # Empêcher le sniffing de type MIME
        response.headers['X-Content-Type-Options'] = 'nosniff'
        
        # Protection XSS
        response.headers['X-XSS-Protection'] = '1; mode=block'
        
        # Contrôle du referrer
        response.headers['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        
        return response

def optimize_static_files():
    """
    Configure l'optimisation des fichiers statiques
    """
    # Cette fonction serait appelée lors de l'initialisation de l'application
    # pour configurer les paramètres de cache pour les fichiers statiques
    pass

# Middleware pour optimiser automatiquement les réponses
def response_optimization_middleware(response: Response) -> Response:
    """
    Middleware qui applique automatiquement les optimisations de réponse
    """
    # Compresser la réponse
    response = ResponseOptimizer.compress_response(response)
    
    # Ajouter les en-têtes de sécurité
    response = ResponseOptimizer.add_security_headers(response)
    
    return response