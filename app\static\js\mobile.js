// JavaScript pour l'application mobile POS
class POSMobileApp {
  constructor() {
    this.cart = [];
    this.currentOrder = null;
    this.isOnline = navigator.onLine;
    this.db = null;
    
    this.init();
  }
  
  async init() {
    // Initialiser la base de données locale
    await this.initDatabase();
    
    // Vérifier le statut en ligne/hors ligne
    this.setupOnlineStatus();
    
    // Enregistrer le service worker
    this.registerServiceWorker();
    
    // Initialiser les écouteurs d'événements
    this.setupEventListeners();
    
    // Charger les données depuis le cache si hors ligne
    if (!this.isOnline) {
      await this.loadFromCache();
    }
    
    console.log('Application mobile POS initialisée');
  }
  
  async initDatabase() {
    // Utiliser IndexedDB pour le stockage hors ligne
    if ('indexedDB' in window) {
      try {
        const request = indexedDB.open('POSMobileDB', 1);
        
        request.onupgradeneeded = (event) => {
          const db = event.target.result;
          
          // Créer les magasins d'objets
          if (!db.objectStoreNames.contains('products')) {
            const productStore = db.createObjectStore('products', { keyPath: 'id' });
            productStore.createIndex('sku', 'sku', { unique: true });
            productStore.createIndex('name', 'name', { unique: false });
          }
          
          if (!db.objectStoreNames.contains('orders')) {
            const orderStore = db.createObjectStore('orders', { keyPath: 'id', autoIncrement: true });
            orderStore.createIndex('timestamp', 'timestamp', { unique: false });
          }
          
          if (!db.objectStoreNames.contains('settings')) {
            db.createObjectStore('settings', { keyPath: 'key' });
          }
        };
        
        request.onsuccess = (event) => {
          this.db = event.target.result;
          console.log('Base de données locale initialisée');
        };
        
        request.onerror = (event) => {
          console.error('Erreur lors de l\'initialisation de la base de données:', event.target.error);
        };
      } catch (error) {
        console.error('IndexedDB non supporté:', error);
      }
    }
  }
  
  setupOnlineStatus() {
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.showNotification('Connexion rétablie', 'success');
      this.syncOfflineData();
    });
    
    window.addEventListener('offline', () => {
      this.isOnline = false;
      this.showNotification('Mode hors ligne activé', 'warning');
    });
  }
  
  registerServiceWorker() {
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.register('/static/pwa/service-worker.js')
        .then(registration => {
          console.log('Service Worker enregistré avec succès:', registration);
        })
        .catch(error => {
          console.error('Erreur lors de l\'enregistrement du Service Worker:', error);
        });
    }
  }
  
  setupEventListeners() {
    // Écouteurs pour les produits
    document.addEventListener('click', (event) => {
      if (event.target.classList.contains('product-item')) {
        const productId = event.target.dataset.productId;
        this.addToCart(productId);
      }
    });
    
    // Écouteurs pour le panier
    document.addEventListener('click', (event) => {
      if (event.target.classList.contains('cart-item-remove')) {
        const itemId = event.target.dataset.itemId;
        this.removeFromCart(itemId);
      }
    });
    
    // Écouteurs pour les boutons d'action
    document.addEventListener('click', (event) => {
      if (event.target.id === 'payButton') {
        this.processPayment();
      } else if (event.target.id === 'cancelButton') {
        this.cancelOrder();
      } else if (event.target.id === 'discountButton') {
        this.applyDiscount();
      }
    });
    
    // Écouteurs pour le clavier numérique
    document.addEventListener('click', (event) => {
      if (event.target.classList.contains('numpad-button')) {
        this.handleNumpadInput(event.target.textContent);
      }
    });
  }
  
  async addToCart(productId) {
    try {
      let product;
      
      // Récupérer le produit depuis l'API ou le cache
      if (this.isOnline) {
        const response = await fetch(`/api/products/${productId}`);
        product = await response.json();
      } else {
        product = await this.getProductFromCache(productId);
      }
      
      if (product) {
        // Ajouter au panier
        const cartItem = {
          id: Date.now() + Math.random(),
          productId: product.id,
          name: product.name,
          price: product.price_cents / 100,
          quantity: 1
        };
        
        this.cart.push(cartItem);
        this.updateCartDisplay();
        this.showNotification(`${product.name} ajouté au panier`, 'success');
        
        // Sauvegarder dans le cache
        await this.saveToCache();
      }
    } catch (error) {
      console.error('Erreur lors de l\'ajout au panier:', error);
      this.showNotification('Erreur lors de l\'ajout au produit', 'error');
    }
  }
  
  removeFromCart(itemId) {
    this.cart = this.cart.filter(item => item.id !== itemId);
    this.updateCartDisplay();
    this.showNotification('Produit supprimé du panier', 'info');
  }
  
  updateCartDisplay() {
    const cartContainer = document.getElementById('cartItems');
    if (!cartContainer) return;
    
    if (this.cart.length === 0) {
      cartContainer.innerHTML = '<p class="text-muted">Panier vide</p>';
      return;
    }
    
    let cartHTML = '';
    let total = 0;
    
    this.cart.forEach(item => {
      const itemTotal = item.price * item.quantity;
      total += itemTotal;
      
      cartHTML += `
        <div class="cart-item" data-item-id="${item.id}">
          <div class="cart-item-name">${item.name}</div>
          <div class="cart-item-quantity">${item.quantity}</div>
          <div class="cart-item-price">€${itemTotal.toFixed(2)}</div>
          <button class="btn btn-sm btn-danger cart-item-remove" data-item-id="${item.id}">
            <i class="fas fa-times"></i>
          </button>
        </div>
      `;
    });
    
    cartContainer.innerHTML = cartHTML;
    
    // Mettre à jour le total
    const totalElement = document.getElementById('cartTotal');
    if (totalElement) {
      totalElement.textContent = `Total: €${total.toFixed(2)}`;
    }
  }
  
  async processPayment() {
    if (this.cart.length === 0) {
      this.showNotification('Le panier est vide', 'warning');
      return;
    }
    
    try {
      // Calculer le total
      const total = this.cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
      
      if (this.isOnline) {
        // Processus de paiement en ligne
        const response = await fetch('/api/payments/process', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': this.getCsrfToken()
          },
          body: JSON.stringify({
            amount: total,
            items: this.cart
          })
        });
        
        const result = await response.json();
        
        if (result.success) {
          this.showNotification('Paiement effectué avec succès', 'success');
          this.completeOrder(result.payment_id);
        } else {
          this.showNotification(`Erreur de paiement: ${result.message}`, 'error');
        }
      } else {
        // Sauvegarder la commande pour traitement ultérieur
        await this.saveOrderForSync({
          amount: total,
          items: this.cart,
          timestamp: new Date()
        });
        
        this.showNotification('Commande enregistrée. Sera traitée lorsque la connexion sera rétablie.', 'info');
        this.completeOrder(null);
      }
    } catch (error) {
      console.error('Erreur lors du traitement du paiement:', error);
      this.showNotification('Erreur lors du traitement du paiement', 'error');
    }
  }
  
  async completeOrder(paymentId) {
    // Sauvegarder la commande dans la base de données locale
    if (this.db) {
      const transaction = this.db.transaction(['orders'], 'readwrite');
      const store = transaction.objectStore('orders');
      
      const order = {
        items: this.cart,
        total: this.cart.reduce((sum, item) => sum + (item.price * item.quantity), 0),
        timestamp: new Date(),
        payment_id: paymentId
      };
      
      const request = store.add(order);
      
      request.onsuccess = () => {
        console.log('Commande enregistrée localement');
      };
      
      request.onerror = (event) => {
        console.error('Erreur lors de l\'enregistrement de la commande:', event.target.error);
      };
    }
    
    // Vider le panier
    this.cart = [];
    this.updateCartDisplay();
    
    // Afficher un message de confirmation
    this.showNotification('Commande terminée', 'success');
  }
  
  cancelOrder() {
    this.cart = [];
    this.updateCartDisplay();
    this.showNotification('Commande annulée', 'info');
  }
  
  applyDiscount() {
    // Pour l'instant, une réduction simple de 10%
    this.showNotification('Réduction de 10% appliquée', 'success');
    // Dans une implémentation réelle, on appliquerait la logique de réduction
  }
  
  handleNumpadInput(value) {
    // Gérer l'entrée du clavier numérique
    console.log('Entrée clavier numérique:', value);
    // Dans une implémentation réelle, on gérerait l'entrée utilisateur
  }
  
  async saveToCache() {
    if (this.db) {
      // Sauvegarder le panier dans le cache
      const transaction = this.db.transaction(['settings'], 'readwrite');
      const store = transaction.objectStore('settings');
      
      const cartData = {
        key: 'cart',
        value: this.cart
      };
      
      const request = store.put(cartData);
      
      request.onerror = (event) => {
        console.error('Erreur lors de la sauvegarde du panier:', event.target.error);
      };
    }
  }
  
  async loadFromCache() {
    if (this.db) {
      // Charger le panier depuis le cache
      const transaction = this.db.transaction(['settings'], 'readonly');
      const store = transaction.objectStore('settings');
      
      const request = store.get('cart');
      
      request.onsuccess = (event) => {
        const result = event.target.result;
        if (result) {
          this.cart = result.value || [];
          this.updateCartDisplay();
        }
      };
      
      request.onerror = (event) => {
        console.error('Erreur lors du chargement du panier:', event.target.error);
      };
    }
  }
  
  async getProductFromCache(productId) {
    if (this.db) {
      const transaction = this.db.transaction(['products'], 'readonly');
      const store = transaction.objectStore('products');
      
      const request = store.get(parseInt(productId));
      
      return new Promise((resolve, reject) => {
        request.onsuccess = (event) => {
          resolve(event.target.result);
        };
        
        request.onerror = (event) => {
          reject(event.target.error);
        };
      });
    }
    return null;
  }
  
  async saveOrderForSync(order) {
    if (this.db) {
      const transaction = this.db.transaction(['orders'], 'readwrite');
      const store = transaction.objectStore('orders');
      
      const request = store.add(order);
      
      return new Promise((resolve, reject) => {
        request.onsuccess = () => {
          resolve();
        };
        
        request.onerror = (event) => {
          reject(event.target.error);
        };
      });
    }
  }
  
  async syncOfflineData() {
    if (this.db && this.isOnline) {
      // Synchroniser les commandes hors ligne
      const transaction = this.db.transaction(['orders'], 'readonly');
      const store = transaction.objectStore('orders');
      
      const request = store.getAll();
      
      request.onsuccess = async (event) => {
        const orders = event.target.result;
        
        for (const order of orders) {
          try {
            // Envoyer la commande au serveur
            const response = await fetch('/api/orders/sync', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': this.getCsrfToken()
              },
              body: JSON.stringify(order)
            });
            
            const result = await response.json();
            
            if (result.success) {
              // Supprimer la commande synchronisée
              const deleteTransaction = this.db.transaction(['orders'], 'readwrite');
              const deleteStore = deleteTransaction.objectStore('orders');
              deleteStore.delete(order.id);
              
              console.log(`Commande ${order.id} synchronisée avec succès`);
            }
          } catch (error) {
            console.error(`Erreur lors de la synchronisation de la commande ${order.id}:`, error);
          }
        }
        
        this.showNotification('Données synchronisées avec succès', 'success');
      };
      
      request.onerror = (event) => {
        console.error('Erreur lors de la synchronisation des données hors ligne:', event.target.error);
      };
    }
  }
  
  getCsrfToken() {
    // Récupérer le jeton CSRF du meta tag
    const metaTag = document.querySelector('meta[name="csrf-token"]');
    return metaTag ? metaTag.getAttribute('content') : '';
  }
  
  showNotification(message, type = 'info') {
    // Créer une notification toast
    const toastContainer = document.getElementById('toastContainer');
    if (!toastContainer) return;
    
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type} border-0`;
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', 'assertive');
    toast.setAttribute('aria-atomic', 'true');
    
    toast.innerHTML = `
      <div class="d-flex">
        <div class="toast-body">
          ${message}
        </div>
        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
      </div>
    `;
    
    toastContainer.appendChild(toast);
    
    // Afficher la notification
    const bsToast = new bootstrap.Toast(toast, { delay: 3000 });
    bsToast.show();
    
    // Supprimer la notification après affichage
    toast.addEventListener('hidden.bs.toast', () => {
      toast.remove();
    });
  }
}

// Initialiser l'application lorsque le DOM est chargé
document.addEventListener('DOMContentLoaded', () => {
  window.posApp = new POSMobileApp();
});

// Gérer les notifications push
if ('serviceWorker' in navigator && 'PushManager' in window) {
  navigator.serviceWorker.ready.then((registration) => {
    // Demander la permission pour les notifications
    Notification.requestPermission().then((permission) => {
      if (permission === 'granted') {
        console.log('Permission de notification accordée');
      }
    });
  });
}