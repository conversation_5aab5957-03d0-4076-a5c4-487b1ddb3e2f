"""Tests unitaires pour les modèles de l'application POS"""
import unittest
from datetime import datetime
from app.extensions import db
from tests import BaseTestCase

class ModelTestCase(BaseTestCase):
    """Tests pour les modèles de l'application"""
    
    def test_user_model(self):
        """Test du modèle User"""
        from app.modules.accounts.models import User
        
        # Créer un utilisateur de test
        user = User(
            username='testuser',
            email='<EMAIL>',
            first_name='Test',
            last_name='User'
        )
        user.set_password('password123')
        
        # Sauvegarder dans la base de données
        db.session.add(user)
        db.session.commit()
        
        # Récupérer l'utilisateur
        retrieved_user = User.query.filter_by(username='testuser').first()
        
        # Vérifier les propriétés
        self.assertIsNotNone(retrieved_user)
        self.assertEqual(retrieved_user.email, '<EMAIL>')
        self.assertEqual(retrieved_user.first_name, 'Test')
        self.assertEqual(retrieved_user.last_name, 'User')
        self.assertTrue(retrieved_user.check_password('password123'))
        self.assertFalse(retrieved_user.check_password('wrongpassword'))
        
        # Vérifier les méthodes
        self.assertEqual(str(retrieved_user), 'testuser')
        self.assertIn('Test', repr(retrieved_user))
    
    def test_business_model(self):
        """Test du modèle Business"""
        from app.modules.accounts.models import Business, BusinessType
        
        # Créer un type d'entreprise
        business_type = BusinessType(
            name='Restaurant',
            description='Restaurant business type'
        )
        db.session.add(business_type)
        db.session.commit()
        
        # Créer une entreprise
        business = Business(
            name='Test Restaurant',
            business_type_id=business_type.id,
            address='123 Test St',
            phone='555-1234',
            email='<EMAIL>'
        )
        
        db.session.add(business)
        db.session.commit()
        
        # Récupérer l'entreprise
        retrieved_business = Business.query.filter_by(name='Test Restaurant').first()
        
        # Vérifier les propriétés
        self.assertIsNotNone(retrieved_business)
        self.assertEqual(retrieved_business.name, 'Test Restaurant')
        self.assertEqual(retrieved_business.business_type.name, 'Restaurant')
        
        # Vérifier les relations
        self.assertEqual(business_type.businesses[0], business)
    
    def test_product_model(self):
        """Test du modèle Product"""
        from app.modules.catalog.models import Product, Category
        
        # Créer une catégorie
        category = Category(
            name='Test Category',
            business_id_fk=1
        )
        db.session.add(category)
        db.session.commit()
        
        # Créer un produit
        product = Product(
            sku='TEST001',
            name='Test Product',
            description='A test product',
            category_id_fk=category.id,
            price_cents=1000,  # 10.00€
            cost_cents=500,    # 5.00€
            business_id_fk=1
        )
        
        db.session.add(product)
        db.session.commit()
        
        # Récupérer le produit
        retrieved_product = Product.query.filter_by(sku='TEST001').first()
        
        # Vérifier les propriétés
        self.assertIsNotNone(retrieved_product)
        self.assertEqual(retrieved_product.name, 'Test Product')
        self.assertEqual(retrieved_product.price, 10.00)
        self.assertEqual(retrieved_product.cost, 5.00)
        self.assertEqual(retrieved_product.category.name, 'Test Category')
    
    def test_pos_order_model(self):
        """Test du modèle POS Order"""
        from app.modules.pos.models import POSOrder, POSOrderItem
        from app.modules.catalog.models import Product
        
        # Créer un produit
        product = Product(
            sku='ORDER001',
            name='Order Product',
            price_cents=1500,  # 15.00€
            business_id_fk=1
        )
        db.session.add(product)
        db.session.commit()
        
        # Créer une commande
        order = POSOrder(
            business_id_fk=1,
            customer_name='Test Customer',
            total_cents=1500,
            status='completed'
        )
        db.session.add(order)
        db.session.commit()
        
        # Créer un item de commande
        order_item = POSOrderItem(
            order_id_fk=order.id,
            product_id_fk=product.id,
            quantity=1,
            unit_price_cents=1500,
            total_price_cents=1500
        )
        db.session.add(order_item)
        db.session.commit()
        
        # Récupérer la commande
        retrieved_order = POSOrder.query.filter_by(id=order.id).first()
        
        # Vérifier les propriétés
        self.assertIsNotNone(retrieved_order)
        self.assertEqual(retrieved_order.customer_name, 'Test Customer')
        self.assertEqual(retrieved_order.total, 15.00)
        self.assertEqual(len(retrieved_order.items), 1)
        self.assertEqual(retrieved_order.items[0].product.name, 'Order Product')

if __name__ == '__main__':
    unittest.main()