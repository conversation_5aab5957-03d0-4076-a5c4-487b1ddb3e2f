# Configuration Prometheus pour l'application POS System
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # Fichiers de règles
  - "rules/*.yml"

scrape_configs:
  # Monitoring de l'application
  - job_name: 'pos-system'
    static_configs:
      - targets: ['app:5000']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # Monitoring de la base de données
  - job_name: 'postgresql'
    static_configs:
      - targets: ['database:9187']
    scrape_interval: 30s

  # Monitoring de Redis
  - job_name: 'redis'
    static_configs:
      - targets: ['cache:9121']
    scrape_interval: 30s

  # Monitoring du système
  - job_name: 'node'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 15s

  # Monitoring des conteneurs
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 15s