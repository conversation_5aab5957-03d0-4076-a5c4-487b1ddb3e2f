{% extends "base.html" %}

{% block title %}Modifier {{ supplier.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- En-tête -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-1">
                        <i class="fas fa-edit text-warning me-2"></i>
                        Modifier {{ supplier.name }}
                    </h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="{{ url_for('suppliers.index') }}">Fournisseurs</a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="{{ url_for('suppliers.view_supplier', id=supplier.id) }}">{{ supplier.name }}</a>
                            </li>
                            <li class="breadcrumb-item active">Modifier</li>
                        </ol>
                    </nav>
                </div>
                <div class="btn-group">
                    <a href="{{ url_for('suppliers.view_supplier', id=supplier.id) }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Retour aux détails
                    </a>
                    <a href="{{ url_for('suppliers.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-list me-1"></i>Liste des fournisseurs
                    </a>
                </div>
            </div>

            <!-- Formulaire -->
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card shadow-sm">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-truck me-2"></i>
                                Modifier les informations du fournisseur
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" novalidate>
                                {{ form.hidden_tag() }}
                                
                                <!-- Informations de base -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="text-warning border-bottom pb-2 mb-3">
                                            <i class="fas fa-info-circle me-1"></i>
                                            Informations de base
                                        </h6>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        {{ form.name.label(class="form-label") }}
                                        {{ form.name(class="form-control" + (" is-invalid" if form.name.errors else "")) }}
                                        {% if form.name.errors %}
                                            <div class="invalid-feedback">
                                                {% for error in form.name.errors %}{{ error }}{% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Code fournisseur</label>
                                        <input type="text" class="form-control" value="{{ supplier.supplier_code or 'Généré automatiquement' }}" readonly>
                                        <small class="form-text text-muted">Le code ne peut pas être modifié</small>
                                    </div>
                                    
                                    <div class="col-md-12 mb-3">
                                        {{ form.company_name.label(class="form-label") }}
                                        {{ form.company_name(class="form-control" + (" is-invalid" if form.company_name.errors else "")) }}
                                        {% if form.company_name.errors %}
                                            <div class="invalid-feedback">
                                                {% for error in form.company_name.errors %}{{ error }}{% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Contact -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="text-warning border-bottom pb-2 mb-3">
                                            <i class="fas fa-address-book me-1"></i>
                                            Contact
                                        </h6>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        {{ form.email.label(class="form-label") }}
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                            {{ form.email(class="form-control" + (" is-invalid" if form.email.errors else "")) }}
                                            {% if form.email.errors %}
                                                <div class="invalid-feedback">
                                                    {% for error in form.email.errors %}{{ error }}{% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        {{ form.phone.label(class="form-label") }}
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-phone"></i></span>
                                            {{ form.phone(class="form-control" + (" is-invalid" if form.phone.errors else "")) }}
                                            {% if form.phone.errors %}
                                                <div class="invalid-feedback">
                                                    {% for error in form.phone.errors %}{{ error }}{% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-12 mb-3">
                                        {{ form.website.label(class="form-label") }}
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-globe"></i></span>
                                            {{ form.website(class="form-control" + (" is-invalid" if form.website.errors else ""), placeholder="https://www.exemple.com") }}
                                            {% if form.website.errors %}
                                                <div class="invalid-feedback">
                                                    {% for error in form.website.errors %}{{ error }}{% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>

                                <!-- Informations commerciales -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="text-warning border-bottom pb-2 mb-3">
                                            <i class="fas fa-handshake me-1"></i>
                                            Informations commerciales
                                        </h6>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        {{ form.category.label(class="form-label") }}
                                        {{ form.category(class="form-control" + (" is-invalid" if form.category.errors else "")) }}
                                        {% if form.category.errors %}
                                            <div class="invalid-feedback">
                                                {% for error in form.category.errors %}{{ error }}{% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        {{ form.tax_number.label(class="form-label") }}
                                        {{ form.tax_number(class="form-control" + (" is-invalid" if form.tax_number.errors else "")) }}
                                        {% if form.tax_number.errors %}
                                            <div class="invalid-feedback">
                                                {% for error in form.tax_number.errors %}{{ error }}{% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        {{ form.payment_terms.label(class="form-label") }}
                                        {{ form.payment_terms(class="form-control" + (" is-invalid" if form.payment_terms.errors else "")) }}
                                        {% if form.payment_terms.errors %}
                                            <div class="invalid-feedback">
                                                {% for error in form.payment_terms.errors %}{{ error }}{% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        {{ form.credit_limit.label(class="form-label") }}
                                        <div class="input-group">
                                            {{ form.credit_limit(class="form-control" + (" is-invalid" if form.credit_limit.errors else ""), step="0.01") }}
                                            <span class="input-group-text">€</span>
                                            {% if form.credit_limit.errors %}
                                                <div class="invalid-feedback">
                                                    {% for error in form.credit_limit.errors %}{{ error }}{% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>

                                <!-- Notes et statut -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="text-warning border-bottom pb-2 mb-3">
                                            <i class="fas fa-sticky-note me-1"></i>
                                            Notes et statut
                                        </h6>
                                    </div>
                                    
                                    <div class="col-md-12 mb-3">
                                        {{ form.notes.label(class="form-label") }}
                                        {{ form.notes(class="form-control" + (" is-invalid" if form.notes.errors else ""), rows="4") }}
                                        {% if form.notes.errors %}
                                            <div class="invalid-feedback">
                                                {% for error in form.notes.errors %}{{ error }}{% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                    
                                    <div class="col-md-12">
                                        <div class="form-check form-switch">
                                            {{ form.is_active(class="form-check-input") }}
                                            {{ form.is_active.label(class="form-check-label") }}
                                        </div>
                                    </div>
                                </div>

                                <!-- Informations de suivi -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="text-muted border-bottom pb-2 mb-3">
                                            <i class="fas fa-clock me-1"></i>
                                            Informations de suivi
                                        </h6>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <label class="form-label">Créé le</label>
                                        <input type="text" class="form-control" value="{{ supplier.created_at.strftime('%d/%m/%Y à %H:%M') }}" readonly>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <label class="form-label">Dernière modification</label>
                                        <input type="text" class="form-control" value="{{ supplier.updated_at.strftime('%d/%m/%Y à %H:%M') }}" readonly>
                                    </div>
                                </div>

                                <!-- Boutons d'action -->
                                <div class="row">
                                    <div class="col-12">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <button type="button" class="btn btn-outline-danger me-2" onclick="confirmDelete({{ supplier.id }}, '{{ supplier.name }}')">
                                                    <i class="fas fa-trash me-1"></i>Supprimer ce fournisseur
                                                </button>
                                            </div>
                                            <div>
                                                <a href="{{ url_for('suppliers.view_supplier', id=supplier.id) }}" class="btn btn-outline-secondary me-2">
                                                    <i class="fas fa-times me-1"></i>Annuler
                                                </a>
                                                <button type="submit" class="btn btn-warning">
                                                    <i class="fas fa-save me-1"></i>Enregistrer les modifications
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Informations supplémentaires -->
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">Actions rapides</h6>
                                </div>
                                <div class="card-body">
                                    <div class="d-grid gap-2">
                                        <a href="{{ url_for('suppliers.add_contact', supplier_id=supplier.id) }}" class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-user-plus me-1"></i>Ajouter un contact
                                        </a>
                                        <a href="{{ url_for('suppliers.add_product', supplier_id=supplier.id) }}" class="btn btn-outline-success btn-sm">
                                            <i class="fas fa-box me-1"></i>Ajouter un produit
                                        </a>
                                        <a href="{{ url_for('suppliers.add_evaluation', supplier_id=supplier.id) }}" class="btn btn-outline-warning btn-sm">
                                            <i class="fas fa-star me-1"></i>Ajouter une évaluation
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">Statistiques rapides</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row text-center">
                                        <div class="col-4">
                                            <div class="mb-1">
                                                <i class="fas fa-users text-primary"></i>
                                            </div>
                                            <small class="text-muted">Contacts</small>
                                            <div class="fw-bold">{{ supplier.contacts|length if supplier.contacts else 0 }}</div>
                                        </div>
                                        <div class="col-4">
                                            <div class="mb-1">
                                                <i class="fas fa-boxes text-success"></i>
                                            </div>
                                            <small class="text-muted">Produits</small>
                                            <div class="fw-bold">{{ supplier.products|length if supplier.products else 0 }}</div>
                                        </div>
                                        <div class="col-4">
                                            <div class="mb-1">
                                                <i class="fas fa-star text-warning"></i>
                                            </div>
                                            <small class="text-muted">Note moy.</small>
                                            <div class="fw-bold">
                                                {% if supplier.average_rating %}
                                                {{ "%.1f"|format(supplier.average_rating) }}
                                                {% else %}
                                                N/A
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de confirmation de suppression -->
<div id="deleteModal" class="hidden fixed inset-0 z-50 flex items-center justify-center modal-backdrop">
  <div class="modal fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
    <div class="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
      <div class="px-6 py-4 border-b">
        <h3 class="text-lg font-semibold text-gray-900">Confirmer la suppression</h3>
        <button type="button" class="close-modal absolute top-4 right-4 text-gray-500 hover:text-gray-700" onclick="closeModal('deleteModal')">
          <i class="fas fa-times"></i>
        </button>
      </div>
      
      <div class="px-6 py-4">
        <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <i class="fas fa-exclamation-triangle text-yellow-400"></i>
            </div>
            <div class="ml-3">
              <p class="text-sm text-yellow-700">
                <strong>Attention !</strong> Cette action est irréversible.
              </p>
            </div>
          </div>
        </div>
        <p class="text-gray-700 mb-2">
          Êtes-vous sûr de vouloir supprimer le fournisseur <strong id="supplierName"></strong> ?
        </p>
        <p class="text-sm text-gray-500">
          Note: La suppression ne sera possible que si le fournisseur n'a pas de produits associés.
        </p>
      </div>
      
      <div class="px-6 py-4 border-t bg-gray-50 flex justify-end space-x-3">
        <button type="button" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-100" onclick="closeModal('deleteModal')">
          Annuler
        </button>
        <form id="deleteForm" method="POST" style="display: inline;">
          <button type="submit" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">
            Supprimer définitivement
          </button>
        </form>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('suppliers.static', filename='js/modals.js') }}"></script>
{% endblock %}