"""Routes du module d'Intelligence Artificielle"""
from flask import render_template, request, jsonify, current_app
from datetime import datetime, timedelta
import logging

from app.modules.ai import bp
from app.modules.ai.models import (
    SalesForecastModel, 
    ProductRecommendationModel, 
    CustomerSegmentationModel,
    InventoryOptimizationModel,
    ChurnPredictionModel,
    SentimentAnalysisModel
)
from app.modules.sales.models import Order as POSOrder
from app.modules.catalog.models import Product
from app.modules.inventory.models import StockMovement
from app.modules.customers.models import Customer
from app.extensions import db

logger = logging.getLogger(__name__)

@bp.route('/')
def index():
    """Page d'accueil de l'IA"""
    return render_template('ai/index.html')

@bp.route('/sales_forecast')
def sales_forecast():
    """Page de prévision des ventes"""
    return render_template('ai/sales_forecast.html')

@bp.route('/api/sales_forecast/train', methods=['POST'])
def train_sales_forecast():
    """Entraîne le modèle de prévision des ventes"""
    try:
        # Récupérer les données de vente des 30 derniers jours
        thirty_days_ago = datetime.utcnow() - timedelta(days=30)
        orders = POSOrder.query.filter(
            POSOrder.created_at >= thirty_days_ago
        ).all()
        
        if not orders:
            return jsonify({
                'success': False,
                'message': "Aucune donnée de vente disponible pour l'entraînement"
            }), 400
        
        # Entraîner le modèle
        model = SalesForecastModel()
        metrics = model.train(orders)
        
        # Sauvegarder le modèle
        model.save_model('instance/sales_forecast_model.pkl')
        
        return jsonify({
            'success': True,
            'message': 'Modèle de prévision des ventes entraîné avec succès',
            'metrics': metrics
        })
        
    except Exception as e:
        logger.error(f"Erreur lors de l'entraînement du modèle de prévision: {e}")
        return jsonify({
            'success': False,
            'message': f"Erreur lors de l'entraînement: {str(e)}"
        }), 500

@bp.route('/api/sales_forecast/predict', methods=['POST'])
def predict_sales():
    """Prédit les ventes futures"""
    try:
        # Récupérer les paramètres de la requête
        data = request.get_json()
        
        # Charger le modèle
        model = SalesForecastModel()
        model.load_model('instance/sales_forecast_model.pkl')
        
        if not model.is_trained:
            return jsonify({
                'success': False,
                'message': "Le modèle n'est pas encore entraîné"
            }), 400
        
        # Faire la prédiction
        prediction = model.predict(data)
        
        return jsonify({
            'success': True,
            'prediction': prediction,
            'currency': 'EUR'
        })
        
    except Exception as e:
        logger.error(f"Erreur lors de la prédiction des ventes: {e}")
        return jsonify({
            'success': False,
            'message': f"Erreur lors de la prédiction: {str(e)}"
        }), 500

@bp.route('/recommendations')
def recommendations():
    """Page de recommandations de produits"""
    return render_template('ai/recommendations.html')

@bp.route('/api/recommendations/train', methods=['POST'])
def train_recommendations():
    """Entraîne le modèle de recommandation de produits"""
    try:
        # Récupérer toutes les commandes
        orders = POSOrder.query.all()
        
        if not orders:
            return jsonify({
                'success': False,
                'message': "Aucune donnée de commande disponible pour l'entraînement"
            }), 400
        
        # Entraîner le modèle
        model = ProductRecommendationModel()
        metrics = model.train(orders)
        
        # Sauvegarder le modèle
        import joblib
        joblib.dump(model, 'instance/recommendation_model.pkl')
        
        return jsonify({
            'success': True,
            'message': 'Modèle de recommandation entraîné avec succès',
            'metrics': metrics
        })
        
    except Exception as e:
        logger.error(f"Erreur lors de l'entraînement du modèle de recommandation: {e}")
        return jsonify({
            'success': False,
            'message': f"Erreur lors de l'entraînement: {str(e)}"
        }), 500

@bp.route('/api/recommendations/product/<int:product_id>')
def get_product_recommendations(product_id):
    """Obtient les recommandations pour un produit"""
    try:
        # Charger le modèle
        import joblib
        model = joblib.load('instance/recommendation_model.pkl')
        
        if not model.is_trained:
            return jsonify({
                'success': False,
                'message': "Le modèle n'est pas encore entraîné"
            }), 400
        
        # Obtenir les recommandations
        recommendations = model.get_recommendations(product_id)
        
        # Récupérer les informations des produits recommandés
        recommended_products = []
        for rec in recommendations:
            product = Product.query.get(rec['product_id'])
            if product:
                recommended_products.append({
                    'id': product.id,
                    'name': product.name,
                    'sku': product.sku,
                    'confidence': rec['confidence'],
                    'support': rec['support']
                })
        
        return jsonify({
            'success': True,
            'recommendations': recommended_products
        })
        
    except Exception as e:
        logger.error(f"Erreur lors de l'obtention des recommandations: {e}")
        return jsonify({
            'success': False,
            'message': f"Erreur lors de l'obtention des recommandations: {str(e)}"
        }), 500

@bp.route('/customer_segmentation')
def customer_segmentation():
    """Page de segmentation client"""
    return render_template('ai/customer_segmentation.html')

@bp.route('/api/customer_segmentation/train', methods=['POST'])
def train_customer_segmentation():
    """Entraîne le modèle de segmentation client"""
    try:
        # Récupérer toutes les commandes
        orders = POSOrder.query.all()
        
        if not orders:
            return jsonify({
                'success': False,
                'message': "Aucune donnée de commande disponible pour l'entraînement"
            }), 400
        
        # Entraîner le modèle
        model = CustomerSegmentationModel()
        metrics = model.train(orders)
        
        # Sauvegarder le modèle
        import joblib
        joblib.dump(model, 'instance/segmentation_model.pkl')
        
        return jsonify({
            'success': True,
            'message': 'Modèle de segmentation client entraîné avec succès',
            'metrics': metrics
        })
        
    except Exception as e:
        logger.error(f"Erreur lors de l'entraînement du modèle de segmentation: {e}")
        return jsonify({
            'success': False,
            'message': f"Erreur lors de l'entraînement: {str(e)}"
        }), 500

@bp.route('/api/customer_segmentation/analyze/<int:customer_id>')
def analyze_customer_segment(customer_id):
    """Analyse le segment d'un client"""
    try:
        # Charger le modèle
        import joblib
        model = joblib.load('instance/segmentation_model.pkl')
        
        if not model.is_trained:
            return jsonify({
                'success': False,
                'message': "Le modèle n'est pas encore entraîné"
            }), 400
        
        # Récupérer l'historique du client
        customer_orders = POSOrder.query.filter_by(customer_id_fk=customer_id).all()
        
        if not customer_orders:
            return jsonify({
                'success': False,
                'message': "Aucun historique de commande pour ce client"
            }), 400
        
        # Calculer les caractéristiques du client
        total_spent = sum(order.total_cents / 100 for order in customer_orders)
        order_count = len(customer_orders)
        avg_order_value = total_spent / order_count if order_count > 0 else 0
        frequency = order_count
        
        customer_features = {
            'total_spent': total_spent,
            'order_count': order_count,
            'avg_order_value': avg_order_value,
            'frequency': frequency
        }
        
        # Prédire le segment
        segment = model.predict_segment(customer_features)
        
        # Obtenir les caractéristiques des segments
        segment_characteristics = model.get_segment_characteristics()
        
        return jsonify({
            'success': True,
            'segment': segment,
            'customer_features': customer_features,
            'segment_characteristics': segment_characteristics
        })
        
    except Exception as e:
        logger.error(f"Erreur lors de l'analyse du segment client: {e}")
        return jsonify({
            'success': False,
            'message': f"Erreur lors de l'analyse: {str(e)}"
        }), 500

@bp.route('/inventory_optimization')
def inventory_optimization():
    """Page d'optimisation des stocks"""
    return render_template('ai/inventory_optimization.html')

@bp.route('/api/inventory_optimization/analyze')
def analyze_inventory():
    """Analyse l'optimisation des stocks"""
    try:
        # Récupérer les mouvements de stock
        stock_movements = StockMovement.query.all()
        
        if not stock_movements:
            return jsonify({
                'success': False,
                'message': "Aucune donnée de stock disponible"
            }), 400
        
        # Analyser les performances
        model = InventoryOptimizationModel()
        analysis = model.analyze_inventory_performance(stock_movements)
        
        # Récupérer les informations des produits à risque
        low_stock_products = []
        for item in analysis.get('low_stock_items', []):
            product = Product.query.get(item['product_id'])
            if product:
                low_stock_products.append({
                    'id': product.id,
                    'name': product.name,
                    'sku': product.sku,
                    'current_stock': item['current_stock']
                })
        
        high_stock_products = []
        for item in analysis.get('high_stock_items', []):
            product = Product.query.get(item['product_id'])
            if product:
                high_stock_products.append({
                    'id': product.id,
                    'name': product.name,
                    'sku': product.sku,
                    'current_stock': item['current_stock']
                })
        
        analysis['low_stock_products'] = low_stock_products
        analysis['high_stock_products'] = high_stock_products
        
        return jsonify({
            'success': True,
            'analysis': analysis
        })
        
    except Exception as e:
        logger.error(f"Erreur lors de l'analyse des stocks: {e}")
        return jsonify({
            'success': False,
            'message': f"Erreur lors de l'analyse: {str(e)}"
        }), 500

# Nouvelles routes pour les fonctionnalités supplémentaires

@bp.route('/churn_prediction')
def churn_prediction():
    """Page de prédiction de churn client"""
    return render_template('ai/churn_prediction.html')

@bp.route('/api/churn_prediction/train', methods=['POST'])
def train_churn_prediction():
    """Entraîne le modèle de prédiction de churn"""
    try:
        # Récupérer tous les clients et commandes
        customers = Customer.query.all()
        orders = POSOrder.query.all()
        
        if not customers:
            return jsonify({
                'success': False,
                'message': "Aucune donnée client disponible pour l'entraînement"
            }), 400
        
        # Entraîner le modèle
        model = ChurnPredictionModel()
        metrics = model.train(customers, orders)
        
        # Sauvegarder le modèle
        import joblib
        joblib.dump(model, 'instance/churn_prediction_model.pkl')
        
        return jsonify({
            'success': True,
            'message': 'Modèle de prédiction de churn entraîné avec succès',
            'metrics': metrics
        })
        
    except Exception as e:
        logger.error(f"Erreur lors de l'entraînement du modèle de churn: {e}")
        return jsonify({
            'success': False,
            'message': f"Erreur lors de l'entraînement: {str(e)}"
        }), 500

@bp.route('/api/churn_prediction/risk_customers')
def get_churn_risk_customers():
    """Obtient les clients à risque de churn"""
    try:
        # Charger le modèle
        import joblib
        model = joblib.load('instance/churn_prediction_model.pkl')
        
        if not model.is_trained:
            return jsonify({
                'success': False,
                'message': "Le modèle n'est pas encore entraîné"
            }), 400
        
        # Récupérer les clients et commandes
        customers = Customer.query.all()
        orders = POSOrder.query.all()
        
        # Obtenir les clients à risque
        risk_customers = model.get_churn_risk_customers(customers, orders)
        
        # Récupérer les informations des clients
        customer_details = []
        for risk_customer in risk_customers:
            customer = Customer.query.get(risk_customer['customer_id'])
            if customer:
                customer_details.append({
                    'id': customer.id,
                    'name': f"{customer.first_name} {customer.last_name}",
                    'email': customer.email,
                    'churn_probability': risk_customer['churn_probability'],
                    'days_since_last_order': risk_customer['days_since_last_order'],
                    'total_spent': risk_customer['total_spent']
                })
        
        return jsonify({
            'success': True,
            'risk_customers': customer_details
        })
        
    except Exception as e:
        logger.error(f"Erreur lors de l'obtention des clients à risque: {e}")
        return jsonify({
            'success': False,
            'message': f"Erreur lors de l'obtention des clients à risque: {str(e)}"
        }), 500

@bp.route('/sentiment_analysis')
def sentiment_analysis():
    """Page d'analyse de sentiment"""
    return render_template('ai/sentiment_analysis.html')

@bp.route('/api/sentiment_analysis/analyze', methods=['POST'])
def analyze_sentiment():
    """Analyse le sentiment d'un texte"""
    try:
        # Récupérer le texte de la requête
        data = request.get_json()
        text = data.get('text', '')
        
        if not text:
            return jsonify({
                'success': False,
                'message': "Aucun texte fourni pour l'analyse"
            }), 400
        
        # Analyser le sentiment
        model = SentimentAnalysisModel()
        sentiment = model.analyze_sentiment(text)
        
        return jsonify({
            'success': True,
            'sentiment': sentiment
        })
        
    except Exception as e:
        logger.error(f"Erreur lors de l'analyse de sentiment: {e}")
        return jsonify({
            'success': False,
            'message': f"Erreur lors de l'analyse de sentiment: {str(e)}"
        }), 500