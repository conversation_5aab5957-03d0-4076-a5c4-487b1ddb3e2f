<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#007bff">
    <meta name="description" content="POS System GT2 - Commandes Mobile">
    
    <title>Commandes Mobile</title>
    
    <!-- PWA Manifest -->
    <link rel="manifest" href="/static/pwa/manifest.json">
    
    <!-- Icons -->
    <link rel="icon" type="image/png" sizes="192x192" href="/static/pwa/icons/icon-192x192.png">
    <link rel="apple-touch-icon" sizes="180x180" href="/static/pwa/icons/icon-180x180.png">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="/static/css/mobile.css">
</head>
<body>
    <div class="pos-interface">
        <!-- Header -->
        <header class="pos-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h5 mb-0">Commandes</h1>
                    <small id="connectionStatus">En ligne</small>
                </div>
                <div>
                    <button class="btn btn-sm btn-light me-2" id="refreshButton">
                        <i class="fas fa-sync"></i>
                    </button>
                    <button class="btn btn-sm btn-light" id="backButton">
                        <i class="fas fa-arrow-left"></i>
                    </button>
                </div>
            </div>
        </header>
        
        <!-- Content -->
        <main class="pos-content">
            <!-- Filtres -->
            <div class="mb-3">
                <div class="d-flex overflow-auto mb-2">
                    <button class="btn btn-outline-primary btn-sm me-2 active">Toutes</button>
                    <button class="btn btn-outline-warning btn-sm me-2">En cours</button>
                    <button class="btn btn-outline-success btn-sm me-2">Terminées</button>
                    <button class="btn btn-outline-danger btn-sm">Annulées</button>
                </div>
                
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-search"></i>
                    </span>
                    <input type="text" class="form-control" placeholder="Rechercher une commande...">
                </div>
            </div>
            
            <!-- Liste des commandes -->
            <div class="orders-list">
                <div class="order-item">
                    <div class="order-header">
                        <div class="order-number">#CMD-00125</div>
                        <div class="order-time">14:30</div>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="order-customer">Client Comptoir</div>
                            <div class="order-items">3 articles</div>
                        </div>
                        <div class="text-end">
                            <div class="order-total">€24.50</div>
                            <div class="order-status bg-warning text-dark">En préparation</div>
                        </div>
                    </div>
                </div>
                
                <div class="order-item">
                    <div class="order-header">
                        <div class="order-number">#CMD-00124</div>
                        <div class="order-time">14:25</div>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="order-customer">Jean Dupont</div>
                            <div class="order-items">2 articles</div>
                        </div>
                        <div class="text-end">
                            <div class="order-total">€15.00</div>
                            <div class="order-status bg-success text-white">Terminée</div>
                        </div>
                    </div>
                </div>
                
                <div class="order-item">
                    <div class="order-header">
                        <div class="order-number">#CMD-00123</div>
                        <div class="order-time">14:15</div>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="order-customer">Marie Martin</div>
                            <div class="order-items">5 articles</div>
                        </div>
                        <div class="text-end">
                            <div class="order-total">€32.75</div>
                            <div class="order-status bg-success text-white">Terminée</div>
                        </div>
                    </div>
                </div>
                
                <div class="order-item">
                    <div class="order-header">
                        <div class="order-number">#CMD-00122</div>
                        <div class="order-time">14:10</div>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="order-customer">Client Comptoir</div>
                            <div class="order-items">1 article</div>
                        </div>
                        <div class="text-end">
                            <div class="order-total">€8.50</div>
                            <div class="order-status bg-danger text-white">Annulée</div>
                        </div>
                    </div>
                </div>
                
                <div class="order-item">
                    <div class="order-header">
                        <div class="order-number">#CMD-00121</div>
                        <div class="order-time">14:05</div>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="order-customer">Pierre Lambert</div>
                            <div class="order-items">4 articles</div>
                        </div>
                        <div class="text-end">
                            <div class="order-total">€28.00</div>
                            <div class="order-status bg-warning text-dark">En préparation</div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
        
        <!-- Footer -->
        <footer class="pos-footer">
            <div class="d-flex justify-content-between">
                <div>
                    <small>Aujourd'hui: <span id="todayOrders">12</span> commandes</small>
                </div>
                <div>
                    <small>Total: <strong id="todayTotal">€245.50</strong></small>
                </div>
            </div>
        </footer>
    </div>
    
    <!-- Toast Container -->
    <div id="toastContainer" class="toast-container"></div>
    
    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/mobile.js"></script>
    
    <!-- Service Worker Registration -->
    <script>
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('/static/pwa/service-worker.js')
                    .then(function(registration) {
                        console.log('Service Worker enregistré avec succès:', registration);
                    })
                    .catch(function(error) {
                        console.log('Erreur lors de l\'enregistrement du Service Worker:', error);
                    });
            });
        }
        
        // Fonctionnalité de retour en arrière
        document.getElementById('backButton').addEventListener('click', function() {
            window.history.back();
        });
        
        // Fonctionnalité d'actualisation
        document.getElementById('refreshButton').addEventListener('click', function() {
            // Animation de rafraîchissement
            const icon = this.querySelector('i');
            icon.classList.add('fa-spin');
            
            // Simuler le rafraîchissement
            setTimeout(() => {
                icon.classList.remove('fa-spin');
                showNotification('Commandes actualisées', 'success');
            }, 1000);
        });
        
        // Fonction pour afficher les notifications
        function showNotification(message, type = 'info') {
            const toastContainer = document.getElementById('toastContainer') || 
                document.createElement('div');
            
            if (!document.getElementById('toastContainer')) {
                toastContainer.id = 'toastContainer';
                toastContainer.className = 'toast-container';
                document.body.appendChild(toastContainer);
            }
            
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.innerHTML = `
                <div class="toast-message">${message}</div>
            `;
            
            toastContainer.appendChild(toast);
            
            // Supprimer automatiquement après 3 secondes
            setTimeout(() => {
                toast.remove();
            }, 3000);
        }
    </script>
</body>
</html>