from __future__ import annotations

from datetime import datetime, date
from typing import Optional, Dict, Any, List
from enum import Enum as PyEnum
from sqlalchemy import Column, Integer, String, Text, DateTime, Date, Boolean, Enum, JSON
from sqlalchemy.orm import relationship

from app.extensions import db


class ReportType(PyEnum):
    """Types de rapports disponibles"""
    SALES = "sales"  # Rapports de ventes
    FINANCIAL = "financial"  # Rapports financiers
    INVENTORY = "inventory"  # Rapports d'inventaire
    PURCHASING = "purchasing"  # Rapports d'achats
    CUSTOMER = "customer"  # Rapports clients
    SUPPLIER = "supplier"  # Rapports fournisseurs
    STAFF = "staff"  # Rapports personnel
    PRODUCTION = "production"  # Rapports production
    ANALYTICS = "analytics"  # Rapports d'analyse
    CUSTOM = "custom"  # Rapports personnalisés


class ReportFrequency(PyEnum):
    """Fréquences de génération des rapports"""
    MANUAL = "manual"  # Manuel
    DAILY = "daily"  # Quotidien
    WEEKLY = "weekly"  # Hebdomadaire
    MONTHLY = "monthly"  # Mensuel
    QUARTERLY = "quarterly"  # Trimestriel
    YEARLY = "yearly"  # Annuel


class ReportFormat(PyEnum):
    """Formats d'export des rapports"""
    HTML = "html"  # Page web
    PDF = "pdf"  # Document PDF
    EXCEL = "excel"  # Fichier Excel
    CSV = "csv"  # Fichier CSV
    JSON = "json"  # Format JSON


class ReportStatus(PyEnum):
    """Statuts d'exécution des rapports"""
    PENDING = "pending"  # En attente
    RUNNING = "running"  # En cours d'exécution
    COMPLETED = "completed"  # Terminé
    FAILED = "failed"  # Échec
    CANCELLED = "cancelled"  # Annulé


class ReportTemplate(db.Model):
    """Modèle pour les templates de rapports"""
    __tablename__ = "report_templates"
    id = Column(Integer, primary_key=True)
    business_id_fk = Column(Integer, db.ForeignKey("businesses.id"), nullable=False, index=True)
    
    # Informations de base
    name = Column(String(255), nullable=False, index=True)
    description = Column(Text, nullable=True)
    report_type = Column(Enum(ReportType), nullable=False, index=True)
    
    # Configuration du rapport
    data_sources = Column(JSON, nullable=False)  # Sources de données (tables, requêtes)
    filters = Column(JSON, nullable=True)  # Filtres par défaut
    grouping = Column(JSON, nullable=True)  # Regroupements
    sorting = Column(JSON, nullable=True)  # Tri
    columns = Column(JSON, nullable=False)  # Colonnes à afficher
    charts = Column(JSON, nullable=True)  # Configuration des graphiques
    
    # Mise en forme
    template_html = Column(Text, nullable=True)  # Template HTML personnalisé
    css_styles = Column(Text, nullable=True)  # Styles CSS
    header_footer = Column(JSON, nullable=True)  # En-tête et pied de page
    
    # Permissions et visibilité
    is_public = Column(Boolean, default=False, nullable=False)
    created_by = Column(String(255), nullable=False)
    allowed_roles = Column(JSON, nullable=True)  # Rôles autorisés
    
    # Statut
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Dates
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Relations
    schedules = relationship("ReportSchedule", back_populates="template", lazy="dynamic", cascade="all, delete-orphan")
    executions = relationship("ReportExecution", back_populates="template", lazy="dynamic", cascade="all, delete-orphan")
    
    def get_executions_count(self):
        """Retourne le nombre d'exécutions"""
        return self.executions.count()
    
    def get_last_execution(self):
        """Retourne la dernière exécution"""
        return self.executions.order_by(ReportExecution.created_at.desc()).first()
    
    __table_args__ = (
        db.Index("idx_template_business", "business_id_fk"),
        db.Index("idx_template_type", "report_type"),
        db.Index("idx_template_name", "name"),
    )


class ReportSchedule(db.Model):
    """Modèle pour la planification des rapports"""
    __tablename__ = "report_schedules"
    id = Column(Integer, primary_key=True)
    business_id_fk = Column(Integer, db.ForeignKey("businesses.id"), nullable=False, index=True)
    template_id_fk = Column(Integer, db.ForeignKey("report_templates.id"), nullable=False, index=True)
    
    # Informations de base
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    
    # Planification
    frequency = Column(Enum(ReportFrequency), nullable=False)
    cron_expression = Column(String(100), nullable=True)  # Expression cron pour planifications complexes
    time_of_day = Column(String(5), nullable=True)  # Heure d'exécution (HH:MM)
    day_of_week = Column(Integer, nullable=True)  # Jour de la semaine (0-6)
    day_of_month = Column(Integer, nullable=True)  # Jour du mois (1-31)
    
    # Configuration d'export
    output_formats = Column(JSON, nullable=False)  # Formats de sortie
    auto_export = Column(Boolean, default=False, nullable=False)  # Export automatique
    export_path = Column(String(500), nullable=True)  # Chemin d'export
    
    # Destinataires
    email_recipients = Column(JSON, nullable=True)  # Emails des destinataires
    send_email = Column(Boolean, default=False, nullable=False)
    email_subject = Column(String(255), nullable=True)
    email_body = Column(Text, nullable=True)
    
    # Filtres personnalisés pour cette planification
    custom_filters = Column(JSON, nullable=True)
    
    # Statut
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Dates
    next_run = Column(DateTime, nullable=True)
    last_run = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Relations
    template = relationship("ReportTemplate", back_populates="schedules")
    executions = relationship("ReportExecution", back_populates="schedule", lazy="dynamic")
    
    def calculate_next_run(self):
        """Calcule la prochaine date d'exécution"""
        from datetime import timedelta
        
        if not self.is_active:
            self.next_run = None
            return
        
        now = datetime.utcnow()
        
        if self.frequency == ReportFrequency.DAILY:
            self.next_run = now + timedelta(days=1)
        elif self.frequency == ReportFrequency.WEEKLY:
            self.next_run = now + timedelta(weeks=1)
        elif self.frequency == ReportFrequency.MONTHLY:
            # Ajouter un mois (approximation)
            if now.month == 12:
                self.next_run = now.replace(year=now.year + 1, month=1)
            else:
                self.next_run = now.replace(month=now.month + 1)
        elif self.frequency == ReportFrequency.QUARTERLY:
            # Ajouter 3 mois
            months_to_add = 3
            new_month = now.month + months_to_add
            new_year = now.year + (new_month - 1) // 12
            new_month = ((new_month - 1) % 12) + 1
            self.next_run = now.replace(year=new_year, month=new_month)
        elif self.frequency == ReportFrequency.YEARLY:
            self.next_run = now.replace(year=now.year + 1)
        else:
            self.next_run = None  # Manuel
    
    __table_args__ = (
        db.Index("idx_schedule_business", "business_id_fk"),
        db.Index("idx_schedule_template", "template_id_fk"),
        db.Index("idx_schedule_frequency", "frequency"),
        db.Index("idx_schedule_next_run", "next_run"),
    )


class ReportExecution(db.Model):
    """Modèle pour l'historique d'exécution des rapports"""
    __tablename__ = "report_executions"
    id = Column(Integer, primary_key=True)
    business_id_fk = Column(Integer, db.ForeignKey("businesses.id"), nullable=False, index=True)
    template_id_fk = Column(Integer, db.ForeignKey("report_templates.id"), nullable=False, index=True)
    schedule_id_fk = Column(Integer, db.ForeignKey("report_schedules.id"), nullable=True, index=True)
    
    # Informations d'exécution
    execution_id = Column(String(36), nullable=False, unique=True, index=True)  # UUID
    status = Column(Enum(ReportStatus), default=ReportStatus.PENDING, nullable=False, index=True)
    
    # Paramètres d'exécution
    filters_applied = Column(JSON, nullable=True)  # Filtres appliqués
    date_range_start = Column(Date, nullable=True)
    date_range_end = Column(Date, nullable=True)
    output_format = Column(Enum(ReportFormat), nullable=False)
    
    # Résultats
    rows_count = Column(Integer, nullable=True)  # Nombre de lignes
    file_path = Column(String(500), nullable=True)  # Chemin du fichier généré
    file_size = Column(Integer, nullable=True)  # Taille du fichier en bytes
    
    # Temps d'exécution
    executed_at = Column(DateTime, default=datetime.utcnow, nullable=False)  # Add this missing attribute
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    duration_seconds = Column(Integer, nullable=True)
    
    # Erreurs
    error_message = Column(Text, nullable=True)
    error_details = Column(JSON, nullable=True)
    
    # Métadonnées
    executed_by = Column(String(255), nullable=False)
    user_agent = Column(String(500), nullable=True)
    ip_address = Column(String(45), nullable=True)
    
    # Dates
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    # Relations
    template = relationship("ReportTemplate", back_populates="executions")
    schedule = relationship("ReportSchedule", back_populates="executions")
    data = relationship("ReportData", back_populates="execution", lazy="dynamic", cascade="all, delete-orphan")
    
    @property
    def is_successful(self):
        return self.status == ReportStatus.COMPLETED
    
    @property
    def file_size_mb(self):
        return round(self.file_size / 1024 / 1024, 2) if self.file_size else 0
    
    def calculate_duration(self):
        """Calcule la durée d'exécution"""
        if self.started_at and self.completed_at:
            delta = self.completed_at - self.started_at
            self.duration_seconds = int(delta.total_seconds())
    
    __table_args__ = (
        db.Index("idx_execution_business", "business_id_fk"),
        db.Index("idx_execution_template", "template_id_fk"),
        db.Index("idx_execution_schedule", "schedule_id_fk"),
        db.Index("idx_execution_status", "status"),
        db.Index("idx_execution_created", "created_at"),
    )


class ReportData(db.Model):
    """Modèle pour stocker les données des rapports (cache)"""
    __tablename__ = "report_data"
    id = Column(Integer, primary_key=True)
    execution_id_fk = Column(Integer, db.ForeignKey("report_executions.id"), nullable=False, index=True)
    
    # Données
    data_type = Column(String(50), nullable=False)  # raw_data, aggregated_data, chart_data
    data_json = Column(JSON, nullable=False)  # Données sérialisées
    
    # Métadonnées
    data_hash = Column(String(64), nullable=True, index=True)  # Hash pour vérifier l'intégrité
    compression = Column(String(20), nullable=True)  # Type de compression (gzip, etc.)
    
    # Dates
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    expires_at = Column(DateTime, nullable=True)  # Date d'expiration du cache
    
    # Relations
    execution = relationship("ReportExecution", back_populates="data")
    
    @property
    def is_expired(self):
        return self.expires_at and datetime.utcnow() > self.expires_at
    
    __table_args__ = (
        db.Index("idx_report_data_execution", "execution_id_fk"),
        db.Index("idx_report_data_type", "data_type"),
        db.Index("idx_report_data_expires", "expires_at"),
    )


class ReportWidget(db.Model):
    """Modèle pour les widgets de dashboard"""
    __tablename__ = "report_widgets"
    id = Column(Integer, primary_key=True)
    business_id_fk = Column(Integer, db.ForeignKey("businesses.id"), nullable=False, index=True)
    template_id_fk = Column(Integer, db.ForeignKey("report_templates.id"), nullable=True, index=True)
    
    # Configuration du widget
    name = Column(String(255), nullable=False)
    widget_type = Column(String(50), nullable=False)  # chart, table, metric, gauge
    position_x = Column(Integer, default=0, nullable=False)
    position_y = Column(Integer, default=0, nullable=False)
    width = Column(Integer, default=4, nullable=False)
    height = Column(Integer, default=3, nullable=False)
    
    # Configuration spécifique
    config = Column(JSON, nullable=False)  # Configuration du widget
    refresh_interval = Column(Integer, default=300, nullable=False)  # Intervalle de rafraîchissement (secondes)
    
    # Visibilité
    is_visible = Column(Boolean, default=True, nullable=False)
    dashboard_page = Column(String(100), default='main', nullable=False)
    
    # Dates
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Relations
    template = relationship("ReportTemplate")
    
    __table_args__ = (
        db.Index("idx_widget_business", "business_id_fk"),
        db.Index("idx_widget_template", "template_id_fk"),
        db.Index("idx_widget_dashboard", "dashboard_page"),
    )


