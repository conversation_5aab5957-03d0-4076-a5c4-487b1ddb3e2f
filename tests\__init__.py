"""Package de tests pour l'application POS"""
import os
import sys
import unittest
from flask import Flask
from app import create_app
from app.extensions import db

# Ajouter le répertoire racine au path pour les imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class BaseTestCase(unittest.TestCase):
    """Classe de base pour les tests"""
    
    def setUp(self):
        """Initialisation avant chaque test"""
        self.app = create_app()
        self.app.config['TESTING'] = True
        self.app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
        self.app.config['WTF_CSRF_ENABLED'] = False
        self.app.config['CACHE_TYPE'] = 'null'  # Désactiver le cache pendant les tests
        
        self.client = self.app.test_client()
        self.ctx = self.app.app_context()
        self.ctx.push()
        
        # Créer les tables de la base de données
        with self.app.app_context():
            db.create_all()
    
    def tearDown(self):
        """Nettoyage après chaque test"""
        with self.app.app_context():
            db.session.remove()
            db.drop_all()
        
        self.ctx.pop()

def create_test_app():
    """Crée une application Flask pour les tests"""
    app = create_app()
    app.config['TESTING'] = True
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
    app.config['WTF_CSRF_ENABLED'] = False
    return app

if __name__ == '__main__':
    unittest.main()