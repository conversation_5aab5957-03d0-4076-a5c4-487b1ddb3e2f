{% extends "base.html" %}

{% block title %}Gestion des Stocks - Pharmacie{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Gestion des Stocks</h1>
                <div class="btn-group" role="group">
                    <a href="{{ url_for('pharmacy.new_stock_movement') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Nouveau Mouvement
                    </a>
                    <a href="{{ url_for('pharmacy.stock_movements') }}" class="btn btn-outline-primary">
                        <i class="fas fa-list"></i> Historique
                    </a>
                </div>
            </div>

            <!-- Alertes de stock -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card border-warning">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-exclamation-triangle"></i> 
                                Stocks Faibles ({{ low_stock_meds|length }})
                            </h5>
                        </div>
                        <div class="card-body">
                            {% if low_stock_meds %}
                                <div class="table-responsive" style="max-height: 300px; overflow-y: auto;">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Médicament</th>
                                                <th>Stock</th>
                                                <th>Min.</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for med in low_stock_meds %}
                                            <tr>
                                                <td>
                                                    <strong>{{ med.name }}</strong>
                                                    {% if med.generic_name %}
                                                        <br><small class="text-muted">{{ med.generic_name }}</small>
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    <span class="badge bg-danger">{{ med.current_stock }}</span>
                                                </td>
                                                <td>{{ med.minimum_stock }}</td>
                                                <td>
                                                    <button class="btn btn-sm btn-outline-primary" 
                                                            onclick="quickRestock({{ med.id }}, '{{ med.name }}')">
                                                        <i class="fas fa-plus"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            {% else %}
                                <p class="text-muted text-center mb-0">Aucun stock faible détecté</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card border-info">
                        <div class="card-header bg-info text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-clock"></i> 
                                Expiration Prochaine ({{ expiring_meds|length }})
                            </h5>
                        </div>
                        <div class="card-body">
                            {% if expiring_meds %}
                                <div class="table-responsive" style="max-height: 300px; overflow-y: auto;">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Médicament</th>
                                                <th>Lot</th>
                                                <th>Expiration</th>
                                                <th>Qté</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for item in expiring_meds %}
                                            <tr>
                                                <td>
                                                    <strong>{{ item.medication.name }}</strong>
                                                </td>
                                                <td>
                                                    <small>{{ item.batch_number or 'N/A' }}</small>
                                                </td>
                                                <td>
                                                    <span class="badge bg-warning text-dark">
                                                        {{ item.expiry_date.strftime('%d/%m/%Y') }}
                                                    </span>
                                                </td>
                                                <td>{{ item.quantity }}</td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            {% else %}
                                <p class="text-muted text-center mb-0">Aucune expiration prochaine</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Mouvements récents -->
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">Mouvements Récents</h5>
                        <a href="{{ url_for('pharmacy.stock_movements') }}" class="btn btn-sm btn-outline-primary">
                            Voir tout
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    {% if recent_movements %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Médicament</th>
                                        <th>Type</th>
                                        <th>Quantité</th>
                                        <th>Utilisateur</th>
                                        <th>Notes</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for movement in recent_movements %}
                                    <tr>
                                        <td>{{ movement.created_at.strftime('%d/%m/%Y %H:%M') }}</td>
                                        <td>
                                            <strong>{{ movement.medication.name }}</strong>
                                            {% if movement.batch_number %}
                                                <br><small class="text-muted">Lot: {{ movement.batch_number }}</small>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% set movement_colors = {
                                                'in': 'success',
                                                'out': 'danger',
                                                'adjustment': 'warning',
                                                'expired': 'secondary',
                                                'damaged': 'dark'
                                            } %}
                                            {% set movement_labels = {
                                                'in': 'Entrée',
                                                'out': 'Sortie',
                                                'adjustment': 'Ajustement',
                                                'expired': 'Péremption',
                                                'damaged': 'Détérioration'
                                            } %}
                                            <span class="badge bg-{{ movement_colors.get(movement.movement_type, 'secondary') }}">
                                                {{ movement_labels.get(movement.movement_type, movement.movement_type) }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="fw-bold {% if movement.quantity > 0 %}text-success{% else %}text-danger{% endif %}">
                                                {{ movement.quantity:+d }}
                                            </span>
                                        </td>
                                        <td>{{ movement.created_by.username if movement.created_by else 'Système' }}</td>
                                        <td>
                                            {% if movement.notes %}
                                                {{ movement.notes[:50] }}
                                                {% if movement.notes|length > 50 %}...{% endif %}
                                            {% else %}
                                                <span class="text-muted">-</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-boxes fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Aucun mouvement de stock récent</h5>
                            <p class="text-muted">Les mouvements de stock apparaîtront ici une fois que vous commencerez à gérer votre inventaire.</p>
                            <a href="{{ url_for('pharmacy.new_stock_movement') }}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Premier Mouvement
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de réassort rapide -->
<div class="modal fade" id="quickRestockModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Réassort Rapide</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="quickRestockForm">
                    <input type="hidden" id="medicationId" name="medication_id">
                    <div class="mb-3">
                        <label class="form-label">Médicament</label>
                        <input type="text" class="form-control" id="medicationName" readonly>
                    </div>
                    <div class="mb-3">
                        <label for="quantity" class="form-label">Quantité à ajouter</label>
                        <input type="number" class="form-control" id="quantity" name="quantity" min="1" required>
                    </div>
                    <div class="mb-3">
                        <label for="batchNumber" class="form-label">Numéro de lot</label>
                        <input type="text" class="form-control" id="batchNumber" name="batch_number">
                    </div>
                    <div class="mb-3">
                        <label for="expiryDate" class="form-label">Date d'expiration</label>
                        <input type="date" class="form-control" id="expiryDate" name="expiry_date">
                    </div>
                    <div class="mb-3">
                        <label for="supplier" class="form-label">Fournisseur</label>
                        <input type="text" class="form-control" id="supplier" name="supplier">
                    </div>
                    <div class="mb-3">
                        <label for="cost" class="form-label">Coût unitaire (centimes)</label>
                        <input type="number" class="form-control" id="cost" name="unit_cost_cents" min="0">
                    </div>
                    <div class="mb-3">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control" id="notes" name="notes" rows="2"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary" onclick="submitQuickRestock()">
                    <i class="fas fa-plus"></i> Ajouter au Stock
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function quickRestock(medicationId, medicationName) {
    document.getElementById('medicationId').value = medicationId;
    document.getElementById('medicationName').value = medicationName;
    document.getElementById('quickRestockForm').reset();
    document.getElementById('medicationId').value = medicationId; // Reset efface tout
    
    const modal = new bootstrap.Modal(document.getElementById('quickRestockModal'));
    modal.show();
}

function submitQuickRestock() {
    const form = document.getElementById('quickRestockForm');
    const formData = new FormData(form);
    
    // Validation simple
    if (!formData.get('quantity') || parseInt(formData.get('quantity')) <= 0) {
        alert('Veuillez saisir une quantité valide');
        return;
    }
    
    // Préparer les données
    const data = {
        movement_type: 'in',
        quantity: parseInt(formData.get('quantity')),
        batch_number: formData.get('batch_number') || null,
        expiry_date: formData.get('expiry_date') || null,
        supplier_name: formData.get('supplier') || null,
        unit_cost_cents: parseInt(formData.get('unit_cost_cents')) || 0,
        notes: formData.get('notes') || 'Réassort rapide'
    };
    
    // Envoyer la requête
    fetch(`/pharmacy/api/stock/${formData.get('medication_id')}/movement`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.message) {
            alert(`${data.message}. Nouveau stock: ${data.new_stock}`);
            location.reload();
        } else {
            alert(data.error || 'Erreur lors de l\'ajout');
        }
    })
    .catch(error => {
        console.error('Erreur:', error);
        alert('Erreur lors de l\'ajout au stock');
    });
    
    // Fermer le modal
    bootstrap.Modal.getInstance(document.getElementById('quickRestockModal')).hide();
}

// Auto-refresh des données (optionnel)
setTimeout(() => {
    location.reload();
}, 300000); // Rafraîchir toutes les 5 minutes
</script>
{% endblock %}