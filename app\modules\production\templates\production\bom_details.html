{% extends 'base.html' %}
{% block title %}{{ bom.name }} - Détails BOM{% endblock %}

{% block content %}
<div class=\"max-w-7xl mx-auto p-6\">
    <!-- En-tête -->
    <div class=\"flex justify-between items-center mb-6\">
        <div>
            <h1 class=\"text-2xl font-bold text-white mb-2\">{{ bom.name }} <span class=\"text-slate-400\">v{{ bom.version }}</span></h1>
            <p class=\"text-slate-400\">Détails de la nomenclature de fabrication</p>
        </div>
        
        <div class=\"flex gap-3\">
            <a href=\"{{ url_for('production.boms') }}\" 
               class=\"bg-slate-700 hover:bg-slate-600 text-white px-4 py-2 rounded-md text-sm font-medium\">
                ← Retour aux BOM
            </a>
            
            <a href=\"{{ url_for('production.create_production_order') }}?bom_id={{ bom.id }}\" 
               class=\"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium\">
                Créer un ordre
            </a>
        </div>
    </div>
    
    <!-- Informations générales -->
    <div class=\"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6\">
        <!-- Détails du produit -->
        <div class=\"bg-slate-800 rounded-lg p-6\">
            <h3 class=\"text-lg font-semibold text-white mb-4\">Produit final</h3>
            
            {% if bom.product %}
            <div class=\"space-y-3\">
                <div>
                    <span class=\"text-sm text-slate-400\">Nom:</span>
                    <p class=\"text-white font-medium\">{{ bom.product.name }}</p>
                </div>
                
                <div>
                    <span class=\"text-sm text-slate-400\">Quantité produite:</span>
                    <p class=\"text-white font-medium\">{{ bom.quantity_produced }}</p>
                </div>
                
                {% if bom.product.price_cents %}
                <div>
                    <span class=\"text-sm text-slate-400\">Prix de vente:</span>
                    <p class=\"text-white font-medium\">{{ \"%.2f\"|format(bom.product.price_cents / 100) }}€</p>
                </div>
                {% endif %}
                
                <div>
                    <span class=\"text-sm text-slate-400\">Statut:</span>
                    {% if bom.is_active %}
                    <span class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\">
                        Actif
                    </span>
                    {% else %}
                    <span class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800\">
                        Inactif
                    </span>
                    {% endif %}
                </div>
            </div>
            {% else %}
            <p class=\"text-slate-400\">Aucun produit associé</p>
            {% endif %}
        </div>
        
        <!-- Coûts de production -->
        <div class=\"bg-slate-800 rounded-lg p-6\">
            <h3 class=\"text-lg font-semibold text-white mb-4\">Coûts de production</h3>
            
            <div class=\"space-y-3\">
                <div class=\"flex justify-between\">
                    <span class=\"text-slate-400\">Matériaux:</span>
                    <span class=\"text-white font-medium\">{{ \"%.2f\"|format(bom.total_material_cost / 100) }}€</span>
                </div>
                
                <div class=\"flex justify-between\">
                    <span class=\"text-slate-400\">Main d'œuvre:</span>
                    <span class=\"text-white font-medium\">{{ \"%.2f\"|format(bom.total_labor_cost / 100) }}€</span>
                </div>
                
                <hr class=\"border-slate-600\">
                
                <div class=\"flex justify-between text-lg\">
                    <span class=\"text-white font-semibold\">Total:</span>
                    <span class=\"text-green-400 font-bold\">{{ \"%.2f\"|format(bom.total_cost / 100) }}€</span>
                </div>
                
                {% if bom.product and bom.product.price_cents %}
                <div class=\"flex justify-between text-sm\">
                    <span class=\"text-slate-400\">Marge brute:</span>
                    {% set margin = bom.product.price_cents - bom.total_cost %}
                    <span class=\"{{ 'text-green-400' if margin > 0 else 'text-red-400' }} font-medium\">
                        {{ \"%.2f\"|format(margin / 100) }}€ ({{ \"%.1f\"|format((margin / bom.product.price_cents) * 100) }}%)
                    </span>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Statistiques -->
        <div class=\"bg-slate-800 rounded-lg p-6\">
            <h3 class=\"text-lg font-semibold text-white mb-4\">Statistiques</h3>
            
            <div class=\"space-y-3\">
                <div class=\"flex justify-between\">
                    <span class=\"text-slate-400\">Composants:</span>
                    <span class=\"text-white font-medium\">{{ bom.bom_items|length }}</span>
                </div>
                
                <div class=\"flex justify-between\">
                    <span class=\"text-slate-400\">Étapes:</span>
                    <span class=\"text-white font-medium\">{{ bom.production_steps|length }}</span>
                </div>
                
                <div class=\"flex justify-between\">
                    <span class=\"text-slate-400\">Temps total:</span>
                    {% set total_time = bom.production_steps | sum(attribute='total_time_minutes') %}
                    <span class=\"text-white font-medium\">{{ total_time }} min</span>
                </div>
                
                <div class=\"flex justify-between\">
                    <span class=\"text-slate-400\">Ordres créés:</span>
                    <span class=\"text-white font-medium\">{{ bom.production_orders|length }}</span>
                </div>
                
                <div class=\"flex justify-between text-sm\">
                    <span class=\"text-slate-400\">Créée le:</span>
                    <span class=\"text-white\">{{ bom.created_at.strftime('%d/%m/%Y') }}</span>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Description -->
    {% if bom.description %}
    <div class=\"bg-slate-800 rounded-lg p-6 mb-6\">
        <h3 class=\"text-lg font-semibold text-white mb-3\">Description</h3>
        <p class=\"text-slate-300\">{{ bom.description }}</p>
    </div>
    {% endif %}
    
    <!-- Onglets -->
    <div class=\"bg-slate-800 rounded-lg overflow-hidden\">
        <div class=\"border-b border-slate-700\">
            <nav class=\"flex space-x-8 px-6\" aria-label=\"Tabs\">
                <button onclick=\"showTab('components')\" 
                        id=\"tab-components\"
                        class=\"tab-button py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap border-blue-500 text-blue-400\">
                    Composants ({{ bom.bom_items|length }})
                </button>
                
                <button onclick=\"showTab('steps')\" 
                        id=\"tab-steps\"
                        class=\"tab-button py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap border-transparent text-slate-400 hover:text-slate-300\">
                    Étapes ({{ bom.production_steps|length }})
                </button>
                
                <button onclick=\"showTab('orders')\" 
                        id=\"tab-orders\"
                        class=\"tab-button py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap border-transparent text-slate-400 hover:text-slate-300\">
                    Ordres ({{ bom.production_orders|length }})
                </button>
            </nav>
        </div>
        
        <!-- Contenu des onglets -->
        
        <!-- Onglet Composants -->
        <div id=\"content-components\" class=\"tab-content\">
            <div class=\"p-6\">
                <div class=\"flex justify-between items-center mb-4\">
                    <h3 class=\"text-lg font-semibold text-white\">Composants requis</h3>
                    <a href=\"{{ url_for('production.bom_items', bom_id=bom.id) }}\" 
                       class=\"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium\">
                        Gérer les composants
                    </a>
                </div>
                
                {% if bom.bom_items %}
                <div class=\"overflow-x-auto\">
                    <table class=\"min-w-full divide-y divide-slate-700\">
                        <thead>
                            <tr>
                                <th class=\"px-6 py-3 text-left text-xs font-medium text-slate-400 uppercase tracking-wider\">Produit</th>
                                <th class=\"px-6 py-3 text-left text-xs font-medium text-slate-400 uppercase tracking-wider\">Quantité</th>
                                <th class=\"px-6 py-3 text-left text-xs font-medium text-slate-400 uppercase tracking-wider\">Coût unitaire</th>
                                <th class=\"px-6 py-3 text-left text-xs font-medium text-slate-400 uppercase tracking-wider\">Coût total</th>
                                <th class=\"px-6 py-3 text-left text-xs font-medium text-slate-400 uppercase tracking-wider\">Notes</th>
                            </tr>
                        </thead>
                        <tbody class=\"divide-y divide-slate-700\">
                            {% for item in bom.bom_items %}
                            <tr class=\"hover:bg-slate-750\">
                                <td class=\"px-6 py-4 whitespace-nowrap\">
                                    <div class=\"text-sm font-medium text-white\">
                                        {{ item.product.name if item.product else 'Produit supprimé' }}
                                    </div>
                                </td>
                                <td class=\"px-6 py-4 whitespace-nowrap text-sm text-slate-300\">
                                    {{ item.quantity }}
                                </td>
                                <td class=\"px-6 py-4 whitespace-nowrap text-sm text-slate-300\">
                                    {{ \"%.2f\"|format(item.unit_cost_cents / 100) }}€
                                </td>
                                <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-green-400\">
                                    {{ \"%.2f\"|format(item.total_cost_cents / 100) }}€
                                </td>
                                <td class=\"px-6 py-4 text-sm text-slate-400\">
                                    {{ item.notes or '-' }}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class=\"text-center py-8\">
                    <p class=\"text-slate-400\">Aucun composant défini</p>
                    <a href=\"{{ url_for('production.bom_items', bom_id=bom.id) }}\" 
                       class=\"inline-block mt-2 text-blue-400 hover:text-blue-300\">
                        Ajouter des composants
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Onglet Étapes -->
        <div id=\"content-steps\" class=\"tab-content hidden\">
            <div class=\"p-6\">
                <div class=\"flex justify-between items-center mb-4\">
                    <h3 class=\"text-lg font-semibold text-white\">Étapes de production</h3>
                    <a href=\"{{ url_for('production.bom_steps', bom_id=bom.id) }}\" 
                       class=\"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium\">
                        Gérer les étapes
                    </a>
                </div>
                
                {% if bom.production_steps %}
                <div class=\"space-y-4\">
                    {% for step in bom.production_steps | sort(attribute='step_number') %}
                    <div class=\"bg-slate-700 rounded-lg p-4\">
                        <div class=\"flex justify-between items-start\">
                            <div class=\"flex-1\">
                                <div class=\"flex items-center gap-3 mb-2\">
                                    <span class=\"inline-flex items-center justify-center w-8 h-8 rounded-full bg-blue-600 text-white text-sm font-medium\">
                                        {{ step.step_number }}
                                    </span>
                                    <h4 class=\"text-white font-medium\">{{ step.name }}</h4>
                                    {% if step.is_optional %}
                                    <span class=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800\">
                                        Optionnel
                                    </span>
                                    {% endif %}
                                </div>
                                
                                {% if step.description %}
                                <p class=\"text-sm text-slate-300 mb-2\">{{ step.description }}</p>
                                {% endif %}
                                
                                <div class=\"flex gap-6 text-sm text-slate-400\">
                                    <span>Centre: {{ step.work_center.name if step.work_center else 'Non défini' }}</span>
                                    <span>Préparation: {{ step.setup_time_minutes }} min</span>
                                    <span>Exécution: {{ step.run_time_minutes }} min</span>
                                    <span>Total: {{ step.total_time_minutes }} min</span>
                                </div>
                            </div>
                            
                            <div class=\"text-right\">
                                <div class=\"text-sm text-slate-400\">Coût</div>
                                <div class=\"text-white font-medium\">{{ \"%.2f\"|format(step.total_cost_cents / 100) }}€</div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class=\"text-center py-8\">
                    <p class=\"text-slate-400\">Aucune étape définie</p>
                    <a href=\"{{ url_for('production.bom_steps', bom_id=bom.id) }}\" 
                       class=\"inline-block mt-2 text-blue-400 hover:text-blue-300\">
                        Ajouter des étapes
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Onglet Ordres -->
        <div id=\"content-orders\" class=\"tab-content hidden\">
            <div class=\"p-6\">
                <div class=\"flex justify-between items-center mb-4\">
                    <h3 class=\"text-lg font-semibold text-white\">Ordres de production utilisant cette BOM</h3>
                    <a href=\"{{ url_for('production.create_production_order') }}?bom_id={{ bom.id }}\" 
                       class=\"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium\">
                        Créer un ordre
                    </a>
                </div>
                
                {% if bom.production_orders %}
                <div class=\"space-y-3\">
                    {% for order in bom.production_orders | sort(attribute='created_at', reverse=true) %}
                    <div class=\"bg-slate-700 rounded-lg p-4\">
                        <div class=\"flex justify-between items-center\">
                            <div>
                                <a href=\"{{ url_for('production.order_details', order_id=order.id) }}\" 
                                   class=\"text-white font-medium hover:text-blue-400\">
                                    #{{ order.order_number }}
                                </a>
                                
                                <div class=\"text-sm text-slate-400 mt-1\">
                                    {{ order.quantity_to_produce }} unités • 
                                    Créé le {{ order.created_at.strftime('%d/%m/%Y') }} • 
                                    
                                    <span class=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                            {% if order.status == 'draft' %}bg-gray-100 text-gray-800
                                            {% elif order.status == 'planned' %}bg-blue-100 text-blue-800
                                            {% elif order.status == 'released' %}bg-yellow-100 text-yellow-800
                                            {% elif order.status == 'in_progress' %}bg-green-100 text-green-800
                                            {% elif order.status == 'completed' %}bg-green-100 text-green-800
                                            {% elif order.status == 'cancelled' %}bg-red-100 text-red-800
                                            {% endif %}\">
                                        {% if order.status == 'draft' %}Brouillon
                                        {% elif order.status == 'planned' %}Planifié
                                        {% elif order.status == 'released' %}Libéré
                                        {% elif order.status == 'in_progress' %}En cours
                                        {% elif order.status == 'completed' %}Terminé
                                        {% elif order.status == 'cancelled' %}Annulé
                                        {% endif %}
                                    </span>
                                </div>
                            </div>
                            
                            <div class=\"text-right\">
                                <div class=\"text-sm text-slate-400\">Progression</div>
                                <div class=\"text-white font-medium\">{{ \"%.1f\"|format(order.progress_percentage) }}%</div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class=\"text-center py-8\">
                    <p class=\"text-slate-400\">Aucun ordre créé avec cette nomenclature</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Script pour les onglets -->
<script>
function showTab(tabName) {
    // Masquer tous les contenus d'onglets
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.add('hidden');
    });
    
    // Réinitialiser tous les boutons d'onglets
    document.querySelectorAll('.tab-button').forEach(button => {
        button.classList.remove('border-blue-500', 'text-blue-400');
        button.classList.add('border-transparent', 'text-slate-400');
    });
    
    // Afficher le contenu sélectionné
    document.getElementById(`content-${tabName}`).classList.remove('hidden');
    
    // Activer le bouton sélectionné
    const activeButton = document.getElementById(`tab-${tabName}`);
    activeButton.classList.remove('border-transparent', 'text-slate-400');
    activeButton.classList.add('border-blue-500', 'text-blue-400');
}

// Initialiser le premier onglet
document.addEventListener('DOMContentLoaded', function() {
    showTab('components');
});
</script>
{% endblock %}