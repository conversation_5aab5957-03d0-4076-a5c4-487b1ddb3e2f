from flask_wtf import <PERSON>laskForm
from wtforms import (
    StringField, TextAreaField, SelectField, BooleanField,
    DecimalField, DateTimeField, HiddenField, SubmitField, IntegerField
)
from wtforms.validators import DataRequired, Length, Optional, NumberRange
from datetime import datetime, date


class KPIForm(FlaskForm):
    """Formulaire pour créer/modifier un KPI"""
    kpi_code = StringField(
        'Code KPI',
        validators=[DataRequired(), Length(min=2, max=50)],
        render_kw={'placeholder': 'Ex: SALES_DAILY'}
    )
    name = StringField(
        'Nom du KPI',
        validators=[DataRequired(), Length(min=2, max=200)],
        render_kw={'placeholder': 'Ex: Ventes journalières'}
    )
    description = TextAreaField(
        'Description',
        validators=[Optional()],
        render_kw={'rows': 3, 'placeholder': 'Description du KPI'}
    )
    category = SelectField(
        'Catégorie',
        choices=[
            ('sales', 'Ventes'),
            ('inventory', 'Inventaire'),
            ('finance', 'Finance'),
            ('staff', 'Personnel'),
            ('customer', 'Clients'),
            ('other', 'Autre')
        ],
        validators=[DataRequired()]
    )
    calculation_method = SelectField(
        'Méthode de calcul',
        choices=[
            ('sum', 'Somme'),
            ('avg', 'Moyenne'),
            ('count', 'Comptage'),
            ('custom', 'Requête personnalisée')
        ],
        validators=[DataRequired()]
    )
    source_table = StringField(
        'Table source',
        validators=[Optional(), Length(max=100)],
        render_kw={'placeholder': 'Nom de la table (pour calculs standards)'}
    )
    source_column = StringField(
        'Colonne source',
        validators=[Optional(), Length(max=100)],
        render_kw={'placeholder': 'Nom de la colonne (pour somme/moyenne)'}
    )
    custom_query = TextAreaField(
        'Requête SQL personnalisée',
        validators=[Optional()],
        render_kw={'rows': 5, 'placeholder': 'SELECT ... FROM ... WHERE business_id_fk = {business_id}'}
    )
    target_value = DecimalField(
        'Valeur cible',
        validators=[Optional(), NumberRange(min=0)],
        places=2,
        render_kw={'step': '0.01', 'placeholder': 'Objectif à atteindre'}
    )
    threshold_warning = DecimalField(
        'Seuil d\'alerte',
        validators=[Optional(), NumberRange(min=0)],
        places=2,
        render_kw={'step': '0.01', 'placeholder': 'Seuil d\'avertissement'}
    )
    threshold_critical = DecimalField(
        'Seuil critique',
        validators=[Optional(), NumberRange(min=0)],
        places=2,
        render_kw={'step': '0.01', 'placeholder': 'Seuil critique'}
    )
    unit = StringField(
        'Unité',
        validators=[Optional(), Length(max=20)],
        render_kw={'placeholder': 'Ex: €, %, unités'}
    )
    frequency = SelectField(
        'Fréquence de calcul',
        choices=[
            ('daily', 'Quotidienne'),
            ('weekly', 'Hebdomadaire'),
            ('monthly', 'Mensuelle'),
            ('realtime', 'Temps réel')
        ],
        validators=[DataRequired()],
        default='daily'
    )
    is_active = BooleanField(
        'KPI actif',
        default=True
    )
    submit = SubmitField('Enregistrer')


class DashboardForm(FlaskForm):
    """Formulaire pour créer/modifier un tableau de bord"""
    name = StringField(
        'Nom du tableau de bord',
        validators=[DataRequired(), Length(min=2, max=200)],
        render_kw={'placeholder': 'Ex: Tableau de bord commercial'}
    )
    description = TextAreaField(
        'Description',
        validators=[Optional()],
        render_kw={'rows': 3, 'placeholder': 'Description du tableau de bord'}
    )
    dashboard_type = SelectField(
        'Type de tableau de bord',
        choices=[
            ('custom', 'Personnalisé'),
            ('template', 'Modèle'),
            ('system', 'Système')
        ],
        validators=[DataRequired()],
        default='custom'
    )
    is_public = BooleanField(
        'Accessible à tous les utilisateurs',
        default=False
    )
    is_default = BooleanField(
        'Tableau de bord par défaut',
        default=False
    )
    is_active = BooleanField(
        'Actif',
        default=True
    )
    submit = SubmitField('Enregistrer')


class ReportForm(FlaskForm):
    """Formulaire pour créer/modifier un rapport"""
    name = StringField(
        'Nom du rapport',
        validators=[DataRequired(), Length(min=2, max=200)],
        render_kw={'placeholder': 'Ex: Rapport de ventes mensuel'}
    )
    description = TextAreaField(
        'Description',
        validators=[Optional()],
        render_kw={'rows': 3, 'placeholder': 'Description du rapport'}
    )
    category = SelectField(
        'Catégorie',
        choices=[
            ('sales', 'Ventes'),
            ('inventory', 'Inventaire'),
            ('finance', 'Finance'),
            ('staff', 'Personnel'),
            ('customer', 'Clients'),
            ('other', 'Autre')
        ],
        validators=[DataRequired()]
    )
    report_type = SelectField(
        'Type de rapport',
        choices=[
            ('table', 'Tableau'),
            ('chart', 'Graphique'),
            ('summary', 'Résumé'),
            ('detailed', 'Détaillé')
        ],
        validators=[DataRequired()]
    )
    data_source = StringField(
        'Source de données',
        validators=[DataRequired(), Length(max=100)],
        render_kw={'placeholder': 'Table ou vue source'}
    )
    is_scheduled = BooleanField(
        'Rapport planifié',
        default=False
    )
    schedule_frequency = SelectField(
        'Fréquence de planification',
        choices=[
            ('', 'Aucune'),
            ('daily', 'Quotidienne'),
            ('weekly', 'Hebdomadaire'),
            ('monthly', 'Mensuelle')
        ],
        validators=[Optional()]
    )
    auto_export = BooleanField(
        'Export automatique',
        default=False
    )
    export_format = SelectField(
        'Format d\'export',
        choices=[
            ('', 'Aucun'),
            ('pdf', 'PDF'),
            ('excel', 'Excel'),
            ('csv', 'CSV')
        ],
        validators=[Optional()]
    )
    is_active = BooleanField(
        'Rapport actif',
        default=True
    )
    submit = SubmitField('Enregistrer')


class AnalyticsFilterForm(FlaskForm):
    """Formulaire pour filtrer les analyses"""
    date_from = DateTimeField(
        'Date de début',
        validators=[Optional()],
        format='%Y-%m-%d'
    )
    date_to = DateTimeField(
        'Date de fin',
        validators=[Optional()],
        format='%Y-%m-%d'
    )
    period = SelectField(
        'Période',
        choices=[
            ('today', 'Aujourd\'hui'),
            ('yesterday', 'Hier'),
            ('this_week', 'Cette semaine'),
            ('last_week', 'Semaine dernière'),
            ('this_month', 'Ce mois'),
            ('last_month', 'Mois dernier'),
            ('this_year', 'Cette année'),
            ('custom', 'Période personnalisée')
        ],
        default='this_month'
    )
    category = SelectField(
        'Catégorie',
        choices=[
            ('all', 'Toutes'),
            ('sales', 'Ventes'),
            ('inventory', 'Inventaire'),
            ('finance', 'Finance'),
            ('staff', 'Personnel'),
            ('customer', 'Clients')
        ],
        default='all'
    )
    submit = SubmitField('Appliquer')


class KPISearchForm(FlaskForm):
    """Formulaire de recherche pour les KPIs"""
    search = StringField(
        'Recherche',
        validators=[Optional()],
        render_kw={'placeholder': 'Code, nom, description...'}
    )
    category = SelectField(
        'Catégorie',
        choices=[
            ('all', 'Toutes'),
            ('sales', 'Ventes'),
            ('inventory', 'Inventaire'),
            ('finance', 'Finance'),
            ('staff', 'Personnel'),
            ('customer', 'Clients'),
            ('other', 'Autre')
        ],
        default='all'
    )
    status = SelectField(
        'Statut',
        choices=[
            ('all', 'Tous'),
            ('active', 'Actifs'),
            ('inactive', 'Inactifs'),
            ('good', 'Bons'),
            ('warning', 'Alerte'),
            ('critical', 'Critiques')
        ],
        default='all'
    )
    frequency = SelectField(
        'Fréquence',
        choices=[
            ('all', 'Toutes'),
            ('daily', 'Quotidienne'),
            ('weekly', 'Hebdomadaire'),
            ('monthly', 'Mensuelle'),
            ('realtime', 'Temps réel')
        ],
        default='all'
    )
    submit = SubmitField('Rechercher')


class DateRangeForm(FlaskForm):
    """Formulaire simple pour sélectionner une plage de dates"""
    start_date = DateTimeField(
        'Date de début',
        validators=[DataRequired()],
        format='%Y-%m-%d',
        default=lambda: datetime.now().replace(day=1).date()
    )
    end_date = DateTimeField(
        'Date de fin',
        validators=[DataRequired()],
        format='%Y-%m-%d',
        default=lambda: datetime.now().date()
    )
    submit = SubmitField('Appliquer')


class ChartConfigForm(FlaskForm):
    """Formulaire pour configurer les graphiques"""
    chart_type = SelectField(
        'Type de graphique',
        choices=[
            ('line', 'Courbe'),
            ('bar', 'Barres'),
            ('pie', 'Camembert'),
            ('area', 'Aire'),
            ('scatter', 'Nuage de points')
        ],
        validators=[DataRequired()],
        default='line'
    )
    x_axis = StringField(
        'Axe X',
        validators=[DataRequired(), Length(max=100)],
        render_kw={'placeholder': 'Colonne pour l\'axe X'}
    )
    y_axis = StringField(
        'Axe Y',
        validators=[DataRequired(), Length(max=100)],
        render_kw={'placeholder': 'Colonne pour l\'axe Y'}
    )
    title = StringField(
        'Titre du graphique',
        validators=[Optional(), Length(max=200)],
        render_kw={'placeholder': 'Titre optionnel'}
    )
    show_legend = BooleanField(
        'Afficher la légende',
        default=True
    )
    show_grid = BooleanField(
        'Afficher la grille',
        default=True
    )
    height = IntegerField(
        'Hauteur (px)',
        validators=[Optional(), NumberRange(min=200, max=800)],
        default=400
    )
    submit = SubmitField('Configurer')


class BulkKPIActionForm(FlaskForm):
    """Formulaire pour actions en lot sur les KPIs"""
    kpi_ids = HiddenField(
        'IDs des KPIs',
        validators=[DataRequired()]
    )
    action = SelectField(
        'Action',
        choices=[
            ('activate', 'Activer'),
            ('deactivate', 'Désactiver'),
            ('recalculate', 'Recalculer'),
            ('delete', 'Supprimer')
        ],
        validators=[DataRequired()]
    )
    submit = SubmitField('Appliquer')


class ExportForm(FlaskForm):
    """Formulaire pour exporter des données"""
    format_type = SelectField(
        'Format d\'export',
        choices=[
            ('csv', 'CSV'),
            ('excel', 'Excel'),
            ('pdf', 'PDF'),
            ('json', 'JSON')
        ],
        validators=[DataRequired()],
        default='csv'
    )
    include_charts = BooleanField(
        'Inclure les graphiques',
        default=True
    )
    include_summary = BooleanField(
        'Inclure le résumé',
        default=True
    )
    date_range = SelectField(
        'Période des données',
        choices=[
            ('current', 'Période actuelle'),
            ('all', 'Toutes les données'),
            ('custom', 'Période personnalisée')
        ],
        default='current'
    )
    submit = SubmitField('Exporter')