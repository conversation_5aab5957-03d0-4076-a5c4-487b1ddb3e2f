from __future__ import annotations

from datetime import datetime, date, timedelta
from flask import render_template, request, redirect, url_for, flash, jsonify, abort, send_file
from flask_login import login_required, current_user
from sqlalchemy.orm import joinedload
from sqlalchemy import and_, or_, desc, func
import json

from app.extensions import db
from app.modules.accounts.models import Business, User
from app.modules.customers.models import Customer
from app.modules.payments.models import (
    PaymentMethod, Payment, PaymentTransaction, PaymentRefund, Receipt,
    CashMovement, PaymentCashSession as CashSession, PaymentStatus, PaymentMethodType, RefundReason
)
from app.modules.payments.forms import (
    PaymentMethodForm, PaymentForm, QuickPaymentForm, RefundForm, ReceiptForm,
    CashSessionForm, CashSessionCloseForm, CashMovementForm, PaymentSearchForm,
    PaymentReportForm
)

from . import bp


@bp.route("/")
@login_required
def index():
    """Page d'accueil des paiements"""
    business = current_user.business
    
    # Statistiques rapides
    today = date.today()
    start_of_day = datetime.combine(today, datetime.min.time())
    
    # Paiements du jour
    daily_payments = Payment.query.filter(
        Payment.business_id_fk == business.id,
        Payment.created_at >= start_of_day,
        Payment.status == PaymentStatus.COMPLETED
    ).all()
    
    daily_total = sum(payment.amount_cents for payment in daily_payments)
    
    # Méthodes de paiement actives
    active_methods = PaymentMethod.query.filter_by(
        business_id_fk=business.id, is_active=True
    ).count()
    
    # Paiements en attente
    pending_payments = Payment.query.filter_by(
        business_id_fk=business.id, status=PaymentStatus.PENDING
    ).count()
    
    # Session de caisse active
    active_session = CashSession.query.filter_by(
        business_id_fk=business.id, is_active=True
    ).first()
    
    # Paiements récents
    recent_payments = Payment.query.filter_by(business_id_fk=business.id)\
        .options(
            joinedload(Payment.payment_method),
            joinedload(Payment.order)
        ).order_by(desc(Payment.created_at)).limit(10).all()
    
    return render_template("payments/index.html",
                         daily_total=daily_total,
                         active_methods=active_methods,
                         pending_payments=pending_payments,
                         active_session=active_session,
                         recent_payments=recent_payments)


# =============================================================================
# ROUTES MÉTHODES DE PAIEMENT
# =============================================================================

@bp.route("/methods")
@login_required
def payment_methods():
    """Liste des méthodes de paiement"""
    methods = PaymentMethod.query.filter_by(
        business_id_fk=current_user.business.id
    ).order_by(PaymentMethod.name).all()
    
    return render_template("payments/methods/list.html", methods=methods)


@bp.route("/methods/new", methods=["GET", "POST"])
@login_required
def new_payment_method():
    """Nouvelle méthode de paiement"""
    form = PaymentMethodForm()
    
    if form.validate_on_submit():
        # Vérifier s'il y a déjà une méthode par défaut
        if form.is_default.data:
            PaymentMethod.query.filter_by(
                business_id_fk=current_user.business.id,
                is_default=True
            ).update({"is_default": False})
        
        method = PaymentMethod(
            business_id_fk=current_user.business.id,
            **{field.name: field.data for field in form if field.name not in ['csrf_token']}
        )
        
        db.session.add(method)
        db.session.commit()
        
        flash(f"Méthode de paiement '{method.name}' créée avec succès", "success")
        return redirect(url_for("payments.payment_methods"))
    
    return render_template("payments/methods/form.html", form=form, title="Nouvelle méthode")


@bp.route("/methods/<int:method_id>")
@login_required
def payment_method_detail(method_id):
    """Détail d'une méthode de paiement"""
    method = PaymentMethod.query.filter_by(
        id=method_id, business_id_fk=current_user.business.id
    ).first_or_404()
    
    # Statistiques d'utilisation
    stats = db.session.query(
        func.count(Payment.id).label('total_payments'),
        func.sum(Payment.amount_cents).label('total_amount'),
        func.avg(Payment.amount_cents).label('avg_amount')
    ).filter(
        Payment.payment_method_id_fk == method_id,
        Payment.status == PaymentStatus.COMPLETED
    ).first()
    
    return render_template("payments/methods/detail.html", method=method, stats=stats)


@bp.route("/methods/<int:method_id>/edit", methods=["GET", "POST"])
@login_required
def edit_payment_method(method_id):
    """Modifier une méthode de paiement"""
    method = PaymentMethod.query.filter_by(
        id=method_id, business_id_fk=current_user.business.id
    ).first_or_404()
    
    form = PaymentMethodForm(obj=method)
    
    if form.validate_on_submit():
        # Vérifier s'il y a déjà une méthode par défaut
        if form.is_default.data and not method.is_default:
            PaymentMethod.query.filter_by(
                business_id_fk=current_user.business.id,
                is_default=True
            ).update({"is_default": False})
        
        for field in form:
            if field.name not in ['csrf_token']:
                setattr(method, field.name, field.data)
        
        method.updated_at = datetime.utcnow()
        db.session.commit()
        
        flash(f"Méthode de paiement '{method.name}' modifiée avec succès", "success")
        return redirect(url_for("payments.payment_method_detail", method_id=method.id))
    
    return render_template("payments/methods/form.html", 
                         form=form, method=method, title="Modifier méthode")


# =============================================================================
# ROUTES PAIEMENTS
# =============================================================================

@bp.route("/payments")
@login_required
def payments():
    """Liste des paiements"""
    search_form = PaymentSearchForm(business_id=current_user.business.id)
    page = request.args.get("page", 1, type=int)
    
    query = Payment.query.filter_by(business_id_fk=current_user.business.id)\
        .options(
            joinedload(Payment.payment_method),
            joinedload(Payment.order)
        )
    
    # Filtrage par recherche
    if request.args.get("search_query"):
        search = f"%{request.args.get('search_query')}%"
        query = query.filter(or_(
            Payment.receipt_number.ilike(search),
            Payment.external_transaction_id.ilike(search),
            Payment.authorization_code.ilike(search)
        ))
    
    # Filtrage par méthode
    if request.args.get("payment_method_id"):
        query = query.filter_by(payment_method_id_fk=request.args.get("payment_method_id"))
    
    # Filtrage par statut
    if request.args.get("status"):
        query = query.filter_by(status=request.args.get("status"))
    
    # Filtrage par montant
    if request.args.get("amount_min"):
        min_cents = int(float(request.args.get("amount_min")) * 100)
        query = query.filter(Payment.amount_cents >= min_cents)
    
    if request.args.get("amount_max"):
        max_cents = int(float(request.args.get("amount_max")) * 100)
        query = query.filter(Payment.amount_cents <= max_cents)
    
    payments = query.order_by(desc(Payment.created_at)).paginate(
        page=page, per_page=20, error_out=False
    )
    
    return render_template("payments/payments/list.html", 
                         payments=payments, search_form=search_form)


@bp.route("/payments/<int:payment_id>")
@login_required
def payment_detail(payment_id):
    """Détail d'un paiement"""
    payment = Payment.query.filter_by(
        id=payment_id, business_id_fk=current_user.business.id
    ).options(
        joinedload(Payment.payment_method),
        joinedload(Payment.order),
        joinedload(Payment.transactions),
        joinedload(Payment.refunds)
    ).first_or_404()
    
    return render_template("payments/payments/detail.html", payment=payment)


@bp.route("/payments/new", methods=["GET", "POST"])
@login_required
def new_payment():
    """Nouveau paiement manuel"""
    form = PaymentForm(business_id=current_user.business.id)
    
    if form.validate_on_submit():
        # Convertir le montant en centimes
        amount_cents = int(float(form.amount_euros.data) * 100)
        
        # Récupérer la méthode de paiement
        payment_method = PaymentMethod.query.get(form.payment_method_id_fk.data)
        
        # Calculer les frais
        fees_cents = payment_method.calculate_fees(amount_cents)
        net_amount_cents = amount_cents - fees_cents
        
        # Générer un numéro de reçu
        receipt_number = f"PAY-{datetime.now().strftime('%Y%m%d-%H%M%S')}"
        
        payment = Payment(
            business_id_fk=current_user.business.id,
            order_id_fk=form.order_id_fk.data,
            payment_method_id_fk=form.payment_method_id_fk.data,
            amount_cents=amount_cents,
            fees_cents=fees_cents,
            net_amount_cents=net_amount_cents,
            status=PaymentStatus.COMPLETED,  # Paiement manuel = directement complété
            card_holder_name=form.card_holder_name.data,
            card_last_four=form.card_last_four.data,
            card_brand=form.card_brand.data,
            external_transaction_id=form.external_transaction_id.data,
            authorization_code=form.authorization_code.data,
            receipt_number=receipt_number,
            notes=form.notes.data,
            processed_at=datetime.utcnow(),
            created_by_user_id=current_user.id
        )
        
        db.session.add(payment)
        db.session.commit()
        
        flash(f"Paiement {receipt_number} créé avec succès", "success")
        return redirect(url_for("payments.payment_detail", payment_id=payment.id))
    
    return render_template("payments/payments/form.html", form=form, title="Nouveau paiement")


# =============================================================================
# ROUTES REMBOURSEMENTS
# =============================================================================

@bp.route("/payments/<int:payment_id>/refund", methods=["GET", "POST"])
@login_required
def refund_payment(payment_id):
    """Rembourser un paiement"""
    payment = Payment.query.filter_by(
        id=payment_id, business_id_fk=current_user.business.id
    ).first_or_404()
    
    if not payment.is_refundable:
        flash("Ce paiement ne peut pas être remboursé", "error")
        return redirect(url_for("payments.payment_detail", payment_id=payment.id))
    
    form = RefundForm()
    form.payment_id_fk.data = payment_id
    
    # Définir le montant maximum remboursable
    max_refund = payment.refundable_amount_cents / 100
    form.amount_euros.validators[1].max = max_refund
    
    if form.validate_on_submit():
        amount_cents = int(float(form.amount_euros.data) * 100)
        
        if amount_cents > payment.refundable_amount_cents:
            flash("Le montant dépasse le montant remboursable", "error")
        else:
            refund = PaymentRefund(
                payment_id_fk=payment_id,
                amount_cents=amount_cents,
                reason=RefundReason(form.reason.data),
                reason_description=form.reason_description.data,
                status=PaymentStatus.COMPLETED,  # Remboursement manuel = directement complété
                requested_by_user_id=current_user.id,
                processed_by_user_id=current_user.id,
                processed_at=datetime.utcnow(),
                notes=form.notes.data
            )
            
            db.session.add(refund)
            
            # Mettre à jour le statut du paiement si complètement remboursé
            if payment.refundable_amount_cents - amount_cents == 0:
                payment.status = PaymentStatus.REFUNDED
            elif payment.status == PaymentStatus.COMPLETED:
                payment.status = PaymentStatus.PARTIALLY_REFUNDED
            
            db.session.commit()
            
            flash(f"Remboursement de {form.amount_euros.data}€ effectué avec succès", "success")
            return redirect(url_for("payments.payment_detail", payment_id=payment.id))
    
    return render_template("payments/refunds/form.html", 
                         form=form, payment=payment, max_refund=max_refund)


@bp.route("/refunds")
@login_required
def refunds():
    """Liste des remboursements"""
    page = request.args.get("page", 1, type=int)
    
    refunds = PaymentRefund.query.join(Payment).filter(
        Payment.business_id_fk == current_user.business.id
    ).options(
        joinedload(PaymentRefund.payment).joinedload(Payment.payment_method),
        joinedload(PaymentRefund.requested_by)
    ).order_by(desc(PaymentRefund.requested_at)).paginate(
        page=page, per_page=20, error_out=False
    )
    
    return render_template("payments/refunds/list.html", refunds=refunds)


# =============================================================================
# ROUTES SESSIONS DE CAISSE
# =============================================================================

@bp.route("/cash-sessions")
@login_required
def cash_sessions():
    """Liste des sessions de caisse"""
    page = request.args.get("page", 1, type=int)
    
    sessions = CashSession.query.filter_by(business_id_fk=current_user.business.id)\
        .options(
            joinedload(CashSession.opened_by),
            joinedload(CashSession.closed_by)
        ).order_by(desc(CashSession.opened_at)).paginate(
            page=page, per_page=20, error_out=False
        )
    
    return render_template("payments/cash/sessions.html", sessions=sessions)


@bp.route("/cash-sessions/new", methods=["GET", "POST"])
@login_required
def new_cash_session():
    """Nouvelle session de caisse"""
    # Vérifier qu'il n'y a pas déjà une session active
    active_session = CashSession.query.filter_by(
        business_id_fk=current_user.business.id, is_active=True
    ).first()
    
    if active_session:
        flash("Une session de caisse est déjà active", "error")
        return redirect(url_for("payments.cash_sessions"))
    
    form = CashSessionForm()
    
    if form.validate_on_submit():
        opening_balance_cents = int(float(form.opening_balance_euros.data) * 100)
        
        # Générer un numéro de session
        session_number = f"CASH-{datetime.now().strftime('%Y%m%d-%H%M%S')}"
        
        session = CashSession(
            business_id_fk=current_user.business.id,
            session_number=session_number,
            opening_balance_cents=opening_balance_cents,
            opened_by_user_id=current_user.id,
            opening_notes=form.opening_notes.data
        )
        
        db.session.add(session)
        db.session.commit()
        
        flash(f"Session de caisse {session_number} ouverte avec succès", "success")
        return redirect(url_for("payments.cash_sessions"))
    
    return render_template("payments/cash/session_form.html", form=form, title="Ouvrir session")


@bp.route("/cash-sessions/<int:session_id>/close", methods=["GET", "POST"])
@login_required
def close_cash_session(session_id):
    """Fermer une session de caisse"""
    session = CashSession.query.filter_by(
        id=session_id, business_id_fk=current_user.business.id, is_active=True
    ).first_or_404()
    
    form = CashSessionCloseForm()
    form.session_id.data = session_id
    
    # Calculer le solde attendu
    expected_balance_cents = session.opening_balance_cents + session.total_sales_cents - session.total_refunds_cents
    
    if form.validate_on_submit():
        closing_balance_cents = int(float(form.closing_balance_euros.data) * 100)
        difference_cents = closing_balance_cents - expected_balance_cents
        
        session.closing_balance_cents = closing_balance_cents
        session.expected_balance_cents = expected_balance_cents
        session.difference_cents = difference_cents
        session.closed_at = datetime.utcnow()
        session.closed_by_user_id = current_user.id
        session.closing_notes = form.closing_notes.data
        session.is_active = False
        
        db.session.commit()
        
        if difference_cents == 0:
            flash("Session fermée avec succès - Pas d'écart", "success")
        else:
            diff_euros = abs(difference_cents) / 100
            diff_type = "excédent" if difference_cents > 0 else "manque"
            flash(f"Session fermée - {diff_type} de {diff_euros}€", "warning")
        
        return redirect(url_for("payments.cash_sessions"))
    
    return render_template("payments/cash/close_form.html", 
                         form=form, session=session, 
                         expected_balance=expected_balance_cents / 100)


# =============================================================================
# API ROUTES
# =============================================================================

@bp.route("/api/payment-methods")
@login_required
def api_payment_methods():
    """API des méthodes de paiement actives"""
    methods = PaymentMethod.query.filter_by(
        business_id_fk=current_user.business.id, is_active=True
    ).order_by(PaymentMethod.name).all()
    
    return jsonify([{
        "id": method.id,
        "name": method.name,
        "type": method.method_type.value,
        "is_default": method.is_default,
        "requires_authorization": method.requires_authorization,
        "minimum_amount_cents": method.minimum_amount_cents,
        "maximum_amount_cents": method.maximum_amount_cents
    } for method in methods])


@bp.route("/api/process-payment", methods=["POST"])
@login_required
def api_process_payment():
    """API pour traiter un paiement rapide"""
    data = request.get_json()
    
    try:
        # Récupérer la méthode de paiement
        payment_method = PaymentMethod.query.filter_by(
            id=data.get("payment_method_id"),
            business_id_fk=current_user.business.id,
            is_active=True
        ).first()
        
        if not payment_method:
            return jsonify({"error": "Méthode de paiement invalide"}), 400
        
        amount_cents = int(data.get("amount_cents", 0))
        
        if amount_cents <= 0:
            return jsonify({"error": "Montant invalide"}), 400
        
        # Vérifier les limites
        if amount_cents < payment_method.minimum_amount_cents:
            return jsonify({"error": "Montant inférieur au minimum autorisé"}), 400
        
        if (payment_method.maximum_amount_cents and 
            amount_cents > payment_method.maximum_amount_cents):
            return jsonify({"error": "Montant supérieur au maximum autorisé"}), 400
        
        # Calculer les frais
        fees_cents = payment_method.calculate_fees(amount_cents)
        net_amount_cents = amount_cents - fees_cents
        
        # Générer un numéro de reçu
        receipt_number = f"PAY-{datetime.now().strftime('%Y%m%d-%H%M%S')}"
        
        payment = Payment(
            business_id_fk=current_user.business.id,
            order_id_fk=data.get("order_id"),
            payment_method_id_fk=payment_method.id,
            amount_cents=amount_cents,
            fees_cents=fees_cents,
            net_amount_cents=net_amount_cents,
            status=PaymentStatus.COMPLETED,
            receipt_number=receipt_number,
            notes=data.get("notes"),
            processed_at=datetime.utcnow(),
            created_by_user_id=current_user.id
        )
        
        db.session.add(payment)
        
        # Créer un mouvement de caisse si c'est un paiement en espèces
        if payment_method.is_cash:
            active_session = CashSession.query.filter_by(
                business_id_fk=current_user.business.id, is_active=True
            ).first()
            
            cash_movement = CashMovement(
                business_id_fk=current_user.business.id,
                movement_type="sale",
                direction="in",
                amount_cents=amount_cents,
                reference_type="payment",
                reference_id=payment.id,
                reason="Vente",
                cash_session_id=active_session.id if active_session else None,
                created_by_user_id=current_user.id
            )
            
            db.session.add(cash_movement)
        
        db.session.commit()
        
        return jsonify({
            "success": True,
            "payment_id": payment.id,
            "receipt_number": payment.receipt_number,
            "amount_euros": payment.amount_euros,
            "fees_euros": payment.fees_euros,
            "net_amount_euros": payment.net_amount_euros
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({"error": str(e)}), 500


@bp.route("/api/cash-balance")
@login_required
def api_cash_balance():
    """API pour obtenir le solde de caisse actuel"""
    active_session = CashSession.query.filter_by(
        business_id_fk=current_user.business.id, is_active=True
    ).first()
    
    if not active_session:
        return jsonify({"error": "Aucune session de caisse active"}), 404
    
    current_balance = (active_session.opening_balance_cents + 
                      active_session.total_sales_cents - 
                      active_session.total_refunds_cents)
    
    return jsonify({
        "session_id": active_session.id,
        "session_number": active_session.session_number,
        "opening_balance_euros": active_session.opening_balance_euros,
        "current_balance_euros": current_balance / 100,
        "total_sales_euros": active_session.total_sales_cents / 100,
        "total_refunds_euros": active_session.total_refunds_cents / 100
    })


