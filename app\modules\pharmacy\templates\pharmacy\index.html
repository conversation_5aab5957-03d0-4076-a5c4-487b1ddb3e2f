{% extends 'base.html' %}
{% block title %}Pharmacie - Dashboard{% endblock %}

{% block head %}
<style>
    .pharmacy-dashboard {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 20px 0;
    }
    
    .stat-card {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 15px;
        padding: 30px;
        text-align: center;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        transition: transform 0.3s ease;
    }
    
    .stat-card:hover {
        transform: translateY(-5px);
    }
    
    .stat-number {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 10px;
    }
    
    .stat-label {
        color: #6c757d;
        font-size: 1.1rem;
        margin-bottom: 15px;
    }
    
    .stat-icon {
        font-size: 3rem;
        margin-bottom: 15px;
        opacity: 0.7;
    }
    
    .text-medication { color: #17a2b8; }
    .text-prescription { color: #28a745; }
    .text-stock { color: #dc3545; }
    .text-revenue { color: #ffc107; }
    
    .pharmacy-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        margin-bottom: 25px;
    }
    
    .quick-actions {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-top: 20px;
    }
    
    .action-btn {
        background: linear-gradient(45deg, #667eea, #764ba2);
        color: white;
        padding: 15px 20px;
        border-radius: 10px;
        text-decoration: none;
        text-align: center;
        transition: all 0.3s ease;
        border: none;
        font-size: 1rem;
    }
    
    .action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        color: white;
        text-decoration: none;
    }
    
    .prescription-item {
        border-left: 4px solid #667eea;
        padding: 15px;
        margin-bottom: 10px;
        background: #f8f9fa;
        border-radius: 0 8px 8px 0;
    }
    
    .stock-alert {
        background: linear-gradient(45deg, #ff6b6b, #ffa500);
        color: white;
        padding: 10px 15px;
        border-radius: 8px;
        margin-bottom: 10px;
    }
    
    .badge-status {
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 500;
    }
    
    .status-pending { background: #fff3cd; color: #856404; }
    .status-validated { background: #d4edda; color: #155724; }
    .status-dispensed { background: #cce5f0; color: #0c5460; }
</style>
{% endblock %}

{% block content %}
<div class="pharmacy-dashboard">
    <div class="container-fluid">
        <!-- En-tête -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="pharmacy-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1 class="mb-0">🏥 Pharmacie - Dashboard</h1>
                            <p class="text-muted mb-0">Gestion pharmaceutique professionnelle</p>
                        </div>
                        <div class="text-right">
                            <div class="text-muted">{{ moment().format('LLLL') }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistiques principales -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6">
                <div class="stat-card">
                    <div class="stat-icon text-medication">💊</div>
                    <div class="stat-number text-medication">{{ total_medications }}</div>
                    <div class="stat-label">Médicaments actifs</div>
                    <a href="{{ url_for('pharmacy.medications') }}" class="btn btn-outline-info btn-sm">Voir tout</a>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stat-card">
                    <div class="stat-icon text-prescription">📋</div>
                    <div class="stat-number text-prescription">{{ pending_prescriptions }}</div>
                    <div class="stat-label">Ordonnances en attente</div>
                    <a href="{{ url_for('pharmacy.prescriptions') }}?prescription_status=pending" class="btn btn-outline-success btn-sm">Traiter</a>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stat-card">
                    <div class="stat-icon text-stock">⚠️</div>
                    <div class="stat-number text-stock">{{ low_stock_medications }}</div>
                    <div class="stat-label">Alertes stock</div>
                    <a href="{{ url_for('pharmacy.medications') }}?low_stock_only=1" class="btn btn-outline-danger btn-sm">Réassortir</a>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stat-card">
                    <div class="stat-icon text-revenue">💰</div>
                    <div class="stat-number text-revenue">€ 0</div>
                    <div class="stat-label">CA du jour</div>
                    <a href="#" class="btn btn-outline-warning btn-sm">Détail</a>
                </div>
            </div>
        </div>

        <!-- Actions rapides -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="pharmacy-card">
                    <h4 class="mb-3">🚀 Actions rapides</h4>
                    <div class="quick-actions">
                        <a href="{{ url_for('pharmacy.new_prescription') }}" class="action-btn">
                            📋 Nouvelle ordonnance
                        </a>
                        <a href="{{ url_for('pharmacy.new_medication') }}" class="action-btn">
                            💊 Ajouter médicament
                        </a>
                        <a href="{{ url_for('pharmacy.interactions') }}" class="action-btn">
                            ⚡ Interactions
                        </a>
                        <a href="#" class="action-btn">
                            🔍 Vente libre
                        </a>
                        <a href="#" class="action-btn">
                            📊 Rapports
                        </a>
                        <a href="#" class="action-btn">
                            ⚙️ Configuration
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Ordonnances récentes -->
            <div class="col-lg-8">
                <div class="pharmacy-card">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h4 class="mb-0">📋 Ordonnances récentes</h4>
                        <a href="{{ url_for('pharmacy.prescriptions') }}" class="btn btn-outline-primary btn-sm">Voir toutes</a>
                    </div>
                    
                    {% if recent_prescriptions %}
                        {% for prescription in recent_prescriptions %}
                        <div class="prescription-item">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1">
                                        <a href="{{ url_for('pharmacy.prescription_detail', prescription_id=prescription.id) }}" class="text-decoration-none">
                                            {{ prescription.prescription_number }}
                                        </a>
                                    </h6>
                                    <p class="mb-1 text-muted">
                                        <strong>Patient:</strong> {{ prescription.customer.name if prescription.customer else 'N/A' }}<br>
                                        <strong>Médecin:</strong> {{ prescription.doctor_name }}<br>
                                        <strong>Date:</strong> {{ prescription.prescription_date.strftime('%d/%m/%Y') }}
                                    </p>
                                </div>
                                <div class="text-right">
                                    {% if prescription.status.value == 'pending' %}
                                        <span class="badge badge-warning">En attente</span>
                                    {% elif prescription.status.value == 'validated' %}
                                        <span class="badge badge-success">Validée</span>
                                    {% elif prescription.status.value == 'dispensed' %}
                                        <span class="badge badge-info">Délivrée</span>
                                    {% endif %}
                                    <div class="mt-2">
                                        <small class="text-muted">{{ prescription.total_items }} article(s)</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-4">
                            <p class="text-muted">Aucune ordonnance récente</p>
                            <a href="{{ url_for('pharmacy.new_prescription') }}" class="btn btn-primary">Créer une ordonnance</a>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Alertes stock -->
            <div class="col-lg-4">
                <div class="pharmacy-card">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h4 class="mb-0">⚠️ Alertes stock</h4>
                        <a href="{{ url_for('pharmacy.medications') }}?low_stock_only=1" class="btn btn-outline-danger btn-sm">Voir tout</a>
                    </div>
                    
                    {% if low_stock_meds %}
                        {% for medication in low_stock_meds %}
                        <div class="stock-alert">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">{{ medication.name }}</h6>
                                    <small>Stock: {{ medication.current_stock }} / Min: {{ medication.minimum_stock }}</small>
                                </div>
                                <div>
                                    <a href="{{ url_for('pharmacy.medication_detail', medication_id=medication.id) }}" class="btn btn-light btn-sm">
                                        Voir
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-4">
                            <div class="text-success">
                                <i class="fas fa-check-circle fa-3x mb-3"></i>
                                <p>Tous les stocks sont normaux</p>
                            </div>
                        </div>
                    {% endif %}
                </div>

                <!-- Widget météo pharmaceutique -->
                <div class="pharmacy-card mt-4">
                    <h5 class="mb-3">📈 Tendances</h5>
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="text-success">
                                <i class="fas fa-arrow-up"></i>
                                <strong>+15%</strong>
                            </div>
                            <small class="text-muted">Ordonnances</small>
                        </div>
                        <div class="col-6">
                            <div class="text-info">
                                <i class="fas fa-chart-line"></i>
                                <strong>€2,450</strong>
                            </div>
                            <small class="text-muted">CA semaine</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Animation des cartes au chargement
    const cards = document.querySelectorAll('.stat-card, .pharmacy-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        setTimeout(() => {
            card.style.transition = 'all 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
    
    // Mise à jour automatique des statistiques toutes les 30 secondes
    setInterval(() => {
        // API call pour mettre à jour les stats
        console.log('Updating pharmacy stats...');
    }, 30000);
});
</script>
{% endblock %}