"""Optimisations spécifiques aux requêtes de base de données"""
from typing import List, Optional, Any, Dict
from sqlalchemy import text
from sqlalchemy.orm import Query
from app.extensions import db
import logging

logger = logging.getLogger(__name__)

class DatabaseOptimizer:
    """Optimise les requêtes de base de données"""
    
    @staticmethod
    def optimize_query(query: Query) -> Query:
        """
        Applique des optimisations générales à une requête
        """
        # Ajouter des hints pour éviter les scans de table complets
        # quand c'est possible
        return query
        
    @staticmethod
    def get_query_plan(query: Query) -> Optional[Dict[str, Any]]:
        """
        Récupère le plan d'exécution d'une requête (PostgreSQL/MySQL)
        """
        try:
            # Cette méthode est spécifique à PostgreSQL
            # Pour MySQL, il faudrait utiliser EXPLAIN
            raw_query = query.statement.compile()
            explained = db.session.execute(text(f"EXPLAIN ANALYZE {raw_query}")).fetchall()
            return {"plan": [dict(row) for row in explained]}
        except Exception as e:
            logger.warning(f"Could not get query plan: {e}")
            return None
    
    @staticmethod
    def batch_load(model_class: Any, ids: List[int], batch_size: int = 100) -> List[Any]:
        """
        Charge des objets par lots pour éviter les requêtes N+1
        """
        results = []
        for i in range(0, len(ids), batch_size):
            batch_ids = ids[i:i + batch_size]
            batch_results = model_class.query.filter(
                model_class.id.in_(batch_ids)
            ).all()
            results.extend(batch_results)
        return results
    
    @staticmethod
    def prefetch_related(query: Query, relationships: List[str]) -> Query:
        """
        Précharge les relations spécifiées pour éviter les requêtes supplémentaires
        """
        for rel in relationships:
            query = query.options(db.joinedload(rel))
        return query

    @staticmethod
    def select_related_columns(query: Query, *columns) -> Query:
        """
        Sélectionne uniquement les colonnes nécessaires
        """
        return query.with_entities(*columns)
    
    @staticmethod
    def add_index_suggestions(table_name: str) -> List[str]:
        """
        Suggère des index en fonction de l'utilisation des colonnes
        """
        suggestions = []
        
        # Vérifier les colonnes fréquemment utilisées dans les WHERE
        common_filter_columns = ['id', 'business_id_fk', 'created_at', 'updated_at', 'is_active']
        for column in common_filter_columns:
            suggestions.append(f"CREATE INDEX IF NOT EXISTS idx_{table_name}_{column} ON {table_name}({column});")
        
        # Vérifier les colonnes utilisées dans les JOIN
        join_columns = ['business_id_fk', 'category_id_fk', 'user_id_fk']
        for column in join_columns:
            if column in common_filter_columns:
                continue
            suggestions.append(f"CREATE INDEX IF NOT EXISTS idx_{table_name}_{column} ON {table_name}({column});")
            
        return suggestions

def optimize_database():
    """
    Applique des optimisations globales à la base de données
    """
    try:
        # Activer les optimisations de requête
        db.session.execute(text("SET SESSION query_cache_type = ON;"))
        logger.info("Database query cache enabled")
    except Exception as e:
        logger.warning(f"Could not enable query cache: {e}")
    
    # Vérifier les index manquants
    suggestions = []
    # Pour une implémentation complète, on parcourerait toutes les tables
    # et on utiliserait add_index_suggestions
    
    return suggestions