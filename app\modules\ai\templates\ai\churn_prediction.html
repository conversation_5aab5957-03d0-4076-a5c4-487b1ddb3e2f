{% extends "base.html" %}

{% block title %}Prédiction de Churn Client{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-user-times"></i> Prédiction de Churn Client</h1>
        <button id="trainModelBtn" class="btn btn-primary">
            <i class="fas fa-brain"></i> Entraîner le Modèle
        </button>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-bar"></i> Clients à Risque</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="riskCustomersTable">
                            <thead>
                                <tr>
                                    <th>Nom</th>
                                    <th>Email</th>
                                    <th>Probabilité de Churn</th>
                                    <th>Jours Depuis Dernière Commande</th>
                                    <th>Total Dépensé (€)</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Les données seront chargées dynamiquement -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal pour les détails du client -->
<div class="modal fade" id="customerDetailsModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Détails du Client</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="customerDetailsContent">
                <!-- Les détails seront chargés dynamiquement -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                <button type="button" class="btn btn-primary" id="sendCampaignBtn">
                    <i class="fas fa-paper-plane"></i> Envoyer Campagne
                </button>
            </div>
        </div>
    </div>
</div>

<script>
    // Entraîner le modèle
    document.getElementById('trainModelBtn').addEventListener('click', function() {
        fetch('/ai/api/churn_prediction/train', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Modèle entraîné avec succès', 'success');
                loadRiskCustomers();
            } else {
                showNotification(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            showNotification('Erreur lors de l\'entraînement du modèle', 'error');
        });
    });

    // Charger les clients à risque
    function loadRiskCustomers() {
        fetch('/ai/api/churn_prediction/risk_customers')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const tbody = document.querySelector('#riskCustomersTable tbody');
                tbody.innerHTML = '';
                
                data.risk_customers.forEach(customer => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${customer.name}</td>
                        <td>${customer.email}</td>
                        <td>
                            <div class="progress">
                                <div class="progress-bar bg-danger" role="progressbar" 
                                     style="width: ${customer.churn_probability * 100}%"
                                     aria-valuenow="${customer.churn_probability * 100}" 
                                     aria-valuemin="0" aria-valuemax="100">
                                    ${(customer.churn_probability * 100).toFixed(1)}%
                                </div>
                            </div>
                        </td>
                        <td>${customer.days_since_last_order}</td>
                        <td>€${customer.total_spent.toFixed(2)}</td>
                        <td>
                            <button class="btn btn-sm btn-info view-details" data-customer-id="${customer.id}">
                                <i class="fas fa-eye"></i>
                            </button>
                        </td>
                    `;
                    tbody.appendChild(row);
                });
            } else {
                showNotification(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            showNotification('Erreur lors du chargement des clients à risque', 'error');
        });
    }

    // Voir les détails du client
    document.addEventListener('click', function(e) {
        if (e.target.closest('.view-details')) {
            const customerId = e.target.closest('.view-details').dataset.customerId;
            // Dans une implémentation réelle, on chargerait les détails du client
            // Pour cet exemple, nous affichons simplement un message
            showNotification(`Détails du client ${customerId} à implémenter`, 'info');
        }
    });

    // Envoyer une campagne
    document.getElementById('sendCampaignBtn').addEventListener('click', function() {
        showNotification('Fonction d\'envoi de campagne à implémenter', 'info');
    });

    // Charger les données au démarrage
    document.addEventListener('DOMContentLoaded', function() {
        loadRiskCustomers();
    });
</script>
{% endblock %}