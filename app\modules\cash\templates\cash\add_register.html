{% extends "base.html" %}

{% block title %}Ajouter une Caisse{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- En-tête -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-1">
                        <i class="fas fa-plus text-primary me-2"></i>
                        Ajouter une Caisse Enregistreuse
                    </h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="{{ url_for('cash.index') }}">Caisses</a>
                            </li>
                            <li class="breadcrumb-item active">Nouvelle caisse</li>
                        </ol>
                    </nav>
                </div>
                <a href="{{ url_for('cash.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>Retour à la liste
                </a>
            </div>

            <!-- Formulaire -->
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card shadow-sm">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-cash-register me-2"></i>
                                Configuration de la caisse
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" novalidate>
                                {{ form.hidden_tag() }}
                                
                                <!-- Informations de base -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="text-primary border-bottom pb-2 mb-3">
                                            <i class="fas fa-info-circle me-1"></i>
                                            Informations de base
                                        </h6>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        {{ form.register_code.label(class="form-label") }}
                                        <div class="input-group">
                                            {{ form.register_code(class="form-control" + (" is-invalid" if form.register_code.errors else "")) }}
                                            <button type="button" class="btn btn-outline-secondary" onclick="generateCode()">
                                                <i class="fas fa-magic"></i>
                                            </button>
                                            {% if form.register_code.errors %}
                                                <div class="invalid-feedback">
                                                    {% for error in form.register_code.errors %}{{ error }}{% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                        <small class="form-text text-muted">Code unique pour identifier la caisse</small>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        {{ form.name.label(class="form-label") }}
                                        {{ form.name(class="form-control" + (" is-invalid" if form.name.errors else "")) }}
                                        {% if form.name.errors %}
                                            <div class="invalid-feedback">
                                                {% for error in form.name.errors %}{{ error }}{% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                    
                                    <div class="col-md-12 mb-3">
                                        {{ form.location.label(class="form-label") }}
                                        {{ form.location(class="form-control" + (" is-invalid" if form.location.errors else "")) }}
                                        {% if form.location.errors %}
                                            <div class="invalid-feedback">
                                                {% for error in form.location.errors %}{{ error }}{% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                    
                                    <div class="col-md-12 mb-3">
                                        {{ form.description.label(class="form-label") }}
                                        {{ form.description(class="form-control" + (" is-invalid" if form.description.errors else "")) }}
                                        {% if form.description.errors %}
                                            <div class="invalid-feedback">
                                                {% for error in form.description.errors %}{{ error }}{% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Configuration financière -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="text-primary border-bottom pb-2 mb-3">
                                            <i class="fas fa-euro-sign me-1"></i>
                                            Configuration financière
                                        </h6>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        {{ form.initial_cash.label(class="form-label") }}
                                        <div class="input-group">
                                            {{ form.initial_cash(class="form-control" + (" is-invalid" if form.initial_cash.errors else "")) }}
                                            <span class="input-group-text">€</span>
                                            {% if form.initial_cash.errors %}
                                                <div class="invalid-feedback">
                                                    {% for error in form.initial_cash.errors %}{{ error }}{% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                        <small class="form-text text-muted">Montant de départ en caisse</small>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        {{ form.max_cash_limit.label(class="form-label") }}
                                        <div class="input-group">
                                            {{ form.max_cash_limit(class="form-control" + (" is-invalid" if form.max_cash_limit.errors else "")) }}
                                            <span class="input-group-text">€</span>
                                            {% if form.max_cash_limit.errors %}
                                                <div class="invalid-feedback">
                                                    {% for error in form.max_cash_limit.errors %}{{ error }}{% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                        <small class="form-text text-muted">Limite de sécurité (optionnel)</small>
                                    </div>
                                </div>

                                <!-- Configuration des reçus -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="text-primary border-bottom pb-2 mb-3">
                                            <i class="fas fa-receipt me-1"></i>
                                            Configuration des reçus
                                        </h6>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        {{ form.receipt_header.label(class="form-label") }}
                                        {{ form.receipt_header(class="form-control" + (" is-invalid" if form.receipt_header.errors else "")) }}
                                        {% if form.receipt_header.errors %}
                                            <div class="invalid-feedback">
                                                {% for error in form.receipt_header.errors %}{{ error }}{% endfor %}
                                            </div>
                                        {% endif %}
                                        <small class="form-text text-muted">Texte en haut des reçus</small>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        {{ form.receipt_footer.label(class="form-label") }}
                                        {{ form.receipt_footer(class="form-control" + (" is-invalid" if form.receipt_footer.errors else "")) }}
                                        {% if form.receipt_footer.errors %}
                                            <div class="invalid-feedback">
                                                {% for error in form.receipt_footer.errors %}{{ error }}{% endfor %}
                                            </div>
                                        {% endif %}
                                        <small class="form-text text-muted">Texte en bas des reçus</small>
                                    </div>
                                </div>

                                <!-- Options de sécurité et comportement -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="text-primary border-bottom pb-2 mb-3">
                                            <i class="fas fa-cogs me-1"></i>
                                            Options de comportement
                                        </h6>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="form-check form-switch mb-3">
                                            {{ form.requires_manager_approval(class="form-check-input") }}
                                            {{ form.requires_manager_approval.label(class="form-check-label") }}
                                            <small class="form-text text-muted d-block">
                                                Les mouvements de caisse nécessiteront une approbation
                                            </small>
                                        </div>
                                        
                                        <div class="form-check form-switch mb-3">
                                            {{ form.auto_print_receipts(class="form-check-input") }}
                                            {{ form.auto_print_receipts.label(class="form-check-label") }}
                                            <small class="form-text text-muted d-block">
                                                Impression automatique après chaque transaction
                                            </small>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="form-check form-switch mb-3">
                                            {{ form.is_active(class="form-check-input") }}
                                            {{ form.is_active.label(class="form-check-label") }}
                                            <small class="form-text text-muted d-block">
                                                La caisse sera immédiatement utilisable
                                            </small>
                                        </div>
                                    </div>
                                </div>

                                <!-- Boutons d'action -->
                                <div class="row">
                                    <div class="col-12">
                                        <div class="d-flex justify-content-between">
                                            <a href="{{ url_for('cash.index') }}" class="btn btn-outline-secondary">
                                                <i class="fas fa-times me-1"></i>Annuler
                                            </a>
                                            <div>
                                                <button type="submit" class="btn btn-primary">
                                                    <i class="fas fa-save me-1"></i>Créer la caisse
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    
                    <!-- Aide -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-info-circle me-1"></i>
                                Aide - Configuration d'une caisse
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Code de caisse</h6>
                                    <p class="small text-muted">
                                        Identifiant unique pour la caisse. Utilisez des codes courts et mnémotechniques 
                                        comme "CAISSE01", "ACCUEIL", "ETAGE2", etc.
                                    </p>
                                    
                                    <h6>Fond de caisse</h6>
                                    <p class="small text-muted">
                                        Montant de départ en espèces dans la caisse. 
                                        Ce montant sera automatiquement proposé à l'ouverture des sessions.
                                    </p>
                                </div>
                                <div class="col-md-6">
                                    <h6>Limite maximum</h6>
                                    <p class="small text-muted">
                                        Seuil de sécurité au-delà duquel des alertes seront générées. 
                                        Utile pour limiter les risques en cas de vol.
                                    </p>
                                    
                                    <h6>Approbation manager</h6>
                                    <p class="small text-muted">
                                        Si activé, tous les mouvements de caisse (dépôts, retraits) 
                                        devront être approuvés par un manager.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function generateCode() {
    const nameField = document.getElementById('name');
    const codeField = document.getElementById('register_code');
    
    if (nameField.value.trim()) {
        // Générer un code basé sur le nom
        const name = nameField.value.trim();
        const baseCode = name.substring(0, 6).toUpperCase().replace(/[^A-Z0-9]/g, '');
        const randomNum = Math.floor(Math.random() * 99) + 1;
        const code = baseCode + randomNum.toString().padStart(2, '0');
        
        codeField.value = code;
    } else {
        // Générer un code générique
        const randomNum = Math.floor(Math.random() * 9999) + 1;
        const code = 'CAISSE' + randomNum.toString().padStart(4, '0');
        codeField.value = code;
    }
}

// Validation côté client
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const requiredFields = ['register_code', 'name'];
    
    form.addEventListener('submit', function(e) {
        let isValid = true;
        
        requiredFields.forEach(fieldName => {
            const field = document.getElementById(fieldName);
            if (!field.value.trim()) {
                field.classList.add('is-invalid');
                isValid = false;
            } else {
                field.classList.remove('is-invalid');
            }
        });
        
        if (!isValid) {
            e.preventDefault();
        }
    });
    
    // Retirer la classe d'erreur lors de la saisie
    requiredFields.forEach(fieldName => {
        const field = document.getElementById(fieldName);
        field.addEventListener('input', function() {
            if (this.value.trim()) {
                this.classList.remove('is-invalid');
            }
        });
    });
    
    // Formater le code en majuscules
    const codeField = document.getElementById('register_code');
    codeField.addEventListener('input', function() {
        this.value = this.value.toUpperCase().replace(/[^A-Z0-9]/g, '');
    });
});
</script>
{% endblock %}