{% extends 'base.html' %}
{% block title %}Gestion des Écrans KDS{% endblock %}

{% block content %}
<div class="max-w-6xl mx-auto p-6">
    <!-- En-tête -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-white mb-2">Gestion des Écrans KDS</h1>
            <p class="text-slate-400">Configurez vos écrans de cuisine</p>
        </div>
        
        <div class="flex gap-3">
            <a href="{{ url_for('kds.index') }}" 
               class="bg-slate-700 hover:bg-slate-600 text-white px-4 py-2 rounded-md text-sm font-medium">
                ← Retour au KDS
            </a>
        </div>
    </div>
    
    <!-- Formulaire de création -->
    <div class="bg-slate-800 rounded-lg p-6 mb-6">
        <h2 class="text-lg font-semibold text-white mb-4">Ajouter un nouvel écran</h2>
        
        <form method="POST" class="flex gap-4 items-end">
            {{ form.hidden_tag() }}
            
            <div class="flex-1">
                {{ form.name.label(class="block text-sm font-medium text-slate-300 mb-2") }}
                {{ form.name(class="w-full bg-slate-700 border border-slate-600 text-white rounded-md px-3 py-2") }}
                {% if form.name.errors %}
                    <div class="text-red-400 text-sm mt-1">
                        {% for error in form.name.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
            
            <button type="submit" 
                    class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md font-medium">
                Créer l'écran
            </button>
        </form>
    </div>
    
    <!-- Liste des écrans existants -->
    <div class="bg-slate-800 rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-slate-700">
            <h2 class="text-lg font-semibold text-white">Écrans existants</h2>
        </div>
        
        {% if screens %}
        <div class="divide-y divide-slate-700">
            {% for screen in screens %}
            <div class="px-6 py-4 flex justify-between items-center hover:bg-slate-750">
                <div class="flex-1">
                    <h3 class="text-white font-medium">{{ screen.name }}</h3>
                    <p class="text-sm text-slate-400">ID: {{ screen.id }}</p>
                </div>
                
                <div class="flex gap-3">
                    <!-- Lien vers l'affichage plein écran -->
                    <a href="{{ url_for('kds.display_screen', screen_id=screen.id) }}" 
                       target="_blank"
                       class="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm font-medium">
                        Afficher
                    </a>
                    
                    <!-- Bouton d'édition -->
                    <a href="{{ url_for('kds.edit_screen', screen_id=screen.id) }}" 
                       class="bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-1 rounded text-sm font-medium">
                        Éditer
                    </a>
                    
                    <!-- Bouton de suppression -->
                    <form method="POST" action="{{ url_for('kds.delete_screen', screen_id=screen.id) }}" 
                          class="inline" 
                          onsubmit="return confirm('Êtes-vous sûr de vouloir supprimer cet écran ?')">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <button type="submit" 
                                class="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm font-medium">
                            Supprimer
                        </button>
                    </form>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="px-6 py-8 text-center">
            <div class="text-slate-400 mb-4">
                <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
            </div>
            <h3 class="text-lg font-medium text-white mb-2">Aucun écran configuré</h3>
            <p class="text-slate-400">Créez votre premier écran KDS ci-dessus</p>
        </div>
        {% endif %}
    </div>
    
    <!-- Guide d'utilisation -->
    <div class="mt-8 bg-blue-900/20 border border-blue-500/30 rounded-lg p-6">
        <h3 class="text-lg font-semibold text-blue-300 mb-3">Guide d'utilisation</h3>
        <div class="space-y-2 text-sm text-blue-200">
            <p><strong>Créer un écran :</strong> Donnez un nom descriptif (ex: "Cuisine principale", "Grill", "Desserts")</p>
            <p><strong>Afficher :</strong> Ouvre l'écran en plein écran, parfait pour un moniteur dédié</p>
            <p><strong>Éditer :</strong> Modifiez le nom de l'écran</p>
            <p><strong>Supprimer :</strong> Supprime définitivement l'écran (attention !)</p>
        </div>
    </div>
</div>
{% endblock %}