{% extends "base.html" %}

{% block title %}Gestion des modules - {{ super() }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">Accueil</a></li>
                        <li class="breadcrumb-item"><a href="{{ url_for('settings.index') }}">Paramètres</a></li>
                        <li class="breadcrumb-item active">Modules</li>
                    </ol>
                </div>
                <h4 class="page-title">Gestion des modules</h4>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="header-title">Modules disponibles</h4>
                    <p class="text-muted mb-0">
                        Activez ou désactivez les modules selon vos besoins
                        {% if business_type %}
                            (Type d'entreprise: <strong>{{ business_type.name }}</strong>)
                        {% endif %}
                    </p>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('settings.save_module_settings') }}">
                        {{ form.hidden_tag() }}
                        
                        <div class="row">
                            {% for module in available_modules %}
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="card border h-100 {{ 'border-success' if module.is_enabled else 'border-secondary' }}">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <h6 class="card-title mb-0">
                                                {% if module.module_name == 'catalog' %}
                                                    <i class="mdi mdi-tag text-primary"></i> Catalogue
                                                {% elif module.module_name == 'inventory' %}
                                                    <i class="mdi mdi-warehouse text-info"></i> Stocks
                                                {% elif module.module_name == 'sales' %}
                                                    <i class="mdi mdi-cart text-success"></i> Ventes
                                                {% elif module.module_name == 'pos' %}
                                                    <i class="mdi mdi-cash-register text-warning"></i> POS
                                                {% elif module.module_name == 'ingredients' %}
                                                    <i class="mdi mdi-food text-orange"></i> Ingrédients
                                                {% elif module.module_name == 'variants' %}
                                                    <i class="mdi mdi-palette text-purple"></i> Variantes
                                                {% elif module.module_name == 'pharmacy' %}
                                                    <i class="mdi mdi-medical-bag text-danger"></i> Pharmacie
                                                {% elif module.module_name == 'production' %}
                                                    <i class="mdi mdi-factory text-dark"></i> Production
                                                {% elif module.module_name == 'menu_planning' %}
                                                    <i class="mdi mdi-calendar text-success"></i> Planification menus
                                                {% elif module.module_name == 'loyalty' %}
                                                    <i class="mdi mdi-star text-warning"></i> Fidélité
                                                {% elif module.module_name == 'delivery' %}
                                                    <i class="mdi mdi-truck text-info"></i> Livraison
                                                {% else %}
                                                    <i class="mdi mdi-puzzle"></i> {{ module.module_name.title() }}
                                                {% endif %}
                                            </h6>
                                            <div class="form-check form-switch">
                                                <input 
                                                    class="form-check-input" 
                                                    type="checkbox" 
                                                    name="enabled_modules" 
                                                    value="{{ module.module_name }}"
                                                    id="module_{{ module.module_name }}"
                                                    {{ 'checked' if module.is_enabled else '' }}
                                                    {{ 'disabled' if module.is_required else '' }}
                                                >
                                            </div>
                                        </div>
                                        
                                        <p class="card-text text-muted small mb-2">
                                            {% if module.module_name == 'catalog' %}
                                                Gestion des produits, catégories et prix
                                            {% elif module.module_name == 'inventory' %}
                                                Suivi des stocks et mouvements
                                            {% elif module.module_name == 'sales' %}
                                                Gestion des commandes et factures
                                            {% elif module.module_name == 'pos' %}
                                                Interface de vente au comptoir
                                            {% elif module.module_name == 'ingredients' %}
                                                Gestion des ingrédients et recettes
                                            {% elif module.module_name == 'variants' %}
                                                Variantes de produits (tailles, couleurs)
                                            {% elif module.module_name == 'pharmacy' %}
                                                Gestion pharmaceutique et prescriptions
                                            {% elif module.module_name == 'production' %}
                                                Production et manufacturing
                                            {% elif module.module_name == 'menu_planning' %}
                                                Planification des menus et plats
                                            {% elif module.module_name == 'loyalty' %}
                                                Programme de fidélité client
                                            {% elif module.module_name == 'delivery' %}
                                                Gestion des livraisons
                                            {% else %}
                                                Module {{ module.module_name }}
                                            {% endif %}
                                        </p>
                                        
                                        {% if module.is_required %}
                                            <span class="badge bg-warning-subtle text-warning">Obligatoire</span>
                                        {% endif %}
                                        
                                        {% if module.is_enabled %}
                                            <span class="badge bg-success-subtle text-success">Activé</span>
                                        {% else %}
                                            <span class="badge bg-secondary-subtle text-secondary">Désactivé</span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="alert alert-info">
                                    <h6 class="alert-heading">Information</h6>
                                    <p class="mb-0">
                                        <strong>Modules obligatoires :</strong> Certains modules sont essentiels au fonctionnement du système et ne peuvent pas être désactivés.
                                        <br>
                                        <strong>Redémarrage requis :</strong> La modification des modules peut nécessiter un redémarrage de l'application.
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="text-end">
                                    <a href="{{ url_for('settings.index') }}" class="btn btn-secondary me-2">Annuler</a>
                                    {{ form.submit(class="btn btn-primary") }}
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}