{% extends 'base.html' %}
{% block title %}{{ template.name }} - Template{% endblock %}

{% block head %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<style>
    .template-page {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 20px 0;
    }
    
    .template-content {
        background: white;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        padding: 30px;
        margin-top: 20px;
    }
    
    .template-header {
        border-bottom: 3px solid #667eea;
        padding-bottom: 20px;
        margin-bottom: 30px;
    }
    
    .template-title {
        color: #333;
        font-size: 2.2rem;
        font-weight: 700;
        margin-bottom: 10px;
    }
    
    .template-meta {
        display: flex;
        gap: 20px;
        flex-wrap: wrap;
        margin-top: 15px;
    }
    
    .meta-item {
        background: #f8f9ff;
        padding: 10px 15px;
        border-radius: 10px;
        border-left: 4px solid #667eea;
    }
    
    .meta-label {
        font-size: 0.8rem;
        color: #666;
        text-transform: uppercase;
        font-weight: 600;
        margin-bottom: 5px;
    }
    
    .meta-value {
        font-size: 1rem;
        color: #333;
        font-weight: 500;
    }
    
    .section-card {
        background: #f8f9ff;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        border-left: 4px solid #667eea;
    }
    
    .section-title {
        color: #333;
        font-size: 1.4rem;
        font-weight: 600;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .btn-template {
        background: linear-gradient(45deg, #667eea, #764ba2);
        border: none;
        color: white;
        padding: 12px 25px;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
        margin: 5px;
    }
    
    .btn-template:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }
    
    .btn-template.btn-success {
        background: linear-gradient(45deg, #28a745, #20c997);
    }
    
    .btn-template.btn-danger {
        background: linear-gradient(45deg, #dc3545, #fd7e14);
    }
    
    .btn-template.btn-secondary {
        background: linear-gradient(45deg, #6c757d, #adb5bd);
    }
    
    .execution-item {
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 15px;
        transition: all 0.3s ease;
    }
    
    .execution-item:hover {
        border-color: #667eea;
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.1);
    }
    
    .status-badge {
        padding: 6px 15px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
    }
    
    .status-completed {
        background: #d4edda;
        color: #155724;
    }
    
    .status-failed {
        background: #f8d7da;
        color: #721c24;
    }
    
    .status-running {
        background: #fff3cd;
        color: #856404;
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .stat-card {
        background: white;
        border-radius: 10px;
        padding: 20px;
        text-align: center;
        border: 1px solid #e9ecef;
    }
    
    .stat-number {
        font-size: 2rem;
        font-weight: bold;
        color: #667eea;
        margin-bottom: 5px;
    }
    
    .stat-label {
        color: #666;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 1px;
    }
    
    .config-viewer {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 10px;
        padding: 20px;
        font-family: 'Courier New', monospace;
        white-space: pre-wrap;
        max-height: 300px;
        overflow-y: auto;
    }
    
    .type-badge {
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: 600;
        text-transform: uppercase;
    }
    
    .type-sales { background: #e3f2fd; color: #1976d2; }
    .type-financial { background: #f3e5f5; color: #7b1fa2; }
    .type-inventory { background: #e8f5e8; color: #388e3c; }
    .type-customer { background: #fff3e0; color: #f57c00; }
    .type-supplier { background: #fce4ec; color: #c2185b; }
    .type-production { background: #f1f8e9; color: #689f38; }
    
    .chart-container {
        position: relative;
        height: 300px;
        margin-top: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="template-page">
    <div class="container-fluid">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="text-white text-center mb-4">
                    <i class="fas fa-file-alt me-3"></i>
                    Détails du Template
                </h1>
                
                <div class="text-center">
                    <button onclick="executeTemplate()" class="btn-template btn-success">
                        <i class="fas fa-play me-2"></i>Exécuter Maintenant
                    </button>
                    
                    {% if template.created_by_id == current_user.id or current_user.is_admin %}
                    <a href="{{ url_for('reports.edit_template', id=template.id) }}" class="btn-template">
                        <i class="fas fa-edit me-2"></i>Modifier
                    </a>
                    {% endif %}
                    
                    <a href="{{ url_for('reports.list_templates') }}" class="btn-template btn-secondary">
                        <i class="fas fa-list me-2"></i>Tous les Templates
                    </a>
                    
                    <a href="{{ url_for('reports.index') }}" class="btn-template btn-secondary">
                        <i class="fas fa-home me-2"></i>Dashboard
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Contenu principal -->
        <div class="template-content">
            <!-- En-tête du template -->
            <div class="template-header">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h2 class="template-title">{{ template.name }}</h2>
                        <p class="text-muted mb-3">{{ template.description or 'Aucune description disponible' }}</p>
                        
                        <span class="type-badge type-{{ template.report_type.value }}">
                            {{ template.report_type.value }}
                        </span>
                        
                        {% if template.is_public %}
                        <span class="badge bg-success ms-2">
                            <i class="fas fa-globe me-1"></i>Public
                        </span>
                        {% else %}
                        <span class="badge bg-secondary ms-2">
                            <i class="fas fa-lock me-1"></i>Privé
                        </span>
                        {% endif %}
                    </div>
                </div>
                
                <div class="template-meta">
                    <div class="meta-item">
                        <div class="meta-label">Créé par</div>
                        <div class="meta-value">{{ template.created_by.username if template.created_by else 'Système' }}</div>
                    </div>
                    
                    <div class="meta-item">
                        <div class="meta-label">Date de création</div>
                        <div class="meta-value">{{ template.created_at.strftime('%d/%m/%Y %H:%M') }}</div>
                    </div>
                    
                    {% if template.updated_at and template.updated_at != template.created_at %}
                    <div class="meta-item">
                        <div class="meta-label">Dernière modification</div>
                        <div class="meta-value">{{ template.updated_at.strftime('%d/%m/%Y %H:%M') }}</div>
                    </div>
                    {% endif %}
                </div>
            </div>
            
            <!-- Statistiques d'exécution -->
            <div class="section-card">
                <h3 class="section-title">
                    <i class="fas fa-chart-bar"></i>
                    Statistiques d'Exécution
                </h3>
                
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">{{ execution_stats.total_executions }}</div>
                        <div class="stat-label">Total</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-number">{{ execution_stats.successful_executions }}</div>
                        <div class="stat-label">Réussies</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-number">{{ execution_stats.failed_executions }}</div>
                        <div class="stat-label">Échouées</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-number">
                            {% if execution_stats.total_executions > 0 %}
                                {{ ((execution_stats.successful_executions / execution_stats.total_executions) * 100)|round(1) }}%
                            {% else %}
                                0%
                            {% endif %}
                        </div>
                        <div class="stat-label">Taux de Succès</div>
                    </div>
                </div>
                
                {% if execution_stats.total_executions > 0 %}
                <div class="chart-container">
                    <canvas id="executionChart"></canvas>
                </div>
                {% endif %}
            </div>
            
            <!-- Configuration du template -->
            <div class="row">
                <div class="col-lg-6">
                    <div class="section-card">
                        <h3 class="section-title">
                            <i class="fas fa-cog"></i>
                            Configuration de Requête
                        </h3>
                        
                        {% if template.query_config %}
                        <div class="config-viewer">{{ template.query_config | tojson(indent=2) }}</div>
                        {% else %}
                        <p class="text-muted">Aucune configuration de requête définie</p>
                        {% endif %}
                    </div>
                </div>
                
                <div class="col-lg-6">
                    <div class="section-card">
                        <h3 class="section-title">
                            <i class="fas fa-chart-pie"></i>
                            Configuration de Graphiques
                        </h3>
                        
                        {% if template.chart_config %}
                        <div class="config-viewer">{{ template.chart_config | tojson(indent=2) }}</div>
                        {% else %}
                        <p class="text-muted">Aucune configuration de graphiques définie</p>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <!-- Dernières exécutions -->
            <div class="section-card">
                <h3 class="section-title">
                    <i class="fas fa-history"></i>
                    Dernières Exécutions
                </h3>
                
                {% if recent_executions %}
                    {% for execution in recent_executions %}
                    <div class="execution-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">
                                    <a href="{{ url_for('reports.view_execution', id=execution.id) }}" class="text-decoration-none">
                                        Exécution #{{ execution.id }}
                                    </a>
                                </h6>
                                <small class="text-muted">
                                    <i class="fas fa-clock me-1"></i>
                                    {{ execution.executed_at.strftime('%d/%m/%Y %H:%M') }}
                                    
                                    {% if execution.executed_by %}
                                    <span class="ms-3">
                                        <i class="fas fa-user me-1"></i>
                                        {{ execution.executed_by.username }}
                                    </span>
                                    {% endif %}
                                    
                                    {% if execution.completed_at %}
                                    <span class="ms-3">
                                        <i class="fas fa-stopwatch me-1"></i>
                                        {{ ((execution.completed_at - execution.executed_at).total_seconds())|round(2) }}s
                                    </span>
                                    {% endif %}
                                </small>
                            </div>
                            
                            <div class="d-flex align-items-center gap-2">
                                <span class="status-badge status-{{ execution.status.value }}">
                                    {{ execution.status.value }}
                                </span>
                                
                                {% if execution.status.value == 'completed' %}
                                <div class="btn-group" role="group">
                                    <a href="{{ url_for('reports.export_execution', id=execution.id, format='pdf') }}" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-file-pdf"></i>
                                    </a>
                                    <a href="{{ url_for('reports.export_execution', id=execution.id, format='excel') }}" 
                                       class="btn btn-sm btn-outline-success">
                                        <i class="fas fa-file-excel"></i>
                                    </a>
                                    <a href="{{ url_for('reports.export_execution', id=execution.id, format='csv') }}" 
                                       class="btn btn-sm btn-outline-info">
                                        <i class="fas fa-file-csv"></i>
                                    </a>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        {% if execution.error_message %}
                        <div class="alert alert-danger mt-2 mb-0">
                            <small><i class="fas fa-exclamation-triangle me-1"></i>{{ execution.error_message }}</small>
                        </div>
                        {% endif %}
                    </div>
                    {% endfor %}
                    
                    <div class="text-center mt-3">
                        <a href="{{ url_for('reports.list_executions', template_id=template.id) }}" class="btn btn-outline-primary">
                            <i class="fas fa-list me-2"></i>Voir Toutes les Exécutions
                        </a>
                    </div>
                {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-clock fa-3x mb-3"></i>
                        <p>Aucune exécution pour ce template</p>
                        <button onclick="executeTemplate()" class="btn-template btn-success">
                            <i class="fas fa-play me-2"></i>Première Exécution
                        </button>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Modal d'exécution -->
<div class="modal fade" id="executionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Exécution du Template</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="executionStatus">
                    <div class="d-flex align-items-center">
                        <div class="spinner-border spinner-border-sm me-3" role="status"></div>
                        <span>Exécution en cours...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function executeTemplate() {
    const modal = new bootstrap.Modal(document.getElementById('executionModal'));
    modal.show();
    
    document.getElementById('executionStatus').innerHTML = `
        <div class="d-flex align-items-center">
            <div class="spinner-border spinner-border-sm me-3" role="status"></div>
            <span>Exécution en cours...</span>
        </div>
    `;
    
    fetch(`/reports/templates/{{ template.id }}/execute`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
        },
        body: JSON.stringify({})
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            document.getElementById('executionStatus').innerHTML = `
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    ${data.message}
                </div>
                <div class="text-center">
                    <a href="/reports/executions/${data.execution_id}" class="btn-template">
                        Voir le Résultat
                    </a>
                    <button onclick="location.reload()" class="btn-template btn-secondary">
                        Rafraîchir la Page
                    </button>
                </div>
            `;
        } else {
            document.getElementById('executionStatus').innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    ${data.message}
                </div>
            `;
        }
    })
    .catch(error => {
        document.getElementById('executionStatus').innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle me-2"></i>
                Erreur lors de l'exécution: ${error.message}
            </div>
        `;
    });
}

// Graphique des exécutions
{% if execution_stats.total_executions > 0 %}
const ctx = document.getElementById('executionChart').getContext('2d');
const executionChart = new Chart(ctx, {
    type: 'doughnut',
    data: {
        labels: ['Réussies', 'Échouées'],
        datasets: [{
            data: [{{ execution_stats.successful_executions }}, {{ execution_stats.failed_executions }}],
            backgroundColor: ['#28a745', '#dc3545'],
            borderWidth: 0
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    padding: 20,
                    usePointStyle: true
                }
            }
        }
    }
});
{% endif %}
</script>
{% endblock %}