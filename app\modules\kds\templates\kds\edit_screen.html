{% extends 'base.html' %}
{% block title %}Éditer l'écran KDS{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto p-6">
    <!-- En-tête -->
    <div class="mb-6">
        <h1 class="text-2xl font-bold text-white mb-2">Éditer l'écran KDS</h1>
        <p class="text-slate-400">Modifiez les paramètres de l'écran "{{ screen.name }}"</p>
    </div>
    
    <!-- Formulaire d'édition -->
    <div class="bg-slate-800 rounded-lg p-6">
        <form method="POST">
            {{ form.hidden_tag() }}
            
            <div class="mb-6">
                {{ form.name.label(class="block text-sm font-medium text-slate-300 mb-2") }}
                {{ form.name(class="w-full bg-slate-700 border border-slate-600 text-white rounded-md px-3 py-2") }}
                {% if form.name.errors %}
                    <div class="text-red-400 text-sm mt-1">
                        {% for error in form.name.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
                <p class="text-sm text-slate-400 mt-1">Choisissez un nom descriptif pour cet écran</p>
            </div>
            
            <div class="flex gap-3">
                <button type="submit" 
                        class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md font-medium">
                    Enregistrer les modifications
                </button>
                
                <a href="{{ url_for('kds.screens') }}" 
                   class="bg-slate-700 hover:bg-slate-600 text-white px-6 py-2 rounded-md font-medium">
                    Annuler
                </a>
            </div>
        </form>
    </div>
    
    <!-- Aperçu -->
    <div class="mt-6 bg-slate-800 rounded-lg p-6">
        <h3 class="text-lg font-semibold text-white mb-3">Aperçu</h3>
        <div class="bg-slate-900 rounded border border-slate-700 p-4">
            <p class="text-white font-medium">{{ form.name.data or screen.name }}</p>
            <p class="text-sm text-slate-400">ID: {{ screen.id }}</p>
        </div>
    </div>
</div>
{% endblock %}