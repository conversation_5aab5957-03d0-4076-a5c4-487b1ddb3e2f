from __future__ import annotations

from datetime import datetime
from app.extensions import db
from sqlalchemy import func
from enum import Enum


class TicketPriority(Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"


class TicketStatus(Enum):
    OPEN = "open"
    IN_PROGRESS = "in_progress"
    WAITING_CUSTOMER = "waiting_customer"
    RESOLVED = "resolved"
    CLOSED = "closed"


class TicketCategory(Enum):
    TECHNICAL = "technical"
    BILLING = "billing"
    FEATURE_REQUEST = "feature_request"
    BUG_REPORT = "bug_report"
    TRAINING = "training"
    OTHER = "other"


class SupportTicket(db.Model):
    """Modèle pour les tickets de support"""
    __tablename__ = "support_tickets"
    id = db.Column(db.Integer, primary_key=True)
    business_id_fk = db.Column(db.Integer, db.<PERSON>ey("businesses.id"), nullable=False, index=True)
    created_by_id = db.Column(db.Integer, db.<PERSON>ey("users.id"), nullable=False, index=True)
    assigned_to_id = db.Column(db.Integer, db.ForeignKey("users.id"), nullable=True, index=True)
    
    # Informations du ticket
    ticket_number = db.Column(db.String(20), nullable=False, unique=True, index=True)
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text, nullable=False)
    
    # Classification
    category = db.Column(db.Enum(TicketCategory), nullable=False, default=TicketCategory.OTHER)
    priority = db.Column(db.Enum(TicketPriority), nullable=False, default=TicketPriority.MEDIUM)
    status = db.Column(db.Enum(TicketStatus), nullable=False, default=TicketStatus.OPEN)
    
    # Informations techniques
    environment_info = db.Column(db.JSON, nullable=True)  # Info sur l'environnement
    browser_info = db.Column(db.String(500), nullable=True)
    error_details = db.Column(db.Text, nullable=True)
    steps_to_reproduce = db.Column(db.Text, nullable=True)
    
    # Gestion du temps
    estimated_hours = db.Column(db.Integer, nullable=True)  # Estimation en heures
    actual_hours = db.Column(db.Integer, nullable=True)  # Temps réel passé
    due_date = db.Column(db.DateTime, nullable=True)
    
    # Résolution
    resolution = db.Column(db.Text, nullable=True)
    resolution_time_minutes = db.Column(db.Integer, nullable=True)
    customer_satisfaction = db.Column(db.Integer, nullable=True)  # Note de 1 à 5
    customer_feedback = db.Column(db.Text, nullable=True)
    
    # Métadonnées
    is_public = db.Column(db.Boolean, default=False, nullable=False)  # Visible dans FAQ
    tags = db.Column(db.JSON, nullable=True)  # Tags pour recherche
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    resolved_at = db.Column(db.DateTime, nullable=True)
    closed_at = db.Column(db.DateTime, nullable=True)
    
    # Relations
    comments = db.relationship("TicketComment", backref="ticket", lazy=True, cascade="all, delete-orphan")
    attachments = db.relationship("TicketAttachment", backref="ticket", lazy=True, cascade="all, delete-orphan")
    history = db.relationship("TicketHistory", backref="ticket", lazy=True, cascade="all, delete-orphan")
    
    __table_args__ = (
        db.Index("idx_ticket_business", "business_id_fk"),
        db.Index("idx_ticket_creator", "created_by_id"),
        db.Index("idx_ticket_assignee", "assigned_to_id"),
        db.Index("idx_ticket_status", "status"),
        db.Index("idx_ticket_priority", "priority"),
        db.Index("idx_ticket_category", "category"),
        db.Index("idx_ticket_created", "created_at"),
    )
    
    @property
    def is_overdue(self):
        """Vérifie si le ticket est en retard"""
        if self.due_date and self.status not in [TicketStatus.RESOLVED, TicketStatus.CLOSED]:
            return datetime.utcnow() > self.due_date
        return False
    
    @property
    def age_in_hours(self):
        """Retourne l'âge du ticket en heures"""
        return int((datetime.utcnow() - self.created_at).total_seconds() / 3600)
    
    @property
    def response_time_minutes(self):
        """Calcule le temps de première réponse en minutes"""
        first_comment = TicketComment.query.filter_by(
            ticket_id_fk=self.id,
            is_internal=False
        ).order_by(TicketComment.created_at).first()
        
        if first_comment:
            return int((first_comment.created_at - self.created_at).total_seconds() / 60)
        return None
    
    def change_status(self, new_status, user_id, comment=None):
        """Change le statut du ticket et enregistre l'historique"""
        old_status = self.status
        self.status = new_status
        self.updated_at = datetime.utcnow()
        
        if new_status == TicketStatus.RESOLVED:
            self.resolved_at = datetime.utcnow()
            if self.created_at:
                self.resolution_time_minutes = int(
                    (self.resolved_at - self.created_at).total_seconds() / 60
                )
        elif new_status == TicketStatus.CLOSED:
            self.closed_at = datetime.utcnow()
        
        # Enregistrer dans l'historique
        history = TicketHistory(
            ticket_id_fk=self.id,
            user_id_fk=user_id,
            action_type="status_change",
            old_value=old_status.value if old_status else None,
            new_value=new_status.value,
            comment=comment
        )
        db.session.add(history)


class TicketComment(db.Model):
    """Commentaires sur les tickets"""
    __tablename__ = "ticket_comments"
    id = db.Column(db.Integer, primary_key=True)
    ticket_id_fk = db.Column(db.Integer, db.ForeignKey("support_tickets.id"), nullable=False, index=True)
    user_id_fk = db.Column(db.Integer, db.ForeignKey("users.id"), nullable=False, index=True)
    
    # Contenu
    comment = db.Column(db.Text, nullable=False)
    is_internal = db.Column(db.Boolean, default=False, nullable=False)  # Note interne ou visible client
    is_solution = db.Column(db.Boolean, default=False, nullable=False)  # Marquer comme solution
    
    # Temps passé
    time_spent_minutes = db.Column(db.Integer, nullable=True)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    __table_args__ = (
        db.Index("idx_comment_ticket", "ticket_id_fk"),
        db.Index("idx_comment_user", "user_id_fk"),
        db.Index("idx_comment_created", "created_at"),
    )


class TicketAttachment(db.Model):
    """Pièces jointes des tickets"""
    __tablename__ = "ticket_attachments"
    id = db.Column(db.Integer, primary_key=True)
    ticket_id_fk = db.Column(db.Integer, db.ForeignKey("support_tickets.id"), nullable=False, index=True)
    uploaded_by_id = db.Column(db.Integer, db.ForeignKey("users.id"), nullable=False, index=True)
    
    # Informations du fichier
    filename = db.Column(db.String(255), nullable=False)
    original_filename = db.Column(db.String(255), nullable=False)
    file_path = db.Column(db.String(500), nullable=False)
    file_size = db.Column(db.Integer, nullable=False)  # Taille en bytes
    mime_type = db.Column(db.String(100), nullable=True)
    
    # Métadonnées
    description = db.Column(db.String(500), nullable=True)
    is_public = db.Column(db.Boolean, default=False, nullable=False)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    
    __table_args__ = (
        db.Index("idx_attachment_ticket", "ticket_id_fk"),
        db.Index("idx_attachment_user", "uploaded_by_id"),
    )


class TicketHistory(db.Model):
    """Historique des modifications des tickets"""
    __tablename__ = "ticket_history"
    id = db.Column(db.Integer, primary_key=True)
    ticket_id_fk = db.Column(db.Integer, db.ForeignKey("support_tickets.id"), nullable=False, index=True)
    user_id_fk = db.Column(db.Integer, db.ForeignKey("users.id"), nullable=False, index=True)
    
    # Informations de l'action
    action_type = db.Column(db.String(50), nullable=False)  # created, status_change, assigned, etc.
    old_value = db.Column(db.String(500), nullable=True)
    new_value = db.Column(db.String(500), nullable=True)
    comment = db.Column(db.Text, nullable=True)
    
    # Timestamp
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    
    __table_args__ = (
        db.Index("idx_history_ticket", "ticket_id_fk"),
        db.Index("idx_history_user", "user_id_fk"),
        db.Index("idx_history_action", "action_type"),
        db.Index("idx_history_created", "created_at"),
    )


class FAQ(db.Model):
    """Base de connaissances - Questions fréquemment posées"""
    __tablename__ = "faq"
    id = db.Column(db.Integer, primary_key=True)
    business_id_fk = db.Column(db.Integer, db.ForeignKey("businesses.id"), nullable=True, index=True)  # NULL = FAQ globale
    created_by_id = db.Column(db.Integer, db.ForeignKey("users.id"), nullable=False, index=True)
    
    # Contenu
    title = db.Column(db.String(200), nullable=False)
    question = db.Column(db.Text, nullable=False)
    answer = db.Column(db.Text, nullable=False)
    
    # Classification
    category = db.Column(db.String(50), nullable=False, index=True)
    subcategory = db.Column(db.String(50), nullable=True)
    tags = db.Column(db.JSON, nullable=True)  # Tags pour recherche
    
    # Métadonnées
    difficulty_level = db.Column(db.String(20), nullable=False, default='beginner')  # beginner, intermediate, advanced
    view_count = db.Column(db.Integer, default=0, nullable=False)
    helpful_count = db.Column(db.Integer, default=0, nullable=False)
    not_helpful_count = db.Column(db.Integer, default=0, nullable=False)
    
    # Statut
    is_published = db.Column(db.Boolean, default=True, nullable=False)
    is_featured = db.Column(db.Boolean, default=False, nullable=False)
    display_order = db.Column(db.Integer, default=0, nullable=False)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    __table_args__ = (
        db.Index("idx_faq_business", "business_id_fk"),
        db.Index("idx_faq_category", "category"),
        db.Index("idx_faq_published", "is_published"),
        db.Index("idx_faq_featured", "is_featured"),
        db.Index("idx_faq_order", "display_order"),
    )
    
    @property
    def helpfulness_ratio(self):
        """Calcule le ratio d'utilité (votes positifs / total votes)"""
        total_votes = self.helpful_count + self.not_helpful_count
        if total_votes > 0:
            return self.helpful_count / total_votes
        return 0


class KnowledgeArticle(db.Model):
    """Articles de la base de connaissances"""
    __tablename__ = "knowledge_articles"
    id = db.Column(db.Integer, primary_key=True)
    business_id_fk = db.Column(db.Integer, db.ForeignKey("businesses.id"), nullable=True, index=True)
    created_by_id = db.Column(db.Integer, db.ForeignKey("users.id"), nullable=False, index=True)
    
    # Contenu
    title = db.Column(db.String(200), nullable=False)
    slug = db.Column(db.String(250), nullable=False, unique=True, index=True)
    content = db.Column(db.Text, nullable=False)
    summary = db.Column(db.Text, nullable=True)
    
    # Classification
    category = db.Column(db.String(50), nullable=False, index=True)
    subcategory = db.Column(db.String(50), nullable=True)
    tags = db.Column(db.JSON, nullable=True)
    
    # Métadonnées
    difficulty_level = db.Column(db.String(20), nullable=False, default='beginner')
    estimated_read_time = db.Column(db.Integer, nullable=True)  # Temps de lecture en minutes
    view_count = db.Column(db.Integer, default=0, nullable=False)
    
    # Statut
    is_published = db.Column(db.Boolean, default=True, nullable=False)
    is_featured = db.Column(db.Boolean, default=False, nullable=False)
    
    # SEO
    meta_description = db.Column(db.String(300), nullable=True)
    meta_keywords = db.Column(db.String(500), nullable=True)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    published_at = db.Column(db.DateTime, nullable=True)
    
    __table_args__ = (
        db.Index("idx_article_business", "business_id_fk"),
        db.Index("idx_article_category", "category"),
        db.Index("idx_article_published", "is_published"),
        db.Index("idx_article_slug", "slug"),
    )


class SupportMetrics(db.Model):
    """Métriques et statistiques du support"""
    __tablename__ = "support_metrics"
    id = db.Column(db.Integer, primary_key=True)
    business_id_fk = db.Column(db.Integer, db.ForeignKey("businesses.id"), nullable=False, index=True)
    
    # Période
    metric_date = db.Column(db.Date, nullable=False, index=True)
    metric_type = db.Column(db.String(20), nullable=False)  # daily, weekly, monthly
    
    # Métriques des tickets
    tickets_created = db.Column(db.Integer, default=0, nullable=False)
    tickets_resolved = db.Column(db.Integer, default=0, nullable=False)
    tickets_closed = db.Column(db.Integer, default=0, nullable=False)
    
    # Temps de réponse
    avg_response_time_minutes = db.Column(db.Integer, nullable=True)
    avg_resolution_time_minutes = db.Column(db.Integer, nullable=True)
    
    # Satisfaction client
    avg_satisfaction_score = db.Column(db.Float, nullable=True)  # Moyenne des notes de satisfaction
    total_satisfaction_responses = db.Column(db.Integer, default=0, nullable=False)
    
    # Charge de travail
    total_time_spent_minutes = db.Column(db.Integer, default=0, nullable=False)
    backlog_size = db.Column(db.Integer, default=0, nullable=False)  # Tickets en attente
    
    # Timestamp
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    
    __table_args__ = (
        db.Index("idx_metrics_business", "business_id_fk"),
        db.Index("idx_metrics_date", "metric_date"),
        db.Index("idx_metrics_type", "metric_type"),
        db.UniqueConstraint("business_id_fk", "metric_date", "metric_type", name="uq_business_metrics"),
    )