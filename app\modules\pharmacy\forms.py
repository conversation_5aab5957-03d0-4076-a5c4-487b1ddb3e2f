from __future__ import annotations

from datetime import date, datetime, timedelta
from flask_wtf import FlaskForm
from wtforms import (
    StringField, TextAreaField, IntegerField, DecimalField, SelectField, 
    BooleanField, DateField, DateTimeField, HiddenField, FieldList, FormField
)
from wtforms.validators import (
    DataRequired, Length, NumberRange, Optional, ValidationError, Email, Regexp
)
from wtforms.widgets import TextArea

from app.modules.pharmacy.models import (
    MedicationClass, PrescriptionStatus, InteractionSeverity
)


class MedicationForm(FlaskForm):
    """Formulaire pour les médicaments"""
    
    # Informations de base
    name = StringField(
        "Nom du médicament",
        validators=[DataRequired(), Length(min=2, max=200)],
        render_kw={"placeholder": "Ex: Doliprane 1000mg"}
    )
    generic_name = StringField(
        "Dénomination Commune Internationale (DCI)",
        validators=[Optional(), Length(max=200)],
        render_kw={"placeholder": "Ex: Paracétamol"}
    )
    brand_name = StringField(
        "Nom de marque",
        validators=[Optional(), Length(max=200)],
        render_kw={"placeholder": "Ex: Doliprane"}
    )
    
    # Codes d'identification
    cip_code = StringField(
        "Code CIP",
        validators=[Optional(), Length(max=20), Regexp(r'^\d{13}$', message="Le code CIP doit contenir 13 chiffres")],
        render_kw={"placeholder": "3400123456789"}
    )
    atc_code = StringField(
        "Code ATC",
        validators=[Optional(), Length(max=10), Regexp(r'^[A-Z]\d{2}[A-Z]{2}\d{2}$', message="Format ATC invalide (ex: N02BE01)")],
        render_kw={"placeholder": "N02BE01"}
    )
    barcode = StringField(
        "Code-barres",
        validators=[Optional(), Length(max=50)],
        render_kw={"placeholder": "Code-barres du produit"}
    )
    
    # Classification
    medication_class = SelectField(
        "Classification",
        choices=[
            (MedicationClass.OTC.value, "En vente libre"),
            (MedicationClass.PRESCRIPTION.value, "Sur ordonnance"),
            (MedicationClass.CONTROLLED.value, "Stupéfiants/Psychotropes"),
            (MedicationClass.HOSPITAL_ONLY.value, "Usage hospitalier uniquement")
        ],
        default=MedicationClass.OTC.value,
        validators=[DataRequired()]
    )
    therapeutic_class = StringField(
        "Classe thérapeutique",
        validators=[Optional(), Length(max=100)],
        render_kw={"placeholder": "Ex: Antalgique non opioïde"}
    )
    
    # Forme pharmaceutique
    pharmaceutical_form = StringField(
        "Forme pharmaceutique",
        validators=[Optional(), Length(max=100)],
        render_kw={"placeholder": "Ex: Comprimé pelliculé"}
    )
    dosage = StringField(
        "Dosage",
        validators=[Optional(), Length(max=100)],
        render_kw={"placeholder": "Ex: 1000 mg"}
    )
    unit_of_measure = StringField(
        "Unité de mesure",
        validators=[Optional(), Length(max=20)],
        default="unité",
        render_kw={"placeholder": "Ex: comprimé, ml, g"}
    )
    
    # Composition
    active_ingredients = TextAreaField(
        "Principes actifs",
        validators=[Optional()],
        render_kw={"placeholder": "Composition en principes actifs", "rows": 3}
    )
    excipients = TextAreaField(
        "Excipients notoires",
        validators=[Optional()],
        render_kw={"placeholder": "Excipients à effet notoire", "rows": 2}
    )
    
    # Informations réglementaires
    marketing_authorization = StringField(
        "AMM (Autorisation de Mise sur le Marché)",
        validators=[Optional(), Length(max=50)],
        render_kw={"placeholder": "Numéro AMM"}
    )
    is_generic = BooleanField("Médicament générique")
    is_biosimilar = BooleanField("Médicament biosimilaire")
    
    # Conditions de conservation
    storage_temperature_min = IntegerField(
        "Température minimale (°C)",
        validators=[Optional(), NumberRange(min=-80, max=100)],
        render_kw={"placeholder": "Ex: 2"}
    )
    storage_temperature_max = IntegerField(
        "Température maximale (°C)",
        validators=[Optional(), NumberRange(min=-80, max=100)],
        render_kw={"placeholder": "Ex: 8"}
    )
    storage_conditions = TextAreaField(
        "Conditions de conservation",
        validators=[Optional()],
        render_kw={"placeholder": "Conditions spéciales de conservation", "rows": 2}
    )
    
    # Validité
    shelf_life_months = IntegerField(
        "Durée de vie (mois)",
        validators=[Optional(), NumberRange(min=1, max=120)],
        default=36,
        render_kw={"placeholder": "36"}
    )
    
    # Prix et remboursement
    price_cents = IntegerField(
        "Prix (centimes)",
        validators=[DataRequired(), NumberRange(min=0, max=1000000)],
        render_kw={"placeholder": "Prix en centimes (ex: 450 pour 4,50€)"}
    )
    reimbursement_rate = DecimalField(
        "Taux de remboursement (%)",
        validators=[Optional(), NumberRange(min=0, max=100)],
        default=0.0,
        render_kw={"placeholder": "Ex: 65.0"}
    )
    
    # Alertes et contre-indications
    contraindications = TextAreaField(
        "Contre-indications",
        validators=[Optional()],
        render_kw={"placeholder": "Contre-indications principales", "rows": 3}
    )
    side_effects = TextAreaField(
        "Effets indésirables",
        validators=[Optional()],
        render_kw={"placeholder": "Effets indésirables principaux", "rows": 3}
    )
    warnings = TextAreaField(
        "Mises en garde",
        validators=[Optional()],
        render_kw={"placeholder": "Mises en garde et précautions", "rows": 3}
    )
    
    # Posologie
    adult_dosage = TextAreaField(
        "Posologie adulte",
        validators=[Optional()],
        render_kw={"placeholder": "Posologie recommandée pour adulte", "rows": 2}
    )
    pediatric_dosage = TextAreaField(
        "Posologie pédiatrique",
        validators=[Optional()],
        render_kw={"placeholder": "Posologie recommandée pour enfant", "rows": 2}
    )
    elderly_dosage = TextAreaField(
        "Posologie personne âgée",
        validators=[Optional()],
        render_kw={"placeholder": "Posologie recommandée pour personne âgée", "rows": 2}
    )
    
    # Stock et gestion
    current_stock = IntegerField(
        "Stock actuel",
        validators=[Optional(), NumberRange(min=0)],
        default=0,
        render_kw={"placeholder": "Quantité en stock"}
    )
    minimum_stock = IntegerField(
        "Stock minimum",
        validators=[Optional(), NumberRange(min=0)],
        default=0,
        render_kw={"placeholder": "Seuil d'alerte"}
    )
    maximum_stock = IntegerField(
        "Stock maximum",
        validators=[Optional(), NumberRange(min=0)],
        default=100,
        render_kw={"placeholder": "Stock maximum autorisé"}
    )
    
    is_active = BooleanField("Médicament actif", default=True)

    def validate_storage_temperature_max(self, field):
        """Validation des températures de stockage"""
        if field.data and self.storage_temperature_min.data:
            if field.data <= self.storage_temperature_min.data:
                raise ValidationError("La température maximum doit être supérieure à la température minimum")

    def validate_maximum_stock(self, field):
        """Validation du stock maximum"""
        if field.data and self.minimum_stock.data:
            if field.data <= self.minimum_stock.data:
                raise ValidationError("Le stock maximum doit être supérieur au stock minimum")


class PrescriptionForm(FlaskForm):
    """Formulaire pour les ordonnances"""
    
    # ID du client (caché car sélectionné via interface)
    customer_id_fk = HiddenField("ID Client", validators=[DataRequired()])
    
    # Informations de l'ordonnance
    prescription_number = StringField(
        "Numéro d'ordonnance",
        validators=[DataRequired(), Length(min=3, max=50)],
        render_kw={"placeholder": "Ex: ORD-2024-001"}
    )
    prescription_date = DateField(
        "Date de prescription",
        validators=[DataRequired()],
        default=date.today
    )
    validity_date = DateField(
        "Date limite d'utilisation",
        validators=[DataRequired()],
        default=lambda: date.today() + timedelta(days=365)
    )
    
    # Médecin prescripteur
    doctor_name = StringField(
        "Nom du médecin",
        validators=[DataRequired(), Length(min=2, max=200)],
        render_kw={"placeholder": "Dr. Martin Dupont"}
    )
    doctor_rpps = StringField(
        "Numéro RPPS",
        validators=[Optional(), Length(max=20), Regexp(r'^\d{11}$', message="Le numéro RPPS doit contenir 11 chiffres")],
        render_kw={"placeholder": "12345678901"}
    )
    doctor_specialty = StringField(
        "Spécialité médicale",
        validators=[Optional(), Length(max=100)],
        render_kw={"placeholder": "Ex: Médecine générale"}
    )
    
    # Statut
    status = SelectField(
        "Statut",
        choices=[
            (PrescriptionStatus.PENDING.value, "En attente"),
            (PrescriptionStatus.VALIDATED.value, "Validée"),
            (PrescriptionStatus.DISPENSED.value, "Délivrée"),
            (PrescriptionStatus.PARTIALLY_DISPENSED.value, "Partiellement délivrée"),
            (PrescriptionStatus.CANCELLED.value, "Annulée"),
            (PrescriptionStatus.EXPIRED.value, "Expirée")
        ],
        default=PrescriptionStatus.PENDING.value,
        validators=[DataRequired()]
    )
    
    # Informations patient
    patient_weight = DecimalField(
        "Poids du patient (kg)",
        validators=[Optional(), NumberRange(min=0.1, max=500.0)],
        render_kw={"placeholder": "Ex: 70.5"}
    )
    patient_allergies = TextAreaField(
        "Allergies connues",
        validators=[Optional()],
        render_kw={"placeholder": "Allergies et intolérances connues", "rows": 3}
    )
    
    # Notes
    doctor_notes = TextAreaField(
        "Notes du médecin",
        validators=[Optional()],
        render_kw={"placeholder": "Commentaires du médecin prescripteur", "rows": 3}
    )
    pharmacist_notes = TextAreaField(
        "Notes du pharmacien",
        validators=[Optional()],
        render_kw={"placeholder": "Observations du pharmacien", "rows": 3}
    )

    def validate_validity_date(self, field):
        """Validation de la date de validité"""
        if field.data and self.prescription_date.data:
            if field.data <= self.prescription_date.data:
                raise ValidationError("La date limite doit être postérieure à la date de prescription")


class PrescriptionItemForm(FlaskForm):
    """Formulaire pour les articles d'ordonnance"""
    
    # ID du médicament (caché car sélectionné via interface)
    medication_id_fk = HiddenField("ID Médicament", validators=[DataRequired()])
    
    # Prescription médicale
    prescribed_quantity = IntegerField(
        "Quantité prescrite",
        validators=[DataRequired(), NumberRange(min=1, max=1000)],
        render_kw={"placeholder": "Ex: 30"}
    )
    dispensed_quantity = IntegerField(
        "Quantité délivrée",
        validators=[Optional(), NumberRange(min=0, max=1000)],
        default=0,
        render_kw={"placeholder": "Ex: 30"}
    )
    
    # Posologie
    dosage_instructions = TextAreaField(
        "Instructions de posologie",
        validators=[DataRequired()],
        render_kw={"placeholder": "Ex: 1 comprimé matin et soir pendant 15 jours", "rows": 2}
    )
    duration_days = IntegerField(
        "Durée du traitement (jours)",
        validators=[Optional(), NumberRange(min=1, max=365)],
        render_kw={"placeholder": "Ex: 15"}
    )
    
    # Substitution
    substitution_allowed = BooleanField("Substitution autorisée", default=True)
    substitute_medication_id = HiddenField("ID Médicament de substitution")
    substitution_reason = StringField(
        "Raison de la substitution",
        validators=[Optional(), Length(max=200)],
        render_kw={"placeholder": "Raison du changement de médicament"}
    )
    
    # Prix
    unit_price_cents = IntegerField(
        "Prix unitaire (centimes)",
        validators=[DataRequired(), NumberRange(min=0, max=100000)],
        render_kw={"placeholder": "Prix en centimes"}
    )
    
    # Notes
    pharmacist_notes = TextAreaField(
        "Notes du pharmacien",
        validators=[Optional()],
        render_kw={"placeholder": "Observations sur cet article", "rows": 2}
    )

    def validate_dispensed_quantity(self, field):
        """Validation de la quantité délivrée"""
        if field.data and self.prescribed_quantity.data:
            if field.data > self.prescribed_quantity.data:
                raise ValidationError("La quantité délivrée ne peut pas dépasser la quantité prescrite")


class DrugInteractionForm(FlaskForm):
    """Formulaire pour les interactions médicamenteuses"""
    
    # Médicaments concernés (cachés car sélectionnés via interface)
    medication1_id_fk = HiddenField("ID Médicament 1", validators=[DataRequired()])
    medication2_id_fk = HiddenField("ID Médicament 2", validators=[DataRequired()])
    
    # Sévérité et type
    severity = SelectField(
        "Sévérité",
        choices=[
            (InteractionSeverity.MINOR.value, "Mineure"),
            (InteractionSeverity.MODERATE.value, "Modérée"),
            (InteractionSeverity.MAJOR.value, "Majeure"),
            (InteractionSeverity.CONTRAINDICATED.value, "Contre-indiquée")
        ],
        validators=[DataRequired()]
    )
    interaction_type = StringField(
        "Type d'interaction",
        validators=[Optional(), Length(max=100)],
        render_kw={"placeholder": "Ex: Pharmacocinétique, Pharmacodynamique"}
    )
    
    # Description
    description = TextAreaField(
        "Description de l'interaction",
        validators=[DataRequired()],
        render_kw={"placeholder": "Description détaillée de l'interaction", "rows": 4}
    )
    mechanism = TextAreaField(
        "Mécanisme",
        validators=[Optional()],
        render_kw={"placeholder": "Mécanisme biologique de l'interaction", "rows": 3}
    )
    clinical_effects = TextAreaField(
        "Effets cliniques",
        validators=[Optional()],
        render_kw={"placeholder": "Effets cliniques observés ou potentiels", "rows": 3}
    )
    
    # Gestion
    recommendations = TextAreaField(
        "Recommandations",
        validators=[Optional()],
        render_kw={"placeholder": "Recommandations de prise en charge", "rows": 3}
    )
    monitoring_required = BooleanField("Surveillance requise")
    contraindicated = BooleanField("Association contre-indiquée")
    
    # Références
    evidence_level = SelectField(
        "Niveau de preuve",
        choices=[
            ("A", "A - Preuve établie"),
            ("B", "B - Preuve probable"),
            ("C", "C - Preuve possible"),
            ("D", "D - Preuve insuffisante")
        ],
        validators=[Optional()]
    )
    references = TextAreaField(
        "Références bibliographiques",
        validators=[Optional()],
        render_kw={"placeholder": "Sources et références scientifiques", "rows": 2}
    )
    
    is_active = BooleanField("Interaction active", default=True)

    def validate_medication2_id_fk(self, field):
        """Validation pour éviter l'auto-interaction"""
        if field.data == self.medication1_id_fk.data:
            raise ValidationError("Un médicament ne peut pas interagir avec lui-même")


class ComplianceCheckForm(FlaskForm):
    """Formulaire pour les contrôles de conformité"""
    
    # Références (optionnelles)
    prescription_id_fk = HiddenField("ID Ordonnance")
    medication_id_fk = HiddenField("ID Médicament")
    
    # Type de contrôle
    check_type = SelectField(
        "Type de contrôle",
        choices=[
            ("expiry", "Contrôle d'expiration"),
            ("interaction", "Contrôle d'interaction"),
            ("dosage", "Contrôle de posologie"),
            ("allergy", "Contrôle d'allergie"),
            ("contraindication", "Contrôle de contre-indication"),
            ("reimbursement", "Contrôle de remboursement"),
            ("prescription_validity", "Validité de l'ordonnance"),
            ("stock_availability", "Disponibilité en stock")
        ],
        validators=[DataRequired()]
    )
    check_category = SelectField(
        "Catégorie",
        choices=[
            ("safety", "Sécurité"),
            ("regulatory", "Réglementaire"),
            ("quality", "Qualité"),
            ("financial", "Financier")
        ],
        validators=[Optional()]
    )
    
    # Résultat
    passed = SelectField(
        "Résultat",
        choices=[
            ("True", "Conforme"),
            ("False", "Non conforme")
        ],
        validators=[DataRequired()],
        coerce=lambda x: x == "True"
    )
    severity_level = SelectField(
        "Niveau de sévérité",
        choices=[
            ("info", "Information"),
            ("warning", "Avertissement"),
            ("error", "Erreur"),
            ("critical", "Critique")
        ],
        default="info",
        validators=[DataRequired()]
    )
    
    # Description
    check_description = TextAreaField(
        "Description du contrôle",
        validators=[DataRequired()],
        render_kw={"placeholder": "Description détaillée du contrôle effectué", "rows": 3}
    )
    failure_reason = TextAreaField(
        "Raison de l'échec",
        validators=[Optional()],
        render_kw={"placeholder": "Raison de la non-conformité si applicable", "rows": 2}
    )
    recommendations = TextAreaField(
        "Recommandations",
        validators=[Optional()],
        render_kw={"placeholder": "Recommandations d'action", "rows": 2}
    )
    
    # Actions
    action_required = BooleanField("Action requise")
    action_taken = TextAreaField(
        "Action entreprise",
        validators=[Optional()],
        render_kw={"placeholder": "Description de l'action entreprise", "rows": 2}
    )


class MedicationStockMovementForm(FlaskForm):
    """Formulaire pour les mouvements de stock"""
    
    # ID du médicament (caché car sélectionné via interface)
    medication_id_fk = HiddenField("ID Médicament", validators=[DataRequired()])
    
    # Mouvement
    movement_type = SelectField(
        "Type de mouvement",
        choices=[
            ("in", "Entrée en stock"),
            ("out", "Sortie de stock"),
            ("adjustment", "Ajustement d'inventaire"),
            ("expired", "Péremption"),
            ("damaged", "Détérioration"),
            ("returned", "Retour")
        ],
        validators=[DataRequired()]
    )
    quantity = IntegerField(
        "Quantité",
        validators=[DataRequired(), NumberRange(min=-10000, max=10000)],
        render_kw={"placeholder": "Quantité (positive pour entrée, négative pour sortie)"}
    )
    
    # Lot et expiration
    batch_number = StringField(
        "Numéro de lot",
        validators=[Optional(), Length(max=50)],
        render_kw={"placeholder": "Ex: LOT-2024-001"}
    )
    expiry_date = DateField(
        "Date d'expiration",
        validators=[Optional()],
        render_kw={"placeholder": "Date limite d'utilisation"}
    )
    supplier_name = StringField(
        "Fournisseur",
        validators=[Optional(), Length(max=200)],
        render_kw={"placeholder": "Nom du fournisseur"}
    )
    
    # Référence
    reference_type = SelectField(
        "Type de référence",
        choices=[
            ("", "Aucune"),
            ("prescription", "Ordonnance"),
            ("purchase", "Achat"),
            ("adjustment", "Ajustement"),
            ("transfer", "Transfert"),
            ("return", "Retour")
        ],
        validators=[Optional()]
    )
    reference_id = IntegerField(
        "ID de référence",
        validators=[Optional()],
        render_kw={"placeholder": "ID de la référence associée"}
    )
    
    # Prix
    unit_cost_cents = IntegerField(
        "Coût unitaire (centimes)",
        validators=[Optional(), NumberRange(min=0, max=100000)],
        default=0,
        render_kw={"placeholder": "Coût d'achat en centimes"}
    )
    
    # Notes
    notes = TextAreaField(
        "Notes",
        validators=[Optional()],
        render_kw={"placeholder": "Commentaires sur le mouvement", "rows": 3}
    )

    def validate_expiry_date(self, field):
        """Validation de la date d'expiration"""
        if field.data and field.data <= date.today():
            raise ValidationError("La date d'expiration doit être dans le futur")


class PatientAllergyForm(FlaskForm):
    """Formulaire pour les allergies des patients"""
    
    # ID du client (caché car sélectionné via interface)
    customer_id_fk = HiddenField("ID Client", validators=[DataRequired()])
    
    # Allergie
    allergen_name = StringField(
        "Nom de l'allergène",
        validators=[DataRequired(), Length(min=2, max=200)],
        render_kw={"placeholder": "Ex: Pénicilline, Lactose, Arachide"}
    )
    allergen_type = SelectField(
        "Type d'allergène",
        choices=[
            ("medication", "Médicament"),
            ("food", "Alimentaire"),
            ("environmental", "Environnemental"),
            ("other", "Autre")
        ],
        validators=[Optional()]
    )
    
    # Sévérité
    severity = SelectField(
        "Sévérité",
        choices=[
            ("mild", "Légère"),
            ("moderate", "Modérée"),
            ("severe", "Sévère")
        ],
        default="moderate",
        validators=[DataRequired()]
    )
    reactions = TextAreaField(
        "Réactions observées",
        validators=[Optional()],
        render_kw={"placeholder": "Description des réactions allergiques", "rows": 3}
    )
    
    # Informations
    diagnosed_date = DateField(
        "Date de diagnostic",
        validators=[Optional()],
        render_kw={"placeholder": "Date de diagnostic de l'allergie"}
    )
    notes = TextAreaField(
        "Notes",
        validators=[Optional()],
        render_kw={"placeholder": "Informations complémentaires", "rows": 2}
    )
    
    is_active = BooleanField("Allergie active", default=True)

    def validate_diagnosed_date(self, field):
        """Validation de la date de diagnostic"""
        if field.data and field.data > date.today():
            raise ValidationError("La date de diagnostic ne peut pas être dans le futur")


class PharmacySearchForm(FlaskForm):
    """Formulaire de recherche pour la pharmacie"""
    
    # Recherche générale
    search_query = StringField(
        "Rechercher",
        validators=[Optional(), Length(max=200)],
        render_kw={"placeholder": "Nom, code CIP, DCI..."}
    )
    
    # Filtres médicaments
    medication_class = SelectField(
        "Classification",
        choices=[
            ("", "Toutes"),
            (MedicationClass.OTC.value, "En vente libre"),
            (MedicationClass.PRESCRIPTION.value, "Sur ordonnance"),
            (MedicationClass.CONTROLLED.value, "Stupéfiants/Psychotropes"),
            (MedicationClass.HOSPITAL_ONLY.value, "Usage hospitalier")
        ],
        validators=[Optional()]
    )
    
    # Filtres ordonnances
    prescription_status = SelectField(
        "Statut ordonnance",
        choices=[
            ("", "Tous"),
            (PrescriptionStatus.PENDING.value, "En attente"),
            (PrescriptionStatus.VALIDATED.value, "Validée"),
            (PrescriptionStatus.DISPENSED.value, "Délivrée"),
            (PrescriptionStatus.CANCELLED.value, "Annulée")
        ],
        validators=[Optional()]
    )
    
    # Filtres dates
    date_from = DateField(
        "Du",
        validators=[Optional()]
    )
    date_to = DateField(
        "Au",
        validators=[Optional()]
    )
    
    # Filtres stock
    low_stock_only = BooleanField("Stock faible uniquement")
    expired_only = BooleanField("Produits expirés uniquement")

    def validate_date_to(self, field):
        """Validation de la période de dates"""
        if field.data and self.date_from.data:
            if field.data < self.date_from.data:
                raise ValidationError("La date de fin doit être postérieure à la date de début")


class QuickDispenseForm(FlaskForm):
    """Formulaire rapide de délivrance"""
    
    # Sélection médicament
    medication_search = StringField(
        "Rechercher médicament",
        validators=[DataRequired()],
        render_kw={"placeholder": "Tapez le nom ou code CIP du médicament", "autocomplete": "off"}
    )
    medication_id = HiddenField("ID Médicament", validators=[DataRequired()])
    
    # Quantité
    quantity = IntegerField(
        "Quantité",
        validators=[DataRequired(), NumberRange(min=1, max=1000)],
        default=1,
        render_kw={"placeholder": "Quantité à délivrer"}
    )
    
    # Client (optionnel pour vente libre)
    customer_search = StringField(
        "Client (optionnel)",
        validators=[Optional()],
        render_kw={"placeholder": "Nom ou téléphone du client", "autocomplete": "off"}
    )
    customer_id = HiddenField("ID Client")
    
    # Notes
    notes = TextAreaField(
        "Notes",
        validators=[Optional()],
        render_kw={"placeholder": "Commentaires sur la vente", "rows": 2}
    )