{% extends "base.html" %}

{% block title %}Commandes à emporter - {{ super() }}{% endblock %}

{% block extra_css %}
<style>
.product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    max-height: 500px;
    overflow-y: auto;
}

.product-card {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s;
}

.product-card:hover {
    border-color: #0d6efd;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.cart-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid #dee2e6;
}

.order-summary {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
}
</style>
{% endblock %}

{% block content %}
<div class=\"container-fluid\">
    <div class=\"row\">
        <div class=\"col-12\">
            <div class=\"page-title-box\">
                <h4 class=\"page-title\">Commandes à emporter</h4>
            </div>
        </div>
    </div>

    <div class=\"row\">
        <!-- Produits et recettes -->
        <div class=\"col-lg-8\">
            <div class=\"card\">
                <div class=\"card-header\">
                    <h5 class=\"card-title mb-0\">Produits & Recettes</h5>
                </div>
                <div class=\"card-body\">
                    <div class=\"product-grid\">
                        {% for product in products %}
                        <div class=\"product-card\" onclick=\"addToCart('product', {{ product.id }}, '{{ product.name }}', {{ product.price_cents }})\">
                            <h6>{{ product.name }}</h6>
                            <p class=\"text-muted\">{{ \"%.2f\" | format(product.price_cents / 100) }} €</p>
                            {% if product.description %}
                            <small class=\"text-muted\">{{ product.description[:50] }}...</small>
                            {% endif %}
                        </div>
                        {% endfor %}
                        {% for recipe in recipes %}
                        <div class=\"product-card\" onclick=\"addToCart('recipe', {{ recipe.id }}, '{{ recipe.name }}', {{ recipe.product.price_cents }})\">
                            <h6>{{ recipe.name }}</h6>
                            <p class=\"text-muted\">{{ \"%.2f\" | format(recipe.product.price_cents / 100) }} €</p>
                            <small class=\"badge bg-info\">Recette</small>
                            {% if recipe.description %}
                            <br><small class=\"text-muted\">{{ recipe.description[:50] }}...</small>
                            {% endif %}
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Panier et commande -->
        <div class=\"col-lg-4\">
            <div class=\"card\">
                <div class=\"card-header\">
                    <h5 class=\"card-title mb-0\">Commande à emporter</h5>
                </div>
                <div class=\"card-body\">
                    <!-- Informations client -->
                    <div class=\"mb-4\">
                        <h6>Informations client</h6>
                        <div class=\"mb-2\">
                            <label class=\"form-label\">Nom du client *</label>
                            <input type=\"text\" class=\"form-control\" id=\"customer-name\" required>
                        </div>
                        <div class=\"mb-2\">
                            <label class=\"form-label\">Téléphone *</label>
                            <input type=\"tel\" class=\"form-control\" id=\"customer-phone\" required>
                        </div>
                        <div class=\"mb-2\">
                            <label class=\"form-label\">Heure de retrait</label>
                            <input type=\"datetime-local\" class=\"form-control\" id=\"pickup-time\">
                        </div>
                    </div>

                    <!-- Panier -->
                    <div class=\"mb-4\">
                        <h6>Articles commandés</h6>
                        <div id=\"cart-items\">
                            <p class=\"text-muted\">Aucun article dans le panier</p>
                        </div>
                    </div>

                    <!-- Code promo -->
                    <div class=\"mb-4\">
                        <h6>Code promotion</h6>
                        <div class=\"input-group\">
                            <input type=\"text\" class=\"form-control\" id=\"promotion-code\" placeholder=\"Code promo...\">
                            <button class=\"btn btn-outline-secondary\" onclick=\"validatePromotion()\">Appliquer</button>
                        </div>
                        <div id=\"promotion-message\" class=\"mt-2\"></div>
                    </div>

                    <!-- Instructions spéciales -->
                    <div class=\"mb-4\">
                        <label class=\"form-label\">Instructions spéciales</label>
                        <textarea class=\"form-control\" id=\"special-instructions\" rows=\"3\" placeholder=\"Allergies, préférences, etc.\"></textarea>
                    </div>

                    <!-- Résumé de la commande -->
                    <div class=\"order-summary\">
                        <div class=\"d-flex justify-content-between mb-2\">
                            <span>Sous-total:</span>
                            <span id=\"subtotal\">0.00 €</span>
                        </div>
                        <div class=\"d-flex justify-content-between mb-2\" id=\"discount-row\" style=\"display: none;\">
                            <span>Remise:</span>
                            <span id=\"discount\" class=\"text-success\">-0.00 €</span>
                        </div>
                        <hr>
                        <div class=\"d-flex justify-content-between mb-3\">
                            <strong>Total:</strong>
                            <strong id=\"total\">0.00 €</strong>
                        </div>
                        <button class=\"btn btn-success w-100\" onclick=\"confirmTakeawayOrder()\" id=\"confirm-btn\" disabled>
                            Confirmer la commande
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let cart = [];
let subtotal = 0;
let discount = 0;
let validPromotionCode = null;

// Initialiser l'heure de retrait à dans 30 minutes
const now = new Date();
now.setMinutes(now.getMinutes() + 30);
document.getElementById('pickup-time').value = now.toISOString().slice(0, 16);

function addToCart(type, id, name, price) {
    const existingItem = cart.find(item => item.type === type && item.id === id);
    
    if (existingItem) {
        existingItem.qty += 1;
    } else {
        cart.push({
            type: type,
            id: id,
            name: name,
            price: price,
            qty: 1
        });
    }
    
    updateCartDisplay();
}

function removeFromCart(index) {
    cart.splice(index, 1);
    updateCartDisplay();
    
    // Recalculer la promotion si applicable
    if (validPromotionCode) {
        validatePromotion();
    }
}

function updateItemQty(index, newQty) {
    if (newQty <= 0) {
        removeFromCart(index);
    } else {
        cart[index].qty = newQty;
        updateCartDisplay();
        
        // Recalculer la promotion si applicable
        if (validPromotionCode) {
            validatePromotion();
        }
    }
}

function updateCartDisplay() {
    const cartItemsDiv = document.getElementById('cart-items');
    
    if (cart.length === 0) {
        cartItemsDiv.innerHTML = '<p class=\"text-muted\">Aucun article dans le panier</p>';
        subtotal = 0;
    } else {
        cartItemsDiv.innerHTML = '';
        subtotal = 0;
        
        cart.forEach((item, index) => {
            const itemTotal = item.price * item.qty;
            subtotal += itemTotal;
            
            cartItemsDiv.innerHTML += `
                <div class=\"cart-item\">
                    <div>
                        <strong>${item.name}</strong><br>
                        <small>${item.qty} × ${(item.price / 100).toFixed(2)} €</small>
                    </div>
                    <div>
                        <button class=\"btn btn-sm btn-outline-secondary\" onclick=\"updateItemQty(${index}, ${item.qty - 1})\">-</button>
                        <span class=\"mx-2\">${item.qty}</span>
                        <button class=\"btn btn-sm btn-outline-secondary\" onclick=\"updateItemQty(${index}, ${item.qty + 1})\">+</button>
                        <button class=\"btn btn-sm btn-danger ms-2\" onclick=\"removeFromCart(${index})\">×</button>
                    </div>
                </div>
            `;
        });
    }
    
    updateOrderSummary();
}

function updateOrderSummary() {
    document.getElementById('subtotal').textContent = (subtotal / 100).toFixed(2) + ' €';
    document.getElementById('discount').textContent = '-' + (discount / 100).toFixed(2) + ' €';
    document.getElementById('total').textContent = ((subtotal - discount) / 100).toFixed(2) + ' €';
    
    // Afficher/masquer la ligne de remise
    const discountRow = document.getElementById('discount-row');
    discountRow.style.display = discount > 0 ? 'flex' : 'none';
    
    // Activer/désactiver le bouton de confirmation
    const confirmBtn = document.getElementById('confirm-btn');
    const customerName = document.getElementById('customer-name').value;
    const customerPhone = document.getElementById('customer-phone').value;
    
    confirmBtn.disabled = !(cart.length > 0 && customerName && customerPhone);
}

function validatePromotion() {
    const code = document.getElementById('promotion-code').value;
    const messageDiv = document.getElementById('promotion-message');
    
    if (!code) {
        validPromotionCode = null;
        discount = 0;
        updateOrderSummary();
        messageDiv.innerHTML = '';
        return;
    }
    
    fetch('/pos/api/validate-promotion', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ code: code })
    })
    .then(response => response.json())
    .then(data => {
        if (data.valid) {
            validPromotionCode = data.promotion;
            
            // Calculer la remise
            if (subtotal >= data.promotion.min_order_amount_cents) {
                if (data.promotion.discount_type === 'percentage') {
                    discount = Math.floor(subtotal * data.promotion.discount_value / 100);
                    if (data.promotion.max_discount_cents) {
                        discount = Math.min(discount, data.promotion.max_discount_cents);
                    }
                } else {
                    discount = data.promotion.discount_value;
                }
                
                messageDiv.innerHTML = `<small class=\"text-success\">Code appliqué: ${data.promotion.name}</small>`;
            } else {
                discount = 0;
                const minAmount = (data.promotion.min_order_amount_cents / 100).toFixed(2);
                messageDiv.innerHTML = `<small class=\"text-warning\">Commande minimum: ${minAmount} €</small>`;
            }
        } else {
            validPromotionCode = null;
            discount = 0;
            messageDiv.innerHTML = `<small class=\"text-danger\">${data.message}</small>`;
        }
        
        updateOrderSummary();
    })
    .catch(error => {
        console.error('Erreur lors de la validation:', error);
        messageDiv.innerHTML = '<small class=\"text-danger\">Erreur de validation</small>';
    });
}

function confirmTakeawayOrder() {
    const customerName = document.getElementById('customer-name').value;
    const customerPhone = document.getElementById('customer-phone').value;
    const pickupTime = document.getElementById('pickup-time').value;
    const specialInstructions = document.getElementById('special-instructions').value;
    const promotionCode = validPromotionCode ? document.getElementById('promotion-code').value : null;
    
    if (!customerName || !customerPhone) {
        alert('Veuillez remplir le nom et le téléphone du client');
        return;
    }
    
    if (cart.length === 0) {
        alert('Veuillez ajouter au moins un article');
        return;
    }
    
    const orderData = {
        customer_name: customerName,
        customer_phone: customerPhone,
        pickup_time: pickupTime,
        promotion_code: promotionCode,
        special_instructions: specialInstructions,
        items: cart.map(item => ({
            type: item.type,
            [`${item.type}_id`]: item.id,
            qty: item.qty
        }))
    };
    
    fetch('/pos/api/takeaway-order', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(orderData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.ok) {
            alert(`Commande à emporter créée avec succès!\nNuméro de commande: ${data.order_id}\nTotal: ${(data.total_cents / 100).toFixed(2)} €`);
            
            // Réinitialiser le formulaire
            cart = [];
            subtotal = 0;
            discount = 0;
            validPromotionCode = null;
            
            document.getElementById('customer-name').value = '';
            document.getElementById('customer-phone').value = '';
            document.getElementById('promotion-code').value = '';
            document.getElementById('special-instructions').value = '';
            document.getElementById('promotion-message').innerHTML = '';
            
            // Réinitialiser l'heure de retrait
            const now = new Date();
            now.setMinutes(now.getMinutes() + 30);
            document.getElementById('pickup-time').value = now.toISOString().slice(0, 16);
            
            updateCartDisplay();
        } else {
            alert('Erreur: ' + (data.error || 'Impossible de créer la commande'));
        }
    })
    .catch(error => {
        console.error('Erreur:', error);
        alert('Erreur de communication avec le serveur');
    });
}

// Écouter les changements dans les champs client pour activer/désactiver le bouton
document.getElementById('customer-name').addEventListener('input', updateOrderSummary);
document.getElementById('customer-phone').addEventListener('input', updateOrderSummary);
</script>
{% endblock %}"