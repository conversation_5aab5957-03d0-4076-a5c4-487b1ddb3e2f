from __future__ import annotations

from flask_wtf import FlaskForm
from wtforms import StringField, TextAreaField, SelectField, IntegerField, DecimalField, DateField, BooleanField, FieldList, FormField, HiddenField
from wtforms.validators import DataRequired, Length, NumberRange, Optional, Email, ValidationError
from wtforms.widgets import TextArea, NumberInput
from datetime import date, datetime

from app.extensions import db
from app.modules.suppliers.models import Supplier, SupplierProduct
from app.modules.catalog.models import Product
from .models import PurchaseOrderStatus, ReceiptStatus


class PurchaseOrderForm(FlaskForm):
    """Formulaire pour créer/modifier un bon de commande"""
    
    # Informations de base
    supplier_id = SelectField("Fournisseur", validators=[DataRequired()], coerce=int)
    reference = StringField("Référence interne", validators=[Optional(), Length(max=100)])
    supplier_reference = StringField("Référence fournisseur", validators=[Optional(), Length(max=100)])
    
    # Dates
    order_date = DateField("Date de commande", validators=[DataRequired()], default=date.today)
    expected_delivery_date = DateField("Date de livraison prévue", validators=[Optional()])
    
    # Informations de livraison
    delivery_address = TextAreaField("Adresse de livraison", validators=[Optional()], widget=TextArea())
    delivery_instructions = TextAreaField("Instructions de livraison", validators=[Optional()], widget=TextArea())
    
    # Conditions commerciales
    payment_terms = StringField("Conditions de paiement", validators=[Optional(), Length(max=100)])
    currency = SelectField("Devise", validators=[DataRequired()], 
                          choices=[('EUR', 'Euro'), ('USD', 'Dollar US'), ('GBP', 'Livre Sterling')], 
                          default='EUR')
    
    # Montants
    discount_amount = DecimalField("Remise (€)", validators=[Optional(), NumberRange(min=0)], places=2, default=0)
    tax_amount = DecimalField("Taxes (€)", validators=[Optional(), NumberRange(min=0)], places=2, default=0)
    shipping_amount = DecimalField("Frais de port (€)", validators=[Optional(), NumberRange(min=0)], places=2, default=0)
    
    # Notes
    notes = TextAreaField("Notes publiques", validators=[Optional()], widget=TextArea())
    internal_notes = TextAreaField("Notes internes", validators=[Optional()], widget=TextArea())
    
    def __init__(self, business_id, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Charger les fournisseurs
        suppliers = Supplier.query.filter_by(
            business_id_fk=business_id, 
            is_active=True
        ).order_by(Supplier.name).all()
        self.supplier_id.choices = [(0, "Sélectionner un fournisseur")] + [(s.id, s.name) for s in suppliers]


class PurchaseOrderItemForm(FlaskForm):
    """Formulaire pour un article de bon de commande"""
    
    # Référence de l'article
    product_id = SelectField("Produit (optionnel)", validators=[Optional()], coerce=int)
    supplier_product_id = SelectField("Produit fournisseur (optionnel)", validators=[Optional()], coerce=int)
    
    # Informations produit
    product_name = StringField("Nom du produit", validators=[DataRequired(), Length(min=2, max=255)])
    supplier_reference = StringField("Référence fournisseur", validators=[Optional(), Length(max=100)])
    description = TextAreaField("Description", validators=[Optional()], widget=TextArea())
    
    # Quantités et prix
    quantity = IntegerField("Quantité", validators=[DataRequired(), NumberRange(min=1)], widget=NumberInput())
    unit_of_measure = SelectField("Unité", validators=[DataRequired()],
                                 choices=[('unit', 'Unité'), ('kg', 'Kilogramme'), ('liter', 'Litre'), 
                                         ('meter', 'Mètre'), ('box', 'Carton'), ('pack', 'Pack')])
    unit_cost = DecimalField("Prix unitaire (€)", validators=[DataRequired(), NumberRange(min=0)], places=2, widget=NumberInput(step=0.01))
    
    # Dates
    expected_date = DateField("Date de livraison prévue", validators=[Optional()])
    
    # Notes
    notes = TextAreaField("Notes", validators=[Optional()], widget=TextArea())


class QuickPurchaseOrderForm(FlaskForm):
    """Formulaire rapide pour créer un bon de commande simple"""
    
    supplier_id = SelectField("Fournisseur", validators=[DataRequired()], coerce=int)
    reference = StringField("Référence", validators=[Optional(), Length(max=100)])
    expected_delivery_date = DateField("Livraison prévue", validators=[Optional()])
    
    # Article unique
    product_name = StringField("Produit", validators=[DataRequired(), Length(min=2, max=255)])
    quantity = IntegerField("Quantité", validators=[DataRequired(), NumberRange(min=1)], widget=NumberInput())
    unit_cost = DecimalField("Prix unitaire (€)", validators=[DataRequired(), NumberRange(min=0)], places=2, widget=NumberInput(step=0.01))
    
    notes = TextAreaField("Notes", validators=[Optional()], widget=TextArea())
    
    def __init__(self, business_id, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        suppliers = Supplier.query.filter_by(
            business_id_fk=business_id, 
            is_active=True
        ).order_by(Supplier.name).all()
        self.supplier_id.choices = [(0, "Sélectionner un fournisseur")] + [(s.id, s.name) for s in suppliers]


class PurchaseReceiptForm(FlaskForm):
    """Formulaire pour créer/modifier une réception"""
    
    purchase_order_id = SelectField("Bon de commande", validators=[DataRequired()], coerce=int)
    delivery_note_number = StringField("N° bon de livraison", validators=[Optional(), Length(max=100)])
    supplier_invoice_number = StringField("N° facture fournisseur", validators=[Optional(), Length(max=100)])
    
    # Dates
    receipt_date = DateField("Date de réception", validators=[DataRequired()], default=date.today)
    delivery_date = DateField("Date de livraison", validators=[Optional()])
    
    # Informations de livraison
    delivery_person = StringField("Livreur", validators=[Optional(), Length(max=255)])
    delivery_company = StringField("Société de transport", validators=[Optional(), Length(max=255)])
    delivery_notes = TextAreaField("Notes de livraison", validators=[Optional()], widget=TextArea())
    
    # Contrôle qualité
    quality_check_done = BooleanField("Contrôle qualité effectué")
    quality_issues = TextAreaField("Problèmes de qualité", validators=[Optional()], widget=TextArea())
    quality_rating = SelectField("Note qualité", validators=[Optional()], coerce=int,
                                choices=[(0, "Non évalué"), (1, "1 - Très mauvais"), (2, "2 - Mauvais"), 
                                        (3, "3 - Moyen"), (4, "4 - Bon"), (5, "5 - Excellent")])
    
    # Notes
    notes = TextAreaField("Notes publiques", validators=[Optional()], widget=TextArea())
    internal_notes = TextAreaField("Notes internes", validators=[Optional()], widget=TextArea())
    
    def __init__(self, business_id, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Charger les bons de commande en attente de réception
        from .models import PurchaseOrder
        purchase_orders = PurchaseOrder.query.filter_by(
            business_id_fk=business_id
        ).filter(
            PurchaseOrder.status.in_([PurchaseOrderStatus.CONFIRMED, PurchaseOrderStatus.SENT, PurchaseOrderStatus.PARTIAL])
        ).order_by(PurchaseOrder.order_date.desc()).all()
        
        self.purchase_order_id.choices = [(0, "Sélectionner un bon de commande")] + [
            (po.id, f"{po.order_number} - {po.supplier.name if hasattr(po, 'supplier') else 'N/A'}") 
            for po in purchase_orders
        ]


class PurchaseReceiptItemForm(FlaskForm):
    """Formulaire pour un article de réception"""
    
    order_item_id = HiddenField("Article commandé", validators=[DataRequired()])
    
    # Quantités
    quantity_received = IntegerField("Quantité reçue", validators=[DataRequired(), NumberRange(min=0)], widget=NumberInput())
    quantity_damaged = IntegerField("Quantité endommagée", validators=[Optional(), NumberRange(min=0)], widget=NumberInput(), default=0)
    quantity_accepted = IntegerField("Quantité acceptée", validators=[DataRequired(), NumberRange(min=0)], widget=NumberInput())
    
    # Prix réel si différent
    actual_unit_cost = DecimalField("Prix unitaire réel (€)", validators=[Optional(), NumberRange(min=0)], places=2, widget=NumberInput(step=0.01))
    
    # Contrôle qualité
    quality_check = BooleanField("Contrôle qualité OK")
    quality_notes = TextAreaField("Notes qualité", validators=[Optional()], widget=TextArea())
    expiry_date = DateField("Date d'expiration", validators=[Optional()])
    batch_number = StringField("N° de lot", validators=[Optional(), Length(max=100)])
    
    # Notes
    notes = TextAreaField("Notes", validators=[Optional()], widget=TextArea())


class PurchaseRequisitionForm(FlaskForm):
    """Formulaire pour créer/modifier une demande d'achat"""
    
    title = StringField("Titre de la demande", validators=[DataRequired(), Length(min=5, max=255)])
    description = TextAreaField("Description", validators=[Optional()], widget=TextArea())
    
    # Demandeur
    requested_by = StringField("Demandé par", validators=[DataRequired(), Length(min=2, max=255)])
    department = StringField("Département", validators=[Optional(), Length(max=100)])
    priority = SelectField("Priorité", validators=[DataRequired()],
                          choices=[('low', 'Faible'), ('normal', 'Normale'), ('high', 'Haute'), ('urgent', 'Urgente')])
    
    # Dates
    request_date = DateField("Date de demande", validators=[DataRequired()], default=date.today)
    needed_by_date = DateField("Nécessaire pour le", validators=[Optional()])
    
    # Budget
    budget_code = StringField("Code budget", validators=[Optional(), Length(max=50)])
    
    # Notes
    notes = TextAreaField("Notes", validators=[Optional()], widget=TextArea())


class PurchaseRequisitionItemForm(FlaskForm):
    """Formulaire pour un article de demande d'achat"""
    
    product_name = StringField("Nom du produit", validators=[DataRequired(), Length(min=2, max=255)])
    description = TextAreaField("Description", validators=[Optional()], widget=TextArea())
    specifications = TextAreaField("Spécifications", validators=[Optional()], widget=TextArea())
    
    # Quantité
    quantity = IntegerField("Quantité", validators=[DataRequired(), NumberRange(min=1)], widget=NumberInput())
    unit_of_measure = SelectField("Unité", validators=[DataRequired()],
                                 choices=[('unit', 'Unité'), ('kg', 'Kilogramme'), ('liter', 'Litre'), 
                                         ('meter', 'Mètre'), ('box', 'Carton'), ('pack', 'Pack')])
    
    # Prix estimé
    estimated_unit_cost = DecimalField("Prix unitaire estimé (€)", validators=[Optional(), NumberRange(min=0)], places=2, widget=NumberInput(step=0.01))
    
    # Fournisseur suggéré
    suggested_supplier_id = SelectField("Fournisseur suggéré", validators=[Optional()], coerce=int)
    supplier_notes = TextAreaField("Notes fournisseur", validators=[Optional()], widget=TextArea())
    
    # Justification
    justification = TextAreaField("Justification", validators=[Optional()], widget=TextArea())
    
    def __init__(self, business_id, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        suppliers = Supplier.query.filter_by(
            business_id_fk=business_id, 
            is_active=True
        ).order_by(Supplier.name).all()
        self.suggested_supplier_id.choices = [(0, "Aucun")] + [(s.id, s.name) for s in suppliers]


class PurchaseOrderSearchForm(FlaskForm):
    """Formulaire de recherche des bons de commande"""
    
    search = StringField("Recherche", validators=[Optional()], render_kw={"placeholder": "N° commande, fournisseur, référence..."})
    status = SelectField("Statut", validators=[Optional()], coerce=str)
    supplier_id = SelectField("Fournisseur", validators=[Optional()], coerce=int)
    date_from = DateField("Du", validators=[Optional()])
    date_to = DateField("Au", validators=[Optional()])
    
    def __init__(self, business_id, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Statuts disponibles
        status_choices = [('', 'Tous les statuts')] + [(status.value, status.value.title()) for status in PurchaseOrderStatus]
        self.status.choices = status_choices
        
        # Fournisseurs
        suppliers = Supplier.query.filter_by(
            business_id_fk=business_id
        ).order_by(Supplier.name).all()
        self.supplier_id.choices = [(0, "Tous les fournisseurs")] + [(s.id, s.name) for s in suppliers]


class PurchaseReceiptSearchForm(FlaskForm):
    """Formulaire de recherche des réceptions"""
    
    search = StringField("Recherche", validators=[Optional()], render_kw={"placeholder": "N° réception, bon de livraison..."})
    status = SelectField("Statut", validators=[Optional()], coerce=str)
    purchase_order_id = SelectField("Bon de commande", validators=[Optional()], coerce=int)
    date_from = DateField("Du", validators=[Optional()])
    date_to = DateField("Au", validators=[Optional()])
    
    def __init__(self, business_id, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Statuts disponibles
        status_choices = [('', 'Tous les statuts')] + [(status.value, status.value.title()) for status in ReceiptStatus]
        self.status.choices = status_choices
        
        # Bons de commande récents
        from .models import PurchaseOrder
        purchase_orders = PurchaseOrder.query.filter_by(
            business_id_fk=business_id
        ).order_by(PurchaseOrder.order_date.desc()).limit(50).all()
        self.purchase_order_id.choices = [(0, "Tous les bons de commande")] + [
            (po.id, f"{po.order_number}") for po in purchase_orders
        ]


class SupplierOrderForm(FlaskForm):
    """Formulaire pour commander rapidement auprès d'un fournisseur spécifique"""
    
    supplier_id = HiddenField("Fournisseur", validators=[DataRequired()])
    reference = StringField("Référence", validators=[Optional(), Length(max=100)])
    expected_delivery_date = DateField("Livraison prévue", validators=[Optional()])
    
    # Sélection de produits du fournisseur
    supplier_products = FieldList(
        FormField(PurchaseOrderItemForm),
        min_entries=1,
        max_entries=20
    )
    
    notes = TextAreaField("Notes", validators=[Optional()], widget=TextArea())


class BulkPurchaseOrderUpdateForm(FlaskForm):
    """Formulaire pour mettre à jour plusieurs bons de commande en masse"""
    
    order_ids = HiddenField("IDs des commandes", validators=[DataRequired()])
    
    # Actions possibles
    action = SelectField("Action", validators=[DataRequired()],
                        choices=[
                            ('', 'Choisir une action'),
                            ('approve', 'Approuver'),
                            ('send', 'Envoyer'),
                            ('cancel', 'Annuler'),
                            ('update_status', 'Changer le statut')
                        ])
    
    # Nouveau statut si action = update_status
    new_status = SelectField("Nouveau statut", validators=[Optional()], coerce=str)
    
    # Notes pour l'action
    action_notes = TextAreaField("Notes de l'action", validators=[Optional()], widget=TextArea())
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Statuts disponibles pour mise à jour
        status_choices = [('', 'Sélectionner un statut')] + [(status.value, status.value.title()) for status in PurchaseOrderStatus]
        self.new_status.choices = status_choices


class PurchaseAnalyticsForm(FlaskForm):
    """Formulaire pour les analyses d'achat"""
    
    period = SelectField("Période", validators=[DataRequired()],
                        choices=[
                            ('week', 'Cette semaine'),
                            ('month', 'Ce mois'),
                            ('quarter', 'Ce trimestre'),
                            ('year', 'Cette année'),
                            ('custom', 'Période personnalisée')
                        ])
    
    date_from = DateField("Du", validators=[Optional()])
    date_to = DateField("Au", validators=[Optional()])
    
    supplier_id = SelectField("Fournisseur", validators=[Optional()], coerce=int)
    category = StringField("Catégorie", validators=[Optional(), Length(max=100)])
    
    metrics = SelectField("Métriques", validators=[DataRequired()],
                         choices=[
                             ('spending', 'Dépenses par fournisseur'),
                             ('orders', 'Nombre de commandes'),
                             ('delivery', 'Performance de livraison'),
                             ('quality', 'Qualité des réceptions'),
                             ('savings', 'Économies réalisées')
                         ])
    
    def __init__(self, business_id, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        suppliers = Supplier.query.filter_by(
            business_id_fk=business_id
        ).order_by(Supplier.name).all()
        self.supplier_id.choices = [(0, "Tous les fournisseurs")] + [(s.id, s.name) for s in suppliers]


class QuickReceiptForm(FlaskForm):
    """Formulaire rapide pour réceptionner une commande"""
    
    purchase_order_id = SelectField("Bon de commande", validators=[DataRequired()], coerce=int)
    receipt_date = DateField("Date de réception", validators=[DataRequired()], default=date.today)
    delivery_note_number = StringField("N° bon de livraison", validators=[Optional(), Length(max=100)])
    
    # Réception complète ou partielle
    full_receipt = BooleanField("Réception complète", default=True)
    
    # Notes rapides
    notes = TextAreaField("Notes", validators=[Optional()], widget=TextArea())
    quality_ok = BooleanField("Qualité conforme", default=True)
    
    def __init__(self, business_id, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        from .models import PurchaseOrder
        purchase_orders = PurchaseOrder.query.filter_by(
            business_id_fk=business_id
        ).filter(
            PurchaseOrder.status.in_([PurchaseOrderStatus.CONFIRMED, PurchaseOrderStatus.SENT])
        ).order_by(PurchaseOrder.order_date.desc()).all()
        
        self.purchase_order_id.choices = [(0, "Sélectionner un bon de commande")] + [
            (po.id, f"{po.order_number} - {po.supplier.name if hasattr(po, 'supplier') else 'N/A'}") 
            for po in purchase_orders
        ]