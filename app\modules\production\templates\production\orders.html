{% extends 'base.html' %}
{% block title %}Ordres de production{% endblock %}

{% block content %}
<div class=\"max-w-7xl mx-auto p-6\">
    <!-- En-tête -->
    <div class=\"flex justify-between items-center mb-6\">
        <div>
            <h1 class=\"text-2xl font-bold text-white mb-2\">Ordres de production</h1>
            <p class=\"text-slate-400\">Planifiez et suivez vos ordres de fabrication</p>
        </div>
        
        <div class=\"flex gap-3\">
            <a href=\"{{ url_for('production.index') }}\" 
               class=\"bg-slate-700 hover:bg-slate-600 text-white px-4 py-2 rounded-md text-sm font-medium\">
                ← Retour
            </a>
            
            <a href=\"{{ url_for('production.create_production_order') }}\" 
               class=\"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium\">
                Nouvel ordre
            </a>
        </div>
    </div>
    
    <!-- Filtres de recherche -->
    <div class=\"bg-slate-800 rounded-lg p-6 mb-6\">
        <h2 class=\"text-lg font-semibold text-white mb-4\">Filtres et recherche</h2>
        
        <form method=\"GET\" class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4\">
            <div>
                {{ search_form.search.label(class=\"block text-sm font-medium text-slate-300 mb-1\") }}
                {{ search_form.search(class=\"w-full bg-slate-700 border border-slate-600 text-white rounded-md px-3 py-2 text-sm\") }}
            </div>
            
            <div>
                {{ search_form.status.label(class=\"block text-sm font-medium text-slate-300 mb-1\") }}
                {{ search_form.status(class=\"w-full bg-slate-700 border border-slate-600 text-white rounded-md px-3 py-2 text-sm\") }}
            </div>
            
            <div>
                {{ search_form.priority.label(class=\"block text-sm font-medium text-slate-300 mb-1\") }}
                {{ search_form.priority(class=\"w-full bg-slate-700 border border-slate-600 text-white rounded-md px-3 py-2 text-sm\") }}
            </div>
            
            <div>
                {{ search_form.work_center_id.label(class=\"block text-sm font-medium text-slate-300 mb-1\") }}
                {{ search_form.work_center_id(class=\"w-full bg-slate-700 border border-slate-600 text-white rounded-md px-3 py-2 text-sm\") }}
            </div>
            
            <div>
                {{ search_form.date_range.label(class=\"block text-sm font-medium text-slate-300 mb-1\") }}
                {{ search_form.date_range(class=\"w-full bg-slate-700 border border-slate-600 text-white rounded-md px-3 py-2 text-sm\") }}
            </div>
            
            <div>
                {{ search_form.sort_by.label(class=\"block text-sm font-medium text-slate-300 mb-1\") }}
                {{ search_form.sort_by(class=\"w-full bg-slate-700 border border-slate-600 text-white rounded-md px-3 py-2 text-sm\") }}
            </div>
            
            <div class=\"md:col-span-2 lg:col-span-3 xl:col-span-6 flex gap-3\">
                <button type=\"submit\" 
                        class=\"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium\">
                    Filtrer
                </button>
                
                <a href=\"{{ url_for('production.production_orders') }}\" 
                   class=\"bg-slate-600 hover:bg-slate-500 text-white px-4 py-2 rounded-md text-sm font-medium\">
                    Réinitialiser
                </a>
            </div>
        </form>
    </div>
    
    <!-- Liste des ordres -->
    <div class=\"bg-slate-800 rounded-lg overflow-hidden\">
        <div class=\"px-6 py-4 border-b border-slate-700\">
            <div class=\"flex justify-between items-center\">
                <h2 class=\"text-lg font-semibold text-white\">Ordres de production ({{ orders|length }})</h2>
                
                {% if orders %}
                <div class=\"flex gap-2\">
                    <button onclick=\"selectAll()\" 
                            class=\"text-sm text-blue-400 hover:text-blue-300\">
                        Tout sélectionner
                    </button>
                    <span class=\"text-slate-500\">|</span>
                    <button onclick=\"openBulkActions()\" 
                            class=\"text-sm text-blue-400 hover:text-blue-300\">
                        Actions en lot
                    </button>
                </div>
                {% endif %}
            </div>
        </div>
        
        {% if orders %}
        <div class=\"divide-y divide-slate-700\">
            {% for order in orders %}
            <div class=\"px-6 py-4 hover:bg-slate-750\">
                <div class=\"flex items-start justify-between\">
                    <div class=\"flex items-start gap-4\">
                        <!-- Checkbox de sélection -->
                        <input type=\"checkbox\" 
                               class=\"order-checkbox mt-1 w-4 h-4 text-blue-600 bg-slate-700 border-slate-600 rounded focus:ring-blue-500\" 
                               value=\"{{ order.id }}\">
                        
                        <div class=\"flex-1\">
                            <!-- En-tête de l'ordre -->
                            <div class=\"flex items-center gap-3 mb-2\">
                                <a href=\"{{ url_for('production.order_details', order_id=order.id) }}\" 
                                   class=\"text-lg font-medium text-white hover:text-blue-400\">
                                    #{{ order.order_number }}
                                </a>
                                
                                <!-- Statut -->
                                <span class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        {% if order.status == 'draft' %}bg-gray-100 text-gray-800
                                        {% elif order.status == 'planned' %}bg-blue-100 text-blue-800
                                        {% elif order.status == 'released' %}bg-yellow-100 text-yellow-800
                                        {% elif order.status == 'in_progress' %}bg-green-100 text-green-800
                                        {% elif order.status == 'completed' %}bg-green-100 text-green-800
                                        {% elif order.status == 'cancelled' %}bg-red-100 text-red-800
                                        {% endif %}\">
                                    {% if order.status == 'draft' %}Brouillon
                                    {% elif order.status == 'planned' %}Planifié
                                    {% elif order.status == 'released' %}Libéré
                                    {% elif order.status == 'in_progress' %}En cours
                                    {% elif order.status == 'completed' %}Terminé
                                    {% elif order.status == 'cancelled' %}Annulé
                                    {% endif %}
                                </span>
                                
                                <!-- Priorité -->
                                {% if order.priority == 'urgent' %}
                                <span class=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800\">
                                    🔥 Urgent
                                </span>
                                {% elif order.priority == 'high' %}
                                <span class=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800\">
                                    ⚡ Élevée
                                </span>
                                {% endif %}
                                
                                <!-- Indicateur de retard -->
                                {% if order.is_overdue %}
                                <span class=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800\">
                                    ⏰ En retard
                                </span>
                                {% endif %}
                            </div>
                            
                            <!-- Informations du produit -->
                            <div class=\"text-sm text-slate-300 mb-2\">
                                {% if order.bom and order.bom.product %}
                                <strong>Produit:</strong> {{ order.bom.product.name }} 
                                <span class=\"text-slate-400\">• BOM: {{ order.bom.name }} v{{ order.bom.version }}</span>
                                {% endif %}
                            </div>
                            
                            <!-- Quantités et dates -->
                            <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm text-slate-400\">
                                <div>
                                    <span class=\"text-slate-500\">Quantité:</span>
                                    <span class=\"text-white\">{{ order.quantity_produced }}/{{ order.quantity_to_produce }}</span>
                                </div>
                                
                                {% if order.planned_start_date %}
                                <div>
                                    <span class=\"text-slate-500\">Début prévu:</span>
                                    <span class=\"text-white\">{{ order.planned_start_date.strftime('%d/%m/%Y %H:%M') }}</span>
                                </div>
                                {% endif %}
                                
                                {% if order.planned_end_date %}
                                <div>
                                    <span class=\"text-slate-500\">Fin prévue:</span>
                                    <span class=\"text-white\">{{ order.planned_end_date.strftime('%d/%m/%Y %H:%M') }}</span>
                                </div>
                                {% endif %}
                                
                                <div>
                                    <span class=\"text-slate-500\">Créé le:</span>
                                    <span class=\"text-white\">{{ order.created_at.strftime('%d/%m/%Y') }}</span>
                                </div>
                            </div>
                            
                            <!-- Barre de progression -->
                            {% if order.quantity_to_produce > 0 %}
                            <div class=\"mt-3\">
                                <div class=\"flex justify-between text-xs text-slate-400 mb-1\">
                                    <span>Progression</span>
                                    <span>{{ \"%.1f\"|format(order.progress_percentage) }}%</span>
                                </div>
                                <div class=\"w-full bg-slate-700 rounded-full h-2\">
                                    <div class=\"bg-blue-500 h-2 rounded-full transition-all duration-300\" 
                                         style=\"width: {{ order.progress_percentage }}%\"></div>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Actions et coûts -->
                    <div class=\"text-right\">
                        <div class=\"mb-2\">
                            {% if order.estimated_cost_cents %}
                            <div class=\"text-sm text-slate-400\">Coût estimé</div>
                            <div class=\"text-white font-medium\">{{ \"%.2f\"|format(order.estimated_cost_cents / 100) }}€</div>
                            {% endif %}
                            
                            {% if order.actual_cost_cents %}
                            <div class=\"text-sm text-slate-400 mt-1\">Coût réel</div>
                            <div class=\"text-white font-medium\">{{ \"%.2f\"|format(order.actual_cost_cents / 100) }}€</div>
                            {% endif %}
                        </div>
                        
                        <div class=\"flex gap-2\">
                            <!-- Bouton API progression -->
                            <button onclick=\"loadProgress({{ order.id }})\" 
                                    class=\"bg-purple-600 hover:bg-purple-700 text-white px-2 py-1 rounded text-xs font-medium\">
                                📊
                            </button>
                            
                            <!-- Bouton d'édition -->
                            <a href=\"{{ url_for('production.edit_production_order', order_id=order.id) }}\" 
                               class=\"bg-yellow-600 hover:bg-yellow-700 text-white px-2 py-1 rounded text-xs font-medium\">
                                ✏️
                            </a>
                            
                            <!-- Bouton détails -->
                            <a href=\"{{ url_for('production.order_details', order_id=order.id) }}\" 
                               class=\"bg-blue-600 hover:bg-blue-700 text-white px-2 py-1 rounded text-xs font-medium\">
                                👁️
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- Zone d'affichage de la progression détaillée (masquée par défaut) -->
                <div id=\"progress-{{ order.id }}\" class=\"hidden mt-4 p-4 bg-slate-700 rounded\">
                    <div class=\"text-sm text-slate-300\">
                        <div class=\"grid grid-cols-2 md:grid-cols-4 gap-4\">
                            <div>
                                <span class=\"text-slate-400\">Statut:</span>
                                <div id=\"status-{{ order.id }}\">--</div>
                            </div>
                            <div>
                                <span class=\"text-slate-400\">Progression:</span>
                                <div id=\"progress-pct-{{ order.id }}\">--%</div>
                            </div>
                            <div>
                                <span class=\"text-slate-400\">Produit:</span>
                                <div id=\"produced-{{ order.id }}\">--</div>
                            </div>
                            <div>
                                <span class=\"text-slate-400\">En retard:</span>
                                <div id=\"overdue-{{ order.id }}\">--</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        
        {% if orders|length >= 50 %}
        <div class=\"px-6 py-4 border-t border-slate-700 text-center\">
            <p class=\"text-sm text-slate-400\">Affichage limité à 50 résultats. Utilisez les filtres pour affiner votre recherche.</p>
        </div>
        {% endif %}
        
        {% else %}
        <div class=\"px-6 py-8 text-center\">
            <div class=\"text-slate-400 mb-4\">
                <svg class=\"w-12 h-12 mx-auto\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">
                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"></path>
                </svg>
            </div>
            <h3 class=\"text-lg font-medium text-white mb-2\">Aucun ordre trouvé</h3>
            <p class=\"text-slate-400 mb-4\">Aucun ordre de production ne correspond à vos critères</p>
            <a href=\"{{ url_for('production.create_production_order') }}\" 
               class=\"inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md text-sm font-medium\">
                Créer un ordre
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Script pour la gestion des ordres -->
<script>
// Sélectionner tous les ordres
function selectAll() {
    const checkboxes = document.querySelectorAll('.order-checkbox');
    const allChecked = Array.from(checkboxes).every(cb => cb.checked);
    
    checkboxes.forEach(cb => {
        cb.checked = !allChecked;
    });
}

// Ouvrir les actions en lot
function openBulkActions() {
    const selectedIds = Array.from(document.querySelectorAll('.order-checkbox:checked'))
                            .map(cb => cb.value);
    
    if (selectedIds.length === 0) {
        alert('Veuillez sélectionner au moins un ordre');
        return;
    }
    
    const action = prompt(`Actions disponibles pour ${selectedIds.length} ordre(s):\n1. Changer le statut\n2. Changer la priorité\n3. Supprimer\n\nEntrez le numéro de l'action:`);
    
    switch(action) {
        case '1':
            const newStatus = prompt('Nouveau statut (draft/planned/released/in_progress/completed/cancelled):');
            if (newStatus) {
                bulkUpdateStatus(selectedIds, newStatus);
            }
            break;
        case '2':
            const newPriority = prompt('Nouvelle priorité (low/normal/high/urgent):');
            if (newPriority) {
                bulkUpdatePriority(selectedIds, newPriority);
            }
            break;
        case '3':
            if (confirm(`Êtes-vous sûr de vouloir supprimer ${selectedIds.length} ordre(s) ?`)) {
                bulkDelete(selectedIds);
            }
            break;
        default:
            alert('Action non reconnue');
    }
}

// Charger la progression d'un ordre via API
function loadProgress(orderId) {
    const progressDiv = document.getElementById(`progress-${orderId}`);
    
    if (progressDiv.classList.contains('hidden')) {
        fetch(`/production/api/orders/${orderId}/progress`)
            .then(response => response.json())
            .then(data => {
                document.getElementById(`status-${orderId}`).textContent = data.status;
                document.getElementById(`progress-pct-${orderId}`).textContent = `${data.progress_percentage.toFixed(1)}%`;
                document.getElementById(`produced-${orderId}`).textContent = `${data.quantity_produced}/${data.quantity_to_produce}`;
                document.getElementById(`overdue-${orderId}`).textContent = data.is_overdue ? 'Oui' : 'Non';
                
                progressDiv.classList.remove('hidden');
            })
            .catch(error => {
                console.error('Erreur:', error);
                alert('Erreur lors du chargement des données');
            });
    } else {
        progressDiv.classList.add('hidden');
    }
}

// Fonctions d'actions en lot (à implémenter)
function bulkUpdateStatus(ids, status) {
    console.log('Mise à jour statut:', ids, status);
    // TODO: Implémenter l'appel API
}

function bulkUpdatePriority(ids, priority) {
    console.log('Mise à jour priorité:', ids, priority);
    // TODO: Implémenter l'appel API
}

function bulkDelete(ids) {
    console.log('Suppression:', ids);
    // TODO: Implémenter l'appel API
}

// Auto-submit des filtres
document.addEventListener('DOMContentLoaded', function() {
    const selects = document.querySelectorAll('select');
    selects.forEach(select => {
        select.addEventListener('change', function() {
            this.form.submit();
        });
    });
});
</script>
{% endblock %}