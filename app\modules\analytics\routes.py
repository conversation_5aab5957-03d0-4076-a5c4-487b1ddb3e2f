from flask import render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from sqlalchemy import desc, asc, func, and_, or_
from app.extensions import db
from . import bp
from .forms import (
    KPIForm, DashboardForm, ReportForm, AnalyticsFilterForm, 
    KPISearchForm, DateRangeForm, ExportForm
)
from .models import KPI, KPIHistory, Dashboard, Report, AnalyticsCache
from .services import AnalyticsService, SalesAnalytics, InventoryAnalytics, FinancialAnalytics, KPIManager
from datetime import datetime, date, timedelta
import json


@bp.route("/")
@login_required
def index():
    """Tableau de bord principal d'analytics"""
    # Services d'analyse
    analytics_service = AnalyticsService(current_user.business_id)
    sales_analytics = SalesAnalytics(current_user.business_id)
    inventory_analytics = InventoryAnalytics(current_user.business_id)
    financial_analytics = FinancialAnalytics(current_user.business_id)
    
    # KPIs principaux
    key_kpis = KPI.query.filter_by(
        business_id_fk=current_user.business_id,
        is_active=True
    ).limit(8).all()
    
    # Résumé des ventes (derniers 30 jours)
    end_date = date.today()
    start_date = end_date - timedelta(days=30)
    sales_summary = sales_analytics.get_sales_summary(start_date, end_date)
    
    # Résumé de l'inventaire
    inventory_summary = inventory_analytics.get_inventory_summary()
    
    # Analyse financière du mois
    financial_summary = financial_analytics.get_revenue_analysis()
    
    # Top 5 produits
    top_products = sales_analytics.get_top_products(limit=5)
    
    # Ventes par jour (derniers 14 jours)
    sales_trend = sales_analytics.get_sales_by_period('daily', 14)
    
    return render_template(
        'analytics/index.html',
        key_kpis=key_kpis,
        sales_summary=sales_summary,
        inventory_summary=inventory_summary,
        financial_summary=financial_summary,
        top_products=top_products,
        sales_trend=sales_trend
    )


@bp.route("/kpis")
@login_required
def kpis():
    """Liste des KPIs"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    
    form = KPISearchForm(request.args)
    
    # Construction de la requête
    query = KPI.query.filter_by(business_id_fk=current_user.business_id)
    
    # Filtres
    if form.search.data:
        search_filter = or_(
            KPI.kpi_code.ilike(f'%{form.search.data}%'),
            KPI.name.ilike(f'%{form.search.data}%'),
            KPI.description.ilike(f'%{form.search.data}%')
        )
        query = query.filter(search_filter)
    
    if form.category.data and form.category.data != 'all':
        query = query.filter(KPI.category == form.category.data)
    
    if form.status.data and form.status.data != 'all':
        if form.status.data == 'active':
            query = query.filter(KPI.is_active == True)
        elif form.status.data == 'inactive':
            query = query.filter(KPI.is_active == False)
    
    if form.frequency.data and form.frequency.data != 'all':
        query = query.filter(KPI.frequency == form.frequency.data)
    
    # Tri
    query = query.order_by(KPI.category, KPI.name)
    
    # Pagination
    kpis = query.paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    # Statistiques
    total_kpis = KPI.query.filter_by(business_id_fk=current_user.business_id).count()
    active_kpis = KPI.query.filter_by(
        business_id_fk=current_user.business_id,
        is_active=True
    ).count()
    
    return render_template(
        'analytics/kpis.html',
        kpis=kpis,
        form=form,
        total_kpis=total_kpis,
        active_kpis=active_kpis
    )


@bp.route("/kpis/add", methods=['GET', 'POST'])
@login_required
def add_kpi():
    """Ajouter un nouveau KPI"""
    form = KPIForm()
    
    if form.validate_on_submit():
        # Vérifier l'unicité du code
        existing = KPI.query.filter_by(
            business_id_fk=current_user.business_id,
            kpi_code=form.kpi_code.data
        ).first()
        
        if existing:
            flash('Ce code KPI existe déjà.', 'error')
            return render_template('analytics/add_kpi.html', form=form)
        
        kpi = KPI(
            business_id_fk=current_user.business_id,
            kpi_code=form.kpi_code.data,
            name=form.name.data,
            description=form.description.data,
            category=form.category.data,
            calculation_method=form.calculation_method.data,
            source_table=form.source_table.data,
            source_column=form.source_column.data,
            custom_query=form.custom_query.data,
            target_value=form.target_value.data,
            threshold_warning=form.threshold_warning.data,
            threshold_critical=form.threshold_critical.data,
            unit=form.unit.data,
            frequency=form.frequency.data,
            is_active=form.is_active.data
        )
        
        db.session.add(kpi)
        db.session.commit()
        
        flash(f'KPI {kpi.kpi_code} créé avec succès!', 'success')
        return redirect(url_for('analytics.view_kpi', id=kpi.id))
    
    return render_template('analytics/add_kpi.html', form=form)


@bp.route("/kpis/<int:id>")
@login_required
def view_kpi(id):
    """Voir les détails d'un KPI"""
    kpi = KPI.query.filter_by(
        id=id,
        business_id_fk=current_user.business_id
    ).first_or_404()
    
    # Historique des 30 derniers jours
    analytics_service = AnalyticsService(current_user.business_id)
    trend_data = analytics_service.get_kpi_trend(id, days=30)
    
    # Historique récent
    recent_history = KPIHistory.query.filter_by(
        kpi_id_fk=id
    ).order_by(desc(KPIHistory.calculation_date)).limit(10).all()
    
    return render_template(
        'analytics/view_kpi.html',
        kpi=kpi,
        trend_data=trend_data,
        recent_history=recent_history
    )


@bp.route("/kpis/<int:id>/calculate", methods=['POST'])
@login_required
def calculate_kpi(id):
    """Calculer la valeur d'un KPI"""
    kpi = KPI.query.filter_by(
        id=id,
        business_id_fk=current_user.business_id
    ).first_or_404()
    
    try:
        analytics_service = AnalyticsService(current_user.business_id)
        value = analytics_service.calculate_kpi(kpi)
        analytics_service.update_kpi_value(id, value)
        
        flash(f'KPI {kpi.kpi_code} calculé: {value} {kpi.unit or ""}', 'success')
    except Exception as e:
        flash(f'Erreur lors du calcul du KPI: {str(e)}', 'error')
    
    return redirect(url_for('analytics.view_kpi', id=id))


@bp.route("/sales")
@login_required
def sales_analytics():
    """Analytics des ventes"""
    form = AnalyticsFilterForm(request.args)
    
    # Dates par défaut (mois en cours)
    start_date = date.today().replace(day=1)
    end_date = date.today()
    
    # Traitement des filtres
    if form.period.data and form.period.data != 'custom':
        if form.period.data == 'today':
            start_date = end_date = date.today()
        elif form.period.data == 'yesterday':
            start_date = end_date = date.today() - timedelta(days=1)
        elif form.period.data == 'this_week':
            start_date = date.today() - timedelta(days=date.today().weekday())
            end_date = date.today()
        elif form.period.data == 'last_week':
            start_date = date.today() - timedelta(days=date.today().weekday() + 7)
            end_date = start_date + timedelta(days=6)
        elif form.period.data == 'last_month':
            last_month = date.today().replace(day=1) - timedelta(days=1)
            start_date = last_month.replace(day=1)
            end_date = last_month
    elif form.date_from.data and form.date_to.data:
        start_date = form.date_from.data.date()
        end_date = form.date_to.data.date()
    
    # Services d'analyse
    sales_service = SalesAnalytics(current_user.business_id)
    
    # Données d'analyse
    sales_summary = sales_service.get_sales_summary(start_date, end_date)
    top_products = sales_service.get_top_products(limit=10, start_date=start_date, end_date=end_date)
    sales_trend = sales_service.get_sales_by_period('daily', days=(end_date - start_date).days + 1)
    
    return render_template(
        'analytics/sales.html',
        form=form,
        sales_summary=sales_summary,
        top_products=top_products,
        sales_trend=sales_trend,
        start_date=start_date,
        end_date=end_date
    )


@bp.route("/inventory")
@login_required
def inventory_analytics():
    """Analytics de l'inventaire"""
    inventory_service = InventoryAnalytics(current_user.business_id)
    
    # Résumé de l'inventaire
    inventory_summary = inventory_service.get_inventory_summary()
    
    # Produits à rotation lente
    slow_moving = inventory_service.get_slow_moving_products(limit=20)
    
    return render_template(
        'analytics/inventory.html',
        inventory_summary=inventory_summary,
        slow_moving=slow_moving
    )


@bp.route("/financial")
@login_required
def financial_analytics():
    """Analytics financières"""
    form = DateRangeForm(request.args)
    
    # Dates par défaut (mois en cours)
    start_date = date.today().replace(day=1)
    end_date = date.today()
    
    if form.start_date.data and form.end_date.data:
        start_date = form.start_date.data.date()
        end_date = form.end_date.data.date()
    
    financial_service = FinancialAnalytics(current_user.business_id)
    revenue_analysis = financial_service.get_revenue_analysis(start_date, end_date)
    
    return render_template(
        'analytics/financial.html',
        form=form,
        revenue_analysis=revenue_analysis,
        start_date=start_date,
        end_date=end_date
    )


@bp.route("/dashboards")
@login_required
def dashboards():
    """Liste des tableaux de bord"""
    user_dashboards = Dashboard.query.filter_by(
        business_id_fk=current_user.business_id,
        user_id_fk=current_user.id
    ).all()
    
    public_dashboards = Dashboard.query.filter_by(
        business_id_fk=current_user.business_id,
        is_public=True
    ).all()
    
    return render_template(
        'analytics/dashboards.html',
        user_dashboards=user_dashboards,
        public_dashboards=public_dashboards
    )


@bp.route("/reports")
@login_required
def reports():
    """Liste des rapports"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    
    reports = Report.query.filter_by(
        business_id_fk=current_user.business_id
    ).order_by(desc(Report.created_at)).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return render_template('analytics/reports.html', reports=reports)


@bp.route("/api/kpi/<int:kpi_id>/trend")
@login_required
def api_kpi_trend(kpi_id):
    """API pour récupérer la tendance d'un KPI"""
    kpi = KPI.query.filter_by(
        id=kpi_id,
        business_id_fk=current_user.business_id
    ).first_or_404()
    
    days = request.args.get('days', 30, type=int)
    analytics_service = AnalyticsService(current_user.business_id)
    trend_data = analytics_service.get_kpi_trend(kpi_id, days)
    
    return jsonify({
        'kpi_code': kpi.kpi_code,
        'kpi_name': kpi.name,
        'data': trend_data
    })


@bp.route("/api/sales/chart")
@login_required
def api_sales_chart():
    """API pour les données de graphique des ventes"""
    period = request.args.get('period', 'daily')
    days = request.args.get('days', 30, type=int)
    
    sales_service = SalesAnalytics(current_user.business_id)
    chart_data = sales_service.get_sales_by_period(period, days)
    
    return jsonify(chart_data)


@bp.route("/api/calculate_all_kpis", methods=['POST'])
@login_required
def api_calculate_all_kpis():
    """API pour calculer tous les KPIs"""
    try:
        analytics_service = AnalyticsService(current_user.business_id)
        results = analytics_service.calculate_all_kpis()
        
        success_count = sum(1 for r in results if r['success'])
        error_count = len(results) - success_count
        
        return jsonify({
            'success': True,
            'total': len(results),
            'success_count': success_count,
            'error_count': error_count,
            'results': results
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@bp.route("/setup")
@login_required
def setup():
    """Configuration initiale des analytics"""
    # Vérifier si des KPIs existent déjà
    existing_kpis = KPI.query.filter_by(business_id_fk=current_user.business_id).count()
    
    return render_template(
        'analytics/setup.html',
        existing_kpis=existing_kpis
    )


@bp.route("/setup/create_default_kpis", methods=['POST'])
@login_required
def create_default_kpis():
    """Créer les KPIs par défaut"""
    try:
        created_kpis = KPIManager.create_default_kpis(current_user.business_id)
        flash(f'{len(created_kpis)} KPIs par défaut créés avec succès!', 'success')
    except Exception as e:
        flash(f'Erreur lors de la création des KPIs: {str(e)}', 'error')
    
    return redirect(url_for('analytics.kpis'))


@bp.route("/export", methods=['GET', 'POST'])
@login_required
def export():
    """Export des données d'analytics"""
    form = ExportForm()
    
    if form.validate_on_submit():
        # Logique d'export basée sur le format choisi
        format_type = form.format_type.data
        
        # Ici on implémenterait la logique d'export
        flash(f'Export {format_type.upper()} généré avec succès!', 'success')
        return redirect(url_for('analytics.index'))
    
    return render_template('analytics/export.html', form=form)