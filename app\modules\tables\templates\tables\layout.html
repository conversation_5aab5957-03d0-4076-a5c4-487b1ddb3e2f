{% extends 'base.html' %}
{% block title %}Plan de Salle{% endblock %}
{% block content %}
<div class="max-w-7xl mx-auto">
  <div class="flex items-center justify-between mb-6">
    <div>
      <h1 class="text-3xl font-bold">Plan de Salle Interactif</h1>
      <p class="text-slate-400 mt-2">Visualisez et gérez l'organisation de vos espaces</p>
    </div>
    <div class="flex space-x-3">
      <a href="{{ url_for('tables.index') }}" class="px-4 py-2 bg-slate-700 hover:bg-slate-600 text-white rounded-lg">
        Retour à la gestion
      </a>
    </div>
  </div>

  <div class="grid grid-cols-12 gap-6">
    <!-- Sélection de salle -->
    <div class="col-span-12 lg:col-span-3">
      <div class="rounded-xl bg-slate-900 border border-slate-700 p-6">
        <h2 class="text-lg font-semibold mb-4">🏠 Salles</h2>
        
        {% if rooms %}
          <div class="space-y-2">
            {% for room in rooms %}
            <button onclick="loadRoomLayout({{ room.id }})" class="w-full text-left p-3 bg-slate-800 border border-slate-700 rounded-lg hover:bg-slate-700 transition-colors room-button" data-room-id="{{ room.id }}">
              <div class="font-medium">{{ room.name }}</div>
              <div class="text-sm text-slate-400" id="room-{{ room.id }}-info">Chargement...</div>
            </button>
            {% endfor %}
          </div>
        {% else %}
          <div class="text-center py-8">
            <div class="text-4xl mb-4">🏠</div>
            <p class="text-slate-400">Aucune salle disponible</p>
          </div>
        {% endif %}
      </div>
      
      <!-- Légende -->
      <div class="rounded-xl bg-slate-900 border border-slate-700 p-6 mt-6">
        <h3 class="text-lg font-semibold mb-4">Légende</h3>
        <div class="space-y-2 text-sm">
          <div class="flex items-center space-x-2">
            <div class="w-4 h-4 bg-green-600 rounded"></div>
            <span>Table active</span>
          </div>
          <div class="flex items-center space-x-2">
            <div class="w-4 h-4 bg-red-600 rounded"></div>
            <span>Table inactive</span>
          </div>
          <div class="flex items-center space-x-2">
            <div class="w-4 h-4 bg-yellow-600 rounded"></div>
            <span>Table occupée</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Vue du plan -->
    <div class="col-span-12 lg:col-span-9">
      <div class="rounded-xl bg-slate-900 border border-slate-700 p-6">
        <div class="flex items-center justify-between mb-4">
          <h2 class="text-lg font-semibold" id="current-room-title">Sélectionnez une salle</h2>
          <div class="flex space-x-2">
            <button onclick="zoomIn()" class="px-3 py-1 bg-blue-600 hover:bg-blue-500 text-white rounded">🔍+</button>
            <button onclick="zoomOut()" class="px-3 py-1 bg-blue-600 hover:bg-blue-500 text-white rounded">🔍-</button>
            <button onclick="resetView()" class="px-3 py-1 bg-gray-600 hover:bg-gray-500 text-white rounded">⚡</button>
          </div>
        </div>
        
        <div id="layout-container" class="border border-slate-700 rounded-lg bg-slate-800 min-h-96 p-4 overflow-auto">
          <div class="text-center py-16">
            <div class="text-6xl mb-4">🏠</div>
            <h3 class="text-xl font-semibold text-slate-300 mb-2">Plan de Salle</h3>
            <p class="text-slate-400">Sélectionnez une salle pour voir son plan.</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
let currentZoom = 1;
let selectedRoom = null;

document.addEventListener('DOMContentLoaded', function() {
    // Charger les informations pour chaque salle
    {% for room in rooms %}
    loadRoomInfo({{ room.id }});
    {% endfor %}
});

function loadRoomInfo(roomId) {
    // Simuler le chargement des informations de la salle
    setTimeout(() => {
        const infoElement = document.getElementById(`room-${roomId}-info`);
        if (infoElement) {
            // Mock data - à remplacer par un vrai appel API
            const mockData = {
                tables: Math.floor(Math.random() * 10) + 1,
                capacity: Math.floor(Math.random() * 50) + 20
            };
            infoElement.textContent = `${mockData.tables} tables • ${mockData.capacity} places`;
        }
    }, Math.random() * 1000 + 500);
}

function loadRoomLayout(roomId) {
    // Mettre à jour la salle sélectionnée
    selectedRoom = roomId;
    
    // Mettre à jour les boutons
    document.querySelectorAll('.room-button').forEach(btn => {
        if (btn.dataset.roomId == roomId) {
            btn.classList.add('ring-2', 'ring-cyan-500');
        } else {
            btn.classList.remove('ring-2', 'ring-cyan-500');
        }
    });
    
    // Mettre à jour le titre
    const roomButton = document.querySelector(`[data-room-id="${roomId}"]`);
    const roomName = roomButton.querySelector('.font-medium').textContent;
    document.getElementById('current-room-title').textContent = `Plan de ${roomName}`;
    
    // Charger le plan de la salle
    const container = document.getElementById('layout-container');
    container.innerHTML = '<div class="text-center py-8"><div class="text-4xl mb-4">⏳</div><p class="text-slate-400">Chargement du plan...</p></div>';
    
    setTimeout(() => {
        generateRoomLayout(roomId, roomName);
    }, 1000);
}

function generateRoomLayout(roomId, roomName) {
    const container = document.getElementById('layout-container');
    
    // Générer un plan de salle simulé
    const mockTables = [
        { id: 1, code: 'T01', x: 50, y: 50, seats: 4, status: 'active' },
        { id: 2, code: 'T02', x: 200, y: 50, seats: 2, status: 'active' },
        { id: 3, code: 'T03', x: 350, y: 50, seats: 6, status: 'occupied' },
        { id: 4, code: 'T04', x: 50, y: 200, seats: 4, status: 'inactive' },
        { id: 5, code: 'T05', x: 200, y: 200, seats: 8, status: 'active' },
        { id: 6, code: 'T06', x: 350, y: 200, seats: 2, status: 'active' }
    ];
    
    let layoutHTML = `
        <div class="relative w-full h-96" style="transform: scale(${currentZoom});">
            <svg width="500" height="400" class="absolute inset-0">
                <!-- Grille de fond -->
                <defs>
                    <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
                        <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#374151" stroke-width="0.5"/>
                    </pattern>
                </defs>
                <rect width="100%" height="100%" fill="url(#grid)" />
            </svg>
    `;
    
    mockTables.forEach(table => {
        const statusColors = {
            'active': 'bg-green-600 border-green-500',
            'inactive': 'bg-red-600 border-red-500',
            'occupied': 'bg-yellow-600 border-yellow-500'
        };
        
        layoutHTML += `
            <div class="absolute transform -translate-x-1/2 -translate-y-1/2 ${statusColors[table.status]} border-2 rounded-lg p-2 text-white text-sm font-medium cursor-pointer hover:scale-110 transition-transform"
                 style="left: ${table.x}px; top: ${table.y}px;"
                 onclick="showTableDetails(${table.id}, '${table.code}')"
                 title="Table ${table.code} - ${table.seats} places">
                ${table.code}
                <div class="text-xs">${table.seats}👥</div>
            </div>
        `;
    });
    
    layoutHTML += '</div>';
    container.innerHTML = layoutHTML;
}

function showTableDetails(tableId, tableCode) {
    alert(`Détails de la table ${tableCode}\nFonctionnalité à implémenter`);
}

function zoomIn() {
    currentZoom = Math.min(currentZoom + 0.2, 2);
    if (selectedRoom) {
        loadRoomLayout(selectedRoom);
    }
}

function zoomOut() {
    currentZoom = Math.max(currentZoom - 0.2, 0.5);
    if (selectedRoom) {
        loadRoomLayout(selectedRoom);
    }
}

function resetView() {
    currentZoom = 1;
    if (selectedRoom) {
        loadRoomLayout(selectedRoom);
    }
}
</script>
{% endblock %}