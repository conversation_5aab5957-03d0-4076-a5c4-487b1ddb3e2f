{% extends "base.html" %}

{% block title %}Ordonnances - Pharmacie{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Gestion des Ordonnances</h1>
                <a href="{{ url_for('pharmacy.new_prescription') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Nouvelle Ordonnance
                </a>
            </div>

            <!-- Filtres de recherche -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Recherche et Filtres</h5>
                </div>
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            {{ search_form.search_query.label(class="form-label") }}
                            {{ search_form.search_query(class="form-control") }}
                        </div>
                        <div class="col-md-2">
                            {{ search_form.prescription_status.label(class="form-label") }}
                            {{ search_form.prescription_status(class="form-select") }}
                        </div>
                        <div class="col-md-2">
                            {{ search_form.date_from.label(class="form-label") }}
                            {{ search_form.date_from(class="form-control") }}
                        </div>
                        <div class="col-md-2">
                            {{ search_form.date_to.label(class="form-label") }}
                            {{ search_form.date_to(class="form-control") }}
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-outline-primary me-2">
                                <i class="fas fa-search"></i> Rechercher
                            </button>
                            <a href="{{ url_for('pharmacy.prescriptions') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i> Reset
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Liste des ordonnances -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Liste des Ordonnances</h5>
                </div>
                <div class="card-body">
                    {% if prescriptions.items %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Numéro</th>
                                        <th>Patient</th>
                                        <th>Médecin</th>
                                        <th>Date</th>
                                        <th>Statut</th>
                                        <th>Montant</th>
                                        <th>Articles</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for prescription in prescriptions.items %}
                                    <tr>
                                        <td>
                                            <strong>{{ prescription.prescription_number }}</strong>
                                        </td>
                                        <td>
                                            <div class="d-flex flex-column">
                                                <span class="fw-bold">{{ prescription.customer.name }}</span>
                                                {% if prescription.customer.phone %}
                                                    <small class="text-muted">{{ prescription.customer.phone }}</small>
                                                {% endif %}
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex flex-column">
                                                <span>{{ prescription.doctor_name }}</span>
                                                {% if prescription.doctor_specialty %}
                                                    <small class="text-muted">{{ prescription.doctor_specialty }}</small>
                                                {% endif %}
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex flex-column">
                                                <span>{{ prescription.prescription_date.strftime('%d/%m/%Y') }}</span>
                                                <small class="text-muted">Expire: {{ prescription.validity_date.strftime('%d/%m/%Y') }}</small>
                                            </div>
                                        </td>
                                        <td>
                                            {% set status_colors = {
                                                'pending': 'warning',
                                                'validated': 'info',
                                                'dispensed': 'success',
                                                'partially_dispensed': 'primary',
                                                'cancelled': 'danger',
                                                'expired': 'secondary'
                                            } %}
                                            {% set status_labels = {
                                                'pending': 'En attente',
                                                'validated': 'Validée',
                                                'dispensed': 'Délivrée',
                                                'partially_dispensed': 'Partiellement délivrée',
                                                'cancelled': 'Annulée',
                                                'expired': 'Expirée'
                                            } %}
                                            <span class="badge bg-{{ status_colors.get(prescription.status.value, 'secondary') }}">
                                                {{ status_labels.get(prescription.status.value, prescription.status.value) }}
                                            </span>
                                            {% if prescription.is_expired %}
                                                <span class="badge bg-danger ms-1">Expirée</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="d-flex flex-column">
                                                <span class="fw-bold">{{ "%.2f"|format(prescription.total_amount_cents / 100) }}€</span>
                                                {% if prescription.reimbursed_amount_cents > 0 %}
                                                    <small class="text-success">
                                                        Remb: {{ "%.2f"|format(prescription.reimbursed_amount_cents / 100) }}€
                                                    </small>
                                                {% endif %}
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">{{ prescription.total_items }} articles</span>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <a href="{{ url_for('pharmacy.prescription_detail', prescription_id=prescription.id) }}" 
                                                   class="btn btn-outline-primary" title="Voir détails">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                {% if prescription.status.value in ['pending', 'validated'] %}
                                                    <a href="{{ url_for('pharmacy.edit_prescription', prescription_id=prescription.id) }}" 
                                                       class="btn btn-outline-warning" title="Modifier">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                {% endif %}
                                                {% if prescription.status.value in ['validated', 'partially_dispensed'] %}
                                                    <a href="{{ url_for('pharmacy.dispense_prescription', prescription_id=prescription.id) }}" 
                                                       class="btn btn-outline-success" title="Délivrer">
                                                        <i class="fas fa-pills"></i>
                                                    </a>
                                                {% endif %}
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        {% if prescriptions.pages > 1 %}
                            <nav aria-label="Navigation des ordonnances">
                                <ul class="pagination justify-content-center">
                                    {% if prescriptions.has_prev %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('pharmacy.prescriptions', page=prescriptions.prev_num, **request.args) }}">
                                                Précédent
                                            </a>
                                        </li>
                                    {% endif %}
                                    
                                    {% for page_num in prescriptions.iter_pages() %}
                                        {% if page_num %}
                                            {% if page_num != prescriptions.page %}
                                                <li class="page-item">
                                                    <a class="page-link" href="{{ url_for('pharmacy.prescriptions', page=page_num, **request.args) }}">
                                                        {{ page_num }}
                                                    </a>
                                                </li>
                                            {% else %}
                                                <li class="page-item active">
                                                    <span class="page-link">{{ page_num }}</span>
                                                </li>
                                            {% endif %}
                                        {% else %}
                                            <li class="page-item disabled">
                                                <span class="page-link">…</span>
                                            </li>
                                        {% endif %}
                                    {% endfor %}
                                    
                                    {% if prescriptions.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('pharmacy.prescriptions', page=prescriptions.next_num, **request.args) }}">
                                                Suivant
                                            </a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-prescription fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Aucune ordonnance trouvée</h5>
                            <p class="text-muted">Créez votre première ordonnance ou modifiez vos filtres de recherche.</p>
                            <a href="{{ url_for('pharmacy.new_prescription') }}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Nouvelle Ordonnance
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Autocomplétion pour la recherche
    const searchInput = document.querySelector('input[name="search_query"]');
    if (searchInput) {
        // Implémenter l'autocomplétion si nécessaire
    }
});
</script>
{% endblock %}