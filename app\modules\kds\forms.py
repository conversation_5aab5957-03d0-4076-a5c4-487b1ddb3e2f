from __future__ import annotations

from flask_wtf import FlaskForm
from wtforms import StringField, SelectField, HiddenField
from wtforms.validators import DataRequired, Length


class KdsScreenForm(FlaskForm):
    """Formulaire pour créer/éditer un écran KDS"""
    name = StringField(
        "Nom de l'écran",
        validators=[DataRequired(), Length(min=1, max=120)],
        render_kw={"placeholder": "Ex: Cuisine principale, Grill, Desserts..."}
    )


class KdsTicketForm(FlaskForm):
    """Formulaire pour gérer les tickets KDS"""
    order_id = HiddenField("Order ID", validators=[DataRequired()])
    status = SelectField(
        "Statut",
        choices=[
            ("pending", "En attente"),
            ("preparing", "En préparation"),
            ("ready", "Prêt"),
            ("completed", "Terminé")
        ],
        validators=[DataRequired()]
    )


class KdsFilterForm(FlaskForm):
    """Formulaire pour filtrer les tickets KDS"""
    status_filter = SelectField(
        "Filtrer par statut",
        choices=[
            ("", "Tous les statuts"),
            ("pending", "En attente"),
            ("preparing", "En préparation"),
            ("ready", "Prêt"),
            ("completed", "Terminé")
        ],
        default=""
    )
    
    screen_filter = SelectField(
        "Filtrer par écran",
        choices=[],  # Sera peuplé dynamiquement
        default=""
    )