from __future__ import annotations

from flask_wtf import FlaskForm
from wtforms import StringField, TextAreaField, SelectField, IntegerField, DateField, BooleanField, SelectMultipleField
from wtforms.validators import DataRequired, Length, NumberRange, Optional
from wtforms.widgets import TextArea, NumberInput

from .models import ReportType, ReportFrequency, ReportFormat


class ReportTemplateForm(FlaskForm):
    """Formulaire pour créer/modifier un template de rapport"""
    
    name = StringField("Nom du rapport", validators=[DataRequired(), Length(min=3, max=255)])
    description = TextAreaField("Description", validators=[Optional()], widget=TextArea())
    report_type = SelectField("Type de rapport", validators=[DataRequired()], coerce=str)
    data_sources = SelectMultipleField("Sources de données", validators=[DataRequired()], coerce=str)
    columns = SelectMultipleField("Colonnes à afficher", validators=[DataRequired()], coerce=str)
    
    # Graphiques
    enable_charts = BooleanField("Activer les graphiques")
    chart_type = SelectField("Type de graphique", validators=[Optional()],
                           choices=[('', 'Aucun'), ('line', 'Courbe'), ('bar', 'Barres'), ('pie', 'Camembert')])
    
    # Permissions
    is_public = BooleanField("Rapport public")
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.report_type.choices = [(rt.value, rt.value.title()) for rt in ReportType]
        self.data_sources.choices = [('sales', 'Ventes'), ('orders', 'Commandes'), ('customers', 'Clients')]
        self.columns.choices = [('date', 'Date'), ('name', 'Nom'), ('amount', 'Montant')]


class ReportScheduleForm(FlaskForm):
    """Formulaire pour planifier un rapport"""
    
    name = StringField("Nom de la planification", validators=[DataRequired(), Length(min=3, max=255)])
    template_id = SelectField("Template de rapport", validators=[DataRequired()], coerce=int)
    frequency = SelectField("Fréquence", validators=[DataRequired()], coerce=str)
    time_of_day = StringField("Heure (HH:MM)", validators=[Optional()])
    
    # Email
    send_email = BooleanField("Envoyer par email")
    email_recipients = TextAreaField("Destinataires email", validators=[Optional()], widget=TextArea())
    
    # Export
    output_formats = SelectMultipleField("Formats d'export", validators=[DataRequired()], coerce=str)
    is_active = BooleanField("Planification active", default=True)
    
    def __init__(self, business_id, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        from .models import ReportTemplate
        templates = ReportTemplate.query.filter_by(business_id_fk=business_id, is_active=True).all()
        self.template_id.choices = [(0, "Sélectionner")] + [(t.id, t.name) for t in templates]
        
        self.frequency.choices = [(freq.value, freq.value.title()) for freq in ReportFrequency]
        self.output_formats.choices = [(fmt.value, fmt.value.upper()) for fmt in ReportFormat]


class ReportFilterForm(FlaskForm):
    """Formulaire pour filtrer les rapports"""
    
    date_range = SelectField("Période", validators=[Optional()],
                           choices=[('', 'Toutes'), ('today', "Aujourd'hui"), ('this_week', 'Cette semaine'), 
                                   ('this_month', 'Ce mois'), ('custom', 'Personnalisée')])
    date_start = DateField("Date de début", validators=[Optional()])
    date_end = DateField("Date de fin", validators=[Optional()])
    
    category_filter = SelectField("Catégorie", validators=[Optional()], coerce=str)
    status_filter = SelectField("Statut", validators=[Optional()], coerce=str)
    
    def __init__(self, business_id, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        from app.modules.catalog.models import Category
        categories = Category.query.filter_by(business_id_fk=business_id).all()
        self.category_filter.choices = [(0, 'Toutes')] + [(str(c.id), c.name) for c in categories]
        
        self.status_filter.choices = [('', 'Tous'), ('active', 'Actif'), ('completed', 'Terminé')]


class QuickReportForm(FlaskForm):
    """Formulaire pour génération rapide de rapport"""
    
    report_type = SelectField("Type", validators=[DataRequired()], coerce=str,
                            choices=[('sales_summary', 'Résumé ventes'), ('top_products', 'Top produits')])
    date_range = SelectField("Période", validators=[DataRequired()],
                           choices=[('today', "Aujourd'hui"), ('this_week', 'Cette semaine')])
    output_format = SelectField("Format", validators=[DataRequired()], coerce=str)
    include_charts = BooleanField("Inclure graphiques", default=True)
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.output_format.choices = [(fmt.value, fmt.value.upper()) for fmt in ReportFormat]


class ReportExportForm(FlaskForm):
    """Formulaire pour exporter un rapport"""
    
    export_format = SelectField("Format d'export", validators=[DataRequired()], coerce=str)
    include_data = BooleanField("Inclure données", default=True)
    include_charts = BooleanField("Inclure graphiques", default=True)
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.export_format.choices = [(fmt.value, fmt.value.upper()) for fmt in ReportFormat]


class ReportSearchForm(FlaskForm):
    """Formulaire de recherche de rapports"""
    
    search = StringField("Recherche", validators=[Optional()])
    report_type = SelectField("Type", validators=[Optional()], coerce=str)
    status = SelectField("Statut", validators=[Optional()],
                       choices=[('', 'Tous'), ('active', 'Actifs'), ('inactive', 'Inactifs')])
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.report_type.choices = [('', 'Tous')] + [(rt.value, rt.value.title()) for rt in ReportType]