"""Tests de sécurité pour l'application POS"""
import unittest
import re
from tests import BaseTestCase

class SecurityTestCase(BaseTestCase):
    """Tests de sécurité pour l'application"""
    
    def test_xss_protection(self):
        """Test de protection contre XSS"""
        # Test avec un script malveillant dans les paramètres
        malicious_input = "<script>alert('XSS')</script>"
        response = self.client.get(f'/?q={malicious_input}')
        
        # Vérifier que le script n'est pas exécuté
        self.assertNotIn('<script>', str(response.data))
        
        # Vérifier que le contenu est échappé
        self.assertIn('&lt;script&gt;', str(response.data))
    
    def test_sql_injection_protection(self):
        """Test de protection contre SQL injection"""
        # Test avec une injection SQL dans les paramètres
        sql_injection = "1'; DROP TABLE users; --"
        response = self.client.post('/accounts/login', data=dict(
            username=sql_injection,
            password='test'
        ), follow_redirects=True)
        
        # Vérifier que l'application ne plante pas
        self.assertIn(response.status_code, [200, 401])
        
        # Vérifier que la base de données est intacte
        from app.modules.accounts.models import User
        user_count = User.query.count()
        self.assertGreaterEqual(user_count, 0)
    
    def test_csrf_protection(self):
        """Test de protection CSRF"""
        # Test de requête POST sans token CSRF
        response = self.client.post('/accounts/login', data=dict(
            username='test',
            password='test'
        ))
        
        # Avec CSRF désactivé dans les tests, cela devrait fonctionner
        # Dans une vraie application, cela devrait être bloqué
        self.assertIn(response.status_code, [200, 302, 400])
    
    def test_password_hashing(self):
        """Test du hachage des mots de passe"""
        from app.modules.accounts.models import User
        
        # Créer un utilisateur avec un mot de passe
        user = User(
            username='security_test',
            email='<EMAIL>',
            first_name='Security',
            last_name='Test'
        )
        user.set_password('secure_password_123!')
        
        # Vérifier que le mot de passe est haché
        self.assertNotEqual(user.password_hash, 'secure_password_123!')
        self.assertIsNotNone(user.password_hash)
        self.assertTrue(user.check_password('secure_password_123!'))
        self.assertFalse(user.check_password('wrong_password'))
        
        # Vérifier que le hachage utilise un sel
        user2 = User(
            username='security_test2',
            email='<EMAIL>',
            first_name='Security',
            last_name='Test2'
        )
        user2.set_password('secure_password_123!')
        
        # Les hashs devraient être différents même avec le même mot de passe
        self.assertNotEqual(user.password_hash, user2.password_hash)
    
    def test_session_security(self):
        """Test de sécurité des sessions"""
        # Test de fixation de session
        with self.client.session_transaction() as sess:
            sess['user_id'] = 99999  # ID d'utilisateur inexistant
        
        # Essayer d'accéder à une page protégée
        response = self.client.get('/pos/sale', follow_redirects=True)
        
        # Vérifier que l'utilisateur est redirigé vers la page de login
        # (cela dépend de l'implémentation de la protection)
        self.assertIn(response.status_code, [200, 302])
    
    def test_input_validation(self):
        """Test de validation des entrées"""
        # Test de validation d'email
        invalid_emails = [
            'invalid',
            '@invalid',
            'invalid@',
            'invalid@.com',
            'invalid@domain',
        ]
        
        for email in invalid_emails:
            response = self.client.post('/accounts/register', data=dict(
                username='testuser',
                email=email,
                password='password123',
                password2='password123'
            ), follow_redirects=True)
            
            # Vérifier que l'enregistrement échoue
            # (cela dépend de l'implémentation des validateurs)
            pass
    
    def test_file_upload_security(self):
        """Test de sécurité des uploads de fichiers"""
        # Test d'upload de fichier malveillant
        malicious_files = [
            ('.htaccess', 'text/plain'),
            ('test.php', 'application/x-php'),
            ('test.exe', 'application/octet-stream'),
        ]
        
        # Cette partie dépend de l'implémentation des uploads
        # Dans une vraie application, on vérifierait les types de fichiers
        # et les extensions autorisées

class SecurityHeadersTestCase(BaseTestCase):
    """Tests des en-têtes de sécurité"""
    
    def test_security_headers_present(self):
        """Test de présence des en-têtes de sécurité"""
        response = self.client.get('/', follow_redirects=True)
        
        # Vérifier les en-têtes de sécurité courants
        security_headers = {
            'X-Content-Type-Options': 'nosniff',
            'X-Frame-Options': 'SAMEORIGIN',
            'X-XSS-Protection': '1; mode=block'
        }
        
        for header, expected_value in security_headers.items():
            if header in response.headers:
                # Certains headers peuvent avoir des valeurs différentes
                pass

if __name__ == '__main__':
    unittest.main()