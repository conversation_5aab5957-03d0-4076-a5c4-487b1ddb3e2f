{% extends 'base.html' %}
{% block title %}
    {% if medication %}Modifier - {{ medication.name }}{% else %}Nouveau médicament{% endif %} - Pharmacie
{% endblock %}

{% block head %}
<style>
    .medication-form-page {
        background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
        min-height: 100vh;
        padding: 20px 0;
    }
    
    .form-card {
        background: rgba(255, 255, 255, 0.98);
        border-radius: 15px;
        padding: 30px;
        margin-bottom: 25px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }
    
    .form-section {
        border-left: 4px solid #667eea;
        padding-left: 20px;
        margin-bottom: 30px;
    }
    
    .form-section h4 {
        color: #667eea;
        margin-bottom: 20px;
    }
    
    .form-control {
        border-radius: 8px;
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }
    
    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    
    .btn-save {
        background: linear-gradient(45deg, #667eea, #764ba2);
        border: none;
        padding: 12px 30px;
        border-radius: 25px;
        color: white;
        font-weight: 500;
        transition: all 0.3s ease;
    }
    
    .btn-save:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        color: white;
    }
    
    .classification-info {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-top: 10px;
    }
    
    .price-input-group {
        position: relative;
    }
    
    .price-input-group::after {
        content: "€";
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: #6c757d;
        font-weight: 500;
    }
    
    .stock-indicators {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 15px;
        margin-top: 15px;
    }
    
    .stock-box {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        text-align: center;
    }
    
    .required-field::after {
        content: " *";
        color: #dc3545;
    }
</style>
{% endblock %}

{% block content %}
<div class="medication-form-page">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="form-card">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div>
                            <h1 class="mb-1">
                                {% if medication %}
                                    ✏️ Modifier {{ medication.name }}
                                {% else %}
                                    ➕ Nouveau médicament
                                {% endif %}
                            </h1>
                            <p class="text-muted mb-0">Informations pharmaceutiques complètes</p>
                        </div>
                        <div>
                            <a href="{{ url_for('pharmacy.medications') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left"></i> Retour
                            </a>
                        </div>
                    </div>

                    <form method="POST" id="medicationForm">
                        {{ form.hidden_tag() }}
                        
                        <!-- Informations de base -->
                        <div class="form-section">
                            <h4>📋 Informations de base</h4>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        {{ form.name.label(class="form-label required-field") }}
                                        {{ form.name(class="form-control") }}
                                        {% if form.name.errors %}
                                            <div class="text-danger small">{{ form.name.errors[0] }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        {{ form.generic_name.label(class="form-label") }}
                                        {{ form.generic_name(class="form-control") }}
                                        {% if form.generic_name.errors %}
                                            <div class="text-danger small">{{ form.generic_name.errors[0] }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        {{ form.brand_name.label(class="form-label") }}
                                        {{ form.brand_name(class="form-control") }}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        {{ form.therapeutic_class.label(class="form-label") }}
                                        {{ form.therapeutic_class(class="form-control") }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Codes et classification -->
                        <div class="form-section">
                            <h4>🏷️ Codes et classification</h4>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        {{ form.cip_code.label(class="form-label") }}
                                        {{ form.cip_code(class="form-control") }}
                                        {% if form.cip_code.errors %}
                                            <div class="text-danger small">{{ form.cip_code.errors[0] }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        {{ form.atc_code.label(class="form-label") }}
                                        {{ form.atc_code(class="form-control") }}
                                        {% if form.atc_code.errors %}
                                            <div class="text-danger small">{{ form.atc_code.errors[0] }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        {{ form.barcode.label(class="form-label") }}
                                        {{ form.barcode(class="form-control") }}
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        {{ form.medication_class.label(class="form-label required-field") }}
                                        {{ form.medication_class(class="form-control") }}
                                        <div class="classification-info">
                                            <small class="text-muted">
                                                <strong>Vente libre:</strong> Accessible sans ordonnance<br>
                                                <strong>Sur ordonnance:</strong> Prescription médicale requise<br>
                                                <strong>Stupéfiants:</strong> Substances contrôlées<br>
                                                <strong>Hospitalier:</strong> Usage hospitalier uniquement
                                            </small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        {{ form.marketing_authorization.label(class="form-label") }}
                                        {{ form.marketing_authorization(class="form-control") }}
                                    </div>
                                    <div class="row">
                                        <div class="col-6">
                                            <div class="form-check">
                                                {{ form.is_generic(class="form-check-input") }}
                                                {{ form.is_generic.label(class="form-check-label") }}
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="form-check">
                                                {{ form.is_biosimilar(class="form-check-input") }}
                                                {{ form.is_biosimilar.label(class="form-check-label") }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Forme et dosage -->
                        <div class="form-section">
                            <h4>💊 Forme pharmaceutique</h4>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        {{ form.pharmaceutical_form.label(class="form-label") }}
                                        {{ form.pharmaceutical_form(class="form-control") }}
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        {{ form.dosage.label(class="form-label") }}
                                        {{ form.dosage(class="form-control") }}
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        {{ form.unit_of_measure.label(class="form-label") }}
                                        {{ form.unit_of_measure(class="form-control") }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Prix et stock -->
                        <div class="form-section">
                            <h4>💰 Prix et stock</h4>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label required-field">Prix (€)</label>
                                        <div class="price-input-group">
                                            <input type="number" 
                                                   name="price_euros" 
                                                   class="form-control" 
                                                   step="0.01" 
                                                   min="0"
                                                   value="{% if medication %}{{ '%.2f'|format(medication.price_cents / 100) }}{% endif %}"
                                                   placeholder="0.00"
                                                   required>
                                        </div>
                                        <small class="text-muted">Prix de vente public</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        {{ form.reimbursement_rate.label(class="form-label") }}
                                        {{ form.reimbursement_rate(class="form-control") }}
                                        <small class="text-muted">Taux de remboursement Sécurité Sociale</small>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="stock-indicators">
                                <div class="stock-box">
                                    {{ form.current_stock.label(class="form-label") }}
                                    {{ form.current_stock(class="form-control text-center") }}
                                </div>
                                <div class="stock-box">
                                    {{ form.minimum_stock.label(class="form-label") }}
                                    {{ form.minimum_stock(class="form-control text-center") }}
                                </div>
                                <div class="stock-box">
                                    {{ form.maximum_stock.label(class="form-label") }}
                                    {{ form.maximum_stock(class="form-control text-center") }}
                                </div>
                            </div>
                        </div>

                        <!-- Actions -->
                        <div class="text-center">
                            <button type="submit" class="btn btn-save btn-lg">
                                <i class="fas fa-save"></i>
                                {% if medication %}Mettre à jour{% else %}Créer le médicament{% endif %}
                            </button>
                            {% if medication %}
                                <a href="{{ url_for('pharmacy.medication_detail', medication_id=medication.id) }}" 
                                   class="btn btn-outline-primary btn-lg ml-3">
                                    <i class="fas fa-eye"></i> Voir détail
                                </a>
                            {% endif %}
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Conversion automatique du prix en centimes
    const priceInput = document.querySelector('input[name="price_euros"]');
    const form = document.getElementById('medicationForm');
    
    form.addEventListener('submit', function(e) {
        if (priceInput) {
            const priceEuros = parseFloat(priceInput.value) || 0;
            const priceCents = Math.round(priceEuros * 100);
            
            // Créer un champ caché pour les centimes
            const hiddenField = document.createElement('input');
            hiddenField.type = 'hidden';
            hiddenField.name = 'price_cents';
            hiddenField.value = priceCents;
            form.appendChild(hiddenField);
        }
    });

    // Validation temps réel
    const requiredFields = document.querySelectorAll('input[required], select[required]');
    requiredFields.forEach(field => {
        field.addEventListener('blur', function() {
            if (!this.value.trim()) {
                this.classList.add('is-invalid');
            } else {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            }
        });
    });
});
</script>
{% endblock %}