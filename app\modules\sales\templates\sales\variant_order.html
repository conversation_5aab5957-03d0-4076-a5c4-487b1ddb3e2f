{% extends 'base.html' %}
{% block title %}Commande Variante{% endblock %}
{% block content %}
<div class="max-w-4xl mx-auto">
  <div class="flex items-center justify-between mb-6">
    <div>
      <h1 class="text-3xl font-bold">🎨 Commande avec Variantes</h1>
      <p class="text-slate-400 mt-2">Mode commerce - Gestion des variantes produits</p>
    </div>
    <div class="flex space-x-3">
      <a href="{{ url_for('sales.orders') }}" class="px-4 py-2 bg-slate-700 hover:bg-slate-600 text-white rounded-lg">
        Retour aux commandes
      </a>
    </div>
  </div>

  <div class="grid grid-cols-12 gap-6">
    <div class="col-span-12 lg:col-span-6">
      <div class="rounded-xl bg-slate-900 border border-slate-700 p-6">
        <h2 class="text-lg font-semibold mb-4">Nouvelle commande variante</h2>
        <form method="post" class="space-y-4">
          {{ form.csrf_token }}
          
          <div>
            <label class="block text-sm font-medium text-slate-300 mb-2">{{ form.product_id.label.text }}</label>
            {{ form.product_id(id="product-select", class="w-full bg-slate-800 border border-slate-700 rounded-lg px-4 py-3 text-white") }}
          </div>
          
          <div>
            <label class="block text-sm font-medium text-slate-300 mb-2">{{ form.variant_id.label.text }}</label>
            {{ form.variant_id(id="variant-select", class="w-full bg-slate-800 border border-slate-700 rounded-lg px-4 py-3 text-white") }}
          </div>
          
          <div class="grid grid-cols-2 gap-3">
            <div>
              <label class="block text-sm font-medium text-slate-300 mb-2">{{ form.qty.label.text }}</label>
              {{ form.qty(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-4 py-3 text-white") }}
            </div>
            <div>
              <label class="block text-sm font-medium text-slate-300 mb-2">{{ form.custom_price.label.text }}</label>
              {{ form.custom_price(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-4 py-3 text-white", placeholder="Prix par défaut") }}
            </div>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-slate-300 mb-2">{{ form.notes.label.text }}</label>
            {{ form.notes(rows="3", class="w-full bg-slate-800 border border-slate-700 rounded-lg px-4 py-3 text-white", placeholder="Notes optionnelles...") }}
          </div>
          
          <div class="flex justify-end space-x-3 pt-6 border-t border-slate-700">
            <a href="{{ url_for('sales.orders') }}" class="px-6 py-3 bg-slate-700 hover:bg-slate-600 text-white rounded-lg">
              Annuler
            </a>
            {{ form.submit(class="px-6 py-3 bg-purple-600 hover:bg-purple-500 text-white rounded-lg font-medium") }}
          </div>
        </form>
      </div>
    </div>

    <div class="col-span-12 lg:col-span-6">
      <div class="rounded-xl bg-slate-900 border border-slate-700 p-6">
        <h2 class="text-lg font-semibold mb-4">Détails de la variante</h2>
        
        <div id="variant-details" class="space-y-4">
          <div class="text-center py-8">
            <div class="text-6xl mb-4">🎨</div>
            <h3 class="text-xl font-semibold text-slate-300 mb-2">Sélectionnez une variante</h3>
            <p class="text-slate-400">Les détails apparaîtront ici.</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const productSelect = document.getElementById('product-select');
    const variantSelect = document.getElementById('variant-select');
    
    if (productSelect && variantSelect) {
        productSelect.addEventListener('change', function() {
            const productId = this.value;
            if (productId) {
                loadProductVariants(productId);
            } else {
                variantSelect.innerHTML = '<option value="">Sélectionnez d\'abord un produit</option>';
            }
        });
        
        variantSelect.addEventListener('change', function() {
            const variantId = this.value;
            if (variantId) {
                loadVariantDetails(variantId);
            }
        });
    }
});

function loadProductVariants(productId) {
    const variantSelect = document.getElementById('variant-select');
    variantSelect.innerHTML = '<option value="">Chargement...</option>';
    
    setTimeout(() => {
        const mockVariants = [
            { id: 1, name: 'Rouge - M', price: 25.00, stock: 10 },
            { id: 2, name: 'Bleu - L', price: 27.00, stock: 5 }
        ];
        
        variantSelect.innerHTML = '<option value="">Sélectionnez une variante</option>';
        mockVariants.forEach(variant => {
            const option = document.createElement('option');
            option.value = variant.id;
            option.textContent = `${variant.name} - ${variant.price}€ (Stock: ${variant.stock})`;
            variantSelect.appendChild(option);
        });
    }, 500);
}

function loadVariantDetails(variantId) {
    const container = document.getElementById('variant-details');
    
    setTimeout(() => {
        container.innerHTML = `
            <div class="bg-slate-800 rounded-lg p-4">
                <h3 class="text-lg font-semibold text-purple-400">T-Shirt Rouge - Taille M</h3>
                <div class="mt-3 space-y-2">
                    <p><span class="text-slate-400">Prix:</span> <span class="text-green-400 font-bold">25.00 €</span></p>
                    <p><span class="text-slate-400">Stock:</span> <span class="text-cyan-400">10 unités</span></p>
                    <p><span class="text-slate-400">SKU:</span> <span class="font-mono">TSH-RED-M</span></p>
                </div>
            </div>
        `;
    }, 300);
}
</script>
{% endblock %}