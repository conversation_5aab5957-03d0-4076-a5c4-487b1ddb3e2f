"""Tests de l'API REST pour l'application POS"""
import pytest
import json
from flask import url_for

class TestAPIAuth:
    """Tests pour l'authentification API"""
    
    def test_login_success(self, client, test_user):
        """Test de login réussi"""
        response = client.post('/api/v1/auth/login',
                             data=json.dumps({
                                 'username': 'testuser',
                                 'password': 'password123'
                             }),
                             content_type='application/json')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] is True
        assert 'access_token' in data
    
    def test_login_failure(self, client):
        """Test de login échoué"""
        response = client.post('/api/v1/auth/login',
                             data=json.dumps({
                                 'username': 'wronguser',
                                 'password': 'wrongpassword'
                             }),
                             content_type='application/json')
        
        assert response.status_code == 401
        data = json.loads(response.data)
        assert data['success'] is False
    
    def test_protected_route_without_token(self, client):
        """Test d'une route protégée sans token"""
        response = client.get('/api/v1/businesses')
        assert response.status_code == 401

class TestAPIBusiness:
    """Tests pour les endpoints business API"""
    
    def test_get_businesses(self, client, test_user):
        """Test de récupération des entreprises"""
        # D'abord se connecter pour obtenir un token
        login_response = client.post('/api/v1/auth/login',
                                   data=json.dumps({
                                       'username': 'testuser',
                                       'password': 'password123'
                                   }),
                                   content_type='application/json')
        
        assert login_response.status_code == 200
        login_data = json.loads(login_response.data)
        token = login_data['access_token']
        
        # Utiliser le token pour accéder à la route protégée
        response = client.get('/api/v1/businesses',
                            headers={'Authorization': f'Bearer {token}'})
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] is True

class TestAPIProducts:
    """Tests pour les endpoints products API"""
    
    def test_get_products(self, client, test_user, test_product):
        """Test de récupération des produits"""
        # Se connecter pour obtenir un token
        login_response = client.post('/api/v1/auth/login',
                                   data=json.dumps({
                                       'username': 'testuser',
                                       'password': 'password123'
                                   }),
                                   content_type='application/json')
        
        assert login_response.status_code == 200
        login_data = json.loads(login_response.data)
        token = login_data['access_token']
        
        # Récupérer les produits
        response = client.get('/api/v1/products',
                            headers={'Authorization': f'Bearer {token}'})
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] is True
        assert len(data['data']) >= 1

class TestAPIPerformance:
    """Tests de performance de l'API"""
    
    def test_response_time(self, client, test_user):
        """Test du temps de réponse de l'API"""
        import time
        
        # Se connecter pour obtenir un token
        login_response = client.post('/api/v1/auth/login',
                                   data=json.dumps({
                                       'username': 'testuser',
                                       'password': 'password123'
                                   }),
                                   content_type='application/json')
        
        assert login_response.status_code == 200
        login_data = json.loads(login_response.data)
        token = login_data['access_token']
        
        # Mesurer le temps de réponse
        start_time = time.time()
        response = client.get('/api/v1/businesses',
                            headers={'Authorization': f'Bearer {token}'})
        end_time = time.time()
        
        response_time = (end_time - start_time) * 1000  # Convertir en ms
        assert response_time < 500  # Le temps de réponse doit être inférieur à 500ms
        assert response.status_code == 200

if __name__ == '__main__':
    pytest.main(['-v'])