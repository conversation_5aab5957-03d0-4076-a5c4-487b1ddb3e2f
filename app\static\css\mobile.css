/* Styles CSS pour l'application mobile POS */
:root {
  --primary-color: #007bff;
  --secondary-color: #6c757d;
  --success-color: #28a745;
  --danger-color: #dc3545;
  --warning-color: #ffc107;
  --info-color: #17a2b8;
  --light-color: #f8f9fa;
  --dark-color: #343a40;
}

/* Styles de base pour mobile */
@media (max-width: 768px) {
  .container {
    padding-left: 10px;
    padding-right: 10px;
  }
  
  .card {
    margin-bottom: 15px;
  }
  
  .btn {
    padding: 12px 16px;
    font-size: 16px; /* Taille minimale pour le touch */
  }
  
  .form-control {
    font-size: 16px; /* Éviter le zoom sur iOS */
  }
  
  /* Navigation mobile */
  .navbar-nav {
    flex-direction: row;
  }
  
  .nav-item {
    margin-right: 10px;
  }
  
  /* Grille responsive */
  .row {
    margin-left: -5px;
    margin-right: -5px;
  }
  
  [class*="col-"] {
    padding-left: 5px;
    padding-right: 5px;
  }
}

/* Interface POS tactile */
.pos-interface {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.pos-header {
  flex: 0 0 auto;
  background-color: var(--primary-color);
  color: white;
  padding: 10px;
}

.pos-content {
  flex: 1 1 auto;
  overflow-y: auto;
  padding: 10px;
}

.pos-footer {
  flex: 0 0 auto;
  background-color: var(--light-color);
  padding: 10px;
  border-top: 1px solid #dee2e6;
}

/* Grille de produits */
.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 10px;
  margin-bottom: 15px;
}

.product-item {
  background-color: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 10px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.product-item:hover {
  background-color: #f8f9fa;
  transform: translateY(-2px);
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.product-item:active {
  transform: translateY(0);
  background-color: #e9ecef;
}

.product-image {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
  margin-bottom: 5px;
}

.product-name {
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 3px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.product-price {
  font-size: 14px;
  font-weight: bold;
  color: var(--primary-color);
}

/* Panier */
.cart-items {
  max-height: 200px;
  overflow-y: auto;
  margin-bottom: 15px;
}

.cart-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #eee;
}

.cart-item-name {
  flex: 1;
  font-size: 14px;
}

.cart-item-quantity {
  margin: 0 10px;
  min-width: 40px;
  text-align: center;
}

.cart-item-price {
  font-weight: 500;
}

/* Boutons d'action */
.action-buttons {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
  margin-bottom: 15px;
}

.action-button {
  padding: 15px;
  font-size: 14px;
  font-weight: 500;
  border-radius: 8px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-button:active {
  transform: scale(0.95);
}

.btn-pay {
  background-color: var(--success-color);
  color: white;
}

.btn-cancel {
  background-color: var(--danger-color);
  color: white;
}

.btn-discount {
  background-color: var(--warning-color);
  color: #212529;
}

/* Clavier numérique */
.numpad {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  margin-bottom: 15px;
}

.numpad-button {
  padding: 20px;
  font-size: 20px;
  font-weight: 500;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.1s ease;
}

.numpad-button:active {
  background-color: #e9ecef;
  transform: scale(0.95);
}

.numpad-button.function {
  background-color: var(--primary-color);
  color: white;
}

/* Commandes */
.orders-list {
  max-height: 300px;
  overflow-y: auto;
}

.order-item {
  padding: 12px;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  margin-bottom: 10px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.order-item:hover {
  background-color: #f8f9fa;
}

.order-item:active {
  background-color: #e9ecef;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.order-number {
  font-weight: 500;
  color: var(--primary-color);
}

.order-time {
  font-size: 12px;
  color: var(--secondary-color);
}

.order-total {
  font-weight: bold;
  font-size: 16px;
}

.order-status {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  background-color: var(--light-color);
}

/* Notifications */
.toast-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
}

.toast {
  background-color: white;
  border-left: 4px solid var(--primary-color);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  border-radius: 4px;
  padding: 12px 16px;
  margin-bottom: 10px;
  min-width: 250px;
  animation: slideIn 0.3s ease;
}

.toast.success {
  border-left-color: var(--success-color);
}

.toast.error {
  border-left-color: var(--danger-color);
}

.toast.warning {
  border-left-color: var(--warning-color);
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Optimisations pour les écrans tactiles */
* {
  -webkit-tap-highlight-color: transparent;
}

button, a, .product-item {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Accessibilité */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Modes d'affichage */
.dark-mode {
  background-color: #121212;
  color: #ffffff;
}

.dark-mode .card {
  background-color: #1e1e1e;
  border-color: #333;
}

.dark-mode .product-item {
  background-color: #2d2d2d;
  border-color: #444;
  color: #ffffff;
}

.dark-mode .numpad-button {
  background-color: #2d2d2d;
  border-color: #444;
  color: #ffffff;
}

/* Animation de chargement */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(0,0,0,0.1);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Responsive pour petits écrans */
@media (max-width: 480px) {
  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  }
  
  .product-image {
    width: 50px;
    height: 50px;
  }
  
  .product-name {
    font-size: 10px;
  }
  
  .product-price {
    font-size: 12px;
  }
  
  .numpad-button {
    padding: 15px;
    font-size: 18px;
  }
}