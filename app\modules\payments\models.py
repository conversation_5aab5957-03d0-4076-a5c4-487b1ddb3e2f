from __future__ import annotations

from datetime import datetime, date
from decimal import Decimal
from typing import Optional, List
from enum import Enum as PyEnum
from sqlalchemy import Column, Integer, String, Text, DateTime, Date, Numeric, ForeignKey, <PERSON><PERSON>an, Enum
from sqlalchemy.orm import relationship

from app.extensions import db


class PaymentStatus(PyEnum):
    """Statut des paiements"""
    PENDING = "pending"  # En attente
    PROCESSING = "processing"  # En cours de traitement
    COMPLETED = "completed"  # Complété
    FAILED = "failed"  # Échec
    REFUNDED = "refunded"  # Remboursé
    PARTIALLY_REFUNDED = "partially_refunded"  # Partiellement remboursé
    CANCELLED = "cancelled"  # Annulé


class PaymentMethodType(PyEnum):
    """Types de méthodes de paiement"""
    CASH = "cash"  # Espèces
    CARD = "card"  # Carte bancaire
    CREDIT_CARD = "credit_card"  # Carte de crédit
    DEBIT_CARD = "debit_card"  # Carte de débit
    BANK_TRANSFER = "bank_transfer"  # Virement bancaire
    CHECK = "check"  # Chèque
    GIFT_CARD = "gift_card"  # Carte cadeau
    MOBILE_PAYMENT = "mobile_payment"  # Paiement mobile
    CRYPTO = "crypto"  # Cryptomonnaie
    LOYALTY_POINTS = "loyalty_points"  # Points de fidélité
    OTHER = "other"  # Autre


class RefundReason(PyEnum):
    """Raisons de remboursement"""
    CUSTOMER_REQUEST = "customer_request"  # Demande client
    PRODUCT_DEFECT = "product_defect"  # Défaut produit
    SERVICE_ISSUE = "service_issue"  # Problème de service
    DUPLICATE_PAYMENT = "duplicate_payment"  # Paiement en double
    FRAUD = "fraud"  # Fraude
    TECHNICAL_ERROR = "technical_error"  # Erreur technique
    OTHER = "other"  # Autre


class PaymentMethod(db.Model):
    """Méthodes de paiement acceptées par l'entreprise"""
    __tablename__ = "payment_methods"
    
    id = Column(Integer, primary_key=True)
    business_id_fk = Column(Integer, ForeignKey("businesses.id"), nullable=False, index=True)
    
    # Informations de base
    name = Column(String(100), nullable=False)  # Nom affiché
    method_type = Column(Enum(PaymentMethodType), nullable=False, index=True)
    
    # Configuration
    is_active = Column(Boolean, default=True, index=True)
    is_default = Column(Boolean, default=False)
    requires_authorization = Column(Boolean, default=False)
    allow_partial_payments = Column(Boolean, default=True)
    
    # Frais et limites
    fee_percentage = Column(Numeric(5, 4), default=0.0)  # Frais en pourcentage
    fee_fixed_cents = Column(Integer, default=0)  # Frais fixes en centimes
    minimum_amount_cents = Column(Integer, default=0)  # Montant minimum
    maximum_amount_cents = Column(Integer, nullable=True)  # Montant maximum
    
    # Configuration spécifique
    configuration = Column(Text)  # Configuration JSON (API keys, endpoints, etc.)
    
    # Descriptions et aide
    description = Column(Text)
    customer_instructions = Column(Text)  # Instructions pour le client
    
    # Audit
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relations
    business = relationship("Business", backref="payment_methods")
    payments = relationship("Payment", back_populates="payment_method")
    
    __table_args__ = (
        db.Index("idx_payment_method_business", "business_id_fk"),
        db.Index("idx_payment_method_type", "method_type"),
        db.Index("idx_payment_method_active", "is_active"),
    )
    
    def __repr__(self):
        return f"<PaymentMethod {self.name}>"
    
    @property
    def is_cash(self) -> bool:
        """Vérifier si c'est un paiement en espèces"""
        return self.method_type == PaymentMethodType.CASH
    
    @property
    def is_card(self) -> bool:
        """Vérifier si c'est un paiement par carte"""
        return self.method_type in [PaymentMethodType.CARD, PaymentMethodType.CREDIT_CARD, PaymentMethodType.DEBIT_CARD]
    
    def calculate_fees(self, amount_cents: int) -> int:
        """Calculer les frais pour un montant donné"""
        percentage_fee = int(amount_cents * float(self.fee_percentage) / 100)
        return percentage_fee + self.fee_fixed_cents


class Payment(db.Model):
    """Paiement effectué pour une commande"""
    __tablename__ = "payments"
    
    id = Column(Integer, primary_key=True)
    business_id_fk = Column(Integer, ForeignKey("businesses.id"), nullable=False, index=True)
    order_id_fk = Column(Integer, ForeignKey("orders.id"), nullable=False, index=True)
    payment_method_id_fk = Column(Integer, ForeignKey("payment_methods.id"), nullable=False, index=True)
    
    # Informations de paiement
    amount_cents = Column(Integer, nullable=False)  # Montant payé
    fees_cents = Column(Integer, default=0)  # Frais de traitement
    net_amount_cents = Column(Integer, nullable=False)  # Montant net reçu
    
    # Statut et validation
    status = Column(Enum(PaymentStatus), default=PaymentStatus.PENDING, nullable=False, index=True)
    
    # Références externes
    external_transaction_id = Column(String(255), index=True)  # ID transaction externe
    authorization_code = Column(String(100))  # Code d'autorisation
    gateway_response = Column(Text)  # Réponse complète de la passerelle
    
    # Informations de carte (si applicable)
    card_last_four = Column(String(4))  # 4 derniers chiffres
    card_brand = Column(String(20))  # Visa, MasterCard, etc.
    card_holder_name = Column(String(200))
    
    # Informations de traitement
    processed_at = Column(DateTime)  # Date de traitement
    gateway_name = Column(String(50))  # Nom de la passerelle (Stripe, PayPal, etc.)
    
    # Notes et références
    notes = Column(Text)
    receipt_number = Column(String(50), unique=True, index=True)  # Numéro de reçu
    
    # Audit
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by_user_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    
    # Relations
    business = relationship("Business", backref="payments")
    order = relationship("Order", backref="payments")
    payment_method = relationship("PaymentMethod", back_populates="payments")
    created_by = relationship("User")
    transactions = relationship("PaymentTransaction", back_populates="payment", cascade="all, delete-orphan")
    refunds = relationship("PaymentRefund", back_populates="payment", cascade="all, delete-orphan")
    
    __table_args__ = (
        db.Index("idx_payment_order", "order_id_fk"),
        db.Index("idx_payment_status", "status"),
        db.Index("idx_payment_date", "created_at"),
        db.Index("idx_payment_receipt", "receipt_number"),
    )
    
    def __repr__(self):
        return f"<Payment {self.receipt_number} - {self.amount_cents/100}€>"
    
    @property
    def amount_euros(self) -> Decimal:
        """Montant en euros"""
        return Decimal(self.amount_cents) / 100
    
    @property
    def net_amount_euros(self) -> Decimal:
        """Montant net en euros"""
        return Decimal(self.net_amount_cents) / 100
    
    @property
    def fees_euros(self) -> Decimal:
        """Frais en euros"""
        return Decimal(self.fees_cents) / 100
    
    @property
    def is_completed(self) -> bool:
        """Vérifier si le paiement est complété"""
        return self.status == PaymentStatus.COMPLETED
    
    @property
    def is_refundable(self) -> bool:
        """Vérifier si le paiement peut être remboursé"""
        return self.status == PaymentStatus.COMPLETED
    
    @property
    def refunded_amount_cents(self) -> int:
        """Montant total remboursé"""
        return sum(refund.amount_cents for refund in self.refunds if refund.status == PaymentStatus.COMPLETED)
    
    @property
    def refundable_amount_cents(self) -> int:
        """Montant remboursable restant"""
        return max(0, self.amount_cents - self.refunded_amount_cents)


class PaymentTransaction(db.Model):
    """Transaction de paiement détaillée"""
    __tablename__ = "payment_transactions"
    
    id = Column(Integer, primary_key=True)
    payment_id_fk = Column(Integer, ForeignKey("payments.id"), nullable=False, index=True)
    
    # Type de transaction
    transaction_type = Column(String(50), nullable=False, index=True)  # charge, authorize, capture, void
    
    # Montants
    amount_cents = Column(Integer, nullable=False)
    
    # Statut et résultat
    status = Column(Enum(PaymentStatus), nullable=False, index=True)
    gateway_transaction_id = Column(String(255), index=True)
    gateway_response_code = Column(String(20))
    gateway_response_message = Column(Text)
    gateway_raw_response = Column(Text)  # Réponse brute JSON
    
    # Timing
    attempted_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    completed_at = Column(DateTime)
    
    # Audit
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    # Relations
    payment = relationship("Payment", back_populates="transactions")
    
    __table_args__ = (
        db.Index("idx_transaction_payment", "payment_id_fk"),
        db.Index("idx_transaction_type", "transaction_type"),
        db.Index("idx_transaction_status", "status"),
    )
    
    def __repr__(self):
        return f"<PaymentTransaction {self.transaction_type} - {self.amount_cents/100}€>"


class PaymentRefund(db.Model):
    """Remboursement de paiement"""
    __tablename__ = "payment_refunds"
    
    id = Column(Integer, primary_key=True)
    payment_id_fk = Column(Integer, ForeignKey("payments.id"), nullable=False, index=True)
    
    # Informations de remboursement
    amount_cents = Column(Integer, nullable=False)
    reason = Column(Enum(RefundReason), nullable=False)
    reason_description = Column(Text)
    
    # Statut et traitement
    status = Column(Enum(PaymentStatus), default=PaymentStatus.PENDING, nullable=False, index=True)
    external_refund_id = Column(String(255), index=True)
    
    # Timing
    requested_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    processed_at = Column(DateTime)
    
    # Audit
    requested_by_user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    processed_by_user_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    notes = Column(Text)
    
    # Relations
    payment = relationship("Payment", back_populates="refunds")
    requested_by = relationship("User", foreign_keys=[requested_by_user_id])
    processed_by = relationship("User", foreign_keys=[processed_by_user_id])
    
    __table_args__ = (
        db.Index("idx_refund_payment", "payment_id_fk"),
        db.Index("idx_refund_status", "status"),
        db.Index("idx_refund_date", "requested_at"),
    )
    
    def __repr__(self):
        return f"<PaymentRefund {self.amount_cents/100}€ - {self.reason.value}>"
    
    @property
    def amount_euros(self) -> Decimal:
        """Montant en euros"""
        return Decimal(self.amount_cents) / 100


class Receipt(db.Model):
    """Reçu de paiement"""
    __tablename__ = "receipts"
    
    id = Column(Integer, primary_key=True)
    business_id_fk = Column(Integer, ForeignKey("businesses.id"), nullable=False, index=True)
    order_id_fk = Column(Integer, ForeignKey("orders.id"), nullable=False, index=True)
    
    # Informations du reçu
    receipt_number = Column(String(50), unique=True, nullable=False, index=True)
    receipt_type = Column(String(20), default="payment")  # payment, refund, exchange
    
    # Contenu du reçu
    customer_name = Column(String(200))
    customer_email = Column(String(200))
    customer_phone = Column(String(50))
    
    # Montants
    subtotal_cents = Column(Integer, nullable=False)
    tax_cents = Column(Integer, default=0)
    discount_cents = Column(Integer, default=0)
    total_cents = Column(Integer, nullable=False)
    
    # Contenu détaillé
    items_data = Column(Text)  # JSON des articles
    payment_data = Column(Text)  # JSON des paiements
    
    # Configuration d'impression
    print_count = Column(Integer, default=0)
    last_printed_at = Column(DateTime)
    
    # Email
    email_sent = Column(Boolean, default=False)
    email_sent_at = Column(DateTime)
    
    # Notes
    notes = Column(Text)
    
    # Audit
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    created_by_user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Relations
    business = relationship("Business", backref="receipts")
    order = relationship("Order", backref="receipts")
    created_by = relationship("User")
    
    __table_args__ = (
        db.Index("idx_payment_receipt_business", "business_id_fk"),
        db.Index("idx_payment_receipt_order", "order_id_fk"),
        db.Index("idx_payment_receipt_number", "receipt_number"),
        db.Index("idx_payment_receipt_date", "created_at"),
        {'extend_existing': True}
    )
    
    def __repr__(self):
        return f"<Receipt {self.receipt_number}>"
    
    @property
    def total_euros(self) -> Decimal:
        """Total en euros"""
        return Decimal(self.total_cents) / 100


class CashMovement(db.Model):
    """Mouvement de caisse (espèces)"""
    __tablename__ = "cash_movements"
    
    id = Column(Integer, primary_key=True)
    business_id_fk = Column(Integer, ForeignKey("businesses.id"), nullable=False, index=True)
    
    # Type de mouvement
    movement_type = Column(String(20), nullable=False)  # Supprimé l'index automatique
    direction = Column(String(10), nullable=False, index=True)  # in, out
    
    # Montant
    amount_cents = Column(Integer, nullable=False)
    
    # Référence
    reference_type = Column(String(50))  # payment, refund, deposit, etc.
    reference_id = Column(Integer)  # ID de la référence
    
    # Description
    reason = Column(String(200))
    description = Column(Text)
    
    # Session de caisse
    cash_session_id = Column(Integer, ForeignKey("cash_sessions.id"), nullable=True, index=True)
    
    # Audit
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    created_by_user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Relations
    business = relationship("Business", backref="cash_movements")
    created_by = relationship("User")
    cash_session = relationship("PaymentCashSession", backref="movements", foreign_keys=[cash_session_id])
    
    __table_args__ = (
        db.Index("idx_cash_movement_business", "business_id_fk"),
        db.Index("idx_cash_movement_type", "movement_type"),  # Index personnalisé avec nom unique
        db.Index("idx_cash_movement_direction", "direction"),
        db.Index("idx_cash_movement_date", "created_at"),
    )
    
    def __repr__(self):
        return f"<CashMovement {self.direction} {self.amount_cents/100}€>"
    
    @property
    def amount_euros(self) -> Decimal:
        """Montant en euros"""
        return Decimal(self.amount_cents) / 100


class PaymentCashSession(db.Model):
    """Session de caisse"""
    __tablename__ = "cash_sessions"
    
    id = Column(Integer, primary_key=True)
    business_id_fk = Column(Integer, ForeignKey("businesses.id"), nullable=False, index=True)
    
    # Informations de session
    session_number = Column(String(50), nullable=False)  # Supprimé l'index automatique
    
    # Montants
    opening_balance_cents = Column(Integer, default=0)  # Solde d'ouverture
    closing_balance_cents = Column(Integer, nullable=True)  # Solde de fermeture
    expected_balance_cents = Column(Integer, nullable=True)  # Solde attendu
    difference_cents = Column(Integer, nullable=True)  # Différence
    
    # Timing
    opened_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    closed_at = Column(DateTime)
    
    # Statut
    is_active = Column(Boolean, default=True, index=True)
    
    # Utilisateurs
    opened_by_user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    closed_by_user_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    
    # Notes
    opening_notes = Column(Text)
    closing_notes = Column(Text)
    
    # Relations
    business = relationship("Business", backref="cash_sessions")
    opened_by = relationship("User", foreign_keys=[opened_by_user_id])
    closed_by = relationship("User", foreign_keys=[closed_by_user_id])
    
    __table_args__ = (
        db.Index("idx_cash_session_business", "business_id_fk"),
        db.Index("idx_cash_session_active", "is_active"),
        db.Index("idx_cash_session_date", "opened_at"),
        db.Index("idx_payment_cash_session_number", "session_number"),  # Index personnalisé avec nom unique
    )
    
    def __repr__(self):
        return f"<PaymentCashSession {self.session_number}>"
    
    @property
    def opening_balance_euros(self) -> Decimal:
        """Solde d'ouverture en euros"""
        return Decimal(self.opening_balance_cents) / 100
    
    @property
    def closing_balance_euros(self) -> Optional[Decimal]:
        """Solde de fermeture en euros"""
        return Decimal(self.closing_balance_cents) / 100 if self.closing_balance_cents is not None else None
    
    @property
    def total_sales_cents(self) -> int:
        """Total des ventes en centimes"""
        return sum(
            movement.amount_cents 
            for movement in self.movements 
            if movement.movement_type == "sale" and movement.direction == "in"
        )
    
    @property
    def total_refunds_cents(self) -> int:
        """Total des remboursements en centimes"""
        return sum(
            movement.amount_cents 
            for movement in self.movements 
            if movement.movement_type == "refund" and movement.direction == "out"
        )


