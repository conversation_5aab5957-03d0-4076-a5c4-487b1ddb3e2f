// Gestion des modals avec Tailwind CSS pour le module Loyalty

// Fonction pour ouvrir un modal
function openModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.remove('hidden');
        modal.classList.add('flex');
        document.body.classList.add('overflow-hidden');
    }
}

// Fonction pour fermer un modal
function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.add('hidden');
        modal.classList.remove('flex');
        document.body.classList.remove('overflow-hidden');
    }
}

// Fermer le modal en cliquant sur le fond
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('modal-backdrop')) {
        const modal = e.target.closest('.modal');
        if (modal) {
            closeModal(modal.id);
        }
    }
});

// Fermer le modal avec la touche Échap
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        const openModal = document.querySelector('.modal.flex');
        if (openModal) {
            closeModal(openModal.id);
        }
    }
});

// Fonction spécifique pour l'ajout rapide de points
function showAddPointsModal(customerId, customerName) {
    // Mettre à jour le contenu du modal avec les informations du client
    document.querySelector('#quickAddPointsModal select[name="customer_id"]').value = customerId;
    document.querySelector('#quickAddPointsModal .customer-name').textContent = customerName;
    
    // Ouvrir le modal
    openModal('quickAddPointsModal');
}

// Fonction pour soumettre le formulaire d'ajout rapide de points
function submitQuickAddPoints() {
    const form = document.getElementById('quickAddPointsForm');
    const formData = new FormData(form);
    
    fetch('/loyalty/api/quick-add-points', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Points ajoutés avec succès!');
            closeModal('quickAddPointsModal');
            location.reload();
        } else {
            alert('Erreur: ' + data.message);
        }
    });
}

// Fonction pour exporter les données de fidélité
function exportLoyaltyData() {
    window.location.href = '/loyalty/export/csv';
}

// Fonction pour faire expirer les points
function expirePoints() {
    if (confirm('Êtes-vous sûr de vouloir faire expirer les points selon les règles des programmes ?')) {
        fetch('/loyalty/api/expire-points', {method: 'POST'})
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(`${data.expired_count} points ont été expirés.`);
                    location.reload();
                }
            });
    }
}

// Fonction pour traiter les bonus anniversaire
function processBirthdayBonuses() {
    if (confirm('Traiter les bonus anniversaire pour tous les clients éligibles ?')) {
        fetch('/loyalty/api/process-birthday-bonuses', {method: 'POST'})
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(`${data.processed_count} bonus anniversaire ont été traités.`);
                    location.reload();
                }
            });
    }
}

// Fonction pour mettre à jour les niveaux des clients
function updateCustomerLevels() {
    if (confirm('Mettre à jour les niveaux de tous les clients selon leurs points actuels ?')) {
        fetch('/loyalty/api/update-customer-levels', {method: 'POST'})
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(`${data.updated_count} niveaux de clients ont été mis à jour.`);
                    location.reload();
                }
            });
    }
}

// Fonction pour générer un rapport de fidélité
function generateLoyaltyReport() {
    window.open('/loyalty/reports/comprehensive', '_blank');
}