from flask import render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from app.extensions import db, socketio
from app.modules.catalog.models import Product, Promotion, TaxRule
from app.modules.sales.models import Order, OrderItem
from app.modules.ingredients.models import Recipe, Ingredient
from app.modules.variants.models import ProductVariant, ProductAttribute, AttributeValue
from app.modules.delivery.models import Delivery, DeliveryZone, DeliveryDriver
from app.modules.tables.models import Table, DiningRoom
from app.modules.customers.models import Customer
from . import bp
from .forms import POSOrderForm, DeliveryForm, TableOrderForm, TakeawayOrderForm
from datetime import datetime
import logging

# Configuration du logger
logger = logging.getLogger(__name__)


@bp.get("/")
@login_required
def sale():
    """Main POS interface with support for recipes and variants"""
    products = (
        Product.query.filter_by(is_active=True, business_id_fk=current_user.business.id)
        .order_by(Product.name.asc())
        .all()
    )
    
    # Get business configuration to determine mode
    business_config = getattr(current_user, 'business_config', None)
    business_type = None
    if business_config:
        business_type = getattr(business_config, 'business_type', None)
    
    # Load recipes if restaurant mode
    recipes = []
    if business_type and business_type.code == 'restaurant':
        recipes = Recipe.query.filter_by(
            is_active=True, 
            business_id_fk=current_user.business.id
        ).all()
    
    # Load variants if commerce mode
    variants = []
    if business_type and business_type.code in ['commerce', 'clothing']:
        variants = ProductVariant.query.filter_by(
            is_active=True, 
            business_id_fk=current_user.business.id
        ).all()
    
    # Load active promotions
    promotions = Promotion.query.filter_by(
        is_active=True, 
        business_id_fk=current_user.business.id
    ).filter(
        Promotion.valid_from <= datetime.utcnow(),
        Promotion.valid_until >= datetime.utcnow()
    ).all()
    
    # Load tax rules
    tax_rules = TaxRule.query.filter_by(
        is_active=True, 
        business_id_fk=current_user.business.id
    ).all()
    
    # Load delivery zones
    delivery_zones = []
    try:
        delivery_zones = DeliveryZone.query.filter_by(
            is_active=True, 
            business_id_fk=current_user.business.id
        ).all()
    except Exception as e:
        # Si la table n'existe pas encore, continuer sans zones de livraison
        print(f"Warning: Could not load delivery zones: {e}")
    
    # Load tables for restaurant mode
    tables = []
    dining_rooms = []
    if business_type and business_type.code == 'restaurant':
        dining_rooms = DiningRoom.query.filter_by(
            is_active=True, 
            business_id_fk=current_user.business.id
        ).all()
        tables = Table.query.filter_by(
            is_active=True, 
            business_id_fk=current_user.business.id
        ).all()
    
    return render_template("pos/sale.html", 
                         products=products,
                         recipes=recipes,
                         variants=variants,
                         promotions=promotions,
                         tax_rules=tax_rules,
                         delivery_zones=delivery_zones,
                         dining_rooms=dining_rooms,
                         tables=tables,
                         business_type=business_type)


@bp.get("/recipe-mode")
@login_required
def recipe_mode():
    """POS interface optimized for restaurant mode with recipes"""
    recipes = Recipe.query.filter_by(
        is_active=True, 
        business_id_fk=current_user.business.id
    ).all()
    
    ingredients = Ingredient.query.filter_by(
        is_active=True, 
        business_id_fk=current_user.business.id
    ).all()
    
    return render_template("pos/recipe_mode.html", 
                         recipes=recipes,
                         ingredients=ingredients)


@bp.get("/variant-mode")
@login_required
def variant_mode():
    """POS interface optimized for commerce mode with variants"""
    products = Product.query.filter_by(
        is_active=True, 
        business_id_fk=current_user.business.id
    ).all()
    
    variants = ProductVariant.query.filter_by(
        is_active=True, 
        business_id_fk=current_user.business.id
    ).all()
    
    attributes = ProductAttribute.query.filter_by(
        is_active=True, 
        business_id_fk=current_user.business.id
    ).all()
    
    return render_template("pos/variant_mode.html", 
                         products=products,
                         variants=variants,
                         attributes=attributes)


@bp.post("/api/order")
@login_required
def create_order():
    """Create order with support for recipes, variants, promotions, and taxes"""
    try:
        logger.info("Début de la création d'une commande")
        data = request.get_json(force=True)
        logger.info(f"Données reçues: {data}")
        
        # Basic order data
        items = data.get("items", [])
        promotion_code = data.get("promotion_code")
        delivery_info = data.get("delivery_info")
        customer_info = data.get("customer_info", {})
        
        logger.info(f"Items: {items}")
        logger.info(f"Promotion code: {promotion_code}")
        logger.info(f"Delivery info: {delivery_info}")
        logger.info(f"Customer info: {customer_info}")
        
        # Create order
        order = Order(
            business_id_fk=current_user.business.id, 
            status="open", 
            total_cents=0,
            customer_name=customer_info.get("name"),
            customer_phone=customer_info.get("phone"),
            customer_email=customer_info.get("email")
        )
        db.session.add(order)
        db.session.flush()
        logger.info(f"Commande créée avec ID: {order.id}")
        
        # Process items (products, recipes, variants)
        total = 0
        subtotal = 0
        
        for item in items:
            item_type = item.get("type", "product")  # product, recipe, variant
            logger.info(f"Traitement de l'item: {item}")
            
            if item_type == "product":
                product = Product.query.get(item.get("product_id"))
                if not product or not product.is_active:
                    logger.warning(f"Produit non trouvé ou inactif: {item.get('product_id')}")
                    continue
                qty = int(item.get("qty", 1))
                price = product.price_cents
                line_total = price * qty
                subtotal += line_total
                
                db.session.add(OrderItem(
                    order_id_fk=order.id,
                    product_id_fk=product.id,
                    qty=qty,
                    price_cents=price,
                    item_type="product"
                ))
                logger.info(f"Ajout d'un item produit: {product.name}")
                
            elif item_type == "recipe":
                recipe = Recipe.query.get(item.get("recipe_id"))
                if not recipe or not recipe.is_active:
                    logger.warning(f"Recette non trouvée ou inactive: {item.get('recipe_id')}")
                    continue
                qty = int(item.get("qty", 1))
                price = recipe.product.price_cents
                line_total = price * qty
                subtotal += line_total
                
                db.session.add(OrderItem(
                    order_id_fk=order.id,
                    product_id_fk=recipe.product_id_fk,
                    qty=qty,
                    price_cents=price,
                    item_type="recipe",
                    notes=f"Recipe: {recipe.name}"
                ))
                logger.info(f"Ajout d'un item recette: {recipe.name}")
                
            elif item_type == "variant":
                variant = ProductVariant.query.get(item.get("variant_id"))
                if not variant or not variant.is_active:
                    logger.warning(f"Variante non trouvée ou inactive: {item.get('variant_id')}")
                    continue
                qty = int(item.get("qty", 1))
                price = variant.price_cents
                line_total = price * qty
                subtotal += line_total
                
                db.session.add(OrderItem(
                    order_id_fk=order.id,
                    product_id_fk=variant.product_id_fk,
                    qty=qty,
                    price_cents=price,
                    item_type="variant",
                    notes=f"Variant: {variant.name}"
                ))
                logger.info(f"Ajout d'un item variante: {variant.name}")
        
        # Apply promotion if valid
        discount_amount = 0
        if promotion_code:
            logger.info(f"Application de la promotion: {promotion_code}")
            promotion = Promotion.query.filter_by(
                code=promotion_code,
                is_active=True,
                business_id_fk=current_user.business.id
            ).filter(
                Promotion.valid_from <= datetime.utcnow(),
                Promotion.valid_until >= datetime.utcnow()
            ).first()
            
            if promotion and (promotion.usage_limit is None or promotion.used_count < promotion.usage_limit):
                if subtotal >= promotion.min_order_amount_cents:
                    if promotion.discount_type == "percentage":
                        discount_amount = int(subtotal * promotion.discount_value / 100)
                        if promotion.max_discount_cents:
                            discount_amount = min(discount_amount, promotion.max_discount_cents)
                    else:  # fixed_amount
                        discount_amount = promotion.discount_value
                    
                    promotion.used_count += 1
                    logger.info(f"Promotion appliquée: réduction de {discount_amount} cents")
        
        # Calculate taxes
        tax_amount = 0
        tax_rules = TaxRule.query.filter_by(
            is_active=True, 
            business_id_fk=current_user.business.id
        ).all()
        
        for tax_rule in tax_rules:
            if tax_rule.applies_to == "all" or tax_rule.applies_to == "products":
                if tax_rule.is_compound:
                    # Compound tax on subtotal after discount
                    taxable_amount = subtotal - discount_amount
                    tax_amount += int(taxable_amount * tax_rule.rate_percentage / 100)
                else:
                    # Simple tax on subtotal after discount
                    taxable_amount = subtotal - discount_amount
                    tax_amount += int(taxable_amount * tax_rule.rate_percentage / 100)
        
        # Calculate final total
        total = subtotal - discount_amount + tax_amount
        
        # Add delivery fee if delivery
        delivery_fee = 0
        if delivery_info:
            delivery_fee = int(delivery_info.get("fee_cents", 0))
            total += delivery_fee
        
        order.subtotal_cents = subtotal
        order.discount_cents = discount_amount
        order.tax_cents = tax_amount
        order.delivery_fee_cents = delivery_fee
        order.total_cents = total
        
        logger.info(f"Calcul du total: subtotal={subtotal}, discount={discount_amount}, tax={tax_amount}, delivery={delivery_fee}, total={total}")
        
        # Create delivery if requested
        if delivery_info:
            delivery = Delivery(
                business_id_fk=current_user.business.id,
                order_id_fk=order.id,
                zone_id_fk=delivery_info.get("zone_id"),
                pickup_address=delivery_info.get("pickup_address", ""),
                delivery_address=delivery_info.get("delivery_address", ""),
                customer_name=customer_info.get("name", ""),
                customer_phone=customer_info.get("phone", ""),
                delivery_fee_cents=delivery_fee,
                status="pending"
            )
            db.session.add(delivery)
            logger.info("Livraison créée")
        
        db.session.commit()
        logger.info("Commande enregistrée avec succès")
        
        # Emit socket event
        socketio.emit("order_created", {
            "order_id": order.id, 
            "total_cents": total,
            "delivery_requested": bool(delivery_info)
        })
        logger.info("Événement Socket.IO émis")
        
        return jsonify({
            "ok": True, 
            "order_id": order.id, 
            "total_cents": total,
            "subtotal_cents": subtotal,
            "discount_cents": discount_amount,
            "tax_cents": tax_amount,
            "delivery_fee_cents": delivery_fee
        })
    except Exception as e:
        db.session.rollback()
        logger.error(f"Erreur lors de la création de la commande: {e}", exc_info=True)
        return jsonify({"ok": False, "error": "Erreur lors de la création de la commande"}), 500


@bp.post("/api/validate-promotion")
@login_required
def validate_promotion():
    """Validate promotion code and return discount information"""
    data = request.get_json(force=True)
    code = data.get("code")
    
    if not code:
        return jsonify({"valid": False, "message": "Code promotion requis"})
    
    promotion = Promotion.query.filter_by(
        code=code,
        is_active=True,
        business_id_fk=current_user.business.id
    ).filter(
        Promotion.valid_from <= datetime.utcnow(),
        Promotion.valid_until >= datetime.utcnow()
    ).first()
    
    if not promotion:
        return jsonify({"valid": False, "message": "Code promotion invalide"})
    
    if promotion.usage_limit and promotion.used_count >= promotion.usage_limit:
        return jsonify({"valid": False, "message": "Code promotion épuisé"})
    
    return jsonify({
        "valid": True,
        "promotion": {
            "id": promotion.id,
            "name": promotion.name,
            "discount_type": promotion.discount_type,
            "discount_value": promotion.discount_value,
            "min_order_amount_cents": promotion.min_order_amount_cents,
            "max_discount_cents": promotion.max_discount_cents
        }
    })


@bp.get("/api/delivery-zones")
@login_required
def get_delivery_zones():
    """Get available delivery zones for the business"""
    zones = DeliveryZone.query.filter_by(
        is_active=True, 
        business_id_fk=current_user.business.id
    ).all()
    
    return jsonify({
        "zones": [{
            "id": zone.id,
            "name": zone.name,
            "base_fee_cents": zone.base_fee_cents,
            "fee_per_km_cents": zone.fee_per_km_cents,
            "min_order_amount_cents": zone.min_order_amount_cents,
            "estimated_delivery_time_minutes": zone.estimated_delivery_time_minutes
        } for zone in zones]
    })


@bp.get("/api/available-drivers")
@login_required
def get_available_drivers():
    """Get available delivery drivers"""
    drivers = DeliveryDriver.query.filter_by(
        is_active=True,
        is_available=True,
        business_id_fk=current_user.business.id
    ).all()
    
    return jsonify({
        "drivers": [{
            "id": driver.id,
            "name": driver.name,
            "phone": driver.phone,
            "vehicle_info": driver.vehicle_info
        } for driver in drivers]
    })


@bp.get("/table-service")
@login_required
def table_service():
    """POS interface for table service"""
    # Get business configuration to determine mode
    business_config = getattr(current_user, 'business_config', None)
    business_type = None
    if business_config:
        business_type = getattr(business_config, 'business_type', None)
    
    # Load tables for restaurant mode
    tables = []
    dining_rooms = []
    if business_type and business_type.code == 'restaurant':
        dining_rooms = DiningRoom.query.filter_by(
            is_active=True, 
            business_id_fk=current_user.business.id
        ).all()
        tables = Table.query.filter_by(
            is_active=True, 
            business_id_fk=current_user.business.id
        ).all()
    
    return render_template("pos/table_service.html", 
                         dining_rooms=dining_rooms,
                         tables=tables,
                         business_type=business_type)


@bp.get("/takeaway")
@login_required
def takeaway():
    """POS interface for takeaway orders"""
    products = (
        Product.query.filter_by(is_active=True, business_id_fk=current_user.business.id)
        .order_by(Product.name.asc())
        .all()
    )
    
    # Get business configuration to determine mode
    business_config = getattr(current_user, 'business_config', None)
    business_type = None
    if business_config:
        business_type = getattr(business_config, 'business_type', None)
    
    # Load recipes if restaurant mode
    recipes = []
    if business_type and business_type.code == 'restaurant':
        recipes = Recipe.query.filter_by(
            is_active=True, 
            business_id_fk=current_user.business.id
        ).all()
    
    # Load variants if commerce mode
    variants = []
    if business_type and business_type.code in ['commerce', 'clothing']:
        variants = ProductVariant.query.filter_by(
            is_active=True, 
            business_id_fk=current_user.business.id
        ).all()
    
    return render_template("pos/takeaway.html", 
                         products=products,
                         recipes=recipes,
                         variants=variants,
                         business_type=business_type)