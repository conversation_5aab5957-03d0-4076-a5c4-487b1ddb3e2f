"""Intégration comptable pour l'application POS System"""
import requests
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
from flask import current_app
import json

from app.modules.accounting.models import Account, JournalEntry, Ledger
from app.modules.sales.models import POSOrder
from app.extensions import db

logger = logging.getLogger(__name__)

class AccountingIntegration:
    """Gestion de l'intégration comptable avec des systèmes externes"""
    
    def __init__(self):
        """Initialise l'intégration comptable"""
        self.api_base_url = current_app.config.get('ACCOUNTING_API_URL')
        self.api_key = current_app.config.get('ACCOUNTING_API_KEY')
        self.company_id = current_app.config.get('ACCOUNTING_COMPANY_ID')
        
    def sync_chart_of_accounts(self) -> Dict[str, Any]:
        """Synchronise le plan comptable avec le système externe"""
        try:
            if not self.is_configured():
                raise ValueError("L'intégration comptable n'est pas configurée")
            
            # Récupérer les comptes depuis le système externe
            external_accounts = self._fetch_external_accounts()
            
            # Synchroniser avec la base de données locale
            synced_accounts = []
            for ext_account in external_accounts:
                # Vérifier si le compte existe déjà
                account = Account.query.filter_by(
                    account_number=ext_account['account_number']
                ).first()
                
                if account:
                    # Mettre à jour le compte existant
                    account.name = ext_account['name']
                    account.account_type = ext_account['account_type']
                    account.description = ext_account.get('description', '')
                    account.is_active = ext_account.get('is_active', True)
                else:
                    # Créer un nouveau compte
                    account = Account(
                        account_number=ext_account['account_number'],
                        name=ext_account['name'],
                        account_type=ext_account['account_type'],
                        description=ext_account.get('description', ''),
                        is_active=ext_account.get('is_active', True)
                    )
                    db.session.add(account)
                
                synced_accounts.append(account)
            
            db.session.commit()
            logger.info(f"Synchronisation du plan comptable terminée: {len(synced_accounts)} comptes")
            
            return {
                'success': True,
                'synced_accounts': len(synced_accounts)
            }
            
        except Exception as e:
            logger.error(f"Erreur lors de la synchronisation du plan comptable: {e}")
            db.session.rollback()
            return {
                'success': False,
                'error': str(e)
            }
    
    def _fetch_external_accounts(self) -> List[Dict[str, Any]]:
        """Récupère les comptes depuis le système comptable externe"""
        try:
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }
            
            response = requests.get(
                f"{self.api_base_url}/companies/{self.company_id}/accounts",
                headers=headers
            )
            
            if response.status_code == 200:
                return response.json().get('accounts', [])
            else:
                raise Exception(f"Erreur API: {response.status_code} - {response.text}")
                
        except Exception as e:
            logger.error(f"Erreur lors de la récupération des comptes externes: {e}")
            raise
    
    def export_journal_entries(self, start_date: datetime = None, 
                             end_date: datetime = None) -> Dict[str, Any]:
        """Exporte les écritures de journal vers le système comptable externe"""
        try:
            if not self.is_configured():
                raise ValueError("L'intégration comptable n'est pas configurée")
            
            # Récupérer les écritures de journal
            query = JournalEntry.query
            
            if start_date:
                query = query.filter(JournalEntry.date >= start_date)
            if end_date:
                query = query.filter(JournalEntry.date <= end_date)
            
            journal_entries = query.all()
            
            # Exporter vers le système externe
            exported_entries = []
            failed_entries = []
            
            for entry in journal_entries:
                try:
                    result = self._export_journal_entry(entry)
                    if result['success']:
                        exported_entries.append(entry.id)
                    else:
                        failed_entries.append({
                            'entry_id': entry.id,
                            'error': result['error']
                        })
                except Exception as e:
                    failed_entries.append({
                        'entry_id': entry.id,
                        'error': str(e)
                    })
            
            logger.info(f"Export des écritures terminé: {len(exported_entries)} succès, {len(failed_entries)} échecs")
            
            return {
                'success': True,
                'exported_entries': len(exported_entries),
                'failed_entries': len(failed_entries),
                'details': {
                    'exported': exported_entries,
                    'failed': failed_entries
                }
            }
            
        except Exception as e:
            logger.error(f"Erreur lors de l'export des écritures de journal: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _export_journal_entry(self, entry: JournalEntry) -> Dict[str, Any]:
        """Exporte une écriture de journal vers le système externe"""
        try:
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }
            
            # Préparer les données de l'écriture
            entry_data = {
                'date': entry.date.isoformat(),
                'description': entry.description,
                'reference': entry.reference,
                'lines': []
            }
            
            # Ajouter les lignes d'écriture
            for line in entry.ledger_entries:
                entry_data['lines'].append({
                    'account_number': line.account.account_number,
                    'debit': float(line.debit_amount) if line.debit_amount else 0,
                    'credit': float(line.credit_amount) if line.credit_amount else 0,
                    'description': line.description
                })
            
            response = requests.post(
                f"{self.api_base_url}/companies/{self.company_id}/journal_entries",
                headers=headers,
                json=entry_data
            )
            
            if response.status_code in [200, 201]:
                return {'success': True}
            else:
                return {
                    'success': False,
                    'error': f"Erreur API: {response.status_code} - {response.text}"
                }
                
        except Exception as e:
            logger.error(f"Erreur lors de l'export de l'écriture {entry.id}: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def import_sales_data(self, start_date: datetime = None, 
                         end_date: datetime = None) -> Dict[str, Any]:
        """Importe les données de ventes vers le système comptable externe"""
        try:
            if not self.is_configured():
                raise ValueError("L'intégration comptable n'est pas configurée")
            
            # Récupérer les commandes
            query = POSOrder.query
            
            if start_date:
                query = query.filter(POSOrder.created_at >= start_date)
            if end_date:
                query = query.filter(POSOrder.created_at <= end_date)
            
            orders = query.all()
            
            # Exporter vers le système externe
            exported_orders = []
            failed_orders = []
            
            for order in orders:
                try:
                    result = self._export_sales_data(order)
                    if result['success']:
                        exported_orders.append(order.id)
                    else:
                        failed_orders.append({
                            'order_id': order.id,
                            'error': result['error']
                        })
                except Exception as e:
                    failed_orders.append({
                        'order_id': order.id,
                        'error': str(e)
                    })
            
            logger.info(f"Export des données de ventes terminé: {len(exported_orders)} succès, {len(failed_orders)} échecs")
            
            return {
                'success': True,
                'exported_orders': len(exported_orders),
                'failed_orders': len(failed_orders),
                'details': {
                    'exported': exported_orders,
                    'failed': failed_orders
                }
            }
            
        except Exception as e:
            logger.error(f"Erreur lors de l'export des données de ventes: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _export_sales_data(self, order: POSOrder) -> Dict[str, Any]:
        """Exporte une commande vers le système comptable externe"""
        try:
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }
            
            # Préparer les données de la commande
            order_data = {
                'date': order.created_at.isoformat(),
                'order_id': order.id,
                'total_amount': float(order.total_cents) / 100,
                'tax_amount': float(order.tax_cents) / 100 if order.tax_cents else 0,
                'items': []
            }
            
            # Ajouter les articles
            for item in order.items:
                order_data['items'].append({
                    'product_id': item.product_id_fk,
                    'product_name': item.product.name,
                    'quantity': item.quantity,
                    'unit_price': float(item.unit_price_cents) / 100,
                    'total_price': float(item.total_price_cents) / 100
                })
            
            response = requests.post(
                f"{self.api_base_url}/companies/{self.company_id}/sales",
                headers=headers,
                json=order_data
            )
            
            if response.status_code in [200, 201]:
                return {'success': True}
            else:
                return {
                    'success': False,
                    'error': f"Erreur API: {response.status_code} - {response.text}"
                }
                
        except Exception as e:
            logger.error(f"Erreur lors de l'export de la commande {order.id}: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def is_configured(self) -> bool:
        """Vérifie si l'intégration est correctement configurée"""
        return bool(self.api_base_url and self.api_key and self.company_id)
    
    def get_account_balance(self, account_number: str, 
                          start_date: datetime = None, 
                          end_date: datetime = None) -> Dict[str, Any]:
        """Récupère le solde d'un compte sur une période"""
        try:
            if not self.is_configured():
                raise ValueError("L'intégration comptable n'est pas configurée")
            
            # Construire l'URL avec les paramètres
            url = f"{self.api_base_url}/companies/{self.company_id}/accounts/{account_number}/balance"
            
            params = {}
            if start_date:
                params['start_date'] = start_date.isoformat()
            if end_date:
                params['end_date'] = end_date.isoformat()
            
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }
            
            response = requests.get(url, headers=headers, params=params)
            
            if response.status_code == 200:
                return {
                    'success': True,
                    'balance': response.json()
                }
            else:
                return {
                    'success': False,
                    'error': f"Erreur API: {response.status_code} - {response.text}"
                }
                
        except Exception as e:
            logger.error(f"Erreur lors de la récupération du solde du compte {account_number}: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def create_invoice(self, customer_id: int, items: List[Dict[str, Any]], 
                      due_date: datetime) -> Dict[str, Any]:
        """Crée une facture dans le système comptable externe"""
        try:
            if not self.is_configured():
                raise ValueError("L'intégration comptable n'est pas configurée")
            
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }
            
            # Préparer les données de la facture
            invoice_data = {
                'customer_id': customer_id,
                'due_date': due_date.isoformat(),
                'items': items
            }
            
            response = requests.post(
                f"{self.api_base_url}/companies/{self.company_id}/invoices",
                headers=headers,
                json=invoice_data
            )
            
            if response.status_code in [200, 201]:
                return {
                    'success': True,
                    'invoice_id': response.json().get('id'),
                    'invoice_number': response.json().get('number')
                }
            else:
                return {
                    'success': False,
                    'error': f"Erreur API: {response.status_code} - {response.text}"
                }
                
        except Exception as e:
            logger.error(f"Erreur lors de la création de la facture: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_financial_report(self, report_type: str, start_date: datetime, 
                           end_date: datetime) -> Dict[str, Any]:
        """Récupère un rapport financier du système externe"""
        try:
            if not self.is_configured():
                raise ValueError("L'intégration comptable n'est pas configurée")
            
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }
            
            # Préparer les paramètres
            params = {
                'type': report_type,
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat()
            }
            
            response = requests.get(
                f"{self.api_base_url}/companies/{self.company_id}/reports",
                headers=headers,
                params=params
            )
            
            if response.status_code == 200:
                return {
                    'success': True,
                    'report': response.json()
                }
            else:
                return {
                    'success': False,
                    'error': f"Erreur API: {response.status_code} - {response.text}"
                }
                
        except Exception as e:
            logger.error(f"Erreur lors de la récupération du rapport financier: {e}")
            return {
                'success': False,
                'error': str(e)
            }