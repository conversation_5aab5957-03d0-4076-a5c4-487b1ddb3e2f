from flask_wtf import <PERSON>laskForm
from wtforms import (
    StringField, IntegerField, TextAreaField, SelectField, BooleanField,
    DecimalField, DateTimeField, HiddenField, SubmitField
)
from wtforms.validators import (
    DataRequired, Length, Optional, NumberRange, ValidationError
)
from datetime import datetime


class CashRegisterForm(FlaskForm):
    """Formulaire pour ajouter/modifier une caisse enregistreuse"""
    register_code = StringField(
        'Code de la caisse',
        validators=[DataRequired(), Length(min=2, max=20)],
        render_kw={'placeholder': 'Ex: CAISSE001'}
    )
    name = StringField(
        'Nom de la caisse',
        validators=[DataRequired(), Length(min=2, max=120)],
        render_kw={'placeholder': 'Ex: Caisse principale'}
    )
    location = StringField(
        'Emplacement',
        validators=[Optional(), Length(max=255)],
        render_kw={'placeholder': 'Ex: Rez-de-chaussée, entrée'}
    )
    description = TextAreaField(
        'Description',
        validators=[Optional()],
        render_kw={'rows': 3, 'placeholder': 'Description optionnelle de la caisse'}
    )
    initial_cash = DecimalField(
        'Fond de caisse (€)',
        validators=[Optional(), NumberRange(min=0)],
        places=2,
        render_kw={'step': '0.01', 'placeholder': '0.00'}
    )
    max_cash_limit = DecimalField(
        'Limite maximum en caisse (€)',
        validators=[Optional(), NumberRange(min=0)],
        places=2,
        render_kw={'step': '0.01', 'placeholder': 'Optionnel'}
    )
    receipt_header = TextAreaField(
        'En-tête des reçus',
        validators=[Optional()],
        render_kw={'rows': 3, 'placeholder': 'Texte affiché en haut des reçus'}
    )
    receipt_footer = TextAreaField(
        'Pied de page des reçus',
        validators=[Optional()],
        render_kw={'rows': 3, 'placeholder': 'Texte affiché en bas des reçus'}
    )
    requires_manager_approval = BooleanField(
        'Nécessite l\'approbation du manager',
        default=False
    )
    auto_print_receipts = BooleanField(
        'Impression automatique des reçus',
        default=True
    )
    is_active = BooleanField(
        'Caisse active',
        default=True
    )
    submit = SubmitField('Enregistrer')


class EditCashRegisterForm(CashRegisterForm):
    """Formulaire pour modifier une caisse (sans le code)"""
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Le code ne peut pas être modifié
        self.register_code.render_kw = {'readonly': True}


class OpenCashSessionForm(FlaskForm):
    """Formulaire pour ouvrir une session de caisse"""
    opening_cash = DecimalField(
        'Montant d\'ouverture (€)',
        validators=[DataRequired(), NumberRange(min=0)],
        places=2,
        render_kw={'step': '0.01', 'placeholder': '0.00'}
    )
    opening_notes = TextAreaField(
        'Notes d\'ouverture',
        validators=[Optional()],
        render_kw={'rows': 3, 'placeholder': 'Notes optionnelles...'}
    )
    submit = SubmitField('Ouvrir la session')


class CloseCashSessionForm(FlaskForm):
    """Formulaire pour fermer une session de caisse"""
    closing_cash_counted = DecimalField(
        'Montant compté à la fermeture (€)',
        validators=[DataRequired(), NumberRange(min=0)],
        places=2,
        render_kw={'step': '0.01', 'placeholder': '0.00'}
    )
    closing_notes = TextAreaField(
        'Notes de fermeture',
        validators=[Optional()],
        render_kw={'rows': 3, 'placeholder': 'Notes optionnelles...'}
    )
    submit = SubmitField('Fermer la session')


class CashMovementForm(FlaskForm):
    """Formulaire pour ajouter un mouvement de caisse"""
    movement_type = SelectField(
        'Type de mouvement',
        choices=[
            ('in', 'Entrée'),
            ('out', 'Sortie'),
            ('correction', 'Correction')
        ],
        validators=[DataRequired()]
    )
    movement_reason = SelectField(
        'Raison du mouvement',
        choices=[
            ('sale', 'Vente'),
            ('refund', 'Remboursement'),
            ('expense', 'Dépense'),
            ('deposit', 'Dépôt'),
            ('withdrawal', 'Retrait'),
            ('petty_cash', 'Petite caisse'),
            ('bank_deposit', 'Dépôt banque'),
            ('correction', 'Correction'),
            ('tip', 'Pourboire'),
            ('other', 'Autre')
        ],
        validators=[DataRequired()]
    )
    amount = DecimalField(
        'Montant (€)',
        validators=[DataRequired(), NumberRange(min=0.01)],
        places=2,
        render_kw={'step': '0.01', 'placeholder': '0.00'}
    )
    description = StringField(
        'Description',
        validators=[Optional(), Length(max=255)],
        render_kw={'placeholder': 'Description du mouvement'}
    )
    notes = TextAreaField(
        'Notes',
        validators=[Optional()],
        render_kw={'rows': 3, 'placeholder': 'Notes optionnelles...'}
    )
    reference_number = StringField(
        'Numéro de référence',
        validators=[Optional(), Length(max=100)],
        render_kw={'placeholder': 'Référence externe (optionnel)'}
    )
    requires_approval = BooleanField(
        'Nécessite une approbation',
        default=False
    )
    submit = SubmitField('Enregistrer le mouvement')


class CashSearchForm(FlaskForm):
    """Formulaire de recherche pour les caisses"""
    search = StringField(
        'Recherche',
        validators=[Optional()],
        render_kw={'placeholder': 'Code, nom, emplacement...'}
    )
    status = SelectField(
        'Statut',
        choices=[
            ('all', 'Tous'),
            ('active', 'Actives'),
            ('inactive', 'Inactives'),
            ('open', 'Sessions ouvertes'),
            ('closed', 'Sessions fermées')
        ],
        default='all'
    )
    location = StringField(
        'Emplacement',
        validators=[Optional()],
        render_kw={'placeholder': 'Filtrer par emplacement'}
    )
    submit = SubmitField('Rechercher')


class QuickCashForm(FlaskForm):
    """Formulaire rapide pour saisie de cash"""
    amount = DecimalField(
        'Montant (€)',
        validators=[DataRequired(), NumberRange(min=0.01)],
        places=2,
        render_kw={'step': '0.01', 'placeholder': '0.00', 'autofocus': True}
    )
    movement_type = SelectField(
        'Type',
        choices=[
            ('in', 'Entrée'),
            ('out', 'Sortie')
        ],
        validators=[DataRequired()],
        default='in'
    )
    description = StringField(
        'Description rapide',
        validators=[Optional(), Length(max=100)],
        render_kw={'placeholder': 'Description courte...'}
    )
    submit = SubmitField('Ajouter')