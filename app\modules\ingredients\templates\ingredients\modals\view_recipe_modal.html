<!-- Modal Vue Recette -->
<div id="viewRecipeModal" class="hidden fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <!-- Fond noir transparent -->
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
            <div class="absolute inset-0 bg-gray-900 opacity-75" onclick="document.getElementById('viewRecipeModal').classList.add('hidden')"></div>
        </div>

        <!-- Contenu du modal -->
        <div class="inline-block align-bottom bg-slate-900 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-6xl sm:w-full border border-slate-700">
            <div class="bg-green-600 px-4 py-3 border-b border-slate-700">
                <h3 class="text-lg leading-6 font-medium text-white">
                    <i class="fas fa-utensils mr-2"></i>Détails de la Recette
                </h3>
            </div>
            <div class="px-4 py-5 sm:p-6">
                <input type="hidden" id="viewRecipeId">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Informations de base -->
                    <div>
                        <h4 class="text-lg font-medium text-cyan-400 mb-4">Informations de la Recette</h4>
                        
                        <div class="grid grid-cols-3 gap-2 mb-2">
                            <div class="col-span-1 text-sm font-medium text-slate-400">Nom:</div>
                            <div class="col-span-2 text-sm text-slate-200" id="viewRecipeName"></div>
                        </div>
                        
                        <div class="grid grid-cols-3 gap-2 mb-2">
                            <div class="col-span-1 text-sm font-medium text-slate-400">Produit associé:</div>
                            <div class="col-span-2 text-sm text-slate-200" id="viewRecipeProduct"></div>
                        </div>
                        
                        <div class="grid grid-cols-3 gap-2 mb-2">
                            <div class="col-span-1 text-sm font-medium text-slate-400">Description:</div>
                            <div class="col-span-2 text-sm text-slate-200" id="viewRecipeDescription"></div>
                        </div>
                        
                        <div class="grid grid-cols-3 gap-2 mb-2">
                            <div class="col-span-1 text-sm font-medium text-slate-400">Nombre de portions:</div>
                            <div class="col-span-2 text-sm text-slate-200" id="viewRecipeServings"></div>
                        </div>
                        
                        <div class="grid grid-cols-3 gap-2 mb-2">
                            <div class="col-span-1 text-sm font-medium text-slate-400">Statut:</div>
                            <div class="col-span-2 text-sm text-slate-200" id="viewRecipeStatus"></div>
                        </div>
                    </div>
                    
                    <!-- Temps de préparation -->
                    <div>
                        <h4 class="text-lg font-medium text-cyan-400 mb-4">Temps de Préparation</h4>
                        
                        <div class="grid grid-cols-3 gap-2 mb-2">
                            <div class="col-span-1 text-sm font-medium text-slate-400">Préparation:</div>
                            <div class="col-span-2 text-sm text-slate-200" id="viewRecipePrepTime"></div>
                        </div>
                        
                        <div class="grid grid-cols-3 gap-2 mb-2">
                            <div class="col-span-1 text-sm font-medium text-slate-400">Cuisson:</div>
                            <div class="col-span-2 text-sm text-slate-200" id="viewRecipeCookTime"></div>
                        </div>
                        
                        <div class="grid grid-cols-3 gap-2 mb-2">
                            <div class="col-span-1 text-sm font-medium text-slate-400">Total estimé:</div>
                            <div class="col-span-2 text-sm text-slate-200" id="viewRecipeTotalTime"></div>
                        </div>
                        
                        <div class="grid grid-cols-3 gap-2 mb-2">
                            <div class="col-span-1 text-sm font-medium text-slate-400">Notes:</div>
                            <div class="col-span-2 text-sm text-slate-200" id="viewRecipeNotes"></div>
                        </div>
                    </div>
                </div>
                
                <hr class="my-6 border-slate-700">
                
                <!-- Ingrédients de la recette -->
                <div class="mb-6">
                    <h4 class="text-lg font-medium text-cyan-400 mb-4">Ingrédients de la Recette</h4>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-slate-700">
                            <thead class="bg-slate-800">
                                <tr>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-slate-400 uppercase tracking-wider">Ingrédient</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-slate-400 uppercase tracking-wider">Quantité</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-slate-400 uppercase tracking-wider">Unité</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-slate-400 uppercase tracking-wider">Coût unitaire</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-slate-400 uppercase tracking-wider">Coût total</th>
                                </tr>
                            </thead>
                            <tbody id="viewRecipeIngredients" class="divide-y divide-slate-800">
                                <tr>
                                    <td colspan="5" class="px-4 py-3 text-center text-slate-500">Aucun ingrédient</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- Coût total -->
                <div class="bg-blue-900/30 border border-blue-800 rounded-lg p-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 items-center">
                        <div class="text-slate-200 font-medium">
                            <i class="fas fa-coins text-blue-400 mr-2"></i>Coût total de la recette:
                        </div>
                        <div class="text-right">
                            <span class="text-2xl font-bold text-green-400" id="viewRecipeTotalCost">0.00 €</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="bg-slate-800 px-4 py-3 border-t border-slate-700 flex justify-end space-x-3">
                <button type="button" class="px-4 py-2 border border-slate-700 rounded-lg text-slate-300 hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-slate-500"
                        onclick="document.getElementById('viewRecipeModal').classList.add('hidden')">
                    <i class="fas fa-times mr-1"></i>Fermer
                </button>
                <button type="button" class="px-4 py-2 bg-green-600 rounded-lg text-white hover:bg-green-500 focus:outline-none focus:ring-2 focus:ring-green-500" id="editRecipeButton">
                    <i class="fas fa-edit mr-1"></i>Modifier
                </button>
            </div>
        </div>
    </div>
</div>