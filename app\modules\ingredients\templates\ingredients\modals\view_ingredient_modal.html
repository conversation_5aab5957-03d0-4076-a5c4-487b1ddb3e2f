<!-- Modal Vue Ingrédient -->
<div id="viewIngredientModal" class="hidden fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <!-- Fond noir transparent -->
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
            <div class="absolute inset-0 bg-gray-900 opacity-75" onclick="document.getElementById('viewIngredientModal').classList.add('hidden')"></div>
        </div>

        <!-- Contenu du modal -->
        <div class="inline-block align-bottom bg-slate-900 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full border border-slate-700">
            <div class="bg-cyan-600 px-4 py-3 border-b border-slate-700">
                <h3 class="text-lg leading-6 font-medium text-white">
                    <i class="fas fa-carrot mr-2"></i>Détails de l'Ingrédient
                </h3>
            </div>
            <div class="px-4 py-5 sm:p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Informations de base -->
                    <div>
                        <h4 class="text-lg font-medium text-cyan-400 mb-4">Informations Générales</h4>
                        
                        <div class="grid grid-cols-3 gap-2 mb-2">
                            <div class="col-span-1 text-sm font-medium text-slate-400">Nom:</div>
                            <div class="col-span-2 text-sm text-slate-200" id="viewIngredientName"></div>
                        </div>
                        
                        <div class="grid grid-cols-3 gap-2 mb-2">
                            <div class="col-span-1 text-sm font-medium text-slate-400">SKU:</div>
                            <div class="col-span-2 text-sm text-slate-200" id="viewIngredientSKU"></div>
                        </div>
                        
                        <div class="grid grid-cols-3 gap-2 mb-2">
                            <div class="col-span-1 text-sm font-medium text-slate-400">Catégorie:</div>
                            <div class="col-span-2 text-sm text-slate-200" id="viewIngredientCategory"></div>
                        </div>
                        
                        <div class="grid grid-cols-3 gap-2 mb-2">
                            <div class="col-span-1 text-sm font-medium text-slate-400">Description:</div>
                            <div class="col-span-2 text-sm text-slate-200" id="viewIngredientDescription"></div>
                        </div>
                    </div>
                    
                    <!-- Informations techniques -->
                    <div>
                        <h4 class="text-lg font-medium text-cyan-400 mb-4">Détails Techniques</h4>
                        
                        <div class="grid grid-cols-3 gap-2 mb-2">
                            <div class="col-span-1 text-sm font-medium text-slate-400">Unité:</div>
                            <div class="col-span-2 text-sm text-slate-200" id="viewIngredientUnit"></div>
                        </div>
                        
                        <div class="grid grid-cols-3 gap-2 mb-2">
                            <div class="col-span-1 text-sm font-medium text-slate-400">Coût/unité:</div>
                            <div class="col-span-2 text-sm text-slate-200" id="viewIngredientCost"></div>
                        </div>
                        
                        <div class="grid grid-cols-3 gap-2 mb-2">
                            <div class="col-span-1 text-sm font-medium text-slate-400">Stock actuel:</div>
                            <div class="col-span-2 text-sm text-slate-200" id="viewIngredientStock"></div>
                        </div>
                        
                        <div class="grid grid-cols-3 gap-2 mb-2">
                            <div class="col-span-1 text-sm font-medium text-slate-400">Stock minimum:</div>
                            <div class="col-span-2 text-sm text-slate-200" id="viewIngredientMinStock"></div>
                        </div>
                        
                        <div class="grid grid-cols-3 gap-2 mb-2">
                            <div class="col-span-1 text-sm font-medium text-slate-400">Statut:</div>
                            <div class="col-span-2 text-sm text-slate-200" id="viewIngredientStatus"></div>
                        </div>
                    </div>
                </div>
                
                <hr class="my-6 border-slate-700">
                
                <!-- Utilisation dans les Recettes -->
                <div>
                    <h4 class="text-lg font-medium text-cyan-400 mb-4">Utilisation dans les Recettes</h4>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-slate-700">
                            <thead class="bg-slate-800">
                                <tr>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-slate-400 uppercase tracking-wider">Recette</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-slate-400 uppercase tracking-wider">Quantité</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-slate-400 uppercase tracking-wider">Coût</th>
                                </tr>
                            </thead>
                            <tbody id="viewIngredientRecipes" class="divide-y divide-slate-800">
                                <tr>
                                    <td colspan="3" class="px-4 py-3 text-center text-slate-500">Aucune utilisation dans les recettes</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <div class="bg-slate-800 px-4 py-3 border-t border-slate-700 flex justify-end space-x-3">
                <button type="button" class="px-4 py-2 border border-slate-700 rounded-lg text-slate-300 hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-slate-500"
                        onclick="document.getElementById('viewIngredientModal').classList.add('hidden')">
                    <i class="fas fa-times mr-1"></i>Fermer
                </button>
                <button type="button" class="px-4 py-2 bg-cyan-600 rounded-lg text-white hover:bg-cyan-500 focus:outline-none focus:ring-2 focus:ring-cyan-500" id="editIngredientButton">
                    <i class="fas fa-edit mr-1"></i>Modifier
                </button>
            </div>
        </div>
    </div>
</div>