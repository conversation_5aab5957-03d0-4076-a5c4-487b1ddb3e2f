{% extends 'base.html' %}
{% block title %}Analytics - Dashboard{% endblock %}

{% block head %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/date-fns@2.29.3/index.min.js"></script>
<style>
    .analytics-page {
        background: linear-gradient(135deg, #4338ca 0%, #7c3aed 100%);
        min-height: 100vh;
        padding: 20px 0;
    }
    
    .analytics-content {
        background: white;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        padding: 30px;
        margin-bottom: 30px;
    }
    
    .chart-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        padding: 25px;
        margin-bottom: 25px;
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }
    
    .chart-card:hover {
        border-color: #4338ca;
        box-shadow: 0 8px 25px rgba(67, 56, 202, 0.15);
        transform: translateY(-2px);
    }
    
    .chart-title {
        color: #333;
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .chart-container {
        position: relative;
        height: 300px;
        margin-bottom: 20px;
    }
    
    .chart-container.large {
        height: 400px;
    }
    
    .metric-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 25px;
        text-align: center;
        margin-bottom: 20px;
        transition: transform 0.3s ease;
    }
    
    .metric-card:hover {
        transform: translateY(-5px);
    }
    
    .metric-number {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 10px;
    }
    
    .metric-label {
        font-size: 1rem;
        opacity: 0.9;
        text-transform: uppercase;
        letter-spacing: 1px;
    }
    
    .metric-change {
        font-size: 0.9rem;
        margin-top: 10px;
        padding: 5px 10px;
        border-radius: 15px;
        background: rgba(255,255,255,0.2);
    }
    
    .change-positive {
        color: #d4edda;
    }
    
    .change-negative {
        color: #f8d7da;
    }
    
    .filters-section {
        background: #f8f9ff;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 30px;
    }
    
    .filter-group {
        display: flex;
        gap: 15px;
        align-items: center;
        flex-wrap: wrap;
    }
    
    .filter-input {
        border: 1px solid #ddd;
        border-radius: 10px;
        padding: 10px 15px;
        background: white;
        font-size: 0.9rem;
    }
    
    .btn-analytics {
        background: linear-gradient(45deg, #4338ca, #7c3aed);
        border: none;
        color: white;
        padding: 10px 20px;
        border-radius: 20px;
        font-weight: 600;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
    }
    
    .btn-analytics:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(67, 56, 202, 0.4);
        color: white;
        text-decoration: none;
    }
    
    .kpi-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .trend-indicator {
        display: inline-flex;
        align-items: center;
        gap: 5px;
        font-size: 0.8rem;
    }
    
    .trend-up {
        color: #28a745;
    }
    
    .trend-down {
        color: #dc3545;
    }
    
    .data-table {
        background: white;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .data-table table {
        width: 100%;
        margin: 0;
    }
    
    .data-table th {
        background: #4338ca;
        color: white;
        padding: 15px;
        font-weight: 600;
        border: none;
    }
    
    .data-table td {
        padding: 12px 15px;
        border-bottom: 1px solid #f0f0f0;
    }
    
    .data-table tr:hover {
        background: #f8f9ff;
    }
    
    .loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255,255,255,0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 15px;
        z-index: 10;
    }
    
    .export-section {
        text-align: center;
        padding: 20px;
        background: #f8f9ff;
        border-radius: 15px;
        margin-top: 30px;
    }
</style>
{% endblock %}

{% block content %}
<div class="analytics-page">
    <div class="container-fluid">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="text-white text-center mb-4">
                    <i class="fas fa-chart-line me-3"></i>
                    Analytics Dashboard
                </h1>
                
                <div class="text-center">
                    <a href="{{ url_for('reports.index') }}" class="btn-analytics">
                        <i class="fas fa-arrow-left me-2"></i>Retour Dashboard
                    </a>
                    <button onclick="refreshData()" class="btn-analytics">
                        <i class="fas fa-sync-alt me-2"></i>Actualiser
                    </button>
                    <button onclick="exportAnalytics()" class="btn-analytics">
                        <i class="fas fa-download me-2"></i>Exporter
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Filtres -->
        <div class="analytics-content">
            <div class="filters-section">
                <h4 class="mb-3">
                    <i class="fas fa-filter me-2"></i>
                    Filtres d'Analyse
                </h4>
                
                <div class="filter-group">
                    <div>
                        <label class="form-label">Période</label>
                        <select id="periodFilter" class="filter-input">
                            <option value="7">7 derniers jours</option>
                            <option value="30" selected>30 derniers jours</option>
                            <option value="90">90 derniers jours</option>
                            <option value="365">1 an</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="form-label">Date de début</label>
                        <input type="date" id="dateFromFilter" class="filter-input">
                    </div>
                    
                    <div>
                        <label class="form-label">Date de fin</label>
                        <input type="date" id="dateToFilter" class="filter-input">
                    </div>
                    
                    <div style="align-self: end;">
                        <button onclick="applyFilters()" class="btn-analytics">
                            <i class="fas fa-search me-2"></i>Appliquer
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- KPIs principaux -->
        <div class="kpi-grid">
            <div class="metric-card">
                <div class="metric-number" id="totalRevenue">-</div>
                <div class="metric-label">Chiffre d'Affaires</div>
                <div class="metric-change">
                    <span class="trend-indicator trend-up">
                        <i class="fas fa-arrow-up"></i>
                        <span id="revenueChange">-</span>
                    </span>
                </div>
            </div>
            
            <div class="metric-card">
                <div class="metric-number" id="totalSales">-</div>
                <div class="metric-label">Nombre de Ventes</div>
                <div class="metric-change">
                    <span class="trend-indicator trend-up">
                        <i class="fas fa-arrow-up"></i>
                        <span id="salesChange">-</span>
                    </span>
                </div>
            </div>
            
            <div class="metric-card">
                <div class="metric-number" id="avgSale">-</div>
                <div class="metric-label">Panier Moyen</div>
                <div class="metric-change">
                    <span class="trend-indicator trend-up">
                        <i class="fas fa-arrow-up"></i>
                        <span id="avgSaleChange">-</span>
                    </span>
                </div>
            </div>
            
            <div class="metric-card">
                <div class="metric-number" id="totalCustomers">-</div>
                <div class="metric-label">Clients Actifs</div>
                <div class="metric-change">
                    <span class="trend-indicator trend-up">
                        <i class="fas fa-arrow-up"></i>
                        <span id="customersChange">-</span>
                    </span>
                </div>
            </div>
        </div>
        
        <!-- Graphiques principaux -->
        <div class="row">
            <!-- Tendance des ventes -->
            <div class="col-lg-8 mb-4">
                <div class="chart-card">
                    <h3 class="chart-title">
                        <i class="fas fa-chart-line"></i>
                        Évolution des Ventes
                    </h3>
                    <div class="chart-container large" id="salesTrendContainer">
                        <canvas id="salesTrendChart"></canvas>
                        <div class="loading-overlay" id="salesTrendLoading">
                            <div class="spinner-border text-primary" role="status"></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Répartition par catégorie -->
            <div class="col-lg-4 mb-4">
                <div class="chart-card">
                    <h3 class="chart-title">
                        <i class="fas fa-chart-pie"></i>
                        Ventes par Catégorie
                    </h3>
                    <div class="chart-container" id="categoryChartContainer">
                        <canvas id="categoryChart"></canvas>
                        <div class="loading-overlay" id="categoryLoading">
                            <div class="spinner-border text-primary" role="status"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <!-- Top produits -->
            <div class="col-lg-6 mb-4">
                <div class="chart-card">
                    <h3 class="chart-title">
                        <i class="fas fa-trophy"></i>
                        Top Produits
                    </h3>
                    <div class="chart-container" id="topProductsContainer">
                        <canvas id="topProductsChart"></canvas>
                        <div class="loading-overlay" id="topProductsLoading">
                            <div class="spinner-border text-primary" role="status"></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Performance mensuelle -->
            <div class="col-lg-6 mb-4">
                <div class="chart-card">
                    <h3 class="chart-title">
                        <i class="fas fa-calendar-alt"></i>
                        Performance Mensuelle
                    </h3>
                    <div class="chart-container" id="monthlyComparisonContainer">
                        <canvas id="monthlyComparisonChart"></canvas>
                        <div class="loading-overlay" id="monthlyLoading">
                            <div class="spinner-border text-primary" role="status"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Tableau de données détaillées -->
        <div class="analytics-content">
            <h3 class="mb-4">
                <i class="fas fa-table me-2"></i>
                Données Détaillées
            </h3>
            
            <div class="data-table">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Produit</th>
                            <th>Quantité Vendue</th>
                            <th>Chiffre d'Affaires</th>
                            <th>Marge</th>
                            <th>Évolution</th>
                        </tr>
                    </thead>
                    <tbody id="detailedDataTable">
                        <tr>
                            <td colspan="5" class="text-center">
                                <div class="spinner-border" role="status"></div>
                                <div>Chargement des données...</div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- Section d'export -->
        <div class="export-section">
            <h4 class="mb-3">
                <i class="fas fa-download me-2"></i>
                Exporter les Analytics
            </h4>
            <p class="text-muted mb-3">Téléchargez un rapport complet avec tous les graphiques et données.</p>
            
            <button onclick="exportToPDF()" class="btn-analytics me-2">
                <i class="fas fa-file-pdf me-2"></i>PDF
            </button>
            <button onclick="exportToExcel()" class="btn-analytics me-2">
                <i class="fas fa-file-excel me-2"></i>Excel
            </button>
            <button onclick="exportToCSV()" class="btn-analytics">
                <i class="fas fa-file-csv me-2"></i>CSV
            </button>
        </div>
    </div>
</div>

<script>
// Variables globales pour les graphiques
let salesTrendChart, categoryChart, topProductsChart, monthlyChart;

// Charger les données initiales
document.addEventListener('DOMContentLoaded', function() {
    initializeCharts();
    loadAnalyticsData();
});

function initializeCharts() {
    // Graphique de tendance des ventes
    const salesCtx = document.getElementById('salesTrendChart').getContext('2d');
    salesTrendChart = new Chart(salesCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: 'Ventes (€)',
                data: [],
                borderColor: '#4338ca',
                backgroundColor: 'rgba(67, 56, 202, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0,0,0,0.05)'
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        }
    });
    
    // Graphique des catégories
    const categoryCtx = document.getElementById('categoryChart').getContext('2d');
    categoryChart = new Chart(categoryCtx, {
        type: 'doughnut',
        data: {
            labels: [],
            datasets: [{
                data: [],
                backgroundColor: [
                    '#4338ca', '#7c3aed', '#db2777', '#dc2626',
                    '#ea580c', '#d97706', '#65a30d', '#16a34a',
                    '#059669', '#0891b2', '#0284c7', '#2563eb'
                ],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true
                    }
                }
            }
        }
    });
    
    // Graphique des top produits
    const topProductsCtx = document.getElementById('topProductsChart').getContext('2d');
    topProductsChart = new Chart(topProductsCtx, {
        type: 'bar',
        data: {
            labels: [],
            datasets: [{
                label: 'Revenus (€)',
                data: [],
                backgroundColor: 'rgba(67, 56, 202, 0.8)',
                borderColor: '#4338ca',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
    
    // Graphique de comparaison mensuelle
    const monthlyCtx = document.getElementById('monthlyComparisonChart').getContext('2d');
    monthlyChart = new Chart(monthlyCtx, {
        type: 'bar',
        data: {
            labels: ['Mois Précédent', 'Mois Actuel'],
            datasets: [{
                label: 'Revenus (€)',
                data: [0, 0],
                backgroundColor: ['rgba(124, 58, 237, 0.8)', 'rgba(67, 56, 202, 0.8)'],
                borderColor: ['#7c3aed', '#4338ca'],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

function loadAnalyticsData() {
    showLoadingStates();
    
    // Simuler le chargement des données (remplacer par de vrais appels API)
    setTimeout(() => {
        updateKPIs();
        updateSalesTrend();
        updateCategoryChart();
        updateTopProducts();
        updateMonthlyComparison();
        updateDetailedData();
        hideLoadingStates();
    }, 1000);
}

function showLoadingStates() {
    document.getElementById('salesTrendLoading').style.display = 'flex';
    document.getElementById('categoryLoading').style.display = 'flex';
    document.getElementById('topProductsLoading').style.display = 'flex';
    document.getElementById('monthlyLoading').style.display = 'flex';
}

function hideLoadingStates() {
    document.getElementById('salesTrendLoading').style.display = 'none';
    document.getElementById('categoryLoading').style.display = 'none';
    document.getElementById('topProductsLoading').style.display = 'none';
    document.getElementById('monthlyLoading').style.display = 'none';
}

function updateKPIs() {
    // Données d'exemple - remplacer par de vraies données
    document.getElementById('totalRevenue').textContent = '€45,230';
    document.getElementById('revenueChange').textContent = '+12.5%';
    
    document.getElementById('totalSales').textContent = '1,234';
    document.getElementById('salesChange').textContent = '****%';
    
    document.getElementById('avgSale').textContent = '€36.67';
    document.getElementById('avgSaleChange').textContent = '****%';
    
    document.getElementById('totalCustomers').textContent = '567';
    document.getElementById('customersChange').textContent = '+15.3%';
}

function updateSalesTrend() {
    const labels = ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'];
    const data = [1200, 1900, 800, 1500, 2200, 1800, 2400];
    
    salesTrendChart.data.labels = labels;
    salesTrendChart.data.datasets[0].data = data;
    salesTrendChart.update();
}

function updateCategoryChart() {
    const labels = ['Électronique', 'Vêtements', 'Alimentation', 'Livres', 'Sport'];
    const data = [30, 25, 20, 15, 10];
    
    categoryChart.data.labels = labels;
    categoryChart.data.datasets[0].data = data;
    categoryChart.update();
}

function updateTopProducts() {
    const labels = ['Produit A', 'Produit B', 'Produit C', 'Produit D', 'Produit E'];
    const data = [2500, 2100, 1800, 1500, 1200];
    
    topProductsChart.data.labels = labels;
    topProductsChart.data.datasets[0].data = data;
    topProductsChart.update();
}

function updateMonthlyComparison() {
    const data = [15000, 18000];
    
    monthlyChart.data.datasets[0].data = data;
    monthlyChart.update();
}

function updateDetailedData() {
    const tableBody = document.getElementById('detailedDataTable');
    const sampleData = [
        ['Produit A', '150', '€2,500', '€750', '+12%'],
        ['Produit B', '120', '€2,100', '€630', '+8%'],
        ['Produit C', '95', '€1,800', '€540', '-2%'],
        ['Produit D', '80', '€1,500', '€450', '+5%'],
        ['Produit E', '70', '€1,200', '€360', '+15%']
    ];
    
    tableBody.innerHTML = '';
    sampleData.forEach(row => {
        const tr = document.createElement('tr');
        row.forEach(cell => {
            const td = document.createElement('td');
            td.textContent = cell;
            tr.appendChild(td);
        });
        tableBody.appendChild(tr);
    });
}

function applyFilters() {
    const period = document.getElementById('periodFilter').value;
    const dateFrom = document.getElementById('dateFromFilter').value;
    const dateTo = document.getElementById('dateToFilter').value;
    
    console.log('Filtres appliqués:', { period, dateFrom, dateTo });
    
    // Recharger les données avec les nouveaux filtres
    loadAnalyticsData();
}

function refreshData() {
    loadAnalyticsData();
}

function exportAnalytics() {
    console.log('Export analytics...');
}

function exportToPDF() {
    console.log('Export PDF...');
}

function exportToExcel() {
    console.log('Export Excel...');
}

function exportToCSV() {
    console.log('Export CSV...');
}

// Actualisation automatique des données
setInterval(() => {
    if (document.visibilityState === 'visible') {
        loadAnalyticsData();
    }
}, 60000); // Toutes les minutes
</script>
{% endblock %}