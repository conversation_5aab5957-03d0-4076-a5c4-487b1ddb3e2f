{% extends 'base.html' %}
{% block title %}Modifier Variante - {{ variant.name }}{% endblock %}
{% block content %}
<div class="max-w-4xl mx-auto">
  <div class="flex items-center justify-between mb-6">
    <div>
      <h1 class="text-3xl font-bold">Modifier la Variante</h1>
      <p class="text-slate-400 mt-2">{{ variant.name }} - Produit : {{ variant.product.name }}</p>
    </div>
    <div class="flex space-x-3">
      <a href="{{ url_for('catalog.product_variants', id=variant.product_id_fk) }}" class="px-4 py-2 bg-slate-700 hover:bg-slate-600 text-white rounded-lg">
        Retour aux variantes
      </a>
      <a href="{{ url_for('catalog.index') }}" class="px-4 py-2 bg-slate-600 hover:bg-slate-500 text-white rounded-lg">
        Catalogue
      </a>
    </div>
  </div>

  <div class="rounded-xl bg-slate-900 border border-slate-700 p-6">
    <form method="post" class="space-y-6">
      {{ form.csrf_token }}
      
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div>
          <label class="block text-sm font-medium text-slate-300 mb-2">{{ form.name.label.text }}</label>
          {{ form.name(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent") }}
          {% if form.name.errors %}
            <div class="mt-1 text-sm text-red-400">
              {% for error in form.name.errors %}
                <p>{{ error }}</p>
              {% endfor %}
            </div>
          {% endif %}
        </div>
        
        <div>
          <label class="block text-sm font-medium text-slate-300 mb-2">{{ form.sku.label.text }}</label>
          {{ form.sku(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent") }}
          {% if form.sku.errors %}
            <div class="mt-1 text-sm text-red-400">
              {% for error in form.sku.errors %}
                <p>{{ error }}</p>
              {% endfor %}
            </div>
          {% endif %}
        </div>
      </div>
      
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div>
          <label class="block text-sm font-medium text-slate-300 mb-2">{{ form.price_cents.label.text }}</label>
          {{ form.price_cents(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent") }}
          <p class="mt-1 text-sm text-slate-400">Ex: 1500 pour 15.00 €</p>
          {% if form.price_cents.errors %}
            <div class="mt-1 text-sm text-red-400">
              {% for error in form.price_cents.errors %}
                <p>{{ error }}</p>
              {% endfor %}
            </div>
          {% endif %}
        </div>
        
        <div>
          <label class="block text-sm font-medium text-slate-300 mb-2">{{ form.cost_cents.label.text }}</label>
          {{ form.cost_cents(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent") }}
          <p class="mt-1 text-sm text-slate-400">Coût de production (optionnel)</p>
          {% if form.cost_cents.errors %}
            <div class="mt-1 text-sm text-red-400">
              {% for error in form.cost_cents.errors %}
                <p>{{ error }}</p>
              {% endfor %}
            </div>
          {% endif %}
        </div>
        
        <div>
          <label class="block text-sm font-medium text-slate-300 mb-2">{{ form.stock_qty.label.text }}</label>
          {{ form.stock_qty(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent") }}
          {% if form.stock_qty.errors %}
            <div class="mt-1 text-sm text-red-400">
              {% for error in form.stock_qty.errors %}
                <p>{{ error }}</p>
              {% endfor %}
            </div>
          {% endif %}
        </div>
      </div>
      
      <div class="flex justify-end space-x-3 pt-6 border-t border-slate-700">
        <a href="{{ url_for('catalog.product_variants', id=variant.product_id_fk) }}" class="px-6 py-3 bg-slate-700 hover:bg-slate-600 text-white rounded-lg transition-colors">
          Annuler
        </a>
        {{ form.submit(class="px-6 py-3 bg-cyan-600 hover:bg-cyan-500 text-white rounded-lg transition-colors font-medium") }}
      </div>
    </form>
  </div>
  
  <div class="rounded-xl bg-slate-900 border border-slate-700 p-6 mt-6">
    <h3 class="text-lg font-semibold mb-4">Informations de la variante</h3>
    <div class="grid grid-cols-2 gap-4 text-sm">
      <div>
        <span class="text-slate-400">Créée le:</span>
        <span class="text-white ml-2">{{ variant.created_at.strftime('%d/%m/%Y') if variant.created_at else 'N/A' }}</span>
      </div>
      <div>
        <span class="text-slate-400">Modifiée le:</span>
        <span class="text-white ml-2">{{ variant.updated_at.strftime('%d/%m/%Y') if variant.updated_at else 'N/A' }}</span>
      </div>
      <div>
        <span class="text-slate-400">Produit:</span>
        <span class="text-white ml-2">{{ variant.product.name }}</span>
      </div>
      <div>
        <span class="text-slate-400">SKU Produit:</span>
        <span class="text-white font-mono ml-2">{{ variant.product.sku }}</span>
      </div>
    </div>
  </div>
</div>
{% endblock %}