from __future__ import annotations

from datetime import datetime, date
from flask_wtf import FlaskForm
from wtforms import (
    StringField, TextAreaField, SelectField, IntegerField, DecimalField, 
    BooleanField, DateTimeField, HiddenField, PasswordField
)
from wtforms.validators import (
    DataRequired, Length, NumberRange, Optional, ValidationError, Email, Regexp
)

from app.modules.payments.models import (
    PaymentMethodType, PaymentStatus, RefundReason
)


class PaymentMethodForm(FlaskForm):
    """Formulaire pour les méthodes de paiement"""
    
    # Informations de base
    name = StringField(
        "Nom de la méthode",
        validators=[DataRequired(), Length(min=2, max=100)],
        render_kw={"placeholder": "Ex: Carte bancaire, Espèces, PayPal"}
    )
    
    method_type = SelectField(
        "Type de méthode",
        choices=[
            (PaymentMethodType.CASH.value, "Espèces"),
            (PaymentMethodType.CARD.value, "Carte bancaire"),
            (PaymentMethodType.CREDIT_CARD.value, "Carte de crédit"),
            (PaymentMethodType.DEBIT_CARD.value, "Carte de débit"),
            (PaymentMethodType.BANK_TRANSFER.value, "Virement bancaire"),
            (PaymentMethodType.CHECK.value, "Chèque"),
            (PaymentMethodType.GIFT_CARD.value, "Carte cadeau"),
            (PaymentMethodType.MOBILE_PAYMENT.value, "Paiement mobile"),
            (PaymentMethodType.CRYPTO.value, "Cryptomonnaie"),
            (PaymentMethodType.LOYALTY_POINTS.value, "Points de fidélité"),
            (PaymentMethodType.OTHER.value, "Autre")
        ],
        validators=[DataRequired()]
    )
    
    # Configuration
    is_active = BooleanField("Méthode active", default=True)
    is_default = BooleanField("Méthode par défaut")
    requires_authorization = BooleanField("Nécessite une autorisation")
    allow_partial_payments = BooleanField("Autorise les paiements partiels", default=True)
    
    # Frais et limites
    fee_percentage = DecimalField(
        "Frais (%)",
        validators=[Optional(), NumberRange(min=0, max=100)],
        default=0.0,
        render_kw={"placeholder": "Ex: 2.5", "step": "0.01"}
    )
    
    fee_fixed_cents = IntegerField(
        "Frais fixes (centimes)",
        validators=[Optional(), NumberRange(min=0)],
        default=0,
        render_kw={"placeholder": "Ex: 30 pour 0,30€"}
    )
    
    minimum_amount_cents = IntegerField(
        "Montant minimum (centimes)",
        validators=[Optional(), NumberRange(min=0)],
        default=0,
        render_kw={"placeholder": "Ex: 100 pour 1€"}
    )
    
    maximum_amount_cents = IntegerField(
        "Montant maximum (centimes)",
        validators=[Optional(), NumberRange(min=0)],
        render_kw={"placeholder": "Ex: 50000 pour 500€"}
    )
    
    # Configuration spécifique
    configuration = TextAreaField(
        "Configuration (JSON)",
        validators=[Optional()],
        render_kw={"placeholder": "Configuration JSON pour l'intégration", "rows": 4}
    )
    
    # Descriptions
    description = TextAreaField(
        "Description",
        validators=[Optional()],
        render_kw={"placeholder": "Description interne de la méthode", "rows": 3}
    )
    
    customer_instructions = TextAreaField(
        "Instructions client",
        validators=[Optional()],
        render_kw={"placeholder": "Instructions affichées au client", "rows": 3}
    )

    def validate_maximum_amount_cents(self, field):
        """Validation du montant maximum"""
        if field.data and self.minimum_amount_cents.data:
            if field.data <= self.minimum_amount_cents.data:
                raise ValidationError("Le montant maximum doit être supérieur au montant minimum")


class PaymentForm(FlaskForm):
    """Formulaire pour traiter un paiement"""
    
    # Référence de commande (cachée car généralement passée en paramètre)
    order_id_fk = HiddenField("ID Commande", validators=[DataRequired()])
    
    # Méthode de paiement
    payment_method_id_fk = SelectField(
        "Méthode de paiement",
        validators=[DataRequired()],
        coerce=int
    )
    
    # Montant
    amount_euros = DecimalField(
        "Montant (€)",
        validators=[DataRequired(), NumberRange(min=0.01, max=999999.99)],
        render_kw={"placeholder": "Ex: 25.50", "step": "0.01"}
    )
    
    # Informations de carte (optionnelles)
    card_holder_name = StringField(
        "Nom sur la carte",
        validators=[Optional(), Length(max=200)],
        render_kw={"placeholder": "Nom du porteur"}
    )
    
    card_last_four = StringField(
        "4 derniers chiffres",
        validators=[Optional(), Length(min=4, max=4), Regexp(r'^\d{4}$', message="4 chiffres requis")],
        render_kw={"placeholder": "1234"}
    )
    
    card_brand = SelectField(
        "Type de carte",
        choices=[
            ("", "Sélectionner"),
            ("visa", "Visa"),
            ("mastercard", "MasterCard"),
            ("amex", "American Express"),
            ("discover", "Discover"),
            ("other", "Autre")
        ],
        validators=[Optional()]
    )
    
    # Références externes
    external_transaction_id = StringField(
        "ID transaction externe",
        validators=[Optional(), Length(max=255)],
        render_kw={"placeholder": "ID de la passerelle de paiement"}
    )
    
    authorization_code = StringField(
        "Code d'autorisation",
        validators=[Optional(), Length(max=100)],
        render_kw={"placeholder": "Code d'autorisation"}
    )
    
    # Notes
    notes = TextAreaField(
        "Notes",
        validators=[Optional()],
        render_kw={"placeholder": "Notes sur le paiement", "rows": 3}
    )

    def __init__(self, business_id=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        if business_id:
            # Charger les méthodes de paiement actives
            from app.modules.payments.models import PaymentMethod
            methods = PaymentMethod.query.filter_by(
                business_id_fk=business_id, 
                is_active=True
            ).order_by(PaymentMethod.name).all()
            
            self.payment_method_id_fk.choices = [
                (method.id, method.name) for method in methods
            ]


class QuickPaymentForm(FlaskForm):
    """Formulaire de paiement rapide (pour POS)"""
    
    order_id_fk = HiddenField("ID Commande", validators=[DataRequired()])
    payment_method_id_fk = HiddenField("ID Méthode", validators=[DataRequired()])
    amount_cents = HiddenField("Montant", validators=[DataRequired()])
    
    # Champ pour espèces - montant donné
    cash_given_cents = IntegerField(
        "Montant donné (centimes)",
        validators=[Optional(), NumberRange(min=0)],
        render_kw={"placeholder": "Montant donné par le client"}
    )
    
    # Notes rapides
    notes = StringField(
        "Notes",
        validators=[Optional(), Length(max=200)],
        render_kw={"placeholder": "Notes rapides"}
    )


class RefundForm(FlaskForm):
    """Formulaire de remboursement"""
    
    # ID du paiement à rembourser (caché)
    payment_id_fk = HiddenField("ID Paiement", validators=[DataRequired()])
    
    # Montant à rembourser
    amount_euros = DecimalField(
        "Montant à rembourser (€)",
        validators=[DataRequired(), NumberRange(min=0.01, max=999999.99)],
        render_kw={"placeholder": "Ex: 25.50", "step": "0.01"}
    )
    
    # Raison du remboursement
    reason = SelectField(
        "Raison du remboursement",
        choices=[
            (RefundReason.CUSTOMER_REQUEST.value, "Demande client"),
            (RefundReason.PRODUCT_DEFECT.value, "Défaut produit"),
            (RefundReason.SERVICE_ISSUE.value, "Problème de service"),
            (RefundReason.DUPLICATE_PAYMENT.value, "Paiement en double"),
            (RefundReason.FRAUD.value, "Fraude"),
            (RefundReason.TECHNICAL_ERROR.value, "Erreur technique"),
            (RefundReason.OTHER.value, "Autre")
        ],
        validators=[DataRequired()]
    )
    
    # Description détaillée
    reason_description = TextAreaField(
        "Description détaillée",
        validators=[Optional()],
        render_kw={"placeholder": "Expliquez la raison du remboursement", "rows": 4}
    )
    
    # Notes internes
    notes = TextAreaField(
        "Notes internes",
        validators=[Optional()],
        render_kw={"placeholder": "Notes pour l'équipe", "rows": 3}
    )


class ReceiptForm(FlaskForm):
    """Formulaire pour générer/modifier un reçu"""
    
    # ID de commande (caché)
    order_id_fk = HiddenField("ID Commande", validators=[DataRequired()])
    
    # Type de reçu
    receipt_type = SelectField(
        "Type de reçu",
        choices=[
            ("payment", "Paiement"),
            ("refund", "Remboursement"),
            ("exchange", "Échange")
        ],
        default="payment",
        validators=[DataRequired()]
    )
    
    # Informations client
    customer_name = StringField(
        "Nom du client",
        validators=[Optional(), Length(max=200)],
        render_kw={"placeholder": "Nom du client"}
    )
    
    customer_email = StringField(
        "Email du client",
        validators=[Optional(), Email(), Length(max=200)],
        render_kw={"placeholder": "<EMAIL>"}
    )
    
    customer_phone = StringField(
        "Téléphone du client",
        validators=[Optional(), Length(max=50)],
        render_kw={"placeholder": "06 12 34 56 78"}
    )
    
    # Options d'envoi
    email_receipt = BooleanField("Envoyer par email")
    print_receipt = BooleanField("Imprimer", default=True)
    
    # Notes
    notes = TextAreaField(
        "Notes",
        validators=[Optional()],
        render_kw={"placeholder": "Notes sur le reçu", "rows": 2}
    )


class CashSessionForm(FlaskForm):
    """Formulaire pour les sessions de caisse"""
    
    # Montant d'ouverture
    opening_balance_euros = DecimalField(
        "Solde d'ouverture (€)",
        validators=[DataRequired(), NumberRange(min=0)],
        default=0.0,
        render_kw={"placeholder": "Ex: 100.00", "step": "0.01"}
    )
    
    # Notes d'ouverture
    opening_notes = TextAreaField(
        "Notes d'ouverture",
        validators=[Optional()],
        render_kw={"placeholder": "Notes sur l'ouverture de caisse", "rows": 3}
    )


class CashSessionCloseForm(FlaskForm):
    """Formulaire de fermeture de session de caisse"""
    
    # ID de la session (caché)
    session_id = HiddenField("ID Session", validators=[DataRequired()])
    
    # Solde de fermeture compté
    closing_balance_euros = DecimalField(
        "Solde compté (€)",
        validators=[DataRequired(), NumberRange(min=0)],
        render_kw={"placeholder": "Montant réellement compté", "step": "0.01"}
    )
    
    # Notes de fermeture
    closing_notes = TextAreaField(
        "Notes de fermeture",
        validators=[Optional()],
        render_kw={"placeholder": "Notes sur la fermeture, écarts constatés, etc.", "rows": 4}
    )


class CashMovementForm(FlaskForm):
    """Formulaire pour les mouvements de caisse"""
    
    # Type de mouvement
    movement_type = SelectField(
        "Type de mouvement",
        choices=[
            ("deposit", "Dépôt"),
            ("withdrawal", "Retrait"),
            ("adjustment", "Ajustement"),
            ("refund", "Remboursement"),
            ("other", "Autre")
        ],
        validators=[DataRequired()]
    )
    
    # Direction
    direction = SelectField(
        "Direction",
        choices=[
            ("in", "Entrée"),
            ("out", "Sortie")
        ],
        validators=[DataRequired()]
    )
    
    # Montant
    amount_euros = DecimalField(
        "Montant (€)",
        validators=[DataRequired(), NumberRange(min=0.01, max=999999.99)],
        render_kw={"placeholder": "Ex: 50.00", "step": "0.01"}
    )
    
    # Raison
    reason = StringField(
        "Raison",
        validators=[DataRequired(), Length(min=2, max=200)],
        render_kw={"placeholder": "Ex: Retrait pour fond de caisse"}
    )
    
    # Description détaillée
    description = TextAreaField(
        "Description",
        validators=[Optional()],
        render_kw={"placeholder": "Description détaillée du mouvement", "rows": 3}
    )


class PaymentSearchForm(FlaskForm):
    """Formulaire de recherche de paiements"""
    
    # Recherche générale
    search_query = StringField(
        "Rechercher",
        validators=[Optional(), Length(max=200)],
        render_kw={"placeholder": "Numéro de reçu, ID transaction..."}
    )
    
    # Filtres
    payment_method_id = SelectField(
        "Méthode de paiement",
        choices=[("", "Toutes")],
        validators=[Optional()],
        coerce=int
    )
    
    status = SelectField(
        "Statut",
        choices=[
            ("", "Tous"),
            (PaymentStatus.PENDING.value, "En attente"),
            (PaymentStatus.PROCESSING.value, "En cours"),
            (PaymentStatus.COMPLETED.value, "Complété"),
            (PaymentStatus.FAILED.value, "Échec"),
            (PaymentStatus.REFUNDED.value, "Remboursé"),
            (PaymentStatus.CANCELLED.value, "Annulé")
        ],
        validators=[Optional()]
    )
    
    # Filtres de montant
    amount_min = DecimalField(
        "Montant min (€)",
        validators=[Optional(), NumberRange(min=0)],
        render_kw={"placeholder": "0.00", "step": "0.01"}
    )
    
    amount_max = DecimalField(
        "Montant max (€)",
        validators=[Optional(), NumberRange(min=0)],
        render_kw={"placeholder": "1000.00", "step": "0.01"}
    )
    
    # Filtres de dates
    date_from = DateTimeField(
        "Du",
        validators=[Optional()],
        format='%Y-%m-%d'
    )
    
    date_to = DateTimeField(
        "Au",
        validators=[Optional()],
        format='%Y-%m-%d'
    )

    def __init__(self, business_id=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        if business_id:
            # Charger les méthodes de paiement pour le filtre
            from app.modules.payments.models import PaymentMethod
            methods = PaymentMethod.query.filter_by(
                business_id_fk=business_id
            ).order_by(PaymentMethod.name).all()
            
            choices = [("", "Toutes")]
            choices.extend([(method.id, method.name) for method in methods])
            self.payment_method_id.choices = choices

    def validate_amount_max(self, field):
        """Validation des montants"""
        if field.data and self.amount_min.data:
            if field.data <= self.amount_min.data:
                raise ValidationError("Le montant maximum doit être supérieur au montant minimum")
    
    def validate_date_to(self, field):
        """Validation des dates"""
        if field.data and self.date_from.data:
            if field.data <= self.date_from.data:
                raise ValidationError("La date de fin doit être postérieure à la date de début")


class PaymentReportForm(FlaskForm):
    """Formulaire pour les rapports de paiement"""
    
    # Période
    period_type = SelectField(
        "Période",
        choices=[
            ("today", "Aujourd'hui"),
            ("yesterday", "Hier"),
            ("this_week", "Cette semaine"),
            ("last_week", "Semaine dernière"),
            ("this_month", "Ce mois"),
            ("last_month", "Mois dernier"),
            ("custom", "Période personnalisée")
        ],
        default="today",
        validators=[DataRequired()]
    )
    
    # Dates personnalisées
    date_from = DateTimeField(
        "Date de début",
        validators=[Optional()],
        format='%Y-%m-%d'
    )
    
    date_to = DateTimeField(
        "Date de fin",
        validators=[Optional()],
        format='%Y-%m-%d'
    )
    
    # Groupement
    group_by = SelectField(
        "Grouper par",
        choices=[
            ("day", "Jour"),
            ("week", "Semaine"),
            ("month", "Mois"),
            ("payment_method", "Méthode de paiement"),
            ("status", "Statut")
        ],
        default="day",
        validators=[DataRequired()]
    )
    
    # Métriques à inclure
    include_refunds = BooleanField("Inclure les remboursements", default=True)
    include_fees = BooleanField("Inclure les frais", default=True)
    include_failed = BooleanField("Inclure les échecs", default=False)
    
    # Format d'export
    export_format = SelectField(
        "Format d'export",
        choices=[
            ("", "Affichage seulement"),
            ("csv", "CSV"),
            ("excel", "Excel"),
            ("pdf", "PDF")
        ],
        validators=[Optional()]
    )