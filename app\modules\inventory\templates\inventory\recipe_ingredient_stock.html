{% extends 'base.html' %}
{% block title %}Stock Ingrédients - {{ recipe.name }}{% endblock %}
{% block content %}
<div class="max-w-4xl mx-auto">
  <div class="flex items-center justify-between mb-6">
    <div>
      <h1 class="text-3xl font-bold">Stock des Ingrédients</h1>
      <p class="text-slate-400 mt-2">Recette : {{ recipe.name }}</p>
    </div>
    <div class="flex space-x-3">
      <a href="{{ url_for('inventory.recipe_stocks') }}" class="px-4 py-2 bg-slate-700 hover:bg-slate-600 text-white rounded-lg">
        Retour aux Recettes
      </a>
      <a href="{{ url_for('inventory.index') }}" class="px-4 py-2 bg-slate-600 hover:bg-slate-500 text-white rounded-lg">
        Inventaire
      </a>
    </div>
  </div>

  <div class="grid grid-cols-12 gap-6">
    <!-- Formulaire de mise à jour du stock -->
    <div class="col-span-12 lg:col-span-5">
      <div class="rounded-xl bg-slate-900 border border-slate-700 p-6">
        <h2 class="text-lg font-semibold mb-4">Mettre à jour le stock</h2>
        <form method="post" class="space-y-4">
          {{ form.csrf_token }}
          
          <div>
            <label class="block text-sm font-medium text-slate-300 mb-2">{{ form.ingredient_id.label.text }}</label>
            {{ form.ingredient_id(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent") }}
          </div>
          
          <div>
            <label class="block text-sm font-medium text-slate-300 mb-2">{{ form.warehouse_id.label.text }}</label>
            {{ form.warehouse_id(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent") }}
          </div>
          
          <div class="grid grid-cols-2 gap-3">
            <div>
              <label class="block text-sm font-medium text-slate-300 mb-2">{{ form.current_quantity.label.text }}</label>
              {{ form.current_quantity(step="0.01", class="w-full bg-slate-800 border border-slate-700 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent") }}
            </div>
            <div>
              <label class="block text-sm font-medium text-slate-300 mb-2">{{ form.min_quantity.label.text }}</label>
              {{ form.min_quantity(step="0.01", class="w-full bg-slate-800 border border-slate-700 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent") }}
            </div>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-slate-300 mb-2">{{ form.unit.label.text }}</label>
            {{ form.unit(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent") }}
          </div>
          
          <div>
            <label class="block text-sm font-medium text-slate-300 mb-2">{{ form.expiry_date.label.text }}</label>
            {{ form.expiry_date(type="date", class="w-full bg-slate-800 border border-slate-700 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent") }}
          </div>
          
          <div>
            <label class="block text-sm font-medium text-slate-300 mb-2">{{ form.notes.label.text }}</label>
            {{ form.notes(rows="3", class="w-full bg-slate-800 border border-slate-700 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent") }}
          </div>
          
          {{ form.submit(class="w-full bg-cyan-600 hover:bg-cyan-500 text-white font-medium py-3 px-4 rounded-lg transition-colors") }}
        </form>
      </div>
    </div>

    <!-- Liste des ingrédients de la recette -->
    <div class="col-span-12 lg:col-span-7">
      <div class="rounded-xl bg-slate-900 border border-slate-700 p-6">
        <h2 class="text-lg font-semibold mb-4">Ingrédients de la recette</h2>
        
        {% if recipe_items %}
          <div class="space-y-3">
            {% for item in recipe_items %}
            <div class="bg-slate-800 border border-slate-700 rounded-lg p-4">
              <div class="flex items-center justify-between">
                <div class="flex-1">
                  <h3 class="font-semibold">{{ item.ingredient.name }}</h3>
                  <p class="text-sm text-slate-400">Requis: {{ item.quantity }} {{ item.unit }}</p>
                </div>
                <div class="text-right">
                  <div class="text-sm text-slate-300">Stock actuel</div>
                  <div class="text-lg font-bold text-green-400" id="stock-{{ item.ingredient_id_fk }}">
                    Loading...
                  </div>
                </div>
              </div>
              
              <!-- Indicateur de suffisance du stock -->
              <div class="mt-3">
                <div class="flex items-center justify-between text-sm">
                  <span class="text-slate-400">Suffisant pour la recette :</span>
                  <span id="sufficient-{{ item.ingredient_id_fk }}" class="px-2 py-1 rounded text-xs">
                    Vérification...
                  </span>
                </div>
              </div>
            </div>
            {% endfor %}
          </div>
        {% else %}
          <div class="text-center py-8">
            <div class="text-6xl mb-4">📦</div>
            <h3 class="text-xl font-semibold text-slate-300 mb-2">Aucun ingrédient</h3>
            <p class="text-slate-400">Cette recette n'a pas encore d'ingrédients définis.</p>
          </div>
        {% endif %}
      </div>
      
      <!-- Informations sur la recette -->
      <div class="rounded-xl bg-slate-900 border border-slate-700 p-6 mt-6">
        <h3 class="text-lg font-semibold mb-4">Informations de la recette</h3>
        <div class="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span class="text-slate-400">Nom:</span>
            <span class="text-white ml-2">{{ recipe.name }}</span>
          </div>
          <div>
            <span class="text-slate-400">Portions:</span>
            <span class="text-white ml-2">{{ recipe.servings or 'Non défini' }}</span>
          </div>
          <div>
            <span class="text-slate-400">Temps de préparation:</span>
            <span class="text-white ml-2">{{ recipe.preparation_time_minutes or 'Non défini' }} min</span>
          </div>
          <div>
            <span class="text-slate-400">Temps de cuisson:</span>
            <span class="text-white ml-2">{{ recipe.cooking_time_minutes or 'Non défini' }} min</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Charger le stock pour chaque ingrédient
    {% for item in recipe_items %}
    loadIngredientStock({{ item.ingredient_id_fk }}, {{ item.quantity }});
    {% endfor %}
});

function loadIngredientStock(ingredientId, requiredQuantity) {
    // Simuler le chargement du stock (à remplacer par un appel API réel)
    setTimeout(() => {
        const stockElement = document.getElementById(`stock-${ingredientId}`);
        const sufficientElement = document.getElementById(`sufficient-${ingredientId}`);
        
        // Stock simulé (à remplacer par une vraie API)
        const currentStock = Math.floor(Math.random() * 200) + 50;
        
        stockElement.textContent = currentStock + ' unités';
        
        if (currentStock >= requiredQuantity) {
            sufficientElement.textContent = 'Oui';
            sufficientElement.className = 'px-2 py-1 rounded text-xs bg-green-600 text-white';
        } else {
            sufficientElement.textContent = 'Non';
            sufficientElement.className = 'px-2 py-1 rounded text-xs bg-red-600 text-white';
        }
    }, Math.random() * 1000 + 500);
}
</script>
{% endblock %}