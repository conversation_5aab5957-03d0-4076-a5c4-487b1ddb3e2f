{% extends 'base.html' %}
{% block title %}Rapports Personnel{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto">
  <!-- En-tête -->
  <div class="mb-6">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold">📊 Rapports du Personnel</h1>
        <p class="text-slate-400 mt-2">Analyses et statistiques de votre équipe</p>
      </div>
      <div class="flex space-x-3">
        <a href="{{ url_for('staff.index') }}" class="bg-slate-700 hover:bg-slate-600 px-4 py-2 rounded-lg">
          ← Retour au personnel
        </a>
        <button onclick="exportReport()" class="bg-green-600 hover:bg-green-500 px-4 py-2 rounded-lg">
          📥 Exporter
        </button>
      </div>
    </div>
  </div>

  <!-- Métriques principales -->
  <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
    <div class="rounded-xl bg-slate-900 border border-slate-700 p-6">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-slate-400 text-sm">Total Employés</p>
          <h3 class="text-2xl font-bold text-cyan-400">{{ total_employees }}</h3>
        </div>
        <div class="text-3xl">👥</div>
      </div>
      <div class="mt-2 text-sm">
        <span class="text-green-400">{{ active_employees }}</span> actifs, 
        <span class="text-red-400">{{ total_employees - active_employees }}</span> inactifs
      </div>
    </div>
    
    <div class="rounded-xl bg-slate-900 border border-slate-700 p-6">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-slate-400 text-sm">Shifts cette semaine</p>
          <h3 class="text-2xl font-bold text-green-400">{{ week_shifts }}</h3>
        </div>
        <div class="text-3xl">📅</div>
      </div>
      <div class="mt-2 text-sm text-slate-400">
        Planning en cours
      </div>
    </div>
    
    <div class="rounded-xl bg-slate-900 border border-slate-700 p-6">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-slate-400 text-sm">Heures totales</p>
          <h3 class="text-2xl font-bold text-purple-400" id="total-hours-week">-</h3>
        </div>
        <div class="text-3xl">⏰</div>
      </div>
      <div class="mt-2 text-sm text-slate-400">
        Cette semaine
      </div>
    </div>
    
    <div class="rounded-xl bg-slate-900 border border-slate-700 p-6">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-slate-400 text-sm">Coût salarial</p>
          <h3 class="text-2xl font-bold text-yellow-400" id="estimated-cost">-</h3>
        </div>
        <div class="text-3xl">💰</div>
      </div>
      <div class="mt-2 text-sm text-slate-400">
        Estimation semaine
      </div>
    </div>
  </div>

  <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- Répartition par département -->
    <div class="rounded-xl bg-slate-900 border border-slate-700 p-6">
      <h2 class="text-xl font-semibold mb-4">🏢 Répartition par département</h2>
      <div class="space-y-4">
        {% if dept_counts %}
          {% for dept, count in dept_counts %}
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <div class="w-4 h-4 bg-cyan-500 rounded"></div>
              <span class="font-medium">{{ dept or 'Non assigné' }}</span>
            </div>
            <div class="flex items-center space-x-2">
              <span class="text-lg font-semibold">{{ count[0] }}</span>
              <span class="text-sm text-slate-400">employés</span>
            </div>
          </div>
          <div class="w-full bg-slate-700 rounded-full h-2">
            <div class="bg-cyan-500 h-2 rounded-full" style="width: {{ (count[0] / total_employees * 100) if total_employees > 0 else 0 }}%"></div>
          </div>
          {% endfor %}
        {% else %}
          <div class="text-center py-6 text-slate-400">
            <div class="text-4xl mb-2">🏢</div>
            <p>Aucun département défini</p>
          </div>
        {% endif %}
      </div>
    </div>

    <!-- Tendances des heures -->
    <div class="rounded-xl bg-slate-900 border border-slate-700 p-6">
      <h2 class="text-xl font-semibold mb-4">📈 Tendances des heures</h2>
      <div class="space-y-4">
        <!-- Graphique simplifié -->
        <div class="relative h-32 bg-slate-800 rounded border">
          <canvas id="hoursChart" width="100%" height="100%"></canvas>
        </div>
        <div class="grid grid-cols-3 gap-4 text-sm">
          <div class="text-center">
            <div class="font-semibold text-blue-400">Lun-Mer</div>
            <div class="text-slate-400">125h</div>
          </div>
          <div class="text-center">
            <div class="font-semibold text-green-400">Jeu-Ven</div>
            <div class="text-slate-400">140h</div>
          </div>
          <div class="text-center">
            <div class="font-semibold text-purple-400">Week-end</div>
            <div class="text-slate-400">98h</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Performance par employé -->
    <div class="rounded-xl bg-slate-900 border border-slate-700 p-6">
      <h2 class="text-xl font-semibold mb-4">🌟 Top employés (heures)</h2>
      <div class="space-y-3">
        <div class="flex items-center justify-between p-3 bg-slate-800 rounded">
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center text-sm font-bold">1</div>
            <div>
              <div class="font-medium">Dupont Jean</div>
              <div class="text-sm text-slate-400">Chef de cuisine</div>
            </div>
          </div>
          <div class="text-right">
            <div class="font-semibold">42h</div>
            <div class="text-sm text-slate-400">cette semaine</div>
          </div>
        </div>
        
        <div class="flex items-center justify-between p-3 bg-slate-800 rounded">
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-gray-400 rounded-full flex items-center justify-center text-sm font-bold">2</div>
            <div>
              <div class="font-medium">Martin Sophie</div>
              <div class="text-sm text-slate-400">Serveuse</div>
            </div>
          </div>
          <div class="text-right">
            <div class="font-semibold">38h</div>
            <div class="text-sm text-slate-400">cette semaine</div>
          </div>
        </div>
        
        <div class="flex items-center justify-between p-3 bg-slate-800 rounded">
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-orange-600 rounded-full flex items-center justify-center text-sm font-bold">3</div>
            <div>
              <div class="font-medium">Bernard Lucas</div>
              <div class="text-sm text-slate-400">Commis</div>
            </div>
          </div>
          <div class="text-right">
            <div class="font-semibold">35h</div>
            <div class="text-sm text-slate-400">cette semaine</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Pointages récents -->
    <div class="rounded-xl bg-slate-900 border border-slate-700 p-6">
      <h2 class="text-xl font-semibold mb-4">⏰ Activité récente</h2>
      <div class="space-y-3 max-h-64 overflow-y-auto">
        <div class="flex items-center justify-between p-2 bg-slate-800 rounded">
          <div class="flex items-center space-x-3">
            <div class="w-2 h-2 bg-green-400 rounded-full"></div>
            <div>
              <div class="text-sm font-medium">Dupont Jean</div>
              <div class="text-xs text-slate-400">Arrivée</div>
            </div>
          </div>
          <div class="text-xs text-slate-400">08:00</div>
        </div>
        
        <div class="flex items-center justify-between p-2 bg-slate-800 rounded">
          <div class="flex items-center space-x-3">
            <div class="w-2 h-2 bg-blue-400 rounded-full"></div>
            <div>
              <div class="text-sm font-medium">Martin Sophie</div>
              <div class="text-xs text-slate-400">Arrivée</div>
            </div>
          </div>
          <div class="text-xs text-slate-400">08:15</div>
        </div>
        
        <div class="flex items-center justify-between p-2 bg-slate-800 rounded">
          <div class="flex items-center space-x-3">
            <div class="w-2 h-2 bg-yellow-400 rounded-full"></div>
            <div>
              <div class="text-sm font-medium">Bernard Lucas</div>
              <div class="text-xs text-slate-400">Pause</div>
            </div>
          </div>
          <div class="text-xs text-slate-400">10:30</div>
        </div>
        
        <div class="flex items-center justify-between p-2 bg-slate-800 rounded">
          <div class="flex items-center space-x-3">
            <div class="w-2 h-2 bg-red-400 rounded-full"></div>
            <div>
              <div class="text-sm font-medium">Leblanc Marie</div>
              <div class="text-xs text-slate-400">Départ</div>
            </div>
          </div>
          <div class="text-xs text-slate-400">17:00</div>
        </div>
      </div>
      
      <div class="mt-4 pt-4 border-t border-slate-700">
        <a href="{{ url_for('staff.timeclock') }}" class="text-cyan-400 hover:text-cyan-300 text-sm">
          Voir tous les pointages →
        </a>
      </div>
    </div>
  </div>

  <!-- Rapports détaillés -->
  <div class="mt-6 rounded-xl bg-slate-900 border border-slate-700 p-6">
    <h2 class="text-xl font-semibold mb-6">📋 Rapports détaillés</h2>
    
    <!-- Filtres -->
    <div class="mb-6 flex flex-wrap gap-4">
      <select class="bg-slate-800 border border-slate-700 rounded-lg px-3 py-2" id="period-filter">
        <option value="week">Cette semaine</option>
        <option value="month">Ce mois</option>
        <option value="quarter">Ce trimestre</option>
        <option value="year">Cette année</option>
      </select>
      
      <select class="bg-slate-800 border border-slate-700 rounded-lg px-3 py-2" id="department-filter">
        <option value="">Tous les départements</option>
        {% for dept, count in dept_counts %}
        <option value="{{ dept or 'none' }}">{{ dept or 'Non assigné' }}</option>
        {% endfor %}
      </select>
      
      <button onclick="generateReport()" class="bg-cyan-600 hover:bg-cyan-500 px-4 py-2 rounded-lg">
        Générer rapport
      </button>
      
      <button onclick="scheduleReport()" class="bg-purple-600 hover:bg-purple-500 px-4 py-2 rounded-lg">
        📅 Programmer
      </button>
    </div>
    
    <!-- Types de rapports -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <div class="bg-slate-800 border border-slate-700 rounded-lg p-4">
        <h3 class="font-semibold mb-2">📊 Rapport d'heures</h3>
        <p class="text-sm text-slate-400 mb-4">Détail des heures travaillées par employé et département</p>
        <button onclick="downloadReport('hours')" class="w-full bg-blue-600 hover:bg-blue-500 px-3 py-2 rounded text-sm">
          Télécharger PDF
        </button>
      </div>
      
      <div class="bg-slate-800 border border-slate-700 rounded-lg p-4">
        <h3 class="font-semibold mb-2">💰 Rapport de paie</h3>
        <p class="text-sm text-slate-400 mb-4">Calcul des salaires et coûts par période</p>
        <button onclick="downloadReport('payroll')" class="w-full bg-green-600 hover:bg-green-500 px-3 py-2 rounded text-sm">
          Télécharger Excel
        </button>
      </div>
      
      <div class="bg-slate-800 border border-slate-700 rounded-lg p-4">
        <h3 class="font-semibold mb-2">📈 Rapport de performance</h3>
        <p class="text-sm text-slate-400 mb-4">Analyse des performances et présence</p>
        <button onclick="downloadReport('performance')" class="w-full bg-purple-600 hover:bg-purple-500 px-3 py-2 rounded text-sm">
          Télécharger PDF
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Modal de programmation -->
<div id="scheduleModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
  <div class="bg-slate-900 border border-slate-700 rounded-xl p-6 max-w-md w-full mx-4">
    <h3 class="text-lg font-semibold mb-4">📅 Programmer un rapport</h3>
    <form class="space-y-4">
      <div>
        <label class="block text-sm font-medium text-slate-300 mb-2">Type de rapport</label>
        <select class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2">
          <option value="hours">Rapport d'heures</option>
          <option value="payroll">Rapport de paie</option>
          <option value="performance">Rapport de performance</option>
        </select>
      </div>
      
      <div>
        <label class="block text-sm font-medium text-slate-300 mb-2">Fréquence</label>
        <select class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2">
          <option value="weekly">Hebdomadaire</option>
          <option value="monthly">Mensuel</option>
          <option value="quarterly">Trimestriel</option>
        </select>
      </div>
      
      <div>
        <label class="block text-sm font-medium text-slate-300 mb-2">Email de destination</label>
        <input type="email" class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2" placeholder="<EMAIL>">
      </div>
      
      <div class="flex justify-end space-x-3 pt-4">
        <button type="button" onclick="closeScheduleModal()" class="px-4 py-2 bg-slate-700 hover:bg-slate-600 rounded-lg">
          Annuler
        </button>
        <button type="submit" class="px-4 py-2 bg-cyan-600 hover:bg-cyan-500 rounded-lg">
          Programmer
        </button>
      </div>
    </form>
  </div>
</div>

<script>
// Génération de rapports
function generateReport() {
  const period = document.getElementById('period-filter').value;
  const department = document.getElementById('department-filter').value;
  
  // Simuler la génération
  alert(`Génération du rapport pour la période: ${period}, département: ${department || 'tous'}`);
}

function downloadReport(type) {
  // Simuler le téléchargement
  alert(`Téléchargement du rapport ${type} en cours...`);
}

function exportReport() {
  // Simuler l'export
  alert('Export des données en cours...');
}

// Modal de programmation
function scheduleReport() {
  document.getElementById('scheduleModal').classList.remove('hidden');
}

function closeScheduleModal() {
  document.getElementById('scheduleModal').classList.add('hidden');
}

// Calculs simples pour les métriques
function calculateMetrics() {
  // Ces calculs seraient normalement faits côté serveur
  const totalHours = {{ week_shifts }} * 8; // Estimation
  const averageHourlyRate = 15; // €/heure
  const estimatedCost = totalHours * averageHourlyRate;
  
  document.getElementById('total-hours-week').textContent = totalHours + 'h';
  document.getElementById('estimated-cost').textContent = estimatedCost + '€';
}

// Graphique simple (simulation)
function drawChart() {
  const canvas = document.getElementById('hoursChart');
  if (!canvas) return;
  
  const ctx = canvas.getContext('2d');
  const width = canvas.width;
  const height = canvas.height;
  
  // Fond
  ctx.fillStyle = '#334155';
  ctx.fillRect(0, 0, width, height);
  
  // Données simulées
  const data = [30, 35, 40, 45, 42, 38, 33];
  const days = ['L', 'M', 'M', 'J', 'V', 'S', 'D'];
  
  // Barres
  const barWidth = width / data.length - 10;
  const maxValue = Math.max(...data);
  
  data.forEach((value, index) => {
    const barHeight = (value / maxValue) * (height - 20);
    const x = index * (barWidth + 10) + 5;
    const y = height - barHeight - 10;
    
    ctx.fillStyle = '#06b6d4';
    ctx.fillRect(x, y, barWidth, barHeight);
    
    // Étiquettes
    ctx.fillStyle = '#cbd5e1';
    ctx.font = '12px sans-serif';
    ctx.textAlign = 'center';
    ctx.fillText(days[index], x + barWidth/2, height - 2);
  });
}

// Fermeture du modal avec Escape
document.addEventListener('keydown', function(e) {
  if (e.key === 'Escape') {
    closeScheduleModal();
  }
});

// Initialisation
document.addEventListener('DOMContentLoaded', function() {
  calculateMetrics();
  drawChart();
});
</script>
{% endblock %}