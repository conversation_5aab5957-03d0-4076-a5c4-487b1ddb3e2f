"""Benchmarks de performance pour l'application POS"""
import time
import statistics
from typing import List, Callable
import logging

logger = logging.getLogger(__name__)

class PerformanceBenchmark:
    """Classe pour exécuter des benchmarks de performance"""
    
    def __init__(self, name: str):
        self.name = name
        self.results: List[float] = []
    
    def run_benchmark(self, func: Callable, iterations: int = 100) -> dict:
        """
        Exécute un benchmark sur une fonction
        """
        logger.info(f"Running benchmark: {self.name}")
        
        execution_times = []
        for i in range(iterations):
            start_time = time.perf_counter()
            try:
                func()
                end_time = time.perf_counter()
                execution_times.append((end_time - start_time) * 1000)  # Convertir en ms
            except Exception as e:
                logger.warning(f"Benchmark iteration {i} failed: {e}")
                continue
        
        if execution_times:
            stats = {
                'name': self.name,
                'iterations': len(execution_times),
                'avg_time_ms': statistics.mean(execution_times),
                'min_time_ms': min(execution_times),
                'max_time_ms': max(execution_times),
                'median_time_ms': statistics.median(execution_times),
                'stdev_ms': statistics.stdev(execution_times) if len(execution_times) > 1 else 0
            }
            
            logger.info(f"Benchmark {self.name} completed: {stats['avg_time_ms']:.2f}ms avg")
            return stats
        else:
            logger.error(f"Benchmark {self.name} failed: no successful iterations")
            return {
                'name': self.name,
                'iterations': 0,
                'error': 'All iterations failed'
            }

def benchmark_cache_operations():
    """Benchmark des opérations de cache"""
    from app.utils.cache import cache_manager
    
    # Benchmark de mise en cache
    cache_bench = PerformanceBenchmark("Cache Set")
    cache_set_stats = cache_bench.run_benchmark(
        lambda: cache_manager.set(f"test_key_{time.time()}", "test_value" * 100),
        iterations=1000
    )
    
    # Benchmark de récupération du cache
    cache_bench_get = PerformanceBenchmark("Cache Get")
    # Préparer quelques valeurs en cache
    for i in range(100):
        cache_manager.set(f"bench_key_{i}", f"bench_value_{i}" * 50)
    
    cache_get_stats = cache_bench_get.run_benchmark(
        lambda: cache_manager.get(f"bench_key_{int(time.time() * 1000) % 100}"),
        iterations=1000
    )
    
    return [cache_set_stats, cache_get_stats]

def benchmark_pagination():
    """Benchmark de la pagination"""
    from app.utils.pagination import PaginationHelper
    
    # Créer des données de test importantes
    large_dataset = list(range(50000))
    
    pagination_bench = PerformanceBenchmark("Pagination")
    pagination_stats = pagination_bench.run_benchmark(
        lambda: PaginationHelper.paginate_list(large_dataset, page=2500, per_page=20),
        iterations=1000
    )
    
    return [pagination_stats]

def benchmark_query_optimization():
    """Benchmark de l'optimisation des requêtes"""
    from app.utils.query_optimizer import QueryOptimizer
    
    optimizer = QueryOptimizer()
    test_queries = [
        "SELECT * FROM users WHERE id = 1",
        "SELECT u.name, u.email FROM users u JOIN orders o ON u.id = o.user_id WHERE o.status = 'completed'",
        "SELECT COUNT(*) FROM products WHERE category_id = 5 AND price > 100"
    ]
    
    optimization_bench = PerformanceBenchmark("Query Optimization")
    optimization_stats = optimization_bench.run_benchmark(
        lambda: [optimizer.analyze_query(q) for q in test_queries],
        iterations=100
    )
    
    return [optimization_stats]

def run_all_benchmarks():
    """Exécute tous les benchmarks de performance"""
    logger.info("Starting performance benchmarks...")
    
    all_results = []
    
    # Exécuter les benchmarks
    benchmark_functions = [
        benchmark_cache_operations,
        benchmark_pagination,
        benchmark_query_optimization
    ]
    
    for bench_func in benchmark_functions:
        try:
            results = bench_func()
            all_results.extend(results)
        except Exception as e:
            logger.error(f"Benchmark function {bench_func.__name__} failed: {e}")
    
    # Afficher les résultats
    print("\n" + "="*80)
    print("PERFORMANCE BENCHMARK RESULTS")
    print("="*80)
    
    for result in all_results:
        if 'error' in result:
            print(f"{result['name']}: {result['error']}")
        else:
            print(f"{result['name']}:")
            print(f"  Iterations: {result['iterations']}")
            print(f"  Average: {result['avg_time_ms']:.2f}ms")
            print(f"  Min: {result['min_time_ms']:.2f}ms")
            print(f"  Max: {result['max_time_ms']:.2f}ms")
            print(f"  Median: {result['median_time_ms']:.2f}ms")
            print(f"  Std Dev: {result['stdev_ms']:.2f}ms")
            print()
    
    return all_results

if __name__ == '__main__':
    # Configurer le logging
    logging.basicConfig(level=logging.INFO)
    run_all_benchmarks()