{% extends "base.html" %}

{% block title %}{{ supplier.name }} - Détails Fournisseur{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- En-tête -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-1">
                        <i class="fas fa-truck text-primary me-2"></i>
                        {{ supplier.name }}
                        {% if not supplier.is_active %}
                        <span class="badge bg-warning text-dark ms-2">Inactif</span>
                        {% endif %}
                    </h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="{{ url_for('suppliers.index') }}">Fournisseurs</a>
                            </li>
                            <li class="breadcrumb-item active">{{ supplier.name }}</li>
                        </ol>
                    </nav>
                </div>
                <div class="btn-group">
                    <a href="{{ url_for('suppliers.edit_supplier', id=supplier.id) }}" class="btn btn-warning">
                        <i class="fas fa-edit me-1"></i>Modifier
                    </a>
                    <button type="button" class="btn btn-danger" onclick="confirmDelete({{ supplier.id }}, '{{ supplier.name }}')">
                        <i class="fas fa-trash me-1"></i>Supprimer
                    </button>
                    <a href="{{ url_for('suppliers.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Retour
                    </a>
                </div>
            </div>

            <!-- Statistiques rapides -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <h4 class="mb-1">{{ stats.total_contacts }}</h4>
                            <small>Contact(s)</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h4 class="mb-1">{{ stats.total_products }}</h4>
                            <small>Produit(s)</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <h4 class="mb-1">{{ stats.total_evaluations }}</h4>
                            <small>Évaluation(s)</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <h4 class="mb-1">
                                {% if stats.average_rating %}
                                {{ "%.1f"|format(stats.average_rating) }}/5
                                {% else %}
                                N/A
                                {% endif %}
                            </h4>
                            <small>Note moyenne</small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Informations générales -->
                <div class="col-lg-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                Informations générales
                            </h5>
                        </div>
                        <div class="card-body">
                            <dl class="row">
                                <dt class="col-sm-4">Code fournisseur:</dt>
                                <dd class="col-sm-8">
                                    <code>{{ supplier.supplier_code or 'Non défini' }}</code>
                                </dd>
                                
                                <dt class="col-sm-4">Nom:</dt>
                                <dd class="col-sm-8">{{ supplier.name }}</dd>
                                
                                {% if supplier.company_name %}
                                <dt class="col-sm-4">Entreprise:</dt>
                                <dd class="col-sm-8">{{ supplier.company_name }}</dd>
                                {% endif %}
                                
                                {% if supplier.email %}
                                <dt class="col-sm-4">Email:</dt>
                                <dd class="col-sm-8">
                                    <a href="mailto:{{ supplier.email }}">
                                        <i class="fas fa-envelope me-1"></i>{{ supplier.email }}
                                    </a>
                                </dd>
                                {% endif %}
                                
                                {% if supplier.phone %}
                                <dt class="col-sm-4">Téléphone:</dt>
                                <dd class="col-sm-8">
                                    <a href="tel:{{ supplier.phone }}">
                                        <i class="fas fa-phone me-1"></i>{{ supplier.phone }}
                                    </a>
                                </dd>
                                {% endif %}
                                
                                {% if supplier.website %}
                                <dt class="col-sm-4">Site web:</dt>
                                <dd class="col-sm-8">
                                    <a href="{{ supplier.website }}" target="_blank">
                                        <i class="fas fa-globe me-1"></i>{{ supplier.website }}
                                        <i class="fas fa-external-link-alt ms-1"></i>
                                    </a>
                                </dd>
                                {% endif %}
                                
                                {% if supplier.category %}
                                <dt class="col-sm-4">Catégorie:</dt>
                                <dd class="col-sm-8">
                                    <span class="badge bg-secondary">{{ supplier.category }}</span>
                                </dd>
                                {% endif %}
                                
                                <dt class="col-sm-4">Statut:</dt>
                                <dd class="col-sm-8">
                                    {% if supplier.is_active %}
                                    <span class="badge bg-success">Actif</span>
                                    {% else %}
                                    <span class="badge bg-warning text-dark">Inactif</span>
                                    {% endif %}
                                </dd>
                                
                                <dt class="col-sm-4">Créé le:</dt>
                                <dd class="col-sm-8">{{ supplier.created_at.strftime('%d/%m/%Y à %H:%M') }}</dd>
                                
                                {% if supplier.updated_at != supplier.created_at %}
                                <dt class="col-sm-4">Modifié le:</dt>
                                <dd class="col-sm-8">{{ supplier.updated_at.strftime('%d/%m/%Y à %H:%M') }}</dd>
                                {% endif %}
                            </dl>
                        </div>
                    </div>
                </div>

                <!-- Informations commerciales -->
                <div class="col-lg-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header bg-success text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-handshake me-2"></i>
                                Informations commerciales
                            </h5>
                        </div>
                        <div class="card-body">
                            <dl class="row">
                                {% if supplier.tax_number %}
                                <dt class="col-sm-5">Numéro fiscal:</dt>
                                <dd class="col-sm-7">{{ supplier.tax_number }}</dd>
                                {% endif %}
                                
                                {% if supplier.payment_terms %}
                                <dt class="col-sm-5">Conditions de paiement:</dt>
                                <dd class="col-sm-7">{{ supplier.payment_terms }}</dd>
                                {% endif %}
                                
                                {% if supplier.credit_limit_cents %}
                                <dt class="col-sm-5">Limite de crédit:</dt>
                                <dd class="col-sm-7">
                                    <span class="text-success fw-bold">
                                        {{ "%.2f"|format(supplier.credit_limit_cents / 100) }} €
                                    </span>
                                </dd>
                                {% endif %}
                                
                                {% if supplier.average_rating %}
                                <dt class="col-sm-5">Note moyenne:</dt>
                                <dd class="col-sm-7">
                                    <div class="text-warning">
                                        {% for i in range(1, 6) %}
                                            <i class="fas fa-star{% if i > supplier.average_rating %}-o{% endif %}"></i>
                                        {% endfor %}
                                        <span class="ms-2 text-muted">{{ "%.1f"|format(supplier.average_rating) }}/5</span>
                                    </div>
                                </dd>
                                {% endif %}
                            </dl>
                            
                            {% if supplier.notes %}
                            <div class="mt-3">
                                <h6>Notes:</h6>
                                <div class="border rounded p-3 bg-light">
                                    {{ supplier.notes | nl2br }}
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Onglets pour contacts, produits et évaluations -->
            <div class="card">
                <div class="card-header">
                    <ul class="nav nav-tabs card-header-tabs" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" data-bs-toggle="tab" href="#contacts" role="tab">
                                <i class="fas fa-users me-1"></i>
                                Contacts ({{ contacts|length }})
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#products" role="tab">
                                <i class="fas fa-boxes me-1"></i>
                                Produits ({{ products|length }})
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#evaluations" role="tab">
                                <i class="fas fa-star me-1"></i>
                                Évaluations ({{ evaluations|length }})
                            </a>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content">
                        <!-- Onglet Contacts -->
                        <div class="tab-pane fade show active" id="contacts" role="tabpanel">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="mb-0">Contacts du fournisseur</h6>
                                <a href="{{ url_for('suppliers.add_contact', supplier_id=supplier.id) }}" class="btn btn-sm btn-primary">
                                    <i class="fas fa-plus me-1"></i>Ajouter un contact
                                </a>
                            </div>
                            
                            {% if contacts %}
                            <div class="row">
                                {% for contact in contacts %}
                                <div class="col-md-6 mb-3">
                                    <div class="card {% if contact.is_primary %}border-primary{% endif %}">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div>
                                                    <h6 class="card-title">
                                                        {{ contact.first_name }} {{ contact.last_name }}
                                                        {% if contact.is_primary %}
                                                        <span class="badge bg-primary ms-1">Principal</span>
                                                        {% endif %}
                                                    </h6>
                                                    {% if contact.position %}
                                                    <p class="text-muted mb-1">{{ contact.position }}</p>
                                                    {% endif %}
                                                    {% if contact.department %}
                                                    <p class="text-muted mb-1">{{ contact.department }}</p>
                                                    {% endif %}
                                                    {% if contact.email %}
                                                    <p class="mb-1">
                                                        <i class="fas fa-envelope me-1"></i>
                                                        <a href="mailto:{{ contact.email }}">{{ contact.email }}</a>
                                                    </p>
                                                    {% endif %}
                                                    {% if contact.phone %}
                                                    <p class="mb-1">
                                                        <i class="fas fa-phone me-1"></i>
                                                        <a href="tel:{{ contact.phone }}">{{ contact.phone }}</a>
                                                    </p>
                                                    {% endif %}
                                                </div>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-warning" title="Modifier">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-outline-danger" title="Supprimer">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                            {% else %}
                            <div class="text-center py-4">
                                <i class="fas fa-user-plus fa-2x text-muted mb-3"></i>
                                <p class="text-muted">Aucun contact ajouté pour ce fournisseur.</p>
                                <a href="{{ url_for('suppliers.add_contact', supplier_id=supplier.id) }}" class="btn btn-primary">
                                    <i class="fas fa-plus me-1"></i>Ajouter le premier contact
                                </a>
                            </div>
                            {% endif %}
                        </div>

                        <!-- Onglet Produits -->
                        <div class="tab-pane fade" id="products" role="tabpanel">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="mb-0">Produits du fournisseur</h6>
                                <a href="{{ url_for('suppliers.add_product', supplier_id=supplier.id) }}" class="btn btn-sm btn-primary">
                                    <i class="fas fa-plus me-1"></i>Ajouter un produit
                                </a>
                            </div>
                            
                            {% if products %}
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Produit</th>
                                            <th>SKU</th>
                                            <th>SKU Fournisseur</th>
                                            <th>Prix</th>
                                            <th>Qté min</th>
                                            <th>Délai</th>
                                            <th>Statut</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for product in products %}
                                        <tr>
                                            <td>
                                                <strong>{{ product.product_name }}</strong>
                                                {% if product.category %}
                                                <br><small class="text-muted">{{ product.category }}</small>
                                                {% endif %}
                                            </td>
                                            <td><code>{{ product.sku or 'N/A' }}</code></td>
                                            <td><code>{{ product.supplier_sku or 'N/A' }}</code></td>
                                            <td>
                                                {% if product.cost_price_cents %}
                                                {{ "%.2f"|format(product.cost_price_cents / 100) }} €
                                                {% else %}
                                                N/A
                                                {% endif %}
                                            </td>
                                            <td>{{ product.minimum_order_quantity or 'N/A' }}</td>
                                            <td>
                                                {% if product.lead_time_days %}
                                                {{ product.lead_time_days }} jour(s)
                                                {% else %}
                                                N/A
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if product.is_available %}
                                                <span class="badge bg-success">Disponible</span>
                                                {% else %}
                                                <span class="badge bg-danger">Indisponible</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-warning" title="Modifier">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-outline-danger" title="Supprimer">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <div class="text-center py-4">
                                <i class="fas fa-box-open fa-2x text-muted mb-3"></i>
                                <p class="text-muted">Aucun produit ajouté pour ce fournisseur.</p>
                                <a href="{{ url_for('suppliers.add_product', supplier_id=supplier.id) }}" class="btn btn-primary">
                                    <i class="fas fa-plus me-1"></i>Ajouter le premier produit
                                </a>
                            </div>
                            {% endif %}
                        </div>

                        <!-- Onglet Évaluations -->
                        <div class="tab-pane fade" id="evaluations" role="tabpanel">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="mb-0">Évaluations du fournisseur</h6>
                                <a href="{{ url_for('suppliers.add_evaluation', supplier_id=supplier.id) }}" class="btn btn-sm btn-primary">
                                    <i class="fas fa-plus me-1"></i>Ajouter une évaluation
                                </a>
                            </div>
                            
                            {% if evaluations %}
                            <div class="row">
                                {% for evaluation in evaluations %}
                                <div class="col-lg-6 mb-3">
                                    <div class="card">
                                        <div class="card-header d-flex justify-content-between align-items-center">
                                            <div>
                                                <strong>{{ evaluation.evaluation_date.strftime('%d/%m/%Y') }}</strong>
                                                {% if evaluation.evaluator_name %}
                                                <br><small class="text-muted">par {{ evaluation.evaluator_name }}</small>
                                                {% endif %}
                                            </div>
                                            <div class="text-warning">
                                                {% for i in range(1, 6) %}
                                                    <i class="fas fa-star{% if i > evaluation.overall_rating %}-o{% endif %}"></i>
                                                {% endfor %}
                                                <span class="ms-1 text-muted">{{ evaluation.overall_rating }}/5</span>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="row mb-2">
                                                <div class="col-6">
                                                    <small class="text-muted">Qualité:</small>
                                                    <div class="text-warning">
                                                        {% for i in range(1, 6) %}
                                                            <i class="fas fa-star{% if i > evaluation.quality_rating %}-o{% endif %} fa-xs"></i>
                                                        {% endfor %}
                                                    </div>
                                                </div>
                                                <div class="col-6">
                                                    <small class="text-muted">Livraison:</small>
                                                    <div class="text-warning">
                                                        {% for i in range(1, 6) %}
                                                            <i class="fas fa-star{% if i > evaluation.delivery_rating %}-o{% endif %} fa-xs"></i>
                                                        {% endfor %}
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row mb-2">
                                                <div class="col-6">
                                                    <small class="text-muted">Service:</small>
                                                    <div class="text-warning">
                                                        {% for i in range(1, 6) %}
                                                            <i class="fas fa-star{% if i > evaluation.service_rating %}-o{% endif %} fa-xs"></i>
                                                        {% endfor %}
                                                    </div>
                                                </div>
                                                <div class="col-6">
                                                    <small class="text-muted">Prix:</small>
                                                    <div class="text-warning">
                                                        {% for i in range(1, 6) %}
                                                            <i class="fas fa-star{% if i > evaluation.price_rating %}-o{% endif %} fa-xs"></i>
                                                        {% endfor %}
                                                    </div>
                                                </div>
                                            </div>
                                            {% if evaluation.comments %}
                                            <div class="mt-3">
                                                <small class="text-muted">Commentaires:</small>
                                                <p class="mb-0">{{ evaluation.comments | nl2br }}</p>
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                            {% else %}
                            <div class="text-center py-4">
                                <i class="fas fa-star fa-2x text-muted mb-3"></i>
                                <p class="text-muted">Aucune évaluation pour ce fournisseur.</p>
                                <a href="{{ url_for('suppliers.add_evaluation', supplier_id=supplier.id) }}" class="btn btn-primary">
                                    <i class="fas fa-plus me-1"></i>Ajouter la première évaluation
                                </a>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de confirmation de suppression -->
<div id="deleteModal" class="hidden fixed inset-0 z-50 flex items-center justify-center modal-backdrop">
  <div class="modal fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
    <div class="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
      <div class="px-6 py-4 border-b">
        <h3 class="text-lg font-semibold text-gray-900">Confirmer la suppression</h3>
        <button type="button" class="close-modal absolute top-4 right-4 text-gray-500 hover:text-gray-700" onclick="closeModal('deleteModal')">
          <i class="fas fa-times"></i>
        </button>
      </div>
      
      <div class="px-6 py-4">
        <p class="text-gray-700 mb-2">
          Êtes-vous sûr de vouloir supprimer le fournisseur <strong id="supplierName"></strong> ?
        </p>
        <p class="text-sm text-gray-500">
          Cette action est irréversible.
        </p>
      </div>
      
      <div class="px-6 py-4 border-t bg-gray-50 flex justify-end space-x-3">
        <button type="button" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-100" onclick="closeModal('deleteModal')">
          Annuler
        </button>
        <form id="deleteForm" method="POST" style="display: inline;">
          <button type="submit" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">
            Supprimer
          </button>
        </form>
      </div>
    </div>
  </div>
</div>
{% block scripts %}
<script src="{{ url_for('suppliers.static', filename='js/modals.js') }}"></script>
{% endblock %}
{% endblock %}
