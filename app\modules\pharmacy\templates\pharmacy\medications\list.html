{% extends 'base.html' %}
{% block title %}Médicaments - Pharmacie{% endblock %}

{% block head %}
<style>
    .medications-page {
        background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
        min-height: 100vh;
        padding: 20px 0;
    }
    
    .search-card {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }
    
    .medication-card {
        background: white;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 15px;
        transition: all 0.3s ease;
        border-left: 4px solid transparent;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    
    .medication-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
    }
    
    .medication-card.prescription {
        border-left-color: #e74c3c;
    }
    
    .medication-card.otc {
        border-left-color: #27ae60;
    }
    
    .medication-card.controlled {
        border-left-color: #f39c12;
    }
    
    .medication-card.hospital-only {
        border-left-color: #9b59b6;
    }
    
    .stock-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 8px;
    }
    
    .stock-normal { background: #27ae60; }
    .stock-low { background: #f39c12; }
    .stock-critical { background: #e74c3c; }
    
    .medication-header {
        display: flex;
        justify-content: space-between;
        align-items: start;
        margin-bottom: 15px;
    }
    
    .medication-info {
        flex: 1;
    }
    
    .medication-actions {
        display: flex;
        gap: 8px;
    }
    
    .price-badge {
        background: linear-gradient(45deg, #667eea, #764ba2);
        color: white;
        padding: 5px 12px;
        border-radius: 20px;
        font-weight: 500;
        font-size: 0.9rem;
    }
    
    .classification-badge {
        padding: 4px 10px;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    
    .class-prescription { background: #ffebee; color: #c62828; }
    .class-otc { background: #e8f5e8; color: #2e7d32; }
    .class-controlled { background: #fff8e1; color: #ef6c00; }
    .class-hospital { background: #f3e5f5; color: #7b1fa2; }
    
    .search-filters {
        display: grid;
        grid-template-columns: 2fr 1fr 1fr auto;
        gap: 15px;
        align-items: end;
    }
    
    .filter-group {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
    }
</style>
{% endblock %}

{% block content %}
<div class="medications-page">
    <div class="container-fluid">
        <!-- En-tête -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="search-card">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div>
                            <h1 class="mb-1">💊 Médicaments</h1>
                            <p class="text-muted mb-0">Gestion du catalogue pharmaceutique</p>
                        </div>
                        <div>
                            <a href="{{ url_for('pharmacy.new_medication') }}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Nouveau médicament
                            </a>
                        </div>
                    </div>
                    
                    <!-- Formulaire de recherche -->
                    <form method="GET" id="searchForm">
                        <div class="search-filters">
                            <div>
                                {{ search_form.search_query(class="form-control", placeholder="Rechercher par nom, DCI, code CIP...") }}
                            </div>
                            <div>
                                {{ search_form.medication_class(class="form-control") }}
                            </div>
                            <div class="filter-group">
                                <div class="form-check">
                                    {{ search_form.low_stock_only(class="form-check-input") }}
                                    <label class="form-check-label" for="{{ search_form.low_stock_only.id }}">
                                        Stock faible
                                    </label>
                                </div>
                            </div>
                            <div>
                                <button type="submit" class="btn btn-outline-primary">
                                    <i class="fas fa-search"></i> Rechercher
                                </button>
                                <a href="{{ url_for('pharmacy.medications') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i>
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Résultats -->
        <div class="row">
            <div class="col-12">
                {% if medications.items %}
                    <div class="mb-3">
                        <small class="text-muted">
                            {{ medications.total }} médicament(s) trouvé(s) - Page {{ medications.page }} sur {{ medications.pages }}
                        </small>
                    </div>
                    
                    {% for medication in medications.items %}
                    <div class="medication-card {{ medication.medication_class.value }}">
                        <div class="medication-header">
                            <div class="medication-info">
                                <h5 class="mb-2">
                                    <a href="{{ url_for('pharmacy.medication_detail', medication_id=medication.id) }}" 
                                       class="text-decoration-none text-dark">
                                        {{ medication.name }}
                                    </a>
                                    <span class="classification-badge class-{{ medication.medication_class.value }}">
                                        {% if medication.medication_class.value == 'prescription' %}Sur ordonnance
                                        {% elif medication.medication_class.value == 'otc' %}Vente libre
                                        {% elif medication.medication_class.value == 'controlled' %}Stupéfiant
                                        {% elif medication.medication_class.value == 'hospital_only' %}Hospitalier
                                        {% endif %}
                                    </span>
                                </h5>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        {% if medication.generic_name %}
                                            <p class="mb-1"><strong>DCI:</strong> {{ medication.generic_name }}</p>
                                        {% endif %}
                                        {% if medication.dosage %}
                                            <p class="mb-1"><strong>Dosage:</strong> {{ medication.dosage }}</p>
                                        {% endif %}
                                        {% if medication.pharmaceutical_form %}
                                            <p class="mb-1"><strong>Forme:</strong> {{ medication.pharmaceutical_form }}</p>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-6">
                                        {% if medication.cip_code %}
                                            <p class="mb-1"><strong>Code CIP:</strong> {{ medication.cip_code }}</p>
                                        {% endif %}
                                        {% if medication.therapeutic_class %}
                                            <p class="mb-1"><strong>Classe:</strong> {{ medication.therapeutic_class }}</p>
                                        {% endif %}
                                        <p class="mb-1">
                                            <strong>Stock:</strong> 
                                            <span class="stock-indicator 
                                                {% if medication.current_stock <= medication.minimum_stock %}stock-critical
                                                {% elif medication.current_stock <= medication.minimum_stock * 2 %}stock-low
                                                {% else %}stock-normal{% endif %}">
                                            </span>
                                            {{ medication.current_stock }} {{ medication.unit_of_measure }}
                                            {% if medication.current_stock <= medication.minimum_stock %}
                                                <span class="badge badge-danger badge-sm">Critique</span>
                                            {% endif %}
                                        </p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="medication-actions">
                                <div class="text-right">
                                    <div class="price-badge mb-2">
                                        {{ "%.2f"|format(medication.price_cents / 100) }} €
                                    </div>
                                    <div>
                                        <a href="{{ url_for('pharmacy.medication_detail', medication_id=medication.id) }}" 
                                           class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ url_for('pharmacy.edit_medication', medication_id=medication.id) }}" 
                                           class="btn btn-outline-warning btn-sm">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-outline-success btn-sm" 
                                                onclick="quickStock({{ medication.id }})">
                                            <i class="fas fa-boxes"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                    
                    <!-- Pagination -->
                    {% if medications.pages > 1 %}
                    <nav aria-label="Navigation des médicaments">
                        <ul class="pagination justify-content-center">
                            {% if medications.has_prev %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('pharmacy.medications', page=medications.prev_num, **request.args) }}">
                                        Précédent
                                    </a>
                                </li>
                            {% endif %}
                            
                            {% for page_num in medications.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != medications.page %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('pharmacy.medications', page=page_num, **request.args) }}">
                                                {{ page_num }}
                                            </a>
                                        </li>
                                    {% else %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ page_num }}</span>
                                        </li>
                                    {% endif %}
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">…</span>
                                    </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if medications.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('pharmacy.medications', page=medications.next_num, **request.args) }}">
                                        Suivant
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                    
                {% else %}
                    <div class="search-card text-center py-5">
                        <i class="fas fa-pills fa-3x text-muted mb-3"></i>
                        <h4>Aucun médicament trouvé</h4>
                        <p class="text-muted">Aucun médicament ne correspond à vos critères de recherche.</p>
                        <a href="{{ url_for('pharmacy.new_medication') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Ajouter le premier médicament
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Modal mouvement stock rapide -->
<div class="modal fade" id="stockModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Mouvement de stock</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="stockForm">
                    <input type="hidden" id="medicationId">
                    <div class="form-group">
                        <label>Type de mouvement</label>
                        <select class="form-control" id="movementType" required>
                            <option value="in">Entrée en stock</option>
                            <option value="out">Sortie de stock</option>
                            <option value="adjustment">Ajustement</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Quantité</label>
                        <input type="number" class="form-control" id="quantity" required min="1">
                    </div>
                    <div class="form-group">
                        <label>Notes</label>
                        <textarea class="form-control" id="notes" rows="2"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary" onclick="submitStockMovement()">Confirmer</button>
            </div>
        </div>
    </div>
</div>

<script>
function quickStock(medicationId) {
    document.getElementById('medicationId').value = medicationId;
    $('#stockModal').modal('show');
}

function submitStockMovement() {
    const medicationId = document.getElementById('medicationId').value;
    const movementType = document.getElementById('movementType').value;
    const quantity = document.getElementById('quantity').value;
    const notes = document.getElementById('notes').value;
    
    if (!quantity) {
        alert('Veuillez saisir une quantité');
        return;
    }
    
    fetch(`/pharmacy/api/stock/${medicationId}/movement`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            movement_type: movementType,
            quantity: parseInt(quantity),
            notes: notes
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            alert('Erreur: ' + data.error);
        } else {
            alert('Mouvement enregistré avec succès');
            $('#stockModal').modal('hide');
            location.reload();
        }
    })
    .catch(error => {
        alert('Erreur lors de l\'enregistrement');
        console.error(error);
    });
}

// Soumission automatique du formulaire de recherche
document.getElementById('searchForm').addEventListener('change', function() {
    // Auto-submit après un délai pour éviter les soumissions multiples
    clearTimeout(window.searchTimeout);
    window.searchTimeout = setTimeout(() => {
        this.submit();
    }, 500);
});
</script>
{% endblock %}