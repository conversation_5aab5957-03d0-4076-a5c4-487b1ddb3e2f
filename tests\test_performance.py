"""Tests de performance pour l'application POS"""
import unittest
import time
from app.utils.performance_monitor import performance_monitor, timing_decorator
from app.utils.cache import cache_manager
from app.utils.query_optimizer import QueryOptimizer
from app.utils.pagination import PaginationHelper

class PerformanceTestCase(unittest.TestCase):
    """Tests pour les optimisations de performance"""
    
    def setUp(self):
        """Initialisation avant chaque test"""
        performance_monitor.reset_metrics()
    
    def test_timing_decorator(self):
        """Test du décorateur de mesure de performance"""
        @timing_decorator("test_function")
        def slow_function():
            time.sleep(0.01)  # 10ms
            return "result"
        
        result = slow_function()
        self.assertEqual(result, "result")
        
        # Vérifier que la métrique a été enregistrée
        metrics = performance_monitor.metrics["test_function"]
        self.assertEqual(len(metrics), 1)
        self.assertGreater(metrics[0], 5)  # Devrait prendre plus de 5ms
        self.assertLess(metrics[0], 50)    # Mais moins de 50ms
    
    def test_cache_performance(self):
        """Test de la performance du cache"""
        # Test de la mise en cache
        start_time = time.time()
        cache_manager.set("test_key", "test_value", timeout=60)
        set_time = time.time() - start_time
        
        # Test de la récupération du cache
        start_time = time.time()
        value = cache_manager.get("test_key")
        get_time = time.time() - start_time
        
        self.assertEqual(value, "test_value")
        # Les opérations de cache devraient être rapides (moins de 10ms)
        self.assertLess(set_time, 0.01)
        self.assertLess(get_time, 0.01)
    
    def test_query_optimizer_performance(self):
        """Test de la performance de l'optimiseur de requêtes"""
        optimizer = QueryOptimizer()
        
        # Test de l'analyse de requête
        test_query = "SELECT * FROM users WHERE id = 1"
        start_time = time.time()
        suggestions = optimizer.analyze_query(test_query)
        analyze_time = time.time() - start_time
        
        # L'analyse devrait être rapide
        self.assertLess(analyze_time, 0.01)
        self.assertIsInstance(suggestions, list)
    
    def test_pagination_performance(self):
        """Test de la performance de la pagination"""
        # Créer des données de test
        test_data = list(range(1000))
        
        start_time = time.time()
        paginator = PaginationHelper.paginate_list(test_data, page=1, per_page=20)
        pagination_time = time.time() - start_time
        
        self.assertEqual(paginator['page'], 1)
        self.assertEqual(paginator['per_page'], 20)
        self.assertEqual(len(paginator['items']), 20)
        # La pagination devrait être rapide
        self.assertLess(pagination_time, 0.001)
    
    def test_batch_processing_performance(self):
        """Test de la performance du traitement par lots"""
        # Créer des données de test
        test_data = list(range(10000))
        
        start_time = time.time()
        batches = list(PaginationHelper.create_batches(test_data, batch_size=1000))
        batch_time = time.time() - start_time
        
        self.assertEqual(len(batches), 10)
        self.assertEqual(len(batches[0]), 1000)
        # Le traitement par lots devrait être efficace
        self.assertLess(batch_time, 0.01)

if __name__ == '__main__':
    unittest.main()