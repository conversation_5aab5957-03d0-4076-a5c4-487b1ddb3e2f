@echo off
:: Script de sauvegarde automatique pour l'application POS System (Windows)

:: Configuration
set BACKUP_DIR=C:\backups
set DATE=%date:~6,4%%date:~3,2%%date:~0,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set BACKUP_NAME=pos_system_backup_%DATE%
set DB_NAME=pos_system
set DB_USER=pos_user
set DB_HOST=localhost

:: C<PERSON>er le répertoire de sauvegarde
if not exist "%BACKUP_DIR%" mkdir "%BACKUP_DIR%"

:: Sauvegarde de la base de données
echo Starting database backup...
pg_dump -h %DB_HOST% -U %DB_USER% -d %DB_NAME% > "%BACKUP_DIR%\%BACKUP_NAME%.sql"

if %ERRORLEVEL% EQU 0 (
    echo Database backup completed successfully
    
    :: Compresser la sauvegarde
    powershell -command "Compress-Archive -Path '%BACKUP_DIR%\%BACKUP_NAME%.sql' -DestinationPath '%BACKUP_DIR%\%BACKUP_NAME%.zip' -Force"
    
    :: Supprimer le fichier SQL non compressé
    del "%BACKUP_DIR%\%BACKUP_NAME%.sql"
    
    :: Supprimer les sauvegardes plus anciennes que 30 jours
    forfiles /p "%BACKUP_DIR%" /m "pos_system_backup_*.zip" /d -30 /c "cmd /c del @path"
    
    echo Backup completed: %BACKUP_DIR%\%BACKUP_NAME%.zip
) else (
    echo Database backup failed
    exit /b 1
)

:: Sauvegarde des fichiers de l'application
echo Starting application files backup...
powershell -command "Compress-Archive -Path 'C:\app\instance' -DestinationPath '%BACKUP_DIR%\%BACKUP_NAME%_files.zip' -Force"

if %ERRORLEVEL% EQU 0 (
    echo Application files backup completed successfully
    
    :: Supprimer les sauvegardes de fichiers plus anciennes que 30 jours
    forfiles /p "%BACKUP_DIR%" /m "pos_system_backup_*_files.zip" /d -30 /c "cmd /c del @path"
    
    echo Files backup completed: %BACKUP_DIR%\%BACKUP_NAME%_files.zip
) else (
    echo Application files backup failed
    exit /b 1
)

echo All backups completed successfully