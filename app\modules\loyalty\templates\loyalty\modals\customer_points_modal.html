<!-- Modal pour l'historique des points clients pour le module Loyalty -->
<div id="customerPointsModal" class="hidden fixed inset-0 z-50 flex items-center justify-center modal-backdrop">
  <div class="modal fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
    <div class="bg-white rounded-lg shadow-xl w-full max-w-2xl mx-4">
      <div class="px-6 py-4 border-b">
        <h3 class="text-lg font-semibold text-gray-900">Historique des Points - <span id="modalCustomerName"></span></h3>
        <button type="button" class="close-modal absolute top-4 right-4 text-gray-500 hover:text-gray-700" onclick="closeModal('customerPointsModal')">
          <i class="fas fa-times"></i>
        </button>
      </div>
      
      <div class="px-6 py-4 max-h-96 overflow-y-auto">
        <div id="pointsHistoryContent">
          <!-- Contenu chargé dynamiquement -->
          <div class="text-center py-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mb-3"></div>
            <p class="text-gray-500">Chargement de l'historique...</p>
          </div>
        </div>
      </div>
      
      <div class="px-6 py-4 border-t bg-gray-50 flex justify-end">
        <button type="button" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300" onclick="closeModal('customerPointsModal')">
          Fermer
        </button>
      </div>
    </div>
  </div>
</div>

<script>
// Fonction pour afficher l'historique des points d'un client
function showPointsHistory(customerId) {
    // Ouvrir le modal
    openModal('customerPointsModal');
    
    // Charger l'historique des points
    fetch(`/loyalty/api/customer/${customerId}/points-history`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const content = document.getElementById('pointsHistoryContent');
                const customer = data.customer;
                
                // Mettre à jour le nom du client
                document.getElementById('modalCustomerName').textContent = `${customer.first_name} ${customer.last_name}`;
                
                // Générer le contenu de l'historique
                if (data.history && data.history.length > 0) {
                    let historyHtml = `
                        <div class="space-y-3">
                            <div class="grid grid-cols-12 gap-2 text-xs font-semibold text-gray-500 border-b pb-2">
                                <div class="col-span-3">Date</div>
                                <div class="col-span-2">Points</div>
                                <div class="col-span-3">Type</div>
                                <div class="col-span-4">Description</div>
                            </div>
                    `;
                    
                    data.history.forEach(entry => {
                        const date = new Date(entry.created_at).toLocaleDateString('fr-FR');
                        const pointsClass = entry.points > 0 ? 'text-green-600' : 'text-red-600';
                        const typeLabel = getTransactionTypeLabel(entry.transaction_type);
                        
                        historyHtml += `
                            <div class="grid grid-cols-12 gap-2 text-sm py-2 border-b border-gray-100">
                                <div class="col-span-3">${date}</div>
                                <div class="col-span-2 ${pointsClass} font-medium">${entry.points > 0 ? '+' : ''}${entry.points}</div>
                                <div class="col-span-3">
                                    <span class="inline-block px-2 py-1 text-xs rounded bg-gray-100">${typeLabel}</span>
                                </div>
                                <div class="col-span-4 text-gray-600">${entry.description || '-'}</div>
                            </div>
                        `;
                    });
                    
                    historyHtml += '</div>';
                    content.innerHTML = historyHtml;
                } else {
                    content.innerHTML = `
                        <div class="text-center py-8">
                            <i class="fas fa-history text-3xl text-gray-300 mb-3"></i>
                            <p class="text-gray-500">Aucun historique de points pour ce client</p>
                        </div>
                    `;
                }
            } else {
                throw new Error(data.message || 'Erreur lors du chargement de l\'historique');
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            const content = document.getElementById('pointsHistoryContent');
            content.innerHTML = `
                <div class="text-center py-8">
                    <i class="fas fa-exclamation-triangle text-3xl text-red-500 mb-3"></i>
                    <p class="text-red-500">Erreur lors du chargement de l'historique</p>
                    <p class="text-gray-500 text-sm mt-1">${error.message}</p>
                </div>
            `;
        });
}

// Fonction pour obtenir le libellé du type de transaction
function getTransactionTypeLabel(type) {
    const labels = {
        'EARNED': 'Gagnés',
        'REDEEMED': 'Utilisés',
        'ADJUSTMENT': 'Ajustement',
        'EXPIRED': 'Expirés',
        'BONUS': 'Bonus'
    };
    return labels[type] || type;
}
</script>