{% extends "base.html" %}

{% block title %}Service en salle - {{ super() }}{% endblock %}

{% block extra_css %}
<style>
.dining-room {
    border: 2px solid #e3eaef;
    border-radius: 8px;
    margin: 10px;
    padding: 20px;
    position: relative;
    min-height: 400px;
}

.table-item {
    position: absolute;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s;
}

.table-available {
    background: #28a745;
    color: white;
}

.table-occupied {
    background: #dc3545;
    color: white;
}

.table-reserved {
    background: #ffc107;
    color: #212529;
}

.product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    max-height: 400px;
    overflow-y: auto;
}

.product-card {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s;
}

.product-card:hover {
    border-color: #0d6efd;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.cart-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid #dee2e6;
}
</style>
{% endblock %}

{% block content %}
<div class=\"container-fluid\">
    <div class=\"row\">
        <div class=\"col-12\">
            <div class=\"page-title-box\">
                <h4 class=\"page-title\">Service en salle - Gestion des tables</h4>
            </div>
        </div>
    </div>

    <div class=\"row\">
        <!-- Plan des tables -->
        <div class=\"col-lg-8\">
            <div class=\"card\">
                <div class=\"card-header\">
                    <h5 class=\"card-title mb-0\">Plan des tables</h5>
                </div>
                <div class=\"card-body\">
                    {% for dining_room in dining_rooms %}
                    <div class=\"dining-room\" id=\"room-{{ dining_room.id }}\">
                        <h6 class=\"position-absolute\" style=\"top: 5px; left: 10px;\">{{ dining_room.name }}</h6>
                        {% for table in dining_room.tables %}
                        <div class=\"table-item table-{{ table.status }}\" 
                             data-table-id=\"{{ table.id }}\"
                             data-table-number=\"{{ table.number }}\"
                             data-capacity=\"{{ table.capacity }}\"
                             style=\"left: {{ table.position_x or (loop.index0 * 80 + 50) }}px; top: {{ table.position_y or (50 + (loop.index0 // 5) * 80) }}px;\"
                             onclick=\"selectTable({{ table.id }}, '{{ table.number }}', {{ table.capacity }})\">
                            T{{ table.number }}
                        </div>
                        {% endfor %}
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- Panneau de commande -->
        <div class=\"col-lg-4\">
            <!-- Information table sélectionnée -->
            <div class=\"card mb-3\" id=\"selected-table-info\" style=\"display: none;\">
                <div class=\"card-header\">
                    <h5 class=\"card-title mb-0\">Table sélectionnée</h5>
                </div>
                <div class=\"card-body\">
                    <p><strong>Table:</strong> <span id=\"selected-table-number\"></span></p>
                    <p><strong>Capacité:</strong> <span id=\"selected-table-capacity\"></span> personnes</p>
                    <div class=\"mb-3\">
                        <label class=\"form-label\">Nombre de clients</label>
                        <input type=\"number\" class=\"form-control\" id=\"customer-count\" min=\"1\" max=\"20\" value=\"1\">
                    </div>
                    <button class=\"btn btn-primary\" onclick=\"startOrder()\">Nouvelle commande</button>
                    <button class=\"btn btn-info\" onclick=\"viewCurrentOrder()\" id=\"view-order-btn\" style=\"display: none;\">Voir commande</button>
                </div>
            </div>

            <!-- Produits et recettes -->
            <div class=\"card\" id=\"products-panel\" style=\"display: none;\">
                <div class=\"card-header\">
                    <h5 class=\"card-title mb-0\">Produits & Recettes</h5>
                </div>
                <div class=\"card-body\">
                    <div class=\"product-grid\">
                        {% for product in products %}
                        <div class=\"product-card\" onclick=\"addToCart('product', {{ product.id }}, '{{ product.name }}', {{ product.price_cents }})\">
                            <h6>{{ product.name }}</h6>
                            <p class=\"text-muted\">{{ \"%.2f\" | format(product.price_cents / 100) }} €</p>
                        </div>
                        {% endfor %}
                        {% for recipe in recipes %}
                        <div class=\"product-card\" onclick=\"addToCart('recipe', {{ recipe.id }}, '{{ recipe.name }}', {{ recipe.product.price_cents }})\">
                            <h6>{{ recipe.name }}</h6>
                            <p class=\"text-muted\">{{ \"%.2f\" | format(recipe.product.price_cents / 100) }} €</p>
                            <small class=\"badge bg-info\">Recette</small>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>

            <!-- Panier -->
            <div class=\"card mt-3\" id=\"cart-panel\" style=\"display: none;\">
                <div class=\"card-header\">
                    <h5 class=\"card-title mb-0\">Commande en cours</h5>
                </div>
                <div class=\"card-body\">
                    <div id=\"cart-items\"></div>
                    <hr>
                    <div class=\"d-flex justify-content-between\">
                        <strong>Total: <span id=\"cart-total\">0.00 €</span></strong>
                    </div>
                    <div class=\"mt-3\">
                        <textarea class=\"form-control mb-2\" id=\"special-requests\" placeholder=\"Demandes spéciales...\"></textarea>
                        <button class=\"btn btn-success w-100\" onclick=\"confirmOrder()\">Confirmer la commande</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let selectedTableId = null;
let selectedTableNumber = null;
let cart = [];
let cartTotal = 0;

function selectTable(tableId, tableNumber, capacity) {
    selectedTableId = tableId;
    selectedTableNumber = tableNumber;
    
    document.getElementById('selected-table-number').textContent = tableNumber;
    document.getElementById('selected-table-capacity').textContent = capacity;
    document.getElementById('selected-table-info').style.display = 'block';
    
    // Vérifier s'il y a déjà une commande
    const tableElement = document.querySelector(`[data-table-id=\"${tableId}\"]`);
    if (tableElement.classList.contains('table-occupied')) {
        document.getElementById('view-order-btn').style.display = 'inline-block';
    } else {
        document.getElementById('view-order-btn').style.display = 'none';
    }
}

function startOrder() {
    if (!selectedTableId) {
        alert('Veuillez sélectionner une table');
        return;
    }
    
    document.getElementById('products-panel').style.display = 'block';
    document.getElementById('cart-panel').style.display = 'block';
    cart = [];
    updateCartDisplay();
}

function addToCart(type, id, name, price) {
    const existingItem = cart.find(item => item.type === type && item.id === id);
    
    if (existingItem) {
        existingItem.qty += 1;
    } else {
        cart.push({
            type: type,
            id: id,
            name: name,
            price: price,
            qty: 1
        });
    }
    
    updateCartDisplay();
}

function removeFromCart(index) {
    cart.splice(index, 1);
    updateCartDisplay();
}

function updateCartDisplay() {
    const cartItemsDiv = document.getElementById('cart-items');
    cartItemsDiv.innerHTML = '';
    cartTotal = 0;
    
    cart.forEach((item, index) => {
        const itemTotal = item.price * item.qty;
        cartTotal += itemTotal;
        
        cartItemsDiv.innerHTML += `
            <div class=\"cart-item\">
                <div>
                    <strong>${item.name}</strong><br>
                    <small>${item.qty} × ${(item.price / 100).toFixed(2)} €</small>
                </div>
                <div>
                    <button class=\"btn btn-sm btn-outline-secondary\" onclick=\"updateItemQty(${index}, ${item.qty - 1})\">-</button>
                    <span class=\"mx-2\">${item.qty}</span>
                    <button class=\"btn btn-sm btn-outline-secondary\" onclick=\"updateItemQty(${index}, ${item.qty + 1})\">+</button>
                    <button class=\"btn btn-sm btn-danger ms-2\" onclick=\"removeFromCart(${index})\">×</button>
                </div>
            </div>
        `;
    });
    
    document.getElementById('cart-total').textContent = (cartTotal / 100).toFixed(2) + ' €';
}

function updateItemQty(index, newQty) {
    if (newQty <= 0) {
        removeFromCart(index);
    } else {
        cart[index].qty = newQty;
        updateCartDisplay();
    }
}

function confirmOrder() {
    if (!selectedTableId || cart.length === 0) {
        alert('Veuillez sélectionner une table et ajouter des articles');
        return;
    }
    
    const orderData = {
        table_id: selectedTableId,
        customer_count: document.getElementById('customer-count').value,
        special_requests: document.getElementById('special-requests').value,
        items: cart.map(item => ({
            type: item.type,
            [`${item.type}_id`]: item.id,
            qty: item.qty
        }))
    };
    
    fetch('/pos/api/table-order', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(orderData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.ok) {
            alert('Commande créée avec succès!');
            // Mettre à jour le statut de la table
            const tableElement = document.querySelector(`[data-table-id=\"${selectedTableId}\"]`);
            tableElement.className = 'table-item table-occupied';
            
            // Réinitialiser l'interface
            cart = [];
            updateCartDisplay();
            document.getElementById('products-panel').style.display = 'none';
            document.getElementById('cart-panel').style.display = 'none';
            document.getElementById('view-order-btn').style.display = 'inline-block';
        } else {
            alert('Erreur: ' + (data.error || 'Impossible de créer la commande'));
        }
    })
    .catch(error => {
        console.error('Erreur:', error);
        alert('Erreur de communication avec le serveur');
    });
}

function viewCurrentOrder() {
    // Rediriger vers la page de gestion de la commande
    window.location.href = `/sales/orders?table_id=${selectedTableId}`;
}

// Actualiser le statut des tables toutes les 30 secondes
setInterval(() => {
    fetch('/pos/api/tables')
    .then(response => response.json())
    .then(data => {
        data.dining_rooms.forEach(room => {
            room.tables.forEach(table => {
                const tableElement = document.querySelector(`[data-table-id=\"${table.id}\"]`);
                if (tableElement) {
                    tableElement.className = `table-item table-${table.status}`;
                }
            });
        });
    })
    .catch(error => console.error('Erreur lors de l\\'actualisation:', error));
}, 30000);
</script>
{% endblock %}