import os
from flask import Flask
from flask_wtf.csrf import generate_csrf
from .extensions import db, login_manager, csrf, migrate_safe_dummy, socketio


def create_app(seed_data=True) -> Flask:
    env = os.environ.get("FLASK_ENV", "development")
    config_cls = DevConfig if env == "development" else Config

    app = Flask(__name__, instance_relative_config=True)
    app.config.from_object(config_cls)

    # Ensure instance folder exists
    os.makedirs(app.instance_path, exist_ok=True)

    # Initialize extensions
    db.init_app(app)
    login_manager.init_app(app)
    csrf.init_app(app)
    socketio.init_app(app, message_queue=app.config.get("SOCKETIO_REDIS_URL"))

    # Initialize performance optimizations
    try:
        from .config_performance import PerformanceConfig
        PerformanceConfig.init_app(app)
    except ImportError:
        app.logger.warning("Performance configuration not found")

    # Initialize cache
    try:
        from .extensions import cache
        cache.init_app(app)
    except Exception as e:
        app.logger.warning(f"Cache initialization failed: {e}")

    # In dev: reset database schema automatically on each run (no migrations)
    if app.config.get("AUTO_RESET_DB", False) and seed_data:
        with app.app_context():
            # Ensure models are imported so SQLAlchemy metadata is populated
            from .modules.accounts import models as _accounts_models  # noqa: F401
            from .modules.catalog import models as _catalog_models  # noqa: F401
            from .modules.sales import models as _sales_models  # noqa: F401
            from .modules.inventory import models as _inventory_models  # noqa: F401
            from .modules.ingredients import models as _ingredients_models  # noqa: F401
            from .modules.variants import models as _variants_models  # noqa: F401
            from .modules.expenses import models as _expenses_models  # noqa: F401
            from .modules.menu_planning import models as _menu_planning_models  # noqa: F401
            from .modules.loyalty import models as _loyalty_models  # noqa: F401
            from .modules.pharmacy import models as _pharmacy_models  # noqa: F401
            from .modules.reports import models as _reports_models  # noqa: F401
            from .modules.tables import models as _tables_models  # noqa: F401
            from .modules.kds import models as _kds_models  # noqa: F401
            from .modules.payments import models as _payments_models  # noqa: F401
            from .modules.customers import models as _customers_models  # noqa: F401
            from .modules.staff import models as _staff_models  # noqa: F401
            from .modules.cash import models as _cash_models  # noqa: F401
            from .modules.suppliers import models as _suppliers_models  # noqa: F401
            from .modules.purchasing import models as _purchasing_models  # noqa: F401
            from .modules.settings import models as _settings_models  # noqa: F401
            from .modules.audit import models as _audit_models  # noqa: F401
            from .modules.integrations import models as _integrations_models  # noqa: F401
            from .modules.notifications import models as _notifications_models  # noqa: F401
            from .modules.ai import models as _ai_models  # noqa: F401
            from .modules.delivery import models as _delivery_models  # noqa: F401
            from .modules.support import models as _support_models  # noqa: F401

            # migrate_safe_dummy(db)
            # Dans un environnement de développement avec SQLite, la base de données est réinitialisée
            # à chaque démarrage, donc nous n'avons pas besoin d'exécuter migrate_safe_dummy
            # migrate_safe_dummy(db)
            
            # Initialize default business types and module configs (PLAN 0)
            try:
                # Créer d'abord les tables
                db.create_all()
                
                # Puis initialiser les types d'entreprise
                from .modules.settings.utils import BusinessTypeConfigurator
                BusinessTypeConfigurator.create_default_business_types()
            except Exception as e:
                # Avoid crashing dev bootstrap due to seed init
                print(f"[WARN] BusinessTypeConfigurator.create_default_business_types failed: {e}")
            
            # Seed demo data
            from .seed import seed_dev_data
            seed_dev_data()

    # Expose csrf token helper to templates for JS calls
    @app.context_processor
    def inject_csrf_token():
        return {"csrf_token": generate_csrf}

    # Register blueprints
    from .modules.pos import bp as pos_bp
    from .modules.inventory import bp as inventory_bp
    from .modules.ingredients import bp as ingredients_bp
    from .modules.variants import bp as variants_bp
    from .modules.expenses import bp as expenses_bp
    from .modules.menu_planning import bp as menu_planning_bp
    from .modules.loyalty import bp as loyalty_bp
    from .modules.pharmacy import bp as pharmacy_bp
    from .modules.reports import bp as reports_bp
    from .modules.accounts import bp as accounts_bp
    from .modules.catalog import bp as catalog_bp
    from .modules.sales import bp as sales_bp
    from .modules.tables import bp as tables_bp
    from .modules.kds import bp as kds_bp
    from .modules.payments import bp as payments_bp
    from .modules.customers import bp as customers_bp
    from .modules.staff import bp as staff_bp
    from .modules.cash import bp as cash_bp
    from .modules.suppliers import bp as suppliers_bp
    from .modules.purchasing import bp as purchasing_bp
    from .modules.settings import bp as settings_bp
    from .modules.audit import bp as audit_bp
    from .modules.integrations import bp as integrations_bp
    from .modules.notifications import bp as notifications_bp
    from .modules.ai import bp as ai_bp
    from .modules.delivery import bp as delivery_bp
    from .modules.support import bp as support_bp

    app.register_blueprint(pos_bp, url_prefix="/pos")
    app.register_blueprint(inventory_bp, url_prefix="/inventory")
    app.register_blueprint(ingredients_bp, url_prefix="/ingredients")
    app.register_blueprint(variants_bp, url_prefix="/variants")
    app.register_blueprint(expenses_bp, url_prefix="/expenses")
    app.register_blueprint(menu_planning_bp, url_prefix="/menu_planning")
    app.register_blueprint(loyalty_bp, url_prefix="/loyalty")
    app.register_blueprint(pharmacy_bp, url_prefix="/pharmacy")
    app.register_blueprint(reports_bp, url_prefix="/reports")
    app.register_blueprint(accounts_bp, url_prefix="/accounts")
    app.register_blueprint(catalog_bp, url_prefix="/catalog")
    app.register_blueprint(sales_bp, url_prefix="/sales")
    app.register_blueprint(tables_bp, url_prefix="/tables")
    app.register_blueprint(kds_bp, url_prefix="/kds")
    app.register_blueprint(payments_bp, url_prefix="/payments")
    app.register_blueprint(customers_bp, url_prefix="/customers")
    app.register_blueprint(staff_bp, url_prefix="/staff")
    app.register_blueprint(cash_bp, url_prefix="/cash")
    app.register_blueprint(suppliers_bp, url_prefix="/suppliers")
    app.register_blueprint(purchasing_bp, url_prefix="/purchasing")
    app.register_blueprint(settings_bp, url_prefix="/settings")
    app.register_blueprint(audit_bp, url_prefix="/audit")
    app.register_blueprint(integrations_bp, url_prefix="/integrations")
    app.register_blueprint(notifications_bp, url_prefix="/notifications")
    app.register_blueprint(ai_bp, url_prefix="/ai")
    app.register_blueprint(delivery_bp, url_prefix="/delivery")
    app.register_blueprint(support_bp, url_prefix="/support")

    # Root route redirect to POS
    @app.route("/")
    def index_root():
        from flask import redirect, url_for
        return redirect(url_for("pos.sale"))

    return app


class Config:
    SECRET_KEY = os.environ.get("SECRET_KEY", "dev-secret-change-me")
    SQLALCHEMY_DATABASE_URI = os.environ.get(
        "DATABASE_URL",
        "sqlite:///" + os.path.join(os.path.dirname(__file__), "..", "instance", "app.db"),
    )
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SESSION_COOKIE_SECURE = False
    REMEMBER_COOKIE_SECURE = False
    WTF_CSRF_ENABLED = True
    # Socket.IO
    SOCKETIO_REDIS_URL = os.environ.get("REDIS_URL", None)
    # Multi-tenant basics
    DEFAULT_BUSINESS_TYPE = "restaurant"  # or "retail"
    # Tailwind static
    TAILWIND_CDN = "https://cdn.tailwindcss.com"
    DEBUG = False
    TESTING = False
    AUTO_RESET_DB = False


class DevConfig(Config):
    DEBUG = True
    TESTING = True
    # Auto-reset DB in dev each run
    AUTO_RESET_DB = True
    # Dev SQLite in project root instance folder
    SQLALCHEMY_DATABASE_URI = (
        "sqlite:///" + os.path.join(os.getcwd(), "instance", "dev.db")
    )