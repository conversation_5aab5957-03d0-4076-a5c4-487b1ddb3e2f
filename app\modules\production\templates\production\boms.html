{% extends 'base.html' %}
{% block title %}Nomenclatures (BOM){% endblock %}

{% block content %}
<div class=\"max-w-6xl mx-auto p-6\">
    <!-- En-tête -->
    <div class=\"flex justify-between items-center mb-6\">
        <div>
            <h1 class=\"text-2xl font-bold text-white mb-2\">Nomenclatures (BOM)</h1>
            <p class=\"text-slate-400\">Gérez vos gammes de fabrication et composants</p>
        </div>
        
        <div class=\"flex gap-3\">
            <a href=\"{{ url_for('production.index') }}\" 
               class=\"bg-slate-700 hover:bg-slate-600 text-white px-4 py-2 rounded-md text-sm font-medium\">
                ← Retour
            </a>
        </div>
    </div>
    
    <!-- Formulaire de création -->
    <div class=\"bg-slate-800 rounded-lg p-6 mb-6\">
        <h2 class=\"text-lg font-semibold text-white mb-4\">Créer une nouvelle nomenclature</h2>
        
        <form method=\"POST\">
            {{ form.hidden_tag() }}
            
            <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\">
                <div>
                    {{ form.product_id.label(class=\"block text-sm font-medium text-slate-300 mb-2\") }}
                    {{ form.product_id(class=\"w-full bg-slate-700 border border-slate-600 text-white rounded-md px-3 py-2\") }}
                    {% if form.product_id.errors %}
                        <div class=\"text-red-400 text-sm mt-1\">
                            {% for error in form.product_id.errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
                
                <div>
                    {{ form.name.label(class=\"block text-sm font-medium text-slate-300 mb-2\") }}
                    {{ form.name(class=\"w-full bg-slate-700 border border-slate-600 text-white rounded-md px-3 py-2\") }}
                    {% if form.name.errors %}
                        <div class=\"text-red-400 text-sm mt-1\">
                            {% for error in form.name.errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
            </div>
            
            <div class=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4\">
                <div>
                    {{ form.version.label(class=\"block text-sm font-medium text-slate-300 mb-2\") }}
                    {{ form.version(class=\"w-full bg-slate-700 border border-slate-600 text-white rounded-md px-3 py-2\") }}
                    {% if form.version.errors %}
                        <div class=\"text-red-400 text-sm mt-1\">
                            {% for error in form.version.errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
                
                <div>
                    {{ form.quantity_produced.label(class=\"block text-sm font-medium text-slate-300 mb-2\") }}
                    {{ form.quantity_produced(class=\"w-full bg-slate-700 border border-slate-600 text-white rounded-md px-3 py-2\") }}
                    {% if form.quantity_produced.errors %}
                        <div class=\"text-red-400 text-sm mt-1\">
                            {% for error in form.quantity_produced.errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
                
                <div class=\"flex items-center pt-8\">
                    {{ form.is_active(class=\"w-4 h-4 text-blue-600 bg-slate-700 border-slate-600 rounded focus:ring-blue-500\") }}
                    {{ form.is_active.label(class=\"ml-2 text-sm font-medium text-slate-300\") }}
                </div>
            </div>
            
            <div class=\"mb-6\">
                {{ form.description.label(class=\"block text-sm font-medium text-slate-300 mb-2\") }}
                {{ form.description(class=\"w-full bg-slate-700 border border-slate-600 text-white rounded-md px-3 py-2\") }}
                {% if form.description.errors %}
                    <div class=\"text-red-400 text-sm mt-1\">
                        {% for error in form.description.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
            
            <button type=\"submit\" 
                    class=\"bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md font-medium\">
                Créer la nomenclature
            </button>
        </form>
    </div>
    
    <!-- Liste des nomenclatures existantes -->
    <div class=\"bg-slate-800 rounded-lg overflow-hidden\">
        <div class=\"px-6 py-4 border-b border-slate-700\">
            <h2 class=\"text-lg font-semibold text-white\">Nomenclatures existantes</h2>
        </div>
        
        {% if boms %}
        <div class=\"divide-y divide-slate-700\">
            {% for bom in boms %}
            <div class=\"px-6 py-4 hover:bg-slate-750\">
                <div class=\"flex justify-between items-start\">
                    <div class=\"flex-1\">
                        <div class=\"flex items-center gap-3 mb-2\">
                            <a href=\"{{ url_for('production.bom_details', bom_id=bom.id) }}\" 
                               class=\"text-white font-medium hover:text-blue-400\">
                                {{ bom.name }}
                            </a>
                            
                            <span class=\"text-sm text-slate-400\">v{{ bom.version }}</span>
                            
                            {% if bom.is_active %}
                            <span class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\">
                                Actif
                            </span>
                            {% else %}
                            <span class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800\">
                                Inactif
                            </span>
                            {% endif %}
                        </div>
                        
                        <div class=\"text-sm text-slate-400 mb-2\">
                            <strong>Produit:</strong> {{ bom.product.name if bom.product else 'Non défini' }}
                            • <strong>Quantité produite:</strong> {{ bom.quantity_produced }}
                        </div>
                        
                        {% if bom.description %}
                        <p class=\"text-sm text-slate-400 mb-2\">{{ bom.description }}</p>
                        {% endif %}
                        
                        <div class=\"flex gap-6 text-sm text-slate-400\">
                            <span>{{ bom.bom_items|length }} composants</span>
                            <span>{{ bom.production_steps|length }} étapes</span>
                            <span>Coût: {{ \"%.2f\"|format(bom.total_cost / 100) }}€</span>
                            <span>Créée: {{ bom.created_at.strftime('%d/%m/%Y') }}</span>
                        </div>
                    </div>
                    
                    <div class=\"flex gap-3\">
                        <!-- Bouton composants -->
                        <a href=\"{{ url_for('production.bom_items', bom_id=bom.id) }}\" 
                           class=\"bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm font-medium\">
                            Composants
                        </a>
                        
                        <!-- Bouton étapes -->
                        <a href=\"{{ url_for('production.bom_steps', bom_id=bom.id) }}\" 
                           class=\"bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm font-medium\">
                            Étapes
                        </a>
                        
                        <!-- Bouton détails -->
                        <a href=\"{{ url_for('production.bom_details', bom_id=bom.id) }}\" 
                           class=\"bg-purple-600 hover:bg-purple-700 text-white px-3 py-1 rounded text-sm font-medium\">
                            Détails
                        </a>
                    </div>
                </div>
                
                <!-- Aperçu rapide des coûts -->
                <div class=\"mt-3 grid grid-cols-3 gap-4 text-sm\">
                    <div class=\"bg-slate-700 rounded p-2\">
                        <div class=\"text-slate-400\">Matériaux</div>
                        <div class=\"text-white font-medium\">{{ \"%.2f\"|format(bom.total_material_cost / 100) }}€</div>
                    </div>
                    <div class=\"bg-slate-700 rounded p-2\">
                        <div class=\"text-slate-400\">Main d'œuvre</div>
                        <div class=\"text-white font-medium\">{{ \"%.2f\"|format(bom.total_labor_cost / 100) }}€</div>
                    </div>
                    <div class=\"bg-slate-700 rounded p-2\">
                        <div class=\"text-slate-400\">Total</div>
                        <div class=\"text-white font-medium\">{{ \"%.2f\"|format(bom.total_cost / 100) }}€</div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class=\"px-6 py-8 text-center\">
            <div class=\"text-slate-400 mb-4\">
                <svg class=\"w-12 h-12 mx-auto\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">
                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"></path>
                </svg>
            </div>
            <h3 class=\"text-lg font-medium text-white mb-2\">Aucune nomenclature</h3>
            <p class=\"text-slate-400\">Créez votre première nomenclature ci-dessus</p>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}