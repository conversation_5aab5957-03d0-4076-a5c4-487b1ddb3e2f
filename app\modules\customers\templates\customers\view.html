{% extends 'base.html' %}
{% block title %}{{ customer.display_name }} - Détail Client{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto">
  <!-- En-tête -->
  <div class="mb-6">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold">{{ customer.display_name }}</h1>
        <p class="text-slate-400 mt-2">
          {{ customer.customer_code }} • 
          {% if customer.customer_type == 'company' %}Entreprise{% else %}Particulier{% endif %} •
          <PERSON><PERSON><PERSON> le {{ customer.created_at.strftime('%d/%m/%Y') }}
        </p>
      </div>
      <div class="flex space-x-3">
        <a href="{{ url_for('customers.index') }}" class="bg-slate-700 hover:bg-slate-600 px-4 py-2 rounded-lg">
          ← Retour à la liste
        </a>
        <a href="{{ url_for('customers.edit_customer', id=customer.id) }}" class="bg-cyan-600 hover:bg-cyan-500 px-4 py-2 rounded-lg">
          ✏️ Modifier
        </a>
      </div>
    </div>
  </div>

  <!-- Messages -->
  {% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
      <div class="mb-6">
        {% for category, message in messages %}
          <div class="rounded-lg p-4 {% if category == 'error' %}bg-red-900 border border-red-700 text-red-100{% else %}bg-green-900 border border-green-700 text-green-100{% endif %}">
            {{ message }}
          </div>
        {% endfor %}
      </div>
    {% endif %}
  {% endwith %}

  <!-- Statistiques principales -->
  <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
    <div class="rounded-xl bg-slate-900 border border-slate-700 p-6">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-slate-400 text-sm">Commandes totales</p>
          <h3 class="text-2xl font-bold text-cyan-400">{{ stats.total_orders }}</h3>
        </div>
        <div class="text-3xl">🛒</div>
      </div>
    </div>
    
    <div class="rounded-xl bg-slate-900 border border-slate-700 p-6">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-slate-400 text-sm">Total dépensé</p>
          <h3 class="text-2xl font-bold text-green-400">{{ "%.2f"|format(stats.total_spent) }}€</h3>
        </div>
        <div class="text-3xl">💰</div>
      </div>
    </div>
    
    <div class="rounded-xl bg-slate-900 border border-slate-700 p-6">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-slate-400 text-sm">Points fidélité</p>
          <h3 class="text-2xl font-bold text-purple-400">{{ stats.loyalty_points }}</h3>
        </div>
        <div class="text-3xl">⭐</div>
      </div>
    </div>
    
    <div class="rounded-xl bg-slate-900 border border-slate-700 p-6">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-slate-400 text-sm">Dernière visite</p>
          <h3 class="text-lg font-bold text-yellow-400">
            {% if customer.last_visit %}
              {{ customer.last_visit.strftime('%d/%m/%Y') }}
            {% else %}
              Jamais
            {% endif %}
          </h3>
        </div>
        <div class="text-3xl">📅</div>
      </div>
    </div>
  </div>

  <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <!-- Informations principales -->
    <div class="lg:col-span-2 space-y-6">
      <!-- Informations personnelles -->
      <div class="rounded-xl bg-slate-900 border border-slate-700 p-6">
        <div class="flex items-center justify-between mb-4">
          <h2 class="text-xl font-semibold">👤 Informations personnelles</h2>
          <div class="flex items-center space-x-2">
            {% if customer.is_vip %}
              <span class="px-2 py-1 text-xs rounded-full bg-yellow-900 text-yellow-300">⭐ VIP</span>
            {% endif %}
            <span class="px-2 py-1 text-xs rounded-full {% if customer.is_active %}bg-green-900 text-green-300{% else %}bg-red-900 text-red-300{% endif %}">
              {{ 'Actif' if customer.is_active else 'Inactif' }}
            </span>
          </div>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-slate-300 mb-1">Nom complet</label>
              <div class="text-lg">{{ customer.full_name }}</div>
            </div>
            
            {% if customer.company_name %}
              <div>
                <label class="block text-sm font-medium text-slate-300 mb-1">Entreprise</label>
                <div class="text-lg">{{ customer.company_name }}</div>
              </div>
            {% endif %}
            
            <div>
              <label class="block text-sm font-medium text-slate-300 mb-1">Code client</label>
              <div class="font-mono">{{ customer.customer_code }}</div>
            </div>
            
            {% if customer.birth_date %}
              <div>
                <label class="block text-sm font-medium text-slate-300 mb-1">Âge</label>
                <div>{{ customer.age }} ans</div>
              </div>
            {% endif %}
            
            {% if customer.gender %}
              <div>
                <label class="block text-sm font-medium text-slate-300 mb-1">Genre</label>
                <div>{{ {'M': 'Masculin', 'F': 'Féminin', 'Other': 'Autre'}[customer.gender] }}</div>
              </div>
            {% endif %}
          </div>
          
          <div class="space-y-4">
            {% if customer.email %}
              <div>
                <label class="block text-sm font-medium text-slate-300 mb-1">Email</label>
                <div><a href="mailto:{{ customer.email }}" class="text-cyan-400 hover:text-cyan-300">{{ customer.email }}</a></div>
              </div>
            {% endif %}
            
            {% if customer.phone %}
              <div>
                <label class="block text-sm font-medium text-slate-300 mb-1">Téléphone</label>
                <div><a href="tel:{{ customer.phone }}" class="text-cyan-400 hover:text-cyan-300">{{ customer.phone }}</a></div>
              </div>
            {% endif %}
            
            {% if customer.mobile %}
              <div>
                <label class="block text-sm font-medium text-slate-300 mb-1">Mobile</label>
                <div><a href="tel:{{ customer.mobile }}" class="text-cyan-400 hover:text-cyan-300">{{ customer.mobile }}</a></div>
              </div>
            {% endif %}
            
            <div>
              <label class="block text-sm font-medium text-slate-300 mb-1">Langue préférée</label>
              <div>{{ {'fr': 'Français', 'en': 'Anglais', 'es': 'Espagnol', 'de': 'Allemand', 'it': 'Italien'}[customer.preferred_language] }}</div>
            </div>
            
            {% if customer.credit_limit_cents > 0 %}
              <div>
                <label class="block text-sm font-medium text-slate-300 mb-1">Limite de crédit</label>
                <div class="text-green-400">{{ "%.2f"|format(customer.credit_limit) }}€</div>
              </div>
            {% endif %}
          </div>
        </div>
        
        {% if customer.notes %}
          <div class="mt-4 pt-4 border-t border-slate-700">
            <label class="block text-sm font-medium text-slate-300 mb-2">Notes</label>
            <div class="text-slate-300 whitespace-pre-wrap">{{ customer.notes }}</div>
          </div>
        {% endif %}
      </div>

      <!-- Adresses -->
      <div class="rounded-xl bg-slate-900 border border-slate-700 p-6">
        <div class="flex items-center justify-between mb-4">
          <h2 class="text-xl font-semibold">🏠 Adresses</h2>
          <a href="{{ url_for('customers.add_address', customer_id=customer.id) }}" class="bg-cyan-600 hover:bg-cyan-500 px-3 py-1 rounded text-sm">
            ➕ Ajouter
          </a>
        </div>
        
        {% if addresses %}
          <div class="space-y-3">
            {% for address in addresses %}
            <div class="bg-slate-800 border border-slate-700 rounded-lg p-4">
              <div class="flex items-start justify-between">
                <div class="flex-1">
                  <div class="flex items-center space-x-2 mb-2">
                    <h3 class="font-medium">
                      {% if address.label %}{{ address.label }}{% else %}{{ address.address_type|title }}{% endif %}
                    </h3>
                    {% if address.is_default %}
                      <span class="px-2 py-1 text-xs rounded bg-cyan-900 text-cyan-300">Par défaut</span>
                    {% endif %}
                    {% if not address.is_active %}
                      <span class="px-2 py-1 text-xs rounded bg-red-900 text-red-300">Inactive</span>
                    {% endif %}
                  </div>
                  <div class="text-sm text-slate-400">{{ address.full_address }}</div>
                </div>
                <div class="flex space-x-1 ml-4">
                  <a href="{{ url_for('customers.edit_address', id=address.id) }}" class="px-2 py-1 bg-blue-600 hover:bg-blue-500 text-white text-xs rounded">
                    ✏️
                  </a>
                  <form method="post" action="{{ url_for('customers.delete_address', id=address.id) }}" class="inline" onsubmit="return confirm('Supprimer cette adresse ?')">
                    {{ csrf_token() }}
                    <button class="px-2 py-1 bg-red-600 hover:bg-red-500 text-white text-xs rounded">
                      🗑️
                    </button>
                  </form>
                </div>
              </div>
            </div>
            {% endfor %}
          </div>
        {% else %}
          <div class="text-center py-6 text-slate-400">
            <div class="text-3xl mb-2">🏠</div>
            <p class="text-sm">Aucune adresse enregistrée</p>
            <a href="{{ url_for('customers.add_address', customer_id=customer.id) }}" class="text-cyan-400 hover:text-cyan-300 text-sm">
              Ajouter une adresse →
            </a>
          </div>
        {% endif %}
      </div>

      <!-- Historique des visites -->
      <div class="rounded-xl bg-slate-900 border border-slate-700 p-6">
        <div class="flex items-center justify-between mb-4">
          <h2 class="text-xl font-semibold">📋 Historique des visites</h2>
          <a href="{{ url_for('customers.add_visit', customer_id=customer.id) }}" class="bg-cyan-600 hover:bg-cyan-500 px-3 py-1 rounded text-sm">
            ➕ Nouvelle visite
          </a>
        </div>
        
        {% if recent_visits %}
          <div class="space-y-3 max-h-64 overflow-y-auto">
            {% for visit in recent_visits %}
            <div class="bg-slate-800 border border-slate-700 rounded-lg p-3">
              <div class="flex items-center justify-between">
                <div class="flex-1">
                  <div class="flex items-center space-x-3">
                    <span class="px-2 py-1 text-xs rounded 
                      {% if visit.visit_type == 'in_store' %}bg-blue-900 text-blue-300
                      {% elif visit.visit_type == 'delivery' %}bg-green-900 text-green-300
                      {% elif visit.visit_type == 'takeaway' %}bg-yellow-900 text-yellow-300
                      {% else %}bg-purple-900 text-purple-300{% endif %}">
                      {{ {'in_store': 'En magasin', 'delivery': 'Livraison', 'takeaway': 'À emporter', 'online': 'En ligne'}[visit.visit_type] }}
                    </span>
                    <span class="text-sm text-slate-400">{{ visit.visit_date.strftime('%d/%m/%Y %H:%M') }}</span>
                  </div>
                  
                  <div class="mt-2 text-sm text-slate-400">
                    {% if visit.table_number %}Table {{ visit.table_number }}{% endif %}
                    {% if visit.party_size %} • {{ visit.party_size }} personnes{% endif %}
                    {% if visit.duration_minutes %} • {{ visit.duration_minutes }}min{% endif %}
                    {% if visit.satisfaction_rating %}
                      • Note: {{ visit.satisfaction_rating }}/5 
                      <span class="text-yellow-400">{{ '⭐' * visit.satisfaction_rating }}</span>
                    {% endif %}
                  </div>
                  
                  {% if visit.notes %}
                    <div class="mt-1 text-sm text-slate-300">{{ visit.notes }}</div>
                  {% endif %}
                </div>
              </div>
            </div>
            {% endfor %}
          </div>
        {% else %}
          <div class="text-center py-6 text-slate-400">
            <div class="text-3xl mb-2">📋</div>
            <p class="text-sm">Aucune visite enregistrée</p>
            <a href="{{ url_for('customers.add_visit', customer_id=customer.id) }}" class="text-cyan-400 hover:text-cyan-300 text-sm">
              Enregistrer une visite →
            </a>
          </div>
        {% endif %}
      </div>
    </div>

    <!-- Panneau latéral -->
    <div class="lg:col-span-1 space-y-6">
      <!-- Actions rapides -->
      <div class="rounded-xl bg-slate-900 border border-slate-700 p-6">
        <h3 class="text-lg font-semibold mb-4">⚡ Actions rapides</h3>
        <div class="space-y-2">
          <button onclick="toggleVipStatus()" class="w-full {% if customer.is_vip %}bg-yellow-600 hover:bg-yellow-500{% else %}bg-gray-600 hover:bg-gray-500{% endif %} rounded-lg px-4 py-2 text-center">
            {{ '⭐ Retirer VIP' if customer.is_vip else '⭐ Marquer VIP' }}
          </button>
          
          <button onclick="toggleActiveStatus()" class="w-full {% if customer.is_active %}bg-red-600 hover:bg-red-500{% else %}bg-green-600 hover:bg-green-500{% endif %} rounded-lg px-4 py-2 text-center">
            {{ '🚫 Désactiver' if customer.is_active else '✅ Activer' }}
          </button>
          
          <a href="{{ url_for('customers.add_loyalty_points', customer_id=customer.id) }}" class="block w-full bg-purple-600 hover:bg-purple-500 rounded-lg px-4 py-2 text-center">
            ⭐ Ajuster points
          </a>
          
          <button onclick="sendEmail()" class="w-full bg-blue-600 hover:bg-blue-500 rounded-lg px-4 py-2 text-center">
            📧 Envoyer email
          </button>
          
          <button onclick="generateReport()" class="w-full bg-indigo-600 hover:bg-indigo-500 rounded-lg px-4 py-2 text-center">
            📊 Rapport client
          </button>
        </div>
      </div>

      <!-- Groupes d'appartenance -->
      {% if memberships %}
      <div class="rounded-xl bg-slate-900 border border-slate-700 p-6">
        <h3 class="text-lg font-semibold mb-4">🏷️ Groupes</h3>
        <div class="space-y-2">
          {% for membership in memberships %}
          <div class="flex items-center justify-between p-2 bg-slate-800 rounded">
            <div class="flex items-center space-x-2">
              <div class="w-3 h-3 rounded-full" style="background-color: {{ membership.group.color }}"></div>
              <span class="text-sm">{{ membership.group.name }}</span>
            </div>
            <span class="text-xs text-slate-400">
              Depuis {{ membership.joined_at.strftime('%d/%m/%Y') }}
            </span>
          </div>
          {% endfor %}
        </div>
      </div>
      {% endif %}

      <!-- Informations complémentaires -->
      <div class="rounded-xl bg-slate-900 border border-slate-700 p-6">
        <h3 class="text-lg font-semibold mb-4">📊 Détails</h3>
        <div class="space-y-3 text-sm">
          <div class="flex justify-between">
            <span class="text-slate-400">Créé le:</span>
            <span>{{ customer.created_at.strftime('%d/%m/%Y') }}</span>
          </div>
          
          <div class="flex justify-between">
            <span class="text-slate-400">Dernière MAJ:</span>
            <span>{{ customer.updated_at.strftime('%d/%m/%Y') }}</span>
          </div>
          
          {% if customer.current_balance_cents != 0 %}
            <div class="flex justify-between">
              <span class="text-slate-400">Solde compte:</span>
              <span class="{% if customer.current_balance > 0 %}text-green-400{% else %}text-red-400{% endif %}">
                {{ "%.2f"|format(customer.current_balance) }}€
              </span>
            </div>
          {% endif %}
          
          <div class="flex justify-between">
            <span class="text-slate-400">Marketing:</span>
            <span class="{% if customer.marketing_consent %}text-green-400{% else %}text-red-400{% endif %}">
              {{ 'Accepté' if customer.marketing_consent else 'Refusé' }}
            </span>
          </div>
          
          <div class="flex justify-between">
            <span class="text-slate-400">ID client:</span>
            <span class="font-mono">#{{ customer.id }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
function toggleVipStatus() {
  const form = document.createElement('form');
  form.method = 'POST';
  form.action = '{{ url_for("customers.toggle_customer_vip", id=customer.id) }}';
  
  const csrfToken = document.createElement('input');
  csrfToken.type = 'hidden';
  csrfToken.name = 'csrf_token';
  csrfToken.value = '{{ csrf_token() }}';
  
  form.appendChild(csrfToken);
  document.body.appendChild(form);
  form.submit();
}

function toggleActiveStatus() {
  const form = document.createElement('form');
  form.method = 'POST';
  form.action = '{{ url_for("customers.toggle_customer_status", id=customer.id) }}';
  
  const csrfToken = document.createElement('input');
  csrfToken.type = 'hidden';
  csrfToken.name = 'csrf_token';
  csrfToken.value = '{{ csrf_token() }}';
  
  form.appendChild(csrfToken);
  document.body.appendChild(form);
  form.submit();
}

function sendEmail() {
  {% if customer.email %}
    window.location.href = 'mailto:{{ customer.email }}';
  {% else %}
    alert('Ce client n\'a pas d\'adresse email enregistrée');
  {% endif %}
}

function generateReport() {
  alert('Génération du rapport client en cours de développement');
}
</script>
{% endblock %}