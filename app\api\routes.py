from flask import jsonify, request, current_app
from flask_jwt_extended import J<PERSON><PERSON><PERSON><PERSON>, jwt_required, create_access_token, get_jwt_identity, get_jwt
from flask_restx import Api, Resource, fields, Namespace
from datetime import datetime, timedelta
from functools import wraps
import logging

from app.extensions import db
from app.modules.accounts.models import User, Business, Tenant, Subscription
from app.modules.settings.models import Setting
from .utils import create_api_response, require_permissions, rate_limit, APIRateLimiter

# Configuration de la documentation Swagger
api = Api(
    title='POS System GT2 API',
    version='1.0',
    description='API complète pour le système de point de vente SaaS',
    doc='/docs/',
    authorizations={
        'Bearer': {
            'type': 'apiKey',
            'in': 'header',
            'name': 'Authorization',
            'description': 'Ajouter "Bearer " devant votre token JWT'
        }
    },
    security='Bearer'
)

# Namespaces
auth_ns = Namespace('auth', description='Authentification')
business_ns = Namespace('business', description='Gestion des entreprises')
users_ns = Namespace('users', description='Gestion des utilisateurs')
catalog_ns = Namespace('catalog', description='Catalogue de produits')
sales_ns = Namespace('sales', description='Ventes et commandes')
inventory_ns = Namespace('inventory', description='Gestion des stocks')
settings_ns = Namespace('settings', description='Paramètres')

# Ajouter les namespaces à l'API
api.add_namespace(auth_ns)
api.add_namespace(business_ns)
api.add_namespace(users_ns)
api.add_namespace(catalog_ns)
api.add_namespace(sales_ns)
api.add_namespace(inventory_ns)
api.add_namespace(settings_ns)

# Modèles Swagger
login_model = api.model('Login', {
    'email': fields.String(required=True, description='Email de l\'utilisateur'),
    'password': fields.String(required=True, description='Mot de passe')
})

token_model = api.model('Token', {
    'access_token': fields.String(description='Token JWT'),
    'token_type': fields.String(description='Type de token'),
    'expires_in': fields.Integer(description='Expiration en secondes')
})

user_model = api.model('User', {
    'id': fields.Integer(readonly=True),
    'email': fields.String(required=True),
    'first_name': fields.String(),
    'last_name': fields.String(),
    'role': fields.String(),
    'is_active': fields.Boolean(),
    'created_at': fields.DateTime()
})

business_model = api.model('Business', {
    'id': fields.Integer(readonly=True),
    'business_id': fields.String(required=True),
    'business_name': fields.String(required=True),
    'business_type': fields.String(),
    'description': fields.String(),
    'is_active': fields.Boolean(),
    'created_at': fields.DateTime()
})

# Modèles de requête/réponse
login_response_model = api.model('LoginResponse', {
    'success': fields.Boolean(),
    'data': fields.Nested(token_model),
    'message': fields.String()
})

error_response_model = api.model('ErrorResponse', {
    'success': fields.Boolean(default=False),
    'timestamp': fields.DateTime(),
    'message': fields.String(),
    'errors': fields.List(fields.String)
})

# Routes d'authentification
@auth_ns.route('/login')
class Login(Resource):
    @api.expect(login_model)
    @api.response(200, 'Connexion réussie', login_response_model)
    @api.response(400, 'Données invalides', error_response_model)
    @api.response(401, 'Identifiants invalides', error_response_model)
    @rate_limit('anonymous')
    def post(self):
        """Authentification utilisateur et génération de token JWT"""
        data = request.get_json()
        
        if not data or not data.get('email') or not data.get('password'):
            return create_api_response(
                False,
                message="Email et mot de passe requis",
                status_code=400
            )
        
        user = User.query.filter_by(email=data['email']).first()
        
        if not user or not user.check_password(data['password']):
            return create_api_response(
                False,
                message="Identifiants invalides",
                status_code=401
            )
        
        if not user.is_active:
            return create_api_response(
                False,
                message="Compte désactivé",
                status_code=401
            )
        
        if user.is_locked:
            return create_api_response(
                False,
                message="Compte verrouillé. Réessayez plus tard.",
                status_code=401
            )
        
        # Générer le token JWT
        access_token = create_access_token(
            identity=user.id,
            expires_delta=timedelta(hours=24),
            additional_claims={
                'role': user.role,
                'business_id': user.business_id_fk,
                'tenant_id': user.business.tenant_id if user.business else None
            }
        )
        
        # Enregistrer la connexion
        user.record_login(request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR')))
        
        return create_api_response(
            True,
            data={
                'access_token': access_token,
                'token_type': 'Bearer',
                'expires_in': 86400  # 24 heures
            },
            message="Connexion réussie"
        )

@auth_ns.route('/logout')
class Logout(Resource):
    @api.doc(security='Bearer')
    @jwt_required()
    @api.response(200, 'Déconnexion réussie')
    def post(self):
        """Déconnexion utilisateur (invalide le token côté client)"""
        return create_api_response(
            True,
            message="Déconnexion réussie"
        )

@auth_ns.route('/refresh')
class Refresh(Resource):
    @api.doc(security='Bearer')
    @jwt_required(refresh=True)
    @api.response(200, 'Token renouvelé', token_model)
    def post(self):
        """Renouveler le token JWT"""
        current_user_id = get_jwt_identity()
        new_token = create_access_token(identity=current_user_id)
        
        return create_api_response(
            True,
            data={
                'access_token': new_token,
                'token_type': 'Bearer',
                'expires_in': 86400
            },
            message="Token renouvelé"
        )

# Routes des entreprises
@business_ns.route('/')
class BusinessList(Resource):
    @api.doc(security='Bearer')
    @jwt_required()
    @require_permissions('manage_business')
    @api.marshal_list_with(business_model)
    @api.response(200, 'Liste des entreprises')
    @rate_limit('authenticated')
    def get(self):
        """Récupérer la liste des entreprises"""
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        
        if not user:
            return create_api_response(False, message="Utilisateur non trouvé", status_code=404)
        
        businesses = Business.query.filter_by(tenant_id=user.business.tenant_id).all()
        
        return create_api_response(
            True,
            data=[{
                'id': business.id,
                'business_id': business.business_id,
                'business_name': business.business_name,
                'business_type': business.business_type,
                'description': business.description,
                'is_active': business.is_active,
                'created_at': business.created_at.isoformat()
            } for business in businesses]
        )

@business_ns.route('/<int:business_id>')
@business_ns.param('business_id', 'ID de l\'entreprise')
class BusinessResource(Resource):
    @api.doc(security='Bearer')
    @jwt_required()
    @require_permissions('manage_business')
    @api.marshal_with(business_model)
    @api.response(200, 'Détails de l\'entreprise')
    @api.response(404, 'Entreprise non trouvée')
    @rate_limit('authenticated')
    def get(self, business_id):
        """Récupérer les détails d'une entreprise"""
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        
        if not user:
            return create_api_response(False, message="Utilisateur non trouvé", status_code=404)
        
        business = Business.query.filter_by(
            id=business_id,
            tenant_id=user.business.tenant_id
        ).first()
        
        if not business:
            return create_api_response(False, message="Entreprise non trouvée", status_code=404)
        
        return create_api_response(
            True,
            data={
                'id': business.id,
                'business_id': business.business_id,
                'business_name': business.business_name,
                'business_type': business.business_type,
                'description': business.description,
                'is_active': business.is_active,
                'created_at': business.created_at.isoformat()
            }
        )

# Routes des utilisateurs
@users_ns.route('/')
class UserList(Resource):
    @api.doc(security='Bearer')
    @jwt_required()
    @require_permissions('manage_users')
    @api.marshal_list_with(user_model)
    @api.response(200, 'Liste des utilisateurs')
    @rate_limit('authenticated')
    def get(self):
        """Récupérer la liste des utilisateurs"""
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        
        if not user:
            return create_api_response(False, message="Utilisateur non trouvé", status_code=404)
        
        users = User.query.filter_by(business_id_fk=user.business_id_fk).all()
        
        return create_api_response(
            True,
            data=[{
                'id': u.id,
                'email': u.email,
                'first_name': u.first_name,
                'last_name': u.last_name,
                'role': u.role,
                'is_active': u.is_active,
                'created_at': u.created_at.isoformat()
            } for u in users]
        )

@users_ns.route('/<int:user_id>')
@users_ns.param('user_id', 'ID de l\'utilisateur')
class UserResource(Resource):
    @api.doc(security='Bearer')
    @jwt_required()
    @require_permissions('manage_users')
    @api.marshal_with(user_model)
    @api.response(200, 'Détails de l\'utilisateur')
    @api.response(404, 'Utilisateur non trouvé')
    @rate_limit('authenticated')
    def get(self, user_id):
        """Récupérer les détails d'un utilisateur"""
        current_user_id = get_jwt_identity()
        current_user = User.query.get(current_user_id)
        
        if not current_user:
            return create_api_response(False, message="Utilisateur non trouvé", status_code=404)
        
        user = User.query.filter_by(
            id=user_id,
            business_id_fk=current_user.business_id_fk
        ).first()
        
        if not user:
            return create_api_response(False, message="Utilisateur non trouvé", status_code=404)
        
        return create_api_response(
            True,
            data={
                'id': user.id,
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'role': user.role,
                'is_active': user.is_active,
                'created_at': user.created_at.isoformat()
            }
        )

# Routes de test
@api.route('/health')
class HealthCheck(Resource):
    @api.response(200, 'Système en bonne santé')
    @rate_limit('anonymous')
    def get(self):
        """Vérification de l'état du système"""
        return create_api_response(
            True,
            data={
                'status': 'healthy',
                'timestamp': datetime.utcnow().isoformat()
            },
            message="Système en bonne santé"
        )

@api.route('/version')
class Version(Resource):
    @api.response(200, 'Version de l\'API')
    @rate_limit('anonymous')
    def get(self):
        """Récupérer la version de l'API"""
        return create_api_response(
            True,
            data={
                'version': '1.0.0',
                'api_version': 'v1'
            },
            message="Version de l'API"
        )