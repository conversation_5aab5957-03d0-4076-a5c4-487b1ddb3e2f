{% extends "base.html" %}

{% block title %}Paramètres généraux - {{ super() }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">Accueil</a></li>
                        <li class="breadcrumb-item"><a href="{{ url_for('settings.index') }}">Paramètres</a></li>
                        <li class="breadcrumb-item active">Paramètres généraux</li>
                    </ol>
                </div>
                <h4 class="page-title">Paramètres généraux</h4>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="header-title">Configuration de l'entreprise</h4>
                    <p class="text-muted mb-0">Configurez les informations générales de votre entreprise</p>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('settings.save_general_settings') }}">
                        {{ form.hidden_tag() }}
                        
                        <div class="row">
                            <!-- Informations de l'entreprise -->
                            <div class="col-lg-6">
                                <h5 class="mb-3">Informations de l'entreprise</h5>
                                
                                <div class="mb-3">
                                    {{ form.company_name.label(class="form-label") }}
                                    {{ form.company_name(class="form-control", value=settings.get('company_name', '')) }}
                                    {% if form.company_name.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.company_name.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <div class="mb-3">
                                    {{ form.company_address.label(class="form-label") }}
                                    {{ form.company_address(class="form-control", value=settings.get('company_address', '')) }}
                                    {% if form.company_address.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.company_address.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form.company_phone.label(class="form-label") }}
                                            {{ form.company_phone(class="form-control", value=settings.get('company_phone', '')) }}
                                            {% if form.company_phone.errors %}
                                                <div class="invalid-feedback d-block">
                                                    {% for error in form.company_phone.errors %}
                                                        {{ error }}
                                                    {% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form.company_email.label(class="form-label") }}
                                            {{ form.company_email(class="form-control", value=settings.get('company_email', '')) }}
                                            {% if form.company_email.errors %}
                                                <div class="invalid-feedback d-block">
                                                    {% for error in form.company_email.errors %}
                                                        {{ error }}
                                                    {% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form.company_website.label(class="form-label") }}
                                            {{ form.company_website(class="form-control", value=settings.get('company_website', '')) }}
                                            {% if form.company_website.errors %}
                                                <div class="invalid-feedback d-block">
                                                    {% for error in form.company_website.errors %}
                                                        {{ error }}
                                                    {% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form.tax_number.label(class="form-label") }}
                                            {{ form.tax_number(class="form-control", value=settings.get('tax_number', '')) }}
                                            {% if form.tax_number.errors %}
                                                <div class="invalid-feedback d-block">
                                                    {% for error in form.tax_number.errors %}
                                                        {{ error }}
                                                    {% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Paramètres régionaux -->
                            <div class="col-lg-6">
                                <h5 class="mb-3">Paramètres régionaux</h5>
                                
                                <div class="mb-3">
                                    {{ form.currency.label(class="form-label") }}
                                    {% set current_currency = settings.get('currency', 'EUR') %}
                                    {{ form.currency(class="form-select") }}
                                    <script>
                                        document.querySelector('select[name="currency"]').value = '{{ current_currency }}';
                                    </script>
                                    {% if form.currency.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.currency.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <div class="mb-3">
                                    {{ form.timezone.label(class="form-label") }}
                                    {% set current_timezone = settings.get('timezone', 'Europe/Paris') %}
                                    {{ form.timezone(class="form-select") }}
                                    <script>
                                        document.querySelector('select[name="timezone"]').value = '{{ current_timezone }}';
                                    </script>
                                    {% if form.timezone.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.timezone.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <div class="mb-3">
                                    {{ form.date_format.label(class="form-label") }}
                                    {% set current_date_format = settings.get('date_format', 'DD/MM/YYYY') %}
                                    {{ form.date_format(class="form-select") }}
                                    <script>
                                        document.querySelector('select[name="date_format"]').value = '{{ current_date_format }}';
                                    </script>
                                    {% if form.date_format.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.date_format.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <div class="mb-3">
                                    {{ form.language.label(class="form-label") }}
                                    {% set current_language = settings.get('language', 'fr') %}
                                    {{ form.language(class="form-select") }}
                                    <script>
                                        document.querySelector('select[name="language"]').value = '{{ current_language }}';
                                    </script>
                                    {% if form.language.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.language.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="text-end">
                                    <a href="{{ url_for('settings.index') }}" class="btn btn-secondary me-2">Annuler</a>
                                    {{ form.submit(class="btn btn-primary") }}
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}