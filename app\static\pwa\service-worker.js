// Service Worker pour l'application POS PWA
const CACHE_NAME = 'pos-system-v1.0.0';
const urlsToCache = [
  '/',
  '/static/css/bootstrap.min.css',
  '/static/css/style.css',
  '/static/css/mobile.css',
  '/static/js/bootstrap.bundle.min.js',
  '/static/js/mobile.js',
  '/static/pwa/manifest.json'
];

// Installation du service worker
self.addEventListener('install', event => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => {
        console.log('Service Worker: Cache ouvert');
        return cache.addAll(urlsToCache);
      })
  );
});

// Activation du service worker
self.addEventListener('activate', event => {
  event.waitUntil(
    caches.keys().then(cacheNames => {
      return Promise.all(
        cacheNames.map(cacheName => {
          if (cacheName !== CACHE_NAME) {
            console.log('Service Worker: Suppression ancien cache', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
});

// Intercepter les requêtes et servir depuis le cache
self.addEventListener('fetch', event => {
  // Pour les requêtes API, toujours aller sur le réseau
  if (event.request.url.includes('/api/')) {
    event.respondWith(fetch(event.request));
    return;
  }
  
  // Pour les autres requêtes, utiliser le cache avec fallback sur le réseau
  event.respondWith(
    caches.match(event.request)
      .then(response => {
        // Retourner la réponse depuis le cache ou faire la requête réseau
        return response || fetch(event.request);
      })
  );
});

// Gestion des notifications push
self.addEventListener('push', event => {
  if (event.data) {
    const data = event.data.json();
    const title = data.title || 'Notification POS';
    const options = {
      body: data.body || 'Vous avez une nouvelle notification',
      icon: '/static/pwa/icons/icon-192x192.png',
      badge: '/static/pwa/icons/icon-72x72.png',
      data: {
        url: data.url || '/'
      }
    };

    event.waitUntil(
      self.registration.showNotification(title, options)
    );
  }
});

// Gestion du clic sur les notifications
self.addEventListener('notificationclick', event => {
  event.notification.close();

  event.waitUntil(
    clients.openWindow(event.notification.data.url)
  );
});

// Gestion des messages depuis l'application
self.addEventListener('message', event => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
});