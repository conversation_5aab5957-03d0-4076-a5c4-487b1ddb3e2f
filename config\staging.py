"""Configuration de staging pour l'application POS System"""
import os
from .default import Config as DefaultConfig

class StagingConfig(DefaultConfig):
    """Configuration de staging"""
    
    # Mode debug activé pour le staging
    DEBUG = True
    TESTING = False
    
    # Configuration de la base de données
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or \
        '************************************************/pos_system_staging'
    
    # Configuration du secret key
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-staging-secret-key-here'
    
    # Configuration de Redis
    REDIS_URL = os.environ.get('REDIS_URL') or 'redis://cache:6379/1'
    
    # Configuration du cache
    CACHE_TYPE = 'redis'
    CACHE_REDIS_URL = REDIS_URL
    CACHE_DEFAULT_TIMEOUT = 300
    
    # Configuration de l'authentification JWT
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY') or 'your-jwt-staging-key-here'
    JWT_ACCESS_TOKEN_EXPIRES = 3600  # 1 heure
    JWT_REFRESH_TOKEN_EXPIRES = 86400  # 24 heures
    
    # Configuration du logging
    LOG_LEVEL = 'DEBUG'
    LOG_FILE = '/var/log/pos-system/staging.log'
    
    # Configuration de la sécurité
    WTF_CSRF_ENABLED = True
    SESSION_COOKIE_SECURE = False  # False pour le staging
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    
    # Configuration du rate limiting
    RATELIMIT_STORAGE_URL = REDIS_URL
    RATELIMIT_STRATEGY = 'fixed-window'
    DEFAULT_RATE_LIMIT = '5000/hour'
    
    # Configuration des performances
    SQLALCHEMY_POOL_SIZE = 10
    SQLALCHEMY_MAX_OVERFLOW = 20
    SQLALCHEMY_POOL_TIMEOUT = 30
    SQLALCHEMY_POOL_RECYCLE = 3600
    
    # Configuration du cache pour les templates
    TEMPLATE_CACHE_TIMEOUT = 60
    
    # Configuration des fichiers statiques
    SEND_FILE_MAX_AGE_DEFAULT = 3600  # 1 heure
    
    # Configuration de l'API
    API_RATE_LIMIT = '5000/hour'
    
    # Configuration des emails
    MAIL_SERVER = os.environ.get('MAIL_SERVER') or 'smtp.gmail.com'
    MAIL_PORT = int(os.environ.get('MAIL_PORT') or 587)
    MAIL_USE_TLS = os.environ.get('MAIL_USE_TLS', 'true').lower() in ['true', 'on', '1']
    MAIL_USERNAME = os.environ.get('MAIL_USERNAME')
    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD')
    
    # Configuration du monitoring
    ENABLE_MONITORING = True
    MONITORING_ENDPOINT = '/metrics'
    
    # Configuration des sauvegardes
    ENABLE_BACKUP = True
    BACKUP_SCHEDULE = '0 3 * * *'  # Tous les jours à 3h00
    
    @classmethod
    def init_app(cls, app):
        """Initialisation spécifique au staging"""
        DefaultConfig.init_app(app)
        
        # Configuration du logging pour le staging
        import logging
        from logging.handlers import RotatingFileHandler
        
        if not app.debug and not app.testing:
            # Créer le répertoire de logs si nécessaire
            os.makedirs(os.path.dirname(cls.LOG_FILE), exist_ok=True)
            
            # Configurer le handler de fichiers rotatifs
            file_handler = RotatingFileHandler(
                cls.LOG_FILE, 
                maxBytes=10240000,  # 10MB
                backupCount=5
            )
            file_handler.setFormatter(logging.Formatter(
                '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
            ))
            file_handler.setLevel(logging.DEBUG)
            app.logger.addHandler(file_handler)
            
            app.logger.setLevel(logging.DEBUG)
            app.logger.info('POS System staging startup')