{% extends "base.html" %}

{% block title %}Analyse de Sentiment{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-comment-dots"></i> Analyse de Sentiment</h1>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-edit"></i> Analyser un Texte</h5>
                </div>
                <div class="card-body">
                    <form id="sentimentForm">
                        <div class="mb-3">
                            <label for="textInput" class="form-label">Texte à analyser</label>
                            <textarea class="form-control" id="textInput" rows="5" 
                                      placeholder="Entrez le texte à analyser (avis client, commentaire, etc.)"></textarea>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> Analyser le Sentiment
                        </button>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-poll"></i> Résultat de l'Analyse</h5>
                </div>
                <div class="card-body">
                    <div id="analysisResult" class="text-center" style="display: none;">
                        <div class="sentiment-badge mb-3">
                            <span id="sentimentLabel" class="badge fs-5"></span>
                        </div>
                        <div class="sentiment-score mb-3">
                            <h6>Score de Sentiment</h6>
                            <div class="progress">
                                <div id="sentimentBar" class="progress-bar" role="progressbar" 
                                     aria-valuenow="0" aria-valuemin="-100" aria-valuemax="100">
                                    <span id="sentimentScore">0</span>
                                </div>
                            </div>
                        </div>
                        <div class="sentiment-details">
                            <div class="row">
                                <div class="col-6">
                                    <p><i class="fas fa-thumbs-up text-success"></i> Mots Positifs: <span id="positiveWords">0</span></p>
                                </div>
                                <div class="col-6">
                                    <p><i class="fas fa-thumbs-down text-danger"></i> Mots Négatifs: <span id="negativeWords">0</span></p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="noResult" class="text-center text-muted">
                        <i class="fas fa-comment-slash fa-3x mb-3"></i>
                        <p>Aucune analyse effectuée</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-pie"></i> Analyse Globale des Avis</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="card text-center">
                                <div class="card-body">
                                    <i class="fas fa-smile fa-3x text-success mb-3"></i>
                                    <h5 class="card-title">Avis Positifs</h5>
                                    <p class="card-text display-4" id="positiveCount">0</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card text-center">
                                <div class="card-body">
                                    <i class="fas fa-meh fa-3x text-warning mb-3"></i>
                                    <h5 class="card-title">Avis Neutres</h5>
                                    <p class="card-text display-4" id="neutralCount">0</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card text-center">
                                <div class="card-body">
                                    <i class="fas fa-frown fa-3x text-danger mb-3"></i>
                                    <h5 class="card-title">Avis Négatifs</h5>
                                    <p class="card-text display-4" id="negativeCount">0</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Analyser le sentiment
    document.getElementById('sentimentForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const text = document.getElementById('textInput').value.trim();
        if (!text) {
            showNotification('Veuillez entrer un texte à analyser', 'warning');
            return;
        }
        
        fetch('/ai/api/sentiment_analysis/analyze', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            },
            body: JSON.stringify({text: text})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayAnalysisResult(data.sentiment);
            } else {
                showNotification(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            showNotification('Erreur lors de l\'analyse de sentiment', 'error');
        });
    });
    
    // Afficher le résultat de l'analyse
    function displayAnalysisResult(sentiment) {
        // Afficher la section de résultat
        document.getElementById('analysisResult').style.display = 'block';
        document.getElementById('noResult').style.display = 'none';
        
        // Mettre à jour le label de sentiment
        const sentimentLabel = document.getElementById('sentimentLabel');
        sentimentLabel.textContent = sentiment.sentiment_label === 'positive' ? 'Positif' : 
                                   sentiment.sentiment_label === 'negative' ? 'Négatif' : 'Neutre';
        
        // Appliquer la couleur en fonction du sentiment
        sentimentLabel.className = 'badge fs-5 ' + 
            (sentiment.sentiment_label === 'positive' ? 'bg-success' : 
             sentiment.sentiment_label === 'negative' ? 'bg-danger' : 'bg-warning');
        
        // Mettre à jour le score de sentiment
        const score = sentiment.sentiment_score * 100;
        document.getElementById('sentimentScore').textContent = score.toFixed(1);
        const progressBar = document.getElementById('sentimentBar');
        progressBar.style.width = Math.abs(score) + '%';
        progressBar.setAttribute('aria-valuenow', score);
        progressBar.className = 'progress-bar ' + 
            (score > 0 ? 'bg-success' : score < 0 ? 'bg-danger' : 'bg-warning');
        
        // Mettre à jour les compteurs de mots
        document.getElementById('positiveWords').textContent = sentiment.positive_words_count;
        document.getElementById('negativeWords').textContent = sentiment.negative_words_count;
        
        // Mettre à jour les statistiques globales (simulées)
        updateGlobalStats();
    }
    
    // Mettre à jour les statistiques globales (simulées)
    function updateGlobalStats() {
        // Dans une implémentation réelle, ces données viendraient du serveur
        document.getElementById('positiveCount').textContent = '24';
        document.getElementById('neutralCount').textContent = '8';
        document.getElementById('negativeCount').textContent = '3';
    }
    
    // Charger les données au démarrage
    document.addEventListener('DOMContentLoaded', function() {
        updateGlobalStats();
    });
</script>
{% endblock %}