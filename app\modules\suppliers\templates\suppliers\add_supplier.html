{% extends "base.html" %}

{% block title %}Ajouter un Fournisseur{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- En-tête -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-1">
                        <i class="fas fa-plus text-primary me-2"></i>
                        Ajouter un Fournisseur
                    </h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="{{ url_for('suppliers.index') }}">Fournisseurs</a>
                            </li>
                            <li class="breadcrumb-item active">Nouveau fournisseur</li>
                        </ol>
                    </nav>
                </div>
                <a href="{{ url_for('suppliers.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>Retour à la liste
                </a>
            </div>

            <!-- Formulaire -->
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card shadow-sm">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-truck me-2"></i>
                                Informations du fournisseur
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" novalidate>
                                {{ form.hidden_tag() }}
                                
                                <!-- Informations de base -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="text-primary border-bottom pb-2 mb-3">
                                            <i class="fas fa-info-circle me-1"></i>
                                            Informations de base
                                        </h6>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        {{ form.name.label(class="form-label") }}
                                        {{ form.name(class="form-control" + (" is-invalid" if form.name.errors else "")) }}
                                        {% if form.name.errors %}
                                            <div class="invalid-feedback">
                                                {% for error in form.name.errors %}{{ error }}{% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        {{ form.supplier_code.label(class="form-label") }}
                                        <div class="input-group">
                                            {{ form.supplier_code(class="form-control" + (" is-invalid" if form.supplier_code.errors else ""), placeholder="Généré automatiquement si vide") }}
                                            <button type="button" class="btn btn-outline-secondary" onclick="generateCode()">
                                                <i class="fas fa-magic"></i>
                                            </button>
                                            {% if form.supplier_code.errors %}
                                                <div class="invalid-feedback">
                                                    {% for error in form.supplier_code.errors %}{{ error }}{% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                        <small class="form-text text-muted">Laissez vide pour génération automatique</small>
                                    </div>
                                    
                                    <div class="col-md-12 mb-3">
                                        {{ form.company_name.label(class="form-label") }}
                                        {{ form.company_name(class="form-control" + (" is-invalid" if form.company_name.errors else "")) }}
                                        {% if form.company_name.errors %}
                                            <div class="invalid-feedback">
                                                {% for error in form.company_name.errors %}{{ error }}{% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Contact -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="text-primary border-bottom pb-2 mb-3">
                                            <i class="fas fa-address-book me-1"></i>
                                            Contact
                                        </h6>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        {{ form.email.label(class="form-label") }}
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                            {{ form.email(class="form-control" + (" is-invalid" if form.email.errors else "")) }}
                                            {% if form.email.errors %}
                                                <div class="invalid-feedback">
                                                    {% for error in form.email.errors %}{{ error }}{% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        {{ form.phone.label(class="form-label") }}
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-phone"></i></span>
                                            {{ form.phone(class="form-control" + (" is-invalid" if form.phone.errors else "")) }}
                                            {% if form.phone.errors %}
                                                <div class="invalid-feedback">
                                                    {% for error in form.phone.errors %}{{ error }}{% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-12 mb-3">
                                        {{ form.website.label(class="form-label") }}
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-globe"></i></span>
                                            {{ form.website(class="form-control" + (" is-invalid" if form.website.errors else ""), placeholder="https://www.exemple.com") }}
                                            {% if form.website.errors %}
                                                <div class="invalid-feedback">
                                                    {% for error in form.website.errors %}{{ error }}{% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>

                                <!-- Informations commerciales -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="text-primary border-bottom pb-2 mb-3">
                                            <i class="fas fa-handshake me-1"></i>
                                            Informations commerciales
                                        </h6>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        {{ form.category.label(class="form-label") }}
                                        {{ form.category(class="form-control" + (" is-invalid" if form.category.errors else "")) }}
                                        {% if form.category.errors %}
                                            <div class="invalid-feedback">
                                                {% for error in form.category.errors %}{{ error }}{% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        {{ form.tax_number.label(class="form-label") }}
                                        {{ form.tax_number(class="form-control" + (" is-invalid" if form.tax_number.errors else "")) }}
                                        {% if form.tax_number.errors %}
                                            <div class="invalid-feedback">
                                                {% for error in form.tax_number.errors %}{{ error }}{% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        {{ form.payment_terms.label(class="form-label") }}
                                        {{ form.payment_terms(class="form-control" + (" is-invalid" if form.payment_terms.errors else "")) }}
                                        {% if form.payment_terms.errors %}
                                            <div class="invalid-feedback">
                                                {% for error in form.payment_terms.errors %}{{ error }}{% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        {{ form.credit_limit.label(class="form-label") }}
                                        <div class="input-group">
                                            {{ form.credit_limit(class="form-control" + (" is-invalid" if form.credit_limit.errors else ""), step="0.01") }}
                                            <span class="input-group-text">€</span>
                                            {% if form.credit_limit.errors %}
                                                <div class="invalid-feedback">
                                                    {% for error in form.credit_limit.errors %}{{ error }}{% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>

                                <!-- Notes et statut -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="text-primary border-bottom pb-2 mb-3">
                                            <i class="fas fa-sticky-note me-1"></i>
                                            Notes et statut
                                        </h6>
                                    </div>
                                    
                                    <div class="col-md-12 mb-3">
                                        {{ form.notes.label(class="form-label") }}
                                        {{ form.notes(class="form-control" + (" is-invalid" if form.notes.errors else ""), rows="4") }}
                                        {% if form.notes.errors %}
                                            <div class="invalid-feedback">
                                                {% for error in form.notes.errors %}{{ error }}{% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                    
                                    <div class="col-md-12">
                                        <div class="form-check form-switch">
                                            {{ form.is_active(class="form-check-input") }}
                                            {{ form.is_active.label(class="form-check-label") }}
                                        </div>
                                    </div>
                                </div>

                                <!-- Boutons d'action -->
                                <div class="row">
                                    <div class="col-12">
                                        <div class="d-flex justify-content-between">
                                            <a href="{{ url_for('suppliers.index') }}" class="btn btn-outline-secondary">
                                                <i class="fas fa-times me-1"></i>Annuler
                                            </a>
                                            <div>
                                                <button type="submit" class="btn btn-primary">
                                                    <i class="fas fa-save me-1"></i>Enregistrer
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function generateCode() {
    const nameField = document.getElementById('name');
    const codeField = document.getElementById('supplier_code');
    
    if (nameField.value.trim()) {
        // Générer un code basé sur le nom
        const name = nameField.value.trim();
        const baseCode = name.substring(0, 3).toUpperCase();
        const randomNum = Math.floor(Math.random() * 9999) + 1;
        const code = baseCode + randomNum.toString().padStart(4, '0');
        
        codeField.value = code;
    } else {
        alert('Veuillez d\'abord saisir le nom du fournisseur.');
        nameField.focus();
    }
}

// Validation côté client
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const nameField = document.getElementById('name');
    
    form.addEventListener('submit', function(e) {
        let isValid = true;
        
        // Validation du nom (requis)
        if (!nameField.value.trim()) {
            nameField.classList.add('is-invalid');
            isValid = false;
        } else {
            nameField.classList.remove('is-invalid');
        }
        
        if (!isValid) {
            e.preventDefault();
        }
    });
    
    // Retirer la classe d'erreur lors de la saisie
    nameField.addEventListener('input', function() {
        if (this.value.trim()) {
            this.classList.remove('is-invalid');
        }
    });
});
</script>
{% endblock %}