<!-- Modal Ajout Ingrédient -->
<div id="addIngredientModal" class="hidden fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <!-- Fond noir transparent -->
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
            <div class="absolute inset-0 bg-gray-900 opacity-75" onclick="document.getElementById('addIngredientModal').classList.add('hidden')"></div>
        </div>

        <!-- Contenu du modal -->
        <div class="inline-block align-bottom bg-slate-900 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full border border-slate-700">
            <div class="bg-cyan-600 px-4 py-3 border-b border-slate-700">
                <h3 class="text-lg leading-6 font-medium text-white">
                    <i class="fas fa-plus mr-2"></i>Nouvel Ingrédient
                </h3>
            </div>
            <form id="addIngredientForm" method="POST" action="{{ url_for('ingredients.create_ingredient') }}" class="px-4 py-5 sm:p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <!-- Informations de base -->
                    <div class="md:col-span-2">
                        <div class="mb-4">
                            <label for="ingredientName" class="block text-sm font-medium text-slate-300 mb-1">Nom de l'ingrédient *</label>
                            <input type="text" id="ingredientName" name="name" required
                                   class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 text-slate-100 focus:outline-none focus:ring-2 focus:ring-cyan-500">
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label for="ingredientSKU" class="block text-sm font-medium text-slate-300 mb-1">SKU *</label>
                                <input type="text" id="ingredientSKU" name="sku" required
                                       class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 text-slate-100 focus:outline-none focus:ring-2 focus:ring-cyan-500">
                                <p class="mt-1 text-xs text-slate-500">Code unique de l'ingrédient</p>
                            </div>
                            <div>
                                <label for="ingredientCategory" class="block text-sm font-medium text-slate-300 mb-1">Catégorie</label>
                                <select id="ingredientCategory" name="category_id"
                                        class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 text-slate-100 focus:outline-none focus:ring-2 focus:ring-cyan-500">
                                    <option value="">Sélectionner une catégorie</option>
                                    {% for category in categories %}
                                    <option value="{{ category.id }}">{{ category.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <label for="ingredientDescription" class="block text-sm font-medium text-slate-300 mb-1">Description</label>
                            <textarea id="ingredientDescription" name="description" rows="3"
                                      class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 text-slate-100 focus:outline-none focus:ring-2 focus:ring-cyan-500"
                                      placeholder="Description détaillée de l'ingrédient..."></textarea>
                        </div>
                    </div>
                    
                    <!-- Informations techniques -->
                    <div>
                        <div class="mb-4">
                            <label for="ingredientUnit" class="block text-sm font-medium text-slate-300 mb-1">Unité de mesure *</label>
                            <select id="ingredientUnit" name="unit" required
                                    class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 text-slate-100 focus:outline-none focus:ring-2 focus:ring-cyan-500">
                                <option value="">Sélectionner</option>
                                <option value="g">Grammes (g)</option>
                                <option value="kg">Kilogrammes (kg)</option>
                                <option value="ml">Millilitres (ml)</option>
                                <option value="l">Litres (l)</option>
                                <option value="pcs">Pièces (pcs)</option>
                                <option value="oz">Onces (oz)</option>
                                <option value="lb">Livres (lb)</option>
                                <option value="cup">Tasses (cup)</option>
                                <option value="tbsp">Cuillères à soupe (tbsp)</option>
                                <option value="tsp">Cuillères à café (tsp)</option>
                            </select>
                        </div>
                        
                        <div class="mb-4">
                            <label for="ingredientCost" class="block text-sm font-medium text-slate-300 mb-1">Coût par unité (€) *</label>
                            <div class="relative">
                                <input type="number" id="ingredientCost" name="cost_per_unit_cents" step="0.01" min="0" required
                                       class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 text-slate-100 focus:outline-none focus:ring-2 focus:ring-cyan-500 pl-3">
                                <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none text-slate-400">
                                    €
                                </div>
                            </div>
                            <p class="mt-1 text-xs text-slate-500">Coût pour 1 unité de mesure</p>
                        </div>
                        
                        <div class="mb-4">
                            <label for="ingredientStock" class="block text-sm font-medium text-slate-300 mb-1">Stock actuel</label>
                            <input type="number" id="ingredientStock" name="current_stock" step="0.01" min="0" value="0"
                                   class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 text-slate-100 focus:outline-none focus:ring-2 focus:ring-cyan-500">
                        </div>
                        
                        <div class="mb-4">
                            <label for="ingredientMinStock" class="block text-sm font-medium text-slate-300 mb-1">Stock minimum</label>
                            <input type="number" id="ingredientMinStock" name="min_stock_level" step="0.01" min="0" value="0"
                                   class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 text-slate-100 focus:outline-none focus:ring-2 focus:ring-cyan-500">
                            <p class="mt-1 text-xs text-slate-500">Seuil d'alerte de stock</p>
                        </div>
                    </div>
                </div>
                
                <!-- Options avancées -->
                <div class="mt-4">
                    <div class="flex items-center">
                        <input id="ingredientActive" name="is_active" type="checkbox" checked
                               class="h-4 w-4 text-cyan-600 focus:ring-cyan-500 border-slate-700 rounded bg-slate-800">
                        <label for="ingredientActive" class="ml-2 block text-sm text-slate-300">
                            Ingrédient actif
                        </label>
                    </div>
                </div>
                
                <div class="mt-6 flex justify-end space-x-3">
                    <button type="button" 
                            class="px-4 py-2 border border-slate-700 rounded-lg text-slate-300 hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-cyan-500"
                            onclick="document.getElementById('addIngredientModal').classList.add('hidden')">
                        <i class="fas fa-times mr-1"></i>Annuler
                    </button>
                    <button type="submit"
                            class="px-4 py-2 bg-cyan-600 rounded-lg text-white hover:bg-cyan-500 focus:outline-none focus:ring-2 focus:ring-cyan-500">
                        <i class="fas fa-save mr-1"></i>Enregistrer
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Génération automatique du SKU
    const nameInput = document.getElementById('ingredientName');
    const skuInput = document.getElementById('ingredientSKU');
    
    nameInput.addEventListener('input', function() {
        if (!skuInput.value) {
            const sku = this.value
                .toUpperCase()
                .replace(/[^A-Z0-9]/g, '')
                .substring(0, 8);
            skuInput.value = sku;
        }
    });
    
    // Validation du formulaire
    const form = document.getElementById('addIngredientForm');
    form.addEventListener('submit', function(e) {
        if (!form.checkValidity()) {
            e.preventDefault();
            e.stopPropagation();
        }
        form.classList.add('was-validated');
    });
});
</script>