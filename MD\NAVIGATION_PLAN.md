# Plan de Navigation - GT2 SaaS POS

## Vue d'ensemble

Ce document décrit l'organisation de la navigation dans l'application GT2 SaaS POS. Il détaille tous les modules et leurs liens respectifs pour une navigation fluide et complète dans l'application.

## Structure de Navigation

### 1. Vente et Point de Vente
- **POS** : `/pos` - Interface principale de point de vente
- **Commandes** : `/sales` - Gestion des commandes passées
- **Tables** : `/tables` - Gestion des tables (mode restaurant)
- **KDS** : `/kds` - Kitchen Display System

### 2. Catalogue et Produits
- **Catalogue** : `/catalog` - Gestion du catalogue de produits
- **Ingrédients** : `/ingredients` - Gestion des ingrédients
- **Variantes** : `/variants` - Gestion des variantes de produits
- **Recettes** : `/recipes` - Gestion des recettes (mode restaurant)

### 3. Inventaire et Stock
- **Inventaire** : `/inventory` - Gestion des stocks et dépôts
- **Approvisionnement** : `/purchasing` - Gestion des commandes fournisseurs
- **Fournisseurs** : `/suppliers` - Gestion des fournisseurs

### 4. Clients et Fidélité
- **Clients** : `/customers` - Gestion de la base clients
- **Fidélité** : `/loyalty` - Programmes de fidélité
- **Support** : `/support` - Support client

### 5. Finances et Comptabilité
- **Paiements** : `/payments` - Gestion des paiements
- **Caisses** : `/cash` - Gestion des caisses
- **Dépenses** : `/expenses` - Suivi des dépenses
- **Comptabilité** : `/accounting` - Module comptable

### 6. Personnel et RH
- **Personnel** : `/staff` - Gestion du personnel
- **Présence** : `/attendance` - Suivi des présences
- **Paie** : `/payroll` - Gestion de la paie

### 7. Rapports et Analyse
- **Rapports** : `/reports` - Génération de rapports
- **Analyse** : `/analytics` - Analyses avancées
- **IA** : `/ai` - Intelligence artificielle

### 8. Configuration et Administration
- **Paramètres** : `/settings` - Configuration générale
- **Audit** : `/audit` - Journal d'audit
- **Intégrations** : `/integrations` - Intégrations tierces
- **Notifications** : `/notifications` - Gestion des notifications
- **Pharmacie** : `/pharmacy` - Module spécialisé pharmacie

### 9. Planification et Production
- **Planification** : `/menu_planning` - Planification de menus
- **Production** : `/production` - Gestion de la production

### 10. Livraison et Distribution
- **Livraison** : `/delivery` - Gestion des livraisons

## Structure de la Barre de Navigation

### Menu Principal
1. **Vente**
   - POS (`/pos`)
   - Commandes (`/sales`)
   - Tables (`/tables`)
   - KDS (`/kds`)

2. **Catalogue**
   - Catalogue (`/catalog`)
   - Ingrédients (`/ingredients`)
   - Variantes (`/variants`)
   - Recettes (`/recipes`)

3. **Stock**
   - Inventaire (`/inventory`)
   - Approvisionnement (`/purchasing`)
   - Fournisseurs (`/suppliers`)

4. **Clients**
   - Clients (`/customers`)
   - Fidélité (`/loyalty`)
   - Support (`/support`)

5. **Finances**
   - Paiements (`/payments`)
   - Caisses (`/cash`)
   - Dépenses (`/expenses`)
   - Comptabilité (`/accounting`)

6. **Personnel**
   - Personnel (`/staff`)
   - Présence (`/attendance`)
   - Paie (`/payroll`)

7. **Analyse**
   - Rapports (`/reports`)
   - Analyse (`/analytics`)
   - IA (`/ai`)

8. **Configuration**
   - Paramètres (`/settings`)
   - Audit (`/audit`)
   - Intégrations (`/integrations`)
   - Notifications (`/notifications`)
   - Pharmacie (`/pharmacy`)

9. **Planification**
   - Planification (`/menu_planning`)
   - Production (`/production`)

10. **Distribution**
    - Livraison (`/delivery`)

### Menu Utilisateur
- Profil utilisateur
- Déconnexion (`/accounts/logout`)
- Connexion (`/accounts/login`) - Pour les utilisateurs non authentifiés

## Notes d'Implémentation

1. **Structure HTML** : La barre de navigation doit utiliser des listes déroulantes pour regrouper les modules par catégories.
2. **Accessibilité** : Tous les liens doivent être accessibles via le clavier et avoir des attributs ARIA appropriés.
3. **Responsive** : La navigation doit s'adapter aux appareils mobiles avec un menu hamburger.
4. **Permissions** : Les liens doivent être conditionnellement affichés selon les permissions de l'utilisateur.
5. **Navigation active** : L'élément de menu actif doit être mis en évidence.

## Liens Spécifiques par Module

### Module POS (`/pos`)
- Interface de vente : `/pos`
- Mode recette : `/pos/recipe-mode`
- Mode variante : `/pos/variant-mode`

### Module Ventes (`/sales`)
- Liste des commandes : `/sales`
- Détails de commande : `/sales/<id>`
- Commandes en attente : `/sales/pending`

### Module Inventaire (`/inventory`)
- Vue d'ensemble : `/inventory`
- Mouvements de stock : `/inventory/stock-movements`
- Dépôts : `/inventory/warehouses`

### Module Catalogue (`/catalog`)
- Produits : `/catalog`
- Catégories : `/catalog/categories`
- Promotions : `/catalog/promotions`

### Module IA (`/ai`)
- Tableau de bord : `/ai`
- Prévision des ventes : `/ai/sales_forecast`
- Recommandations : `/ai/recommendations`
- Segmentation client : `/ai/customer_segmentation`
- Optimisation des stocks : `/ai/inventory_optimization`
- Prédiction de churn : `/ai/churn_prediction`
- Analyse de sentiment : `/ai/sentiment_analysis`

## Mises à Jour Futures

Ce plan sera mis à jour régulièrement pour refléter les nouveaux modules et fonctionnalités ajoutés à l'application.