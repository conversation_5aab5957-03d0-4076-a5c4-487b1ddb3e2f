{% extends 'base.html' %}
{% block title %}Modifier Shift{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto">
  <!-- En-tête -->
  <div class="mb-6">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold">Modifier Shift</h1>
        <p class="text-slate-400 mt-2">
          {{ shift.employee.last_name }} {{ shift.employee.first_name }} - 
          {{ shift.shift_date.strftime('%d/%m/%Y') }} 
          {{ shift.start_time.strftime('%H:%M') }}-{{ shift.end_time.strftime('%H:%M') }}
        </p>
      </div>
      <a href="{{ url_for('staff.index') }}" class="bg-slate-700 hover:bg-slate-600 px-4 py-2 rounded-lg">
        ← Retour à la liste
      </a>
    </div>
  </div>

  <!-- Informations actuelles du shift -->
  <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
    <div class="rounded-xl bg-slate-900 border border-slate-700 p-4">
      <div class="text-center">
        <div class="text-2xl mb-2">👤</div>
        <h3 class="font-semibold text-sm">{{ shift.employee.last_name }} {{ shift.employee.first_name }}</h3>
        <p class="text-xs text-slate-400">{{ shift.employee.employee_code }}</p>
      </div>
    </div>
    
    <div class="rounded-xl bg-slate-900 border border-slate-700 p-4">
      <div class="text-center">
        <div class="text-2xl mb-2">📅</div>
        <h3 class="font-semibold text-sm">{{ shift.shift_date.strftime('%d/%m/%Y') }}</h3>
        <p class="text-xs text-slate-400">Date du shift</p>
      </div>
    </div>
    
    <div class="rounded-xl bg-slate-900 border border-slate-700 p-4">
      <div class="text-center">
        <div class="text-2xl mb-2">⏰</div>
        <h3 class="font-semibold text-sm">{{ shift.start_time.strftime('%H:%M') }}-{{ shift.end_time.strftime('%H:%M') }}</h3>
        <p class="text-xs text-slate-400">Horaires</p>
      </div>
    </div>
    
    <div class="rounded-xl bg-slate-900 border border-slate-700 p-4">
      <div class="text-center">
        <div class="text-2xl mb-2">{% if shift.is_active %}✅{% else %}🚫{% endif %}</div>
        <h3 class="font-semibold text-sm {% if shift.is_active %}text-green-400{% else %}text-red-400{% endif %}">
          {{ 'Actif' if shift.is_active else 'Inactif' }}
        </h3>
        <p class="text-xs text-slate-400">Statut</p>
      </div>
    </div>
  </div>

  <!-- Formulaire de modification -->
  <div class="rounded-xl bg-slate-900 border border-slate-700 p-6">
    <h2 class="text-xl font-semibold mb-6">✏️ Modifier les détails du shift</h2>
    
    <form method="post" class="space-y-6">
      {{ form.csrf_token }}
      
      <!-- Messages d'erreur -->
      {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
          {% for category, message in messages %}
            <div class="rounded-lg p-4 {% if category == 'error' %}bg-red-900 border border-red-700 text-red-100{% else %}bg-green-900 border border-green-700 text-green-100{% endif %}">
              {{ message }}
            </div>
          {% endfor %}
        {% endif %}
      {% endwith %}

      <!-- Informations principales -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-slate-300 mb-2">
              {{ form.employee_id_fk.label.text }} <span class="text-red-400">*</span>
            </label>
            {{ form.employee_id_fk(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 focus:ring-2 focus:ring-cyan-500 focus:border-transparent") }}
            {% if form.employee_id_fk.errors %}
              <p class="text-red-400 text-sm mt-1">{{ form.employee_id_fk.errors[0] }}</p>
            {% endif %}
          </div>
          
          <div>
            <label class="block text-sm font-medium text-slate-300 mb-2">
              {{ form.shift_date.label.text }} <span class="text-red-400">*</span>
            </label>
            {{ form.shift_date(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 focus:ring-2 focus:ring-cyan-500 focus:border-transparent") }}
            {% if form.shift_date.errors %}
              <p class="text-red-400 text-sm mt-1">{{ form.shift_date.errors[0] }}</p>
            {% endif %}
          </div>
          
          <div>
            <label class="block text-sm font-medium text-slate-300 mb-2">
              {{ form.position.label.text }}
            </label>
            {{ form.position(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 focus:ring-2 focus:ring-cyan-500 focus:border-transparent") }}
            {% if form.position.errors %}
              <p class="text-red-400 text-sm mt-1">{{ form.position.errors[0] }}</p>
            {% endif %}
          </div>
        </div>
        
        <div class="space-y-4">
          <div class="grid grid-cols-2 gap-3">
            <div>
              <label class="block text-sm font-medium text-slate-300 mb-2">
                {{ form.start_time.label.text }} <span class="text-red-400">*</span>
              </label>
              {{ form.start_time(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 focus:ring-2 focus:ring-cyan-500 focus:border-transparent") }}
              {% if form.start_time.errors %}
                <p class="text-red-400 text-sm mt-1">{{ form.start_time.errors[0] }}</p>
              {% endif %}
            </div>
            
            <div>
              <label class="block text-sm font-medium text-slate-300 mb-2">
                {{ form.end_time.label.text }} <span class="text-red-400">*</span>
              </label>
              {{ form.end_time(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 focus:ring-2 focus:ring-cyan-500 focus:border-transparent") }}
              {% if form.end_time.errors %}
                <p class="text-red-400 text-sm mt-1">{{ form.end_time.errors[0] }}</p>
              {% endif %}
            </div>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-slate-300 mb-2">
              {{ form.break_duration_minutes.label.text }}
            </label>
            {{ form.break_duration_minutes(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 focus:ring-2 focus:ring-cyan-500 focus:border-transparent") }}
            {% if form.break_duration_minutes.errors %}
              <p class="text-red-400 text-sm mt-1">{{ form.break_duration_minutes.errors[0] }}</p>
            {% endif %}
            <p class="text-xs text-slate-400 mt-1">Durée en minutes (ex: 30 pour 30 minutes)</p>
          </div>
          
          <div class="flex items-center">
            {{ form.is_active(class="w-4 h-4 text-cyan-600 bg-slate-800 border-slate-700 rounded focus:ring-cyan-500 focus:ring-2") }}
            <label class="ml-3 text-sm font-medium text-slate-300">
              {{ form.is_active.label.text }}
            </label>
          </div>
        </div>
      </div>

      <!-- Notes -->
      <div>
        <label class="block text-sm font-medium text-slate-300 mb-2">
          {{ form.notes.label.text }}
        </label>
        {{ form.notes(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 focus:ring-2 focus:ring-cyan-500 focus:border-transparent", rows="3") }}
        {% if form.notes.errors %}
          <p class="text-red-400 text-sm mt-1">{{ form.notes.errors[0] }}</p>
        {% endif %}
      </div>

      <!-- Calcul automatique de la durée -->
      <div class="bg-slate-800 border border-slate-700 rounded-lg p-4">
        <h3 class="font-semibold mb-2">📊 Informations calculées</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
          <div class="flex justify-between">
            <span class="text-slate-400">Durée totale:</span>
            <span class="font-medium" id="total-duration">-</span>
          </div>
          <div class="flex justify-between">
            <span class="text-slate-400">Durée de pause:</span>
            <span class="font-medium" id="break-duration">-</span>
          </div>
          <div class="flex justify-between">
            <span class="text-slate-400">Durée de travail:</span>
            <span class="font-medium text-cyan-400" id="work-duration">-</span>
          </div>
        </div>
      </div>

      <!-- Actions -->
      <div class="flex items-center justify-between pt-6 border-t border-slate-700">
        <a href="{{ url_for('staff.index') }}" class="bg-slate-700 hover:bg-slate-600 px-6 py-2 rounded-lg">
          Annuler
        </a>
        <div class="flex space-x-3">
          <button type="button" onclick="confirmDelete()" class="bg-red-600 hover:bg-red-500 px-6 py-2 rounded-lg">
            🗑️ Supprimer
          </button>
          {{ form.submit(class="bg-cyan-600 hover:bg-cyan-500 px-6 py-2 rounded-lg font-medium") }}
        </div>
      </div>
    </form>
  </div>

  <!-- Historique des pointages pour ce shift -->
  <div class="mt-6 rounded-xl bg-slate-900 border border-slate-700 p-6">
    <h3 class="text-lg font-semibold mb-4">⏰ Pointages associés</h3>
    <div class="text-center py-4 text-slate-400">
      <div class="text-4xl mb-2">⏰</div>
      <p>Aucun pointage enregistré pour ce shift</p>
      <a href="{{ url_for('staff.timeclock') }}" class="inline-block mt-2 text-cyan-400 hover:text-cyan-300">
        Ajouter un pointage →
      </a>
    </div>
  </div>
</div>

<!-- Modal de confirmation de suppression -->
<div id="deleteModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
  <div class="bg-slate-900 border border-slate-700 rounded-xl p-6 max-w-md w-full mx-4">
    <h3 class="text-lg font-semibold mb-4">⚠️ Confirmer la suppression</h3>
    <p class="text-slate-300 mb-6">
      Êtes-vous sûr de vouloir supprimer ce shift ? Cette action est irréversible.
      <br><br>
      <strong>{{ shift.employee.last_name }} {{ shift.employee.first_name }}</strong><br>
      {{ shift.shift_date.strftime('%d/%m/%Y') }} - {{ shift.start_time.strftime('%H:%M') }}-{{ shift.end_time.strftime('%H:%M') }}
    </p>
    <div class="flex justify-end space-x-3">
      <button onclick="closeDeleteModal()" class="px-4 py-2 bg-slate-700 hover:bg-slate-600 rounded-lg">
        Annuler
      </button>
      <form method="post" action="{{ url_for('staff.delete_shift', id=shift.id) }}" class="inline">
        {{ csrf_token() }}
        <button class="px-4 py-2 bg-red-600 hover:bg-red-500 rounded-lg">
          Supprimer définitivement
        </button>
      </form>
    </div>
  </div>
</div>

<script>
function confirmDelete() {
  document.getElementById('deleteModal').classList.remove('hidden');
}

function closeDeleteModal() {
  document.getElementById('deleteModal').classList.add('hidden');
}

function calculateDuration() {
  const startTime = document.getElementById('start_time').value;
  const endTime = document.getElementById('end_time').value;
  const breakMinutes = parseInt(document.getElementById('break_duration_minutes').value) || 0;
  
  if (startTime && endTime) {
    const start = new Date('2000-01-01 ' + startTime);
    const end = new Date('2000-01-01 ' + endTime);
    
    if (end > start) {
      const totalMinutes = (end - start) / 1000 / 60;
      const workMinutes = totalMinutes - breakMinutes;
      
      document.getElementById('total-duration').textContent = Math.floor(totalMinutes / 60) + 'h ' + (totalMinutes % 60) + 'min';
      document.getElementById('break-duration').textContent = Math.floor(breakMinutes / 60) + 'h ' + (breakMinutes % 60) + 'min';
      document.getElementById('work-duration').textContent = Math.floor(workMinutes / 60) + 'h ' + (workMinutes % 60) + 'min';
    }
  }
}

// Écouter les changements sur les champs de temps
document.addEventListener('DOMContentLoaded', function() {
  const startTimeField = document.querySelector('input[name="start_time"]');
  const endTimeField = document.querySelector('input[name="end_time"]');
  const breakField = document.querySelector('input[name="break_duration_minutes"]');
  
  if (startTimeField) {
    startTimeField.id = 'start_time';
    startTimeField.addEventListener('change', calculateDuration);
  }
  
  if (endTimeField) {
    endTimeField.id = 'end_time';
    endTimeField.addEventListener('change', calculateDuration);
  }
  
  if (breakField) {
    breakField.id = 'break_duration_minutes';
    breakField.addEventListener('input', calculateDuration);
  }
  
  // Calcul initial
  calculateDuration();
});

// Fermeture du modal avec Escape
document.addEventListener('keydown', function(e) {
  if (e.key === 'Escape') {
    closeDeleteModal();
  }
});
</script>
{% endblock %}