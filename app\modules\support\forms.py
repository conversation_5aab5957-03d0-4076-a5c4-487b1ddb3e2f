from flask_wtf import FlaskForm
from wtforms import (
    StringField, TextAreaField, SelectField, BooleanField,
    IntegerField, DateTimeField, HiddenField, SubmitField
)
from wtforms.validators import DataRequired, Length, Optional, NumberRange, Email
from flask_wtf.file import FileField, FileAllowed
from .models import TicketPriority, TicketStatus, TicketCategory
from datetime import datetime


class SupportTicketForm(FlaskForm):
    """Formulaire pour créer un ticket de support"""
    title = StringField(
        'Titre du ticket',
        validators=[DataRequired(), Length(min=5, max=200)],
        render_kw={'placeholder': 'Décrivez brièvement votre problème'}
    )
    description = TextAreaField(
        'Description détaillée',
        validators=[DataRequired(), Length(min=20)],
        render_kw={'rows': 6, 'placeholder': 'Décrivez votre problème en détail...'}
    )
    category = SelectField(
        'Catégorie',
        choices=[
            (TicketCategory.TECHNICAL.value, 'Problème technique'),
            (TicketCategory.BILLING.value, 'Facturation'),
            (TicketCategory.FEATURE_REQUEST.value, 'Demande de fonctionnalité'),
            (TicketCategory.BUG_REPORT.value, 'Signalement de bug'),
            (TicketCategory.TRAINING.value, 'Formation'),
            (TicketCategory.OTHER.value, 'Autre')
        ],
        validators=[DataRequired()]
    )
    priority = SelectField(
        'Priorité',
        choices=[
            (TicketPriority.LOW.value, 'Faible'),
            (TicketPriority.MEDIUM.value, 'Moyenne'),
            (TicketPriority.HIGH.value, 'Haute'),
            (TicketPriority.URGENT.value, 'Urgente')
        ],
        validators=[DataRequired()],
        default=TicketPriority.MEDIUM.value
    )
    steps_to_reproduce = TextAreaField(
        'Étapes pour reproduire (optionnel)',
        validators=[Optional()],
        render_kw={'rows': 4, 'placeholder': '1. Aller à...\n2. Cliquer sur...\n3. Voir l\'erreur...'}
    )
    browser_info = StringField(
        'Informations navigateur (optionnel)',
        validators=[Optional(), Length(max=500)],
        render_kw={'placeholder': 'Chrome 91.0, Windows 10, etc.'}
    )
    error_details = TextAreaField(
        'Détails de l\'erreur (optionnel)',
        validators=[Optional()],
        render_kw={'rows': 3, 'placeholder': 'Messages d\'erreur, codes d\'erreur, etc.'}
    )
    submit = SubmitField('Créer le ticket')


class EditTicketForm(FlaskForm):
    """Formulaire pour modifier un ticket (par les admins)"""
    title = StringField(
        'Titre du ticket',
        validators=[DataRequired(), Length(min=5, max=200)]
    )
    description = TextAreaField(
        'Description',
        validators=[DataRequired(), Length(min=20)],
        render_kw={'rows': 6}
    )
    category = SelectField(
        'Catégorie',
        choices=[
            (TicketCategory.TECHNICAL.value, 'Problème technique'),
            (TicketCategory.BILLING.value, 'Facturation'),
            (TicketCategory.FEATURE_REQUEST.value, 'Demande de fonctionnalité'),
            (TicketCategory.BUG_REPORT.value, 'Signalement de bug'),
            (TicketCategory.TRAINING.value, 'Formation'),
            (TicketCategory.OTHER.value, 'Autre')
        ],
        validators=[DataRequired()]
    )
    priority = SelectField(
        'Priorité',
        choices=[
            (TicketPriority.LOW.value, 'Faible'),
            (TicketPriority.MEDIUM.value, 'Moyenne'),
            (TicketPriority.HIGH.value, 'Haute'),
            (TicketPriority.URGENT.value, 'Urgente')
        ],
        validators=[DataRequired()]
    )
    status = SelectField(
        'Statut',
        choices=[
            (TicketStatus.OPEN.value, 'Ouvert'),
            (TicketStatus.IN_PROGRESS.value, 'En cours'),
            (TicketStatus.WAITING_CUSTOMER.value, 'En attente client'),
            (TicketStatus.RESOLVED.value, 'Résolu'),
            (TicketStatus.CLOSED.value, 'Fermé')
        ],
        validators=[DataRequired()]
    )
    assigned_to_id = SelectField(
        'Assigné à',
        choices=[],  # Sera rempli dynamiquement
        coerce=int,
        validators=[Optional()]
    )
    estimated_hours = IntegerField(
        'Estimation (heures)',
        validators=[Optional(), NumberRange(min=0, max=1000)],
        render_kw={'placeholder': 'Temps estimé'}
    )
    due_date = DateTimeField(
        'Date d\'échéance',
        validators=[Optional()],
        format='%Y-%m-%d %H:%M'
    )
    resolution = TextAreaField(
        'Résolution',
        validators=[Optional()],
        render_kw={'rows': 4, 'placeholder': 'Décrivez la solution...'}
    )
    is_public = BooleanField(
        'Visible dans la FAQ publique',
        default=False
    )
    submit = SubmitField('Mettre à jour')


class TicketCommentForm(FlaskForm):
    """Formulaire pour ajouter un commentaire à un ticket"""
    comment = TextAreaField(
        'Commentaire',
        validators=[DataRequired(), Length(min=5)],
        render_kw={'rows': 4, 'placeholder': 'Votre commentaire...'}
    )
    is_internal = BooleanField(
        'Note interne (non visible par le client)',
        default=False
    )
    is_solution = BooleanField(
        'Marquer comme solution',
        default=False
    )
    time_spent_minutes = IntegerField(
        'Temps passé (minutes)',
        validators=[Optional(), NumberRange(min=0, max=600)],
        render_kw={'placeholder': 'Temps passé'}
    )
    submit = SubmitField('Ajouter le commentaire')


class TicketSearchForm(FlaskForm):
    """Formulaire de recherche pour les tickets"""
    search = StringField(
        'Recherche',
        validators=[Optional()],
        render_kw={'placeholder': 'Numéro, titre, description...'}
    )
    status = SelectField(
        'Statut',
        choices=[
            ('all', 'Tous'),
            (TicketStatus.OPEN.value, 'Ouvert'),
            (TicketStatus.IN_PROGRESS.value, 'En cours'),
            (TicketStatus.WAITING_CUSTOMER.value, 'En attente client'),
            (TicketStatus.RESOLVED.value, 'Résolu'),
            (TicketStatus.CLOSED.value, 'Fermé')
        ],
        default='all'
    )
    category = SelectField(
        'Catégorie',
        choices=[
            ('all', 'Toutes'),
            (TicketCategory.TECHNICAL.value, 'Technique'),
            (TicketCategory.BILLING.value, 'Facturation'),
            (TicketCategory.FEATURE_REQUEST.value, 'Fonctionnalité'),
            (TicketCategory.BUG_REPORT.value, 'Bug'),
            (TicketCategory.TRAINING.value, 'Formation'),
            (TicketCategory.OTHER.value, 'Autre')
        ],
        default='all'
    )
    priority = SelectField(
        'Priorité',
        choices=[
            ('all', 'Toutes'),
            (TicketPriority.URGENT.value, 'Urgente'),
            (TicketPriority.HIGH.value, 'Haute'),
            (TicketPriority.MEDIUM.value, 'Moyenne'),
            (TicketPriority.LOW.value, 'Faible')
        ],
        default='all'
    )
    assigned_to = SelectField(
        'Assigné à',
        choices=[],  # Sera rempli dynamiquement
        coerce=int,
        validators=[Optional()]
    )
    created_by = SelectField(
        'Créé par',
        choices=[],  # Sera rempli dynamiquement
        coerce=int,
        validators=[Optional()]
    )
    submit = SubmitField('Rechercher')


class FAQForm(FlaskForm):
    """Formulaire pour créer/modifier une FAQ"""
    title = StringField(
        'Titre',
        validators=[DataRequired(), Length(min=5, max=200)],
        render_kw={'placeholder': 'Titre de la question'}
    )
    question = TextAreaField(
        'Question',
        validators=[DataRequired(), Length(min=10)],
        render_kw={'rows': 3, 'placeholder': 'Question détaillée...'}
    )
    answer = TextAreaField(
        'Réponse',
        validators=[DataRequired(), Length(min=20)],
        render_kw={'rows': 8, 'placeholder': 'Réponse détaillée...'}
    )
    category = StringField(
        'Catégorie',
        validators=[DataRequired(), Length(max=50)],
        render_kw={'placeholder': 'Ex: Configuration, Facturation, etc.'}
    )
    subcategory = StringField(
        'Sous-catégorie',
        validators=[Optional(), Length(max=50)],
        render_kw={'placeholder': 'Sous-catégorie (optionnel)'}
    )
    difficulty_level = SelectField(
        'Niveau de difficulté',
        choices=[
            ('beginner', 'Débutant'),
            ('intermediate', 'Intermédiaire'),
            ('advanced', 'Avancé')
        ],
        validators=[DataRequired()],
        default='beginner'
    )
    tags = StringField(
        'Tags',
        validators=[Optional()],
        render_kw={'placeholder': 'mot-clé1, mot-clé2, mot-clé3'}
    )
    display_order = IntegerField(
        'Ordre d\'affichage',
        validators=[Optional(), NumberRange(min=0)],
        default=0
    )
    is_published = BooleanField(
        'Publié',
        default=True
    )
    is_featured = BooleanField(
        'Mise en avant',
        default=False
    )
    submit = SubmitField('Enregistrer')


class KnowledgeArticleForm(FlaskForm):
    """Formulaire pour créer/modifier un article de base de connaissances"""
    title = StringField(
        'Titre',
        validators=[DataRequired(), Length(min=5, max=200)],
        render_kw={'placeholder': 'Titre de l\'article'}
    )
    slug = StringField(
        'URL slug',
        validators=[DataRequired(), Length(min=5, max=250)],
        render_kw={'placeholder': 'url-friendly-title'}
    )
    summary = TextAreaField(
        'Résumé',
        validators=[Optional()],
        render_kw={'rows': 3, 'placeholder': 'Résumé de l\'article...'}
    )
    content = TextAreaField(
        'Contenu',
        validators=[DataRequired(), Length(min=50)],
        render_kw={'rows': 15, 'placeholder': 'Contenu complet de l\'article...'}
    )
    category = StringField(
        'Catégorie',
        validators=[DataRequired(), Length(max=50)],
        render_kw={'placeholder': 'Ex: Guides, Tutoriels, etc.'}
    )
    subcategory = StringField(
        'Sous-catégorie',
        validators=[Optional(), Length(max=50)],
        render_kw={'placeholder': 'Sous-catégorie (optionnel)'}
    )
    difficulty_level = SelectField(
        'Niveau de difficulté',
        choices=[
            ('beginner', 'Débutant'),
            ('intermediate', 'Intermédiaire'),
            ('advanced', 'Avancé')
        ],
        validators=[DataRequired()],
        default='beginner'
    )
    estimated_read_time = IntegerField(
        'Temps de lecture estimé (minutes)',
        validators=[Optional(), NumberRange(min=1, max=120)],
        render_kw={'placeholder': 'Temps en minutes'}
    )
    tags = StringField(
        'Tags',
        validators=[Optional()],
        render_kw={'placeholder': 'mot-clé1, mot-clé2, mot-clé3'}
    )
    meta_description = StringField(
        'Description méta (SEO)',
        validators=[Optional(), Length(max=300)],
        render_kw={'placeholder': 'Description pour les moteurs de recherche'}
    )
    meta_keywords = StringField(
        'Mots-clés méta (SEO)',
        validators=[Optional(), Length(max=500)],
        render_kw={'placeholder': 'mot-clé1, mot-clé2, mot-clé3'}
    )
    is_published = BooleanField(
        'Publié',
        default=True
    )
    is_featured = BooleanField(
        'Mise en avant',
        default=False
    )
    submit = SubmitField('Enregistrer')


class TicketAttachmentForm(FlaskForm):
    """Formulaire pour ajouter une pièce jointe à un ticket"""
    file = FileField(
        'Fichier',
        validators=[
            DataRequired(),
            FileAllowed(['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx', 'txt', 'csv', 'xlsx'], 
                       'Types autorisés: images, PDF, documents Office, texte')
        ]
    )
    description = StringField(
        'Description',
        validators=[Optional(), Length(max=500)],
        render_kw={'placeholder': 'Description du fichier (optionnel)'}
    )
    is_public = BooleanField(
        'Visible par le client',
        default=True
    )
    submit = SubmitField('Télécharger')


class CustomerSatisfactionForm(FlaskForm):
    """Formulaire d'évaluation de satisfaction client"""
    satisfaction_score = SelectField(
        'Note de satisfaction',
        choices=[
            (1, '1 - Très insatisfait'),
            (2, '2 - Insatisfait'),
            (3, '3 - Neutre'),
            (4, '4 - Satisfait'),
            (5, '5 - Très satisfait')
        ],
        coerce=int,
        validators=[DataRequired()]
    )
    feedback = TextAreaField(
        'Commentaire (optionnel)',
        validators=[Optional()],
        render_kw={'rows': 4, 'placeholder': 'Votre retour nous aide à améliorer notre service...'}
    )
    submit = SubmitField('Envoyer l\'évaluation')


class BulkTicketActionForm(FlaskForm):
    """Formulaire pour actions en lot sur les tickets"""
    ticket_ids = HiddenField(
        'IDs des tickets',
        validators=[DataRequired()]
    )
    action = SelectField(
        'Action',
        choices=[
            ('assign', 'Assigner'),
            ('change_status', 'Changer le statut'),
            ('change_priority', 'Changer la priorité'),
            ('add_tag', 'Ajouter un tag'),
            ('close', 'Fermer'),
            ('delete', 'Supprimer')
        ],
        validators=[DataRequired()]
    )
    assigned_to_id = SelectField(
        'Assigner à',
        choices=[],  # Sera rempli dynamiquement
        coerce=int,
        validators=[Optional()]
    )
    new_status = SelectField(
        'Nouveau statut',
        choices=[
            (TicketStatus.OPEN.value, 'Ouvert'),
            (TicketStatus.IN_PROGRESS.value, 'En cours'),
            (TicketStatus.WAITING_CUSTOMER.value, 'En attente client'),
            (TicketStatus.RESOLVED.value, 'Résolu'),
            (TicketStatus.CLOSED.value, 'Fermé')
        ],
        validators=[Optional()]
    )
    new_priority = SelectField(
        'Nouvelle priorité',
        choices=[
            (TicketPriority.LOW.value, 'Faible'),
            (TicketPriority.MEDIUM.value, 'Moyenne'),
            (TicketPriority.HIGH.value, 'Haute'),
            (TicketPriority.URGENT.value, 'Urgente')
        ],
        validators=[Optional()]
    )
    tag_to_add = StringField(
        'Tag à ajouter',
        validators=[Optional(), Length(max=50)],
        render_kw={'placeholder': 'nouveau-tag'}
    )
    comment = TextAreaField(
        'Commentaire (optionnel)',
        validators=[Optional()],
        render_kw={'rows': 3, 'placeholder': 'Commentaire sur l\'action...'}
    )
    submit = SubmitField('Appliquer')


class SupportReportForm(FlaskForm):
    """Formulaire pour générer des rapports de support"""
    report_type = SelectField(
        'Type de rapport',
        choices=[
            ('summary', 'Résumé général'),
            ('tickets_by_status', 'Tickets par statut'),
            ('tickets_by_category', 'Tickets par catégorie'),
            ('response_times', 'Temps de réponse'),
            ('satisfaction', 'Satisfaction client'),
            ('agent_performance', 'Performance agents')
        ],
        validators=[DataRequired()]
    )
    date_from = DateTimeField(
        'Date de début',
        validators=[DataRequired()],
        format='%Y-%m-%d',
        default=lambda: datetime.now().replace(day=1).date()
    )
    date_to = DateTimeField(
        'Date de fin',
        validators=[DataRequired()],
        format='%Y-%m-%d',
        default=lambda: datetime.now().date()
    )
    format_type = SelectField(
        'Format',
        choices=[
            ('html', 'HTML'),
            ('pdf', 'PDF'),
            ('csv', 'CSV'),
            ('excel', 'Excel')
        ],
        validators=[DataRequired()],
        default='html'
    )
    include_details = BooleanField(
        'Inclure les détails',
        default=True
    )
    submit = SubmitField('Générer le rapport')