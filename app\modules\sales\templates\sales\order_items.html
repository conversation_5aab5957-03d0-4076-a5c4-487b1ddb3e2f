{% extends 'base.html' %}
{% block title %}Articles - Commande #{{ order.id }}{% endblock %}
{% block content %}
<div class="max-w-7xl mx-auto">
  <div class="flex items-center justify-between mb-6">
    <div>
      <h1 class="text-3xl font-bold">Commande #{{ order.id }}</h1>
      <p class="text-slate-400 mt-2">
        Statut: <span class="px-2 py-1 rounded text-xs bg-{{ 'green' if order.status == 'delivered' else 'orange' }}-600 text-white">{{ order.status|title }}</span> • 
        Total: <span class="text-cyan-400 font-medium">{{ "%.2f"|format(order.total_cents/100) }} €</span> •
        {{ order.created_at.strftime('%d/%m/%Y %H:%M') if order.created_at else 'Date inconnue' }}
      </p>
    </div>
    <div class="flex space-x-3">
      <a href="{{ url_for('sales.orders') }}" class="px-4 py-2 bg-slate-700 hover:bg-slate-600 text-white rounded-lg">
        Retour aux commandes
      </a>
      <a href="{{ url_for('sales.edit_order', id=order.id) }}" class="px-4 py-2 bg-blue-600 hover:bg-blue-500 text-white rounded-lg">
        ✏️ Éditer commande
      </a>
    </div>
  </div>

  <div class="grid grid-cols-12 gap-6">
    <!-- Formulaires d'ajout d'articles -->
    <div class="col-span-12 lg:col-span-5 space-y-6">
      <!-- Navigation entre les types d'ajout -->
      <div class="rounded-xl bg-slate-900 border border-slate-700 p-6">
        <div class="flex space-x-2 mb-4">
          <button id="tab-classic" class="px-3 py-1 bg-cyan-600 text-white rounded text-sm font-medium">Classique</button>
          {% if recipe_form %}
          <button id="tab-recipe" class="px-3 py-1 bg-slate-700 text-slate-300 rounded text-sm font-medium" data-feature="recipes">Recettes</button>
          {% endif %}
          {% if variant_form %}
          <button id="tab-variant" class="px-3 py-1 bg-slate-700 text-slate-300 rounded text-sm font-medium" data-feature="variants">Variantes</button>
          {% endif %}
        </div>
        
        <!-- Formulaire classique -->
        <div id="form-classic">
          <h3 class="text-lg font-semibold mb-4">Ajouter un article</h3>
          <form method="post" class="space-y-4">
            {{ form.csrf_token }}
            <input type="hidden" name="classic_add" value="1" />
            
            <div>
              <label class="block text-sm font-medium text-slate-300 mb-2">Produit ID</label>
              {{ form.product_id(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-4 py-3 text-white") }}
            </div>
            
            <div class="grid grid-cols-2 gap-3">
              <div>
                <label class="block text-sm font-medium text-slate-300 mb-2">{{ form.qty.label.text }}</label>
                {{ form.qty(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-4 py-3 text-white") }}
              </div>
              <div>
                <label class="block text-sm font-medium text-slate-300 mb-2">{{ form.price_cents.label.text }}</label>
                {{ form.price_cents(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-4 py-3 text-white") }}
              </div>
            </div>
            
            <button type="submit" class="w-full bg-cyan-600 hover:bg-cyan-500 text-white font-medium py-3 px-4 rounded-lg">
              📦 Ajouter l'article
            </button>
          </form>
        </div>
        
        <!-- Formulaire recettes -->
        {% if recipe_form %}
        <div id="form-recipe" class="hidden">
          <h3 class="text-lg font-semibold mb-4">🍳 Ajouter une recette</h3>
          <form method="post" class="space-y-4">
            {{ recipe_form.csrf_token }}
            <input type="hidden" name="recipe_add" value="1" />
            
            <div>
              <label class="block text-sm font-medium text-slate-300 mb-2">{{ recipe_form.recipe_id.label.text }}</label>
              {{ recipe_form.recipe_id(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-4 py-3 text-white") }}
            </div>
            
            <div>
              <label class="block text-sm font-medium text-slate-300 mb-2">{{ recipe_form.servings.label.text }}</label>
              {{ recipe_form.servings(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-4 py-3 text-white") }}
            </div>
            
            <div>
              <label class="block text-sm font-medium text-slate-300 mb-2">{{ recipe_form.special_instructions.label.text }}</label>
              {{ recipe_form.special_instructions(rows="3", class="w-full bg-slate-800 border border-slate-700 rounded-lg px-4 py-3 text-white", placeholder="Instructions spéciales...") }}
            </div>
            
            {{ recipe_form.submit(class="w-full bg-green-600 hover:bg-green-500 text-white font-medium py-3 px-4 rounded-lg") }}
          </form>
        </div>
        {% endif %}
        
        <!-- Formulaire variantes -->
        {% if variant_form %}
        <div id="form-variant" class="hidden">
          <h3 class="text-lg font-semibold mb-4">🎨 Ajouter une variante</h3>
          <form method="post" class="space-y-4">
            {{ variant_form.csrf_token }}
            <input type="hidden" name="variant_add" value="1" />
            
            <div>
              <label class="block text-sm font-medium text-slate-300 mb-2">{{ variant_form.product_id.label.text }}</label>
              {{ variant_form.product_id(id="product-select", class="w-full bg-slate-800 border border-slate-700 rounded-lg px-4 py-3 text-white") }}
            </div>
            
            <div>
              <label class="block text-sm font-medium text-slate-300 mb-2">{{ variant_form.variant_id.label.text }}</label>
              {{ variant_form.variant_id(id="variant-select", class="w-full bg-slate-800 border border-slate-700 rounded-lg px-4 py-3 text-white") }}
            </div>
            
            <div class="grid grid-cols-2 gap-3">
              <div>
                <label class="block text-sm font-medium text-slate-300 mb-2">{{ variant_form.qty.label.text }}</label>
                {{ variant_form.qty(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-4 py-3 text-white") }}
              </div>
              <div>
                <label class="block text-sm font-medium text-slate-300 mb-2">{{ variant_form.custom_price.label.text }}</label>
                {{ variant_form.custom_price(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-4 py-3 text-white", placeholder="Optionnel") }}
              </div>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-slate-300 mb-2">{{ variant_form.notes.label.text }}</label>
              {{ variant_form.notes(rows="2", class="w-full bg-slate-800 border border-slate-700 rounded-lg px-4 py-3 text-white", placeholder="Notes optionnelles...") }}
            </div>
            
            {{ variant_form.submit(class="w-full bg-purple-600 hover:bg-purple-500 text-white font-medium py-3 px-4 rounded-lg") }}
          </form>
        </div>
        {% endif %}
      </div>
    </div>

    <!-- Liste des articles de la commande -->
    <div class="col-span-12 lg:col-span-7">
      <div class="rounded-xl bg-slate-900 border border-slate-700 p-6">
        <h2 class="text-lg font-semibold mb-4">Articles de la commande</h2>
        
        {% if items %}
          <div class="space-y-3">
            {% for item in items %}
            <div class="bg-slate-800 border border-slate-700 rounded-lg p-4">
              <div class="flex items-center justify-between">
                <div class="flex-1">
                  <h3 class="font-semibold">Produit #{{ item.product_id_fk }}</h3>
                  <div class="text-sm text-slate-400 mt-1">
                    Quantité: <span class="text-white font-medium">{{ item.qty }}</span> • 
                    Prix unitaire: <span class="text-cyan-400 font-medium">{{ "%.2f"|format(item.price_cents/100) }} €</span> •
                    Total: <span class="text-green-400 font-medium">{{ "%.2f"|format((item.qty * item.price_cents)/100) }} €</span>
                  </div>
                </div>
                <div class="flex space-x-2">
                  <a href="{{ url_for('sales.edit_order_item', order_id=order.id, id=item.id) }}" class="px-3 py-1 bg-blue-600 hover:bg-blue-500 text-white text-xs rounded transition-colors">
                    ✏️ Éditer
                  </a>
                  <form method="post" action="{{ url_for('sales.delete_order_item', order_id=order.id, id=item.id) }}" onsubmit="return confirm('Supprimer cet article ?');" class="inline">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <button class="px-3 py-1 bg-red-600 hover:bg-red-500 text-white text-xs rounded transition-colors">
                      🗑️ Supprimer
                    </button>
                  </form>
                </div>
              </div>
            </div>
            {% endfor %}
            
            <!-- Résumé de la commande -->
            <div class="bg-slate-800 border border-cyan-700 rounded-lg p-4 mt-4">
              <div class="flex items-center justify-between">
                <div class="text-lg font-semibold">Total de la commande</div>
                <div class="text-2xl font-bold text-cyan-400">{{ "%.2f"|format(order.total_cents/100) }} €</div>
              </div>
              <div class="text-sm text-slate-400 mt-1">
                {{ items|length }} article(s) • Commande #{{ order.id }}
              </div>
            </div>
          </div>
        {% else %}
          <div class="text-center py-8">
            <div class="text-6xl mb-4">📦</div>
            <h3 class="text-xl font-semibold text-slate-300 mb-2">Aucun article</h3>
            <p class="text-slate-400">Cette commande ne contient pas encore d'articles.</p>
            <p class="text-slate-400 mt-2">Utilisez les formulaires à gauche pour ajouter des articles.</p>
          </div>
        {% endif %}
      </div>
    </div>
  </div>
</div>

<script>
// Gestion des onglets de formulaires
const formTabs = ['classic', 'recipe', 'variant'];
let currentFormTab = 'classic';

formTabs.forEach(tab => {
    const tabElement = document.getElementById(`tab-${tab}`);
    if (tabElement) {
        tabElement.addEventListener('click', () => {
            switchFormTab(tab);
        });
    }
});

function switchFormTab(tab) {
    formTabs.forEach(t => {
        const tabElement = document.getElementById(`tab-${t}`);
        const formElement = document.getElementById(`form-${t}`);
        
        if (tabElement && formElement) {
            if (t === tab) {
                tabElement.className = 'px-3 py-1 bg-cyan-600 text-white rounded text-sm font-medium';
                formElement.classList.remove('hidden');
            } else {
                tabElement.className = 'px-3 py-1 bg-slate-700 text-slate-300 rounded text-sm font-medium';
                formElement.classList.add('hidden');
            }
        }
    });
    
    currentFormTab = tab;
}

// Gestion des variantes dynamiques
const productSelect = document.getElementById('product-select');
const variantSelect = document.getElementById('variant-select');

if (productSelect && variantSelect) {
    productSelect.addEventListener('change', function() {
        const productId = this.value;
        if (productId) {
            loadProductVariants(productId);
        } else {
            variantSelect.innerHTML = '<option value="">Sélectionnez d\'abord un produit</option>';
        }
    });
}

function loadProductVariants(productId) {
    // Afficher un indicateur de chargement
    variantSelect.innerHTML = '<option value="">Chargement...</option>';
    variantSelect.disabled = true;
    
    // Simuler le chargement des variantes (TODO: remplacer par l'API réelle)
    setTimeout(() => {
        // Simuler des variantes
        const mockVariants = [
            { id: 1, name: 'Rouge - M', price: 25.00, stock: 10 },
            { id: 2, name: 'Bleu - L', price: 27.00, stock: 5 },
            { id: 3, name: 'Vert - S', price: 23.00, stock: 15 }
        ];
        
        variantSelect.innerHTML = '<option value="">Sélectionnez une variante</option>';
        mockVariants.forEach(variant => {
            const option = document.createElement('option');
            option.value = variant.id;
            option.textContent = `${variant.name} - ${variant.price}€ (Stock: ${variant.stock})`;
            variantSelect.appendChild(option);
        });
        
        variantSelect.disabled = false;
    }, 1000);
}

// Initialiser les éléments vides au chargement
document.addEventListener('DOMContentLoaded', function() {
    if (variantSelect) {
        variantSelect.innerHTML = '<option value="">Sélectionnez d\'abord un produit</option>';
    }
});
</script>
{% endblock %}
