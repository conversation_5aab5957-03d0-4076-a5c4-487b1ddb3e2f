<!-- Modal Édition Catégorie -->
<div id="editCategoryModal" class="hidden fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <!-- Fond noir transparent -->
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
            <div class="absolute inset-0 bg-gray-900 opacity-75" onclick="document.getElementById('editCategoryModal').classList.add('hidden')"></div>
        </div>

        <!-- Contenu du modal -->
        <div class="inline-block align-bottom bg-slate-900 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full border border-slate-700">
            <div class="bg-cyan-600 px-4 py-3 border-b border-slate-700">
                <h3 class="text-lg leading-6 font-medium text-white">
                    <i class="fas fa-tags mr-2"></i>Modifier la Catégorie
                </h3>
            </div>
            <form id="editCategoryForm" method="POST" action="" class="px-4 py-5 sm:p-6">
                <div class="modal-body">
                    <input type="hidden" id="editCategoryId" name="id">
                    <div class="mb-4">
                        <label for="editCategoryName" class="block text-sm font-medium text-slate-300 mb-1">Nom de la catégorie *</label>
                        <input type="text" class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 text-slate-100 focus:outline-none focus:ring-2 focus:ring-cyan-500" id="editCategoryName" name="name" required 
                               placeholder="Ex: Légumes, Viandes, Épices...">
                        <p class="mt-1 text-sm text-slate-500">Nom unique pour identifier la catégorie</p>
                    </div>
                    
                    <div class="mb-4">
                        <label for="editCategoryDescription" class="block text-sm font-medium text-slate-300 mb-1">Description</label>
                        <textarea class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 text-slate-100 focus:outline-none focus:ring-2 focus:ring-cyan-500" id="editCategoryDescription" name="description" rows="3" 
                                  placeholder="Description détaillée de la catégorie..."></textarea>
                        <p class="mt-1 text-sm text-slate-500">Description optionnelle pour mieux organiser vos ingrédients</p>
                    </div>
                    
                    <div class="mb-4">
                        <label for="editCategoryColor" class="block text-sm font-medium text-slate-300 mb-1">Couleur de la catégorie</label>
                        <div class="flex items-center gap-2">
                            <input type="color" class="w-12 h-10 bg-slate-800 border border-slate-700 rounded-lg" id="editCategoryColor" 
                                   name="color" value="#4e73df" title="Choisir une couleur">
                            <span class="text-sm text-slate-500">Couleur pour identifier visuellement la catégorie</span>
                        </div>
                    </div>
                </div>
                
                <div class="modal-footer mt-6 flex justify-end space-x-3">
                    <button type="button" class="px-4 py-2 border border-slate-700 rounded-lg text-slate-300 hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-slate-500"
                            onclick="document.getElementById('editCategoryModal').classList.add('hidden')">
                        <i class="fas fa-times mr-1"></i>Annuler
                    </button>
                    <button type="submit" class="px-4 py-2 bg-cyan-600 rounded-lg text-white hover:bg-cyan-500 focus:outline-none focus:ring-2 focus:ring-cyan-500">
                        <i class="fas fa-save mr-1"></i>Enregistrer
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Validation du formulaire
    const form = document.getElementById('editCategoryForm');
    form.addEventListener('submit', function(e) {
        if (!form.checkValidity()) {
            e.preventDefault();
            e.stopPropagation();
        }
        form.classList.add('was-validated');
    });
});
</script>