from typing import Any, Dict, List, Optional, Union
from sqlalchemy.orm import Query
from flask import request, url_for
from math import ceil


class Pagination:
    """Système de pagination avancé pour les requêtes SQLAlchemy"""
    
    def __init__(self, query: Query, page: int = 1, per_page: int = 20, 
                 max_per_page: int = 100, error_out: bool = True):
        """
        Initialise la pagination
        
        Args:
            query: Requête SQLAlchemy à paginer
            page: Numéro de page (commence à 1)
            per_page: Nombre d'éléments par page
            max_per_page: Nombre maximum d'éléments par page
            error_out: Si True, déclenche une erreur 404 pour les pages invalides
        """
        self.query = query
        self.page = page
        self.per_page = min(per_page, max_per_page) if max_per_page else per_page
        self.max_per_page = max_per_page
        self.error_out = error_out
        
        # Calculer le nombre total d'éléments
        self.total = query.count()
        
        # Calculer le nombre de pages
        if self.per_page == 0:
            self.pages = 0
        else:
            self.pages = ceil(self.total / self.per_page)
        
        # Valider la page
        if self.page < 1:
            if self.error_out:
                from flask import abort
                abort(404)
            else:
                self.page = 1
        
        if self.page > self.pages and self.pages > 0:
            if self.error_out:
                from flask import abort
                abort(404)
            else:
                self.page = self.pages if self.pages > 0 else 1
        
        # Calculer les numéros de page
        self.prev_num = self.page - 1 if self.page > 1 else None
        self.next_num = self.page + 1 if self.page < self.pages else None
        
        # Calculer les offsets
        self.offset = (self.page - 1) * self.per_page
        self.limit = self.per_page
    
    @property
    def items(self) -> List[Any]:
        """Récupère les éléments de la page courante"""
        if self.pages == 0:
            return []
        
        return self.query.offset(self.offset).limit(self.limit).all()
    
    @property
    def has_prev(self) -> bool:
        """Vérifie s'il y a une page précédente"""
        return self.page > 1
    
    @property
    def has_next(self) -> bool:
        """Vérifie s'il y a une page suivante"""
        return self.page < self.pages
    
    def iter_pages(self, left_edge: int = 2, left_current: int = 2,
                   right_current: int = 3, right_edge: int = 2) -> List[Optional[int]]:
        """
        Génère une liste de numéros de pages pour la navigation
        
        Args:
            left_edge: Nombre de pages à afficher au début
            left_current: Nombre de pages avant la page courante
            right_current: Nombre de pages après la page courante
            right_edge: Nombre de pages à afficher à la fin
        """
        pages = []
        last = 0
        
        for num in range(1, self.pages + 1):
            if (num <= left_edge or 
                (num > self.page - left_current - 1 and num < self.page + right_current) or
                num > self.pages - right_edge):
                
                if last + 1 != num:
                    pages.append(None)
                pages.append(num)
                last = num
        
        return pages
    
    def to_dict(self) -> Dict[str, Any]:
        """Convertit la pagination en dictionnaire"""
        return {
            'items': [item.to_dict() if hasattr(item, 'to_dict') else item for item in self.items],
            'pagination': {
                'page': self.page,
                'per_page': self.per_page,
                'total': self.total,
                'pages': self.pages,
                'has_prev': self.has_prev,
                'has_next': self.has_next,
                'prev_num': self.prev_num,
                'next_num': self.next_num
            }
        }
    
    def get_links(self, endpoint: str, **kwargs) -> Dict[str, str]:
        """Génère les liens de pagination pour l'API"""
        links = {}
        
        if self.has_prev:
            links['prev'] = url_for(endpoint, page=self.prev_num, per_page=self.per_page, **kwargs)
        
        if self.has_next:
            links['next'] = url_for(endpoint, page=self.next_num, per_page=self.per_page, **kwargs)
        
        links['first'] = url_for(endpoint, page=1, per_page=self.per_page, **kwargs)
        links['last'] = url_for(endpoint, page=self.pages, per_page=self.per_page, **kwargs)
        
        return links


def paginate_query(query: Query, page: int = None, per_page: int = None, 
                  max_per_page: int = 100) -> Pagination:
    """
    Fonction utilitaire pour paginer une requête
    
    Args:
        query: Requête SQLAlchemy à paginer
        page: Numéro de page (défaut: depuis request.args)
        per_page: Éléments par page (défaut: depuis request.args)
        max_per_page: Maximum d'éléments par page
    
    Returns:
        Instance de Pagination
    """
    if page is None:
        page = request.args.get('page', 1, type=int)
    
    if per_page is None:
        per_page = request.args.get('per_page', 20, type=int)
    
    return Pagination(query, page=page, per_page=per_page, max_per_page=max_per_page)


# Décorateur pour ajouter la pagination automatique aux routes
def paginated_response(query_func):
    """
    Décorateur pour ajouter la pagination automatique aux routes
    
    Usage:
        @bp.route('/items')
        @paginated_response
        def get_items():
            return Item.query.filter(Item.active == True)
    """
    def decorator(f):
        def wrapper(*args, **kwargs):
            query = f(*args, **kwargs)
            pagination = paginate_query(query)
            
            return {
                'data': [item.to_dict() for item in pagination.items],
                'pagination': {
                    'page': pagination.page,
                    'per_page': pagination.per_page,
                    'total': pagination.total,
                    'pages': pagination.pages,
                    'has_prev': pagination.has_prev,
                    'has_next': pagination.has_next
                },
                'links': pagination.get_links(request.endpoint)
            }
        return wrapper
    return decorator