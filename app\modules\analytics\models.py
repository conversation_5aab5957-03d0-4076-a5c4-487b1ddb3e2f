from __future__ import annotations

from datetime import datetime, date, timedelta
from app.extensions import db
from sqlalchemy import func, text
from decimal import Decimal
import json


class KPI(db.Model):
    """Indicateurs de Performance Clés"""
    __tablename__ = "kpis"
    id = db.Column(db.Integer, primary_key=True)
    business_id_fk = db.Column(db.Integer, db.ForeignKey("businesses.id"), nullable=False, index=True)
    name = db.Column(db.String(200), nullable=False)
    kpi_code = db.Column(db.String(100), nullable=False, index=True)
    description = db.Column(db.Text, nullable=True)
    category = db.Column(db.String(50), nullable=False, index=True)
    
    # Configuration
    calculation_method = db.Column(db.String(50), nullable=False)  # sql, python, api
    calculation_config = db.Column(db.J<PERSON>, nullable=True)  # Configuration spécifique
    data_source = db.Column(db.String(100), nullable=True)  # Source des données
    refresh_frequency = db.Column(db.String(20), nullable=False, default='daily')  # hourly, daily, weekly, monthly
    
    # Valeurs actuelles
    current_value = db.Column(db.Numeric(15, 2), nullable=True)
    previous_value = db.Column(db.Numeric(15, 2), nullable=True)
    last_calculated = db.Column(db.DateTime, nullable=True)
    
    # Seuils d'alerte
    threshold_warning = db.Column(db.Numeric(15, 2), nullable=True)
    threshold_critical = db.Column(db.Numeric(15, 2), nullable=True)
    target_value = db.Column(db.Numeric(15, 2), nullable=True)
    
    # Métadonnées
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Relations
    history = db.relationship("KPIHistory", backref="kpi", lazy=True, cascade="all, delete-orphan")
    
    __table_args__ = (
        db.Index("idx_kpi_business", "business_id_fk"),
        db.Index("idx_kpi_code", "kpi_code"),
        db.Index("idx_kpi_category", "category"),
        db.UniqueConstraint("business_id_fk", "kpi_code", name="uq_business_kpi_code"),
        {'extend_existing': True}
    )
    
    @property
    def status(self):
        """Retourne le statut du KPI basé sur les seuils"""
        if self.current_value is None:
            return 'unknown'
        
        current = float(self.current_value)
        
        if self.threshold_critical and current <= float(self.threshold_critical):
            return 'critical'
        elif self.threshold_warning and current <= float(self.threshold_warning):
            return 'warning'
        else:
            return 'good'
    
    @property
    def progress_percentage(self):
        """Calcule le pourcentage de progression vers l'objectif"""
        if self.target_value and self.current_value:
            return min(100, (float(self.current_value) / float(self.target_value)) * 100)
        return None


class KPIHistory(db.Model):
    """Historique des valeurs des KPIs"""
    __tablename__ = "kpi_history"
    id = db.Column(db.Integer, primary_key=True)
    kpi_id_fk = db.Column(db.Integer, db.ForeignKey("kpis.id"), nullable=False, index=True)
    
    # Valeurs historiques
    value = db.Column(db.Numeric(15, 2), nullable=False)
    calculation_date = db.Column(db.Date, nullable=False, index=True)
    period_start = db.Column(db.Date, nullable=True)
    period_end = db.Column(db.Date, nullable=True)
    
    # Métadonnées
    calculation_details = db.Column(db.JSON, nullable=True)  # Détails du calcul
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    
    __table_args__ = (
        db.Index("idx_history_kpi", "kpi_id_fk"),
        db.Index("idx_history_date", "calculation_date"),
        db.UniqueConstraint("kpi_id_fk", "calculation_date", name="uq_kpi_date"),
        {'extend_existing': True}
    )


class Dashboard(db.Model):
    """Modèle pour les tableaux de bord personnalisés"""
    __tablename__ = "dashboards"
    id = db.Column(db.Integer, primary_key=True)
    business_id_fk = db.Column(db.Integer, db.ForeignKey("businesses.id"), nullable=False, index=True)
    user_id_fk = db.Column(db.Integer, db.ForeignKey("users.id"), nullable=True, index=True)
    
    # Informations du dashboard
    name = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text, nullable=True)
    dashboard_type = db.Column(db.String(20), nullable=False, default='custom')  # system, custom, template
    
    # Configuration
    layout_config = db.Column(db.JSON, nullable=True)  # Configuration du layout
    widgets = db.Column(db.JSON, nullable=True)  # Liste des widgets et leur configuration
    filters = db.Column(db.JSON, nullable=True)  # Filtres par défaut
    
    # Accès et permissions
    is_public = db.Column(db.Boolean, default=False, nullable=False)
    is_default = db.Column(db.Boolean, default=False, nullable=False)
    access_roles = db.Column(db.JSON, nullable=True)  # Rôles autorisés
    
    # Métadonnées
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    __table_args__ = (
        db.Index("idx_dashboard_business", "business_id_fk"),
        db.Index("idx_dashboard_user", "user_id_fk"),
        db.Index("idx_dashboard_type", "dashboard_type"),
        {'extend_existing': True}
    )


class Report(db.Model):
    """Modèle pour les rapports personnalisés"""
    __tablename__ = "reports"
    id = db.Column(db.Integer, primary_key=True)
    business_id_fk = db.Column(db.Integer, db.ForeignKey("businesses.id"), nullable=False, index=True)
    created_by_id = db.Column(db.Integer, db.ForeignKey("users.id"), nullable=False, index=True)
    
    # Informations du rapport
    name = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text, nullable=True)
    category = db.Column(db.String(50), nullable=False, index=True)  # sales, inventory, finance, staff
    
    # Configuration du rapport
    report_type = db.Column(db.String(20), nullable=False)  # table, chart, summary
    data_source = db.Column(db.String(100), nullable=False)  # table ou vue source
    query_config = db.Column(db.JSON, nullable=False)  # Configuration de la requête
    chart_config = db.Column(db.JSON, nullable=True)  # Configuration des graphiques
    
    # Planification
    is_scheduled = db.Column(db.Boolean, default=False, nullable=False)
    schedule_frequency = db.Column(db.String(20), nullable=True)  # daily, weekly, monthly
    schedule_config = db.Column(db.JSON, nullable=True)  # Configuration de la planification
    
    # Export et partage
    auto_export = db.Column(db.Boolean, default=False, nullable=False)
    export_format = db.Column(db.String(10), nullable=True)  # pdf, excel, csv
    recipients = db.Column(db.JSON, nullable=True)  # Liste des destinataires
    
    # Métadonnées
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    last_generated = db.Column(db.DateTime, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Relations
    executions = db.relationship("ReportExecution", backref="report", lazy=True)
    
    __table_args__ = (
        db.Index("idx_report_business", "business_id_fk"),
        db.Index("idx_report_category", "category"),
        db.Index("idx_report_creator", "created_by_id"),
        {'extend_existing': True}
    )


class ReportExecution(db.Model):
    """Historique d'exécution des rapports"""
    __tablename__ = "report_executions"
    id = db.Column(db.Integer, primary_key=True)
    report_id_fk = db.Column(db.Integer, db.ForeignKey("reports.id"), nullable=False, index=True)
    executed_by_id = db.Column(db.Integer, db.ForeignKey("users.id"), nullable=True, index=True)
    
    # Informations d'exécution
    execution_type = db.Column(db.String(20), nullable=False)  # manual, scheduled, api
    status = db.Column(db.String(20), nullable=False, default='pending')  # pending, success, error
    
    # Résultats
    rows_count = db.Column(db.Integer, nullable=True)
    file_path = db.Column(db.String(500), nullable=True)  # Chemin du fichier généré
    file_size = db.Column(db.Integer, nullable=True)  # Taille en bytes
    
    # Performance
    execution_time_ms = db.Column(db.Integer, nullable=True)  # Temps d'exécution en ms
    error_message = db.Column(db.Text, nullable=True)
    
    # Timestamps
    started_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    completed_at = db.Column(db.DateTime, nullable=True)
    
    __table_args__ = (
        db.Index("idx_execution_report", "report_id_fk"),
        db.Index("idx_execution_user", "executed_by_id"),
        db.Index("idx_execution_date", "started_at"),
        {'extend_existing': True}
    )


class AnalyticsCache(db.Model):
    """Cache pour les résultats d'analyses coûteuses"""
    __tablename__ = "analytics_cache"
    id = db.Column(db.Integer, primary_key=True)
    business_id_fk = db.Column(db.Integer, db.ForeignKey("businesses.id"), nullable=False, index=True)
    
    # Identification du cache
    cache_key = db.Column(db.String(200), nullable=False, index=True)
    cache_type = db.Column(db.String(50), nullable=False, index=True)  # kpi, report, chart
    
    # Données cachées
    data = db.Column(db.JSON, nullable=False)
    metadata = db.Column(db.JSON, nullable=True)  # Métadonnées sur les données
    
    # Expiration
    expires_at = db.Column(db.DateTime, nullable=False, index=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    
    __table_args__ = (
        db.Index("idx_cache_business", "business_id_fk"),
        db.Index("idx_cache_key", "cache_key"),
        db.Index("idx_cache_expiry", "expires_at"),
        db.UniqueConstraint("business_id_fk", "cache_key", name="uq_business_cache_key"),
        {'extend_existing': True}
    )
    
    @property
    def is_expired(self):
        """Vérifie si le cache a expiré"""
        return datetime.utcnow() > self.expires_at
    
    @classmethod
    def get_cached_data(cls, business_id, cache_key):
        """Récupère des données du cache si valides"""
        cache_entry = cls.query.filter_by(
            business_id_fk=business_id,
            cache_key=cache_key
        ).first()
        
        if cache_entry and not cache_entry.is_expired:
            return cache_entry.data
        elif cache_entry:
            # Supprimer le cache expiré
            db.session.delete(cache_entry)
            db.session.commit()
        
        return None
    
    @classmethod
    def set_cached_data(cls, business_id, cache_key, cache_type, data, expires_in_hours=24):
        """Met en cache des données avec expiration"""
        # Supprimer l'ancien cache s'il existe
        existing = cls.query.filter_by(
            business_id_fk=business_id,
            cache_key=cache_key
        ).first()
        
        if existing:
            db.session.delete(existing)
        
        # Créer le nouveau cache
        expires_at = datetime.utcnow() + timedelta(hours=expires_in_hours)
        
        cache_entry = cls(
            business_id_fk=business_id,
            cache_key=cache_key,
            cache_type=cache_type,
            data=data,
            expires_at=expires_at
        )
        
        db.session.add(cache_entry)
        db.session.commit()
        
        return cache_entry