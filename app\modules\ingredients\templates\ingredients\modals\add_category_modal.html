<!-- Modal Ajout Catégorie -->
<div id="addCategoryModal" class="hidden fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <!-- Fond noir transparent -->
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
            <div class="absolute inset-0 bg-gray-900 opacity-75" onclick="document.getElementById('addCategoryModal').classList.add('hidden')"></div>
        </div>

        <!-- Contenu du modal -->
        <div class="inline-block align-bottom bg-slate-900 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full border border-slate-700">
            <div class="bg-blue-600 px-4 py-3 border-b border-slate-700">
                <h3 class="text-lg leading-6 font-medium text-white">
                    Nouvelle Catégorie
                </h3>
            </div>
            <form id="addCategoryForm" method="POST" action="{{ url_for('ingredients.create_category') }}" class="px-4 py-5 sm:p-6">
                <div class="space-y-4">
                    <div>
                        <label for="categoryName" class="block text-sm font-medium text-slate-300 mb-1">Nom de la catégorie *</label>
                        <input type="text" id="categoryName" name="name" required
                               class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 text-slate-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="Ex: Légumes, Viandes, Épices...">
                        <p class="mt-1 text-xs text-slate-500">Nom unique pour identifier la catégorie</p>
                    </div>
                    
                    <div>
                        <label for="categoryDescription" class="block text-sm font-medium text-slate-300 mb-1">Description</label>
                        <textarea id="categoryDescription" name="description" rows="3"
                                  class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 text-slate-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                  placeholder="Description détaillée de la catégorie..."></textarea>
                        <p class="mt-1 text-xs text-slate-500">Description optionnelle pour mieux organiser vos ingrédients</p>
                    </div>
                    
                    <div>
                        <label for="categoryColor" class="block text-sm font-medium text-slate-300 mb-1">Couleur de la catégorie</label>
                        <div class="flex items-center gap-2">
                            <input type="color" id="categoryColor" name="color" value="#4e73df"
                                   class="w-12 h-10 border border-slate-700 rounded-lg bg-slate-800 cursor-pointer">
                            <span class="text-sm text-slate-400">Couleur pour identifier visuellement la catégorie</span>
                        </div>
                    </div>
                    
                    <div class="bg-blue-900/30 border border-blue-800 rounded-lg p-3">
                        <div class="flex items-center">
                            <div>
                                <strong class="text-blue-300">Conseil:</strong>
                                <span class="text-blue-200">Créez des catégories logiques pour organiser vos ingrédients et faciliter la gestion des stocks et des coûts.</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="mt-6 flex justify-end space-x-3">
                    <button type="button" 
                            class="px-4 py-2 border border-slate-700 rounded-lg text-slate-300 hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-blue-500"
                            onclick="document.getElementById('addCategoryModal').classList.add('hidden')">
                        Annuler
                    </button>
                    <button type="submit"
                            class="px-4 py-2 bg-blue-600 rounded-lg text-white hover:bg-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        Créer la Catégorie
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Validation du formulaire
    const form = document.getElementById('addCategoryForm');
    form.addEventListener('submit', function(e) {
        if (!form.checkValidity()) {
            e.preventDefault();
            e.stopPropagation();
        }
        form.classList.add('was-validated');
    });
    
    // Génération automatique d'une description basée sur le nom
    const nameInput = document.getElementById('categoryName');
    const descriptionInput = document.getElementById('categoryDescription');
    
    nameInput.addEventListener('blur', function() {
        if (this.value && !descriptionInput.value) {
            const suggestions = {
                'légumes': 'Catégorie pour tous les légumes frais et transformés',
                'viandes': 'Catégorie pour les viandes fraîches et transformées',
                'poissons': 'Catégorie pour les poissons et fruits de mer',
                'épices': 'Catégorie pour les épices et condiments',
                'céréales': 'Catégorie pour les céréales et grains',
                'laitiers': 'Catégorie pour les produits laitiers',
                'fruits': 'Catégorie pour les fruits frais et secs',
                'boissons': 'Catégorie pour les boissons et liquides',
                'desserts': 'Catégorie pour les ingrédients de desserts',
                'sauces': 'Catégorie pour les sauces et condiments'
            };
            
            const name = this.value.toLowerCase();
            for (const [key, suggestion] of Object.entries(suggestions)) {
                if (name.includes(key)) {
                    descriptionInput.value = suggestion;
                    break;
                }
            }
        }
    });
});
</script>