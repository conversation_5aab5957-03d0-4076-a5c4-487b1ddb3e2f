{% extends "base.html" %}
{% block title %}{{ title or 'Catégorie' }} - {{ super() }}{% endblock %}
{% block content %}
<div class="max-w-4xl mx-auto">
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-bold text-white">{{ title or 'Catégorie' }}</h1>
    <a href="{{ url_for('ingredients.categories') }}" class="bg-slate-700 hover:bg-slate-600 text-white px-4 py-2 rounded-lg">
      ← Retour
    </a>
  </div>
  
  <div class="bg-slate-800 rounded-xl border border-slate-700 p-6">
    <form method="post" class="space-y-6">
      {{ form.hidden_tag() }}
      
      <div>
        <label class="block text-sm font-medium text-slate-300 mb-2" for="name">
          Nom de la catégorie
        </label>
        {{ form.name(class='w-full bg-slate-700 border border-slate-600 text-white rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent') }}
        {% for e in form.name.errors %}
          <div class="text-red-400 text-sm mt-1">{{ e }}</div>
        {% endfor %}
      </div>
      
      <div>
        <label class="block text-sm font-medium text-slate-300 mb-2" for="description">
          Description
        </label>
        {{ form.description(class='w-full bg-slate-700 border border-slate-600 text-white rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent', rows=4) }}
        {% for e in form.description.errors %}
          <div class="text-red-400 text-sm mt-1">{{ e }}</div>
        {% endfor %}
      </div>
      
      <div class="flex justify-end">
        <button type="submit" class="bg-cyan-600 hover:bg-cyan-500 text-white font-medium py-3 px-6 rounded-lg">
          Enregistrer
        </button>
      </div>
    </form>
  </div>
</div>
{% endblock %}