{% extends 'base.html' %}
{% block title %}Rapport Rapide{% endblock %}

{% block head %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<style>
    .quick-report-page {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 20px 0;
    }
    
    .quick-report-content {
        background: white;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        padding: 30px;
        margin-top: 20px;
    }
    
    .report-form {
        background: #f8f9ff;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 30px;
        border-left: 4px solid #667eea;
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .form-label {
        font-weight: 600;
        color: #333;
        margin-bottom: 8px;
        display: block;
    }
    
    .form-control {
        border: 1px solid #ddd;
        border-radius: 10px;
        padding: 12px 15px;
        font-size: 1rem;
        transition: all 0.3s ease;
    }
    
    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        outline: none;
    }
    
    .btn-report {
        background: linear-gradient(45deg, #667eea, #764ba2);
        border: none;
        color: white;
        padding: 12px 30px;
        border-radius: 25px;
        font-weight: 600;
        font-size: 1rem;
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .btn-report:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
    }
    
    .btn-report:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
    }
    
    .report-results {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-top: 30px;
        border: 1px solid #e9ecef;
        display: none;
    }
    
    .results-header {
        border-bottom: 2px solid #667eea;
        padding-bottom: 15px;
        margin-bottom: 25px;
    }
    
    .results-title {
        color: #333;
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 5px;
    }
    
    .results-meta {
        color: #666;
        font-size: 0.9rem;
    }
    
    .metric-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .metric-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 20px;
        text-align: center;
    }
    
    .metric-number {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 8px;
    }
    
    .metric-label {
        font-size: 0.9rem;
        opacity: 0.9;
        text-transform: uppercase;
        letter-spacing: 1px;
    }
    
    .chart-section {
        margin-top: 30px;
    }
    
    .chart-container {
        position: relative;
        height: 300px;
        margin-top: 20px;
    }
    
    .loading-state {
        text-align: center;
        padding: 40px;
        color: #666;
    }
    
    .loading-spinner {
        width: 40px;
        height: 40px;
        margin: 0 auto 20px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #667eea;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    .error-state {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
        border-radius: 10px;
        padding: 20px;
        margin-top: 20px;
    }
    
    .success-state {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
        border-radius: 10px;
        padding: 20px;
        margin-top: 20px;
    }
    
    .export-actions {
        text-align: center;
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #e9ecef;
    }
    
    .btn-export {
        background: #28a745;
        border: none;
        color: white;
        padding: 8px 20px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.9rem;
        margin: 0 5px;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
    }
    
    .btn-export:hover {
        background: #218838;
        transform: translateY(-1px);
        color: white;
        text-decoration: none;
    }
    
    .report-type-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .type-card {
        background: white;
        border: 2px solid #e9ecef;
        border-radius: 15px;
        padding: 20px;
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: center;
    }
    
    .type-card:hover {
        border-color: #667eea;
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.1);
        transform: translateY(-2px);
    }
    
    .type-card.selected {
        border-color: #667eea;
        background: #f8f9ff;
    }
    
    .type-icon {
        font-size: 3rem;
        color: #667eea;
        margin-bottom: 15px;
    }
    
    .type-title {
        font-size: 1.2rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 10px;
    }
    
    .type-description {
        color: #666;
        font-size: 0.9rem;
        line-height: 1.4;
    }
</style>
{% endblock %}

{% block content %}
<div class="quick-report-page">
    <div class="container-fluid">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="text-white text-center mb-4">
                    <i class="fas fa-bolt me-3"></i>
                    Rapport Rapide
                </h1>
                
                <div class="text-center">
                    <a href="{{ url_for('reports.index') }}" class="btn-report" style="background: #6c757d;">
                        <i class="fas fa-arrow-left me-2"></i>Retour Dashboard
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Contenu principal -->
        <div class="quick-report-content">
            <!-- Sélection du type de rapport -->
            <h3 class="mb-4">
                <i class="fas fa-list-alt me-2"></i>
                Choisissez un Type de Rapport
            </h3>
            
            <div class="report-type-cards">
                <div class="type-card" onclick="selectReportType('sales')">
                    <div class="type-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="type-title">Rapport de Ventes</div>
                    <div class="type-description">
                        Analyse des ventes, chiffre d'affaires, tendances et performance des produits
                    </div>
                </div>
                
                <div class="type-card" onclick="selectReportType('inventory')">
                    <div class="type-icon">
                        <i class="fas fa-boxes"></i>
                    </div>
                    <div class="type-title">Rapport d'Inventaire</div>
                    <div class="type-description">
                        État des stocks, valeur d'inventaire, produits en rupture et rotation
                    </div>
                </div>
                
                <div class="type-card" onclick="selectReportType('financial')">
                    <div class="type-icon">
                        <i class="fas fa-euro-sign"></i>
                    </div>
                    <div class="type-title">Rapport Financier</div>
                    <div class="type-description">
                        Analyse financière, revenus, coûts, marges et rentabilité
                    </div>
                </div>
            </div>
            
            <!-- Formulaire de configuration -->
            <form id="quickReportForm" class="report-form">
                <input type="hidden" id="reportType" name="report_type" value="">
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">Date de début</label>
                            <input type="date" id="dateFrom" name="date_from" class="form-control" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">Date de fin</label>
                            <input type="date" id="dateTo" name="date_to" class="form-control" required>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">Période prédéfinie</label>
                            <select id="predefinedPeriod" class="form-control" onchange="setPredefinedPeriod()">
                                <option value="">Personnalisée</option>
                                <option value="today">Aujourd'hui</option>
                                <option value="yesterday">Hier</option>
                                <option value="last7days">7 derniers jours</option>
                                <option value="last30days">30 derniers jours</option>
                                <option value="thismonth">Ce mois</option>
                                <option value="lastmonth">Mois dernier</option>
                                <option value="thisyear">Cette année</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">Filtres additionnels</label>
                            <input type="text" id="filters" name="filters" class="form-control" 
                                   placeholder="Ex: catégorie=électronique">
                        </div>
                    </div>
                </div>
                
                <div class="text-center">
                    <button type="submit" class="btn-report" id="generateBtn">
                        <i class="fas fa-play me-2"></i>
                        Générer le Rapport
                    </button>
                </div>
            </form>
            
            <!-- Zone de résultats -->
            <div id="reportResults" class="report-results">
                <div class="results-header">
                    <div class="results-title" id="resultsTitle">Résultats du Rapport</div>
                    <div class="results-meta" id="resultsMeta">Généré le <span id="generationTime"></span></div>
                </div>
                
                <!-- Métriques principales -->
                <div id="metricsSection" class="metric-grid">
                    <!-- Les métriques seront injectées ici -->
                </div>
                
                <!-- Section graphique -->
                <div id="chartSection" class="chart-section">
                    <h4 class="mb-3">
                        <i class="fas fa-chart-bar me-2"></i>
                        Visualisation
                    </h4>
                    <div class="chart-container">
                        <canvas id="reportChart"></canvas>
                    </div>
                </div>
                
                <!-- Actions d'export -->
                <div class="export-actions">
                    <h5 class="mb-3">Exporter les Résultats</h5>
                    <a href="#" onclick="exportReport('pdf')" class="btn-export">
                        <i class="fas fa-file-pdf me-2"></i>PDF
                    </a>
                    <a href="#" onclick="exportReport('excel')" class="btn-export">
                        <i class="fas fa-file-excel me-2"></i>Excel
                    </a>
                    <a href="#" onclick="exportReport('csv')" class="btn-export">
                        <i class="fas fa-file-csv me-2"></i>CSV
                    </a>
                </div>
            </div>
            
            <!-- États de chargement et d'erreur -->
            <div id="loadingState" class="loading-state" style="display: none;">
                <div class="loading-spinner"></div>
                <p>Génération du rapport en cours...</p>
            </div>
            
            <div id="errorState" class="error-state" style="display: none;">
                <h5><i class="fas fa-exclamation-triangle me-2"></i>Erreur</h5>
                <p id="errorMessage">Une erreur s'est produite lors de la génération du rapport.</p>
            </div>
        </div>
    </div>
</div>

<script>
let selectedReportType = '';
let reportChart = null;

// Initialisation
document.addEventListener('DOMContentLoaded', function() {
    // Définir les dates par défaut (7 derniers jours)
    const today = new Date();
    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    
    document.getElementById('dateTo').value = today.toISOString().split('T')[0];
    document.getElementById('dateFrom').value = weekAgo.toISOString().split('T')[0];
    
    // Gestionnaire de soumission du formulaire
    document.getElementById('quickReportForm').addEventListener('submit', function(e) {
        e.preventDefault();
        generateReport();
    });
});

function selectReportType(type) {
    selectedReportType = type;
    document.getElementById('reportType').value = type;
    
    // Mise à jour visuelle
    document.querySelectorAll('.type-card').forEach(card => {
        card.classList.remove('selected');
    });
    event.currentTarget.classList.add('selected');
}

function setPredefinedPeriod() {
    const period = document.getElementById('predefinedPeriod').value;
    const today = new Date();
    let dateFrom, dateTo;
    
    switch(period) {
        case 'today':
            dateFrom = dateTo = today;
            break;
        case 'yesterday':
            const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
            dateFrom = dateTo = yesterday;
            break;
        case 'last7days':
            dateFrom = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
            dateTo = today;
            break;
        case 'last30days':
            dateFrom = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
            dateTo = today;
            break;
        case 'thismonth':
            dateFrom = new Date(today.getFullYear(), today.getMonth(), 1);
            dateTo = today;
            break;
        case 'lastmonth':
            dateFrom = new Date(today.getFullYear(), today.getMonth() - 1, 1);
            dateTo = new Date(today.getFullYear(), today.getMonth(), 0);
            break;
        case 'thisyear':
            dateFrom = new Date(today.getFullYear(), 0, 1);
            dateTo = today;
            break;
        default:
            return;
    }
    
    document.getElementById('dateFrom').value = dateFrom.toISOString().split('T')[0];
    document.getElementById('dateTo').value = dateTo.toISOString().split('T')[0];
}

function generateReport() {
    if (!selectedReportType) {
        showError('Veuillez sélectionner un type de rapport');
        return;
    }
    
    const formData = new FormData(document.getElementById('quickReportForm'));
    const data = Object.fromEntries(formData.entries());
    
    showLoading();
    
    fetch('/reports/quick-report', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.status === 'success') {
            showResults(data.data);
        } else {
            showError(data.message);
        }
    })
    .catch(error => {
        hideLoading();
        showError('Erreur de connexion: ' + error.message);
    });
}

function showLoading() {
    document.getElementById('loadingState').style.display = 'block';
    document.getElementById('reportResults').style.display = 'none';
    document.getElementById('errorState').style.display = 'none';
    document.getElementById('generateBtn').disabled = true;
}

function hideLoading() {
    document.getElementById('loadingState').style.display = 'none';
    document.getElementById('generateBtn').disabled = false;
}

function showError(message) {
    document.getElementById('errorMessage').textContent = message;
    document.getElementById('errorState').style.display = 'block';
    document.getElementById('reportResults').style.display = 'none';
}

function showResults(data) {
    document.getElementById('errorState').style.display = 'none';
    document.getElementById('reportResults').style.display = 'block';
    
    // Mise à jour des métadonnées
    document.getElementById('generationTime').textContent = new Date().toLocaleString();
    document.getElementById('resultsTitle').textContent = `Rapport ${getReportTypeName(selectedReportType)}`;
    
    // Affichage des métriques
    displayMetrics(data);
    
    // Affichage du graphique
    displayChart(data);
}

function getReportTypeName(type) {
    const names = {
        'sales': 'de Ventes',
        'inventory': 'd\\'Inventaire',
        'financial': 'Financier'
    };
    return names[type] || type;
}

function displayMetrics(data) {
    const metricsSection = document.getElementById('metricsSection');
    metricsSection.innerHTML = '';
    
    // Métriques par type de rapport
    let metrics = [];
    
    if (selectedReportType === 'sales') {
        metrics = [
            { label: 'Total Ventes', value: data.total_sales || 0, icon: 'chart-line' },
            { label: 'Chiffre d\\'Affaires', value: `€${data.total_amount || 0}`, icon: 'euro-sign' },
            { label: 'Panier Moyen', value: `€${data.average_sale || 0}`, icon: 'shopping-cart' }
        ];
    } else if (selectedReportType === 'inventory') {
        metrics = [
            { label: 'Total Produits', value: data.total_products || 0, icon: 'boxes' },
            { label: 'Valeur Stock', value: `€${data.total_stock_value || 0}`, icon: 'warehouse' },
            { label: 'Ruptures', value: data.low_stock_count || 0, icon: 'exclamation-triangle' }
        ];
    } else if (selectedReportType === 'financial') {
        metrics = [
            { label: 'Revenus', value: `€${data.total_revenue || 0}`, icon: 'arrow-up' },
            { label: 'Coûts', value: `€${data.total_costs || 0}`, icon: 'arrow-down' },
            { label: 'Profit Brut', value: `€${data.gross_profit || 0}`, icon: 'chart-bar' }
        ];
    }
    
    metrics.forEach(metric => {
        const metricCard = document.createElement('div');
        metricCard.className = 'metric-card';
        metricCard.innerHTML = `
            <div class="metric-number">${metric.value}</div>
            <div class="metric-label">
                <i class="fas fa-${metric.icon} me-2"></i>
                ${metric.label}
            </div>
        `;
        metricsSection.appendChild(metricCard);
    });
}

function displayChart(data) {
    const ctx = document.getElementById('reportChart').getContext('2d');
    
    // Détruire le graphique existant s'il existe
    if (reportChart) {
        reportChart.destroy();
    }
    
    // Configuration du graphique selon le type de rapport
    let chartConfig = {
        type: 'bar',
        data: {
            labels: ['Exemple'],
            datasets: [{
                label: 'Données',
                data: [100],
                backgroundColor: 'rgba(102, 126, 234, 0.8)',
                borderColor: '#667eea',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    };
    
    reportChart = new Chart(ctx, chartConfig);
}

function exportReport(format) {
    if (!selectedReportType) return;
    
    const params = new URLSearchParams({
        type: selectedReportType,
        format: format,
        date_from: document.getElementById('dateFrom').value,
        date_to: document.getElementById('dateTo').value
    });
    
    // Simuler le téléchargement
    console.log(`Export en ${format}:`, params.toString());
    
    // Créer un lien de téléchargement factice
    const link = document.createElement('a');
    link.href = '#';
    link.download = `rapport_${selectedReportType}_${new Date().toISOString().split('T')[0]}.${format}`;
    link.click();
}
</script>
{% endblock %}