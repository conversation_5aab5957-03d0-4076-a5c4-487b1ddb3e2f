{% extends 'base.html' %}
{% block title %}Kitchen Display System{% endblock %}

{% block head %}
<!-- Socket.IO pour les WebSockets -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
<!-- Script KDS -->
<script src="{{ url_for('kds.static', filename='kds.js') }}"></script>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-slate-900 p-4">
    <!-- En-tête KDS -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold text-white mb-2">Kitchen Display System</h1>
            <p class="text-slate-400">Suivi des commandes en temps réel</p>
        </div>
        
        <div class="flex gap-4">
            <!-- Filtres -->
            <div class="bg-slate-800 rounded-lg p-4">
                <form method="GET" class="flex gap-3" id="kds-filters">
                    {{ form.hidden_tag() }}
                    
                    <div>
                        {{ form.status_filter.label(class="block text-sm font-medium text-slate-300 mb-1") }}
                        {{ form.status_filter(class="bg-slate-700 border border-slate-600 text-white rounded-md px-3 py-2 text-sm") }}
                    </div>
                    
                    <div>
                        {{ form.screen_filter.label(class="block text-sm font-medium text-slate-300 mb-1") }}
                        {{ form.screen_filter(class="bg-slate-700 border border-slate-600 text-white rounded-md px-3 py-2 text-sm") }}
                    </div>
                    
                    <div class="flex items-end">
                        <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                            Filtrer
                        </button>
                    </div>
                </form>
            </div>
            
            <!-- Boutons d'action -->
            <div class="flex gap-2">
                <a href="{{ url_for('kds.screens') }}" 
                   class="bg-slate-700 hover:bg-slate-600 text-white px-4 py-2 rounded-md text-sm font-medium">
                    Gérer les écrans
                </a>
                
                <button id="refresh-tickets" 
                        class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                    Actualiser
                </button>
                
                <div class="flex items-center">
                    <div id="connection-status" class="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                    <span class="text-sm text-slate-400">Connecté</span>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Zone principale des tickets -->
    <div id="tickets-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        {% for ticket in tickets %}
        <div class="kds-ticket bg-slate-800 rounded-lg border-l-4 
                    {% if ticket.status == 'pending' %}border-yellow-500
                    {% elif ticket.status == 'preparing' %}border-blue-500
                    {% elif ticket.status == 'ready' %}border-green-500
                    {% else %}border-gray-500{% endif %}
                    p-4 shadow-lg" 
             data-ticket-id="{{ ticket.id }}" 
             data-order-id="{{ ticket.order_id_fk }}">
            
            <!-- En-tête du ticket -->
            <div class="flex justify-between items-start mb-4">
                <div>
                    <h3 class="text-lg font-semibold text-white">Commande #{{ ticket.order_id_fk }}</h3>
                    <p class="text-sm text-slate-400">{{ ticket.created_at.strftime('%H:%M') }}</p>
                </div>
                
                <div class="ticket-status-badge 
                            {% if ticket.status == 'pending' %}bg-yellow-500
                            {% elif ticket.status == 'preparing' %}bg-blue-500
                            {% elif ticket.status == 'ready' %}bg-green-500
                            {% else %}bg-gray-500{% endif %}
                            text-white px-2 py-1 rounded-full text-xs font-medium">
                    {% if ticket.status == 'pending' %}En attente
                    {% elif ticket.status == 'preparing' %}En préparation
                    {% elif ticket.status == 'ready' %}Prêt
                    {% else %}Terminé{% endif %}
                </div>
            </div>
            
            <!-- Articles de la commande -->
            <div class="space-y-2 mb-4">
                {% for item in ticket.order_items %}
                <div class="bg-slate-700 rounded p-3">
                    <div class="flex justify-between items-center">
                        <div>
                            <p class="text-white font-medium">{{ item.product.name if item.product else 'Produit supprimé' }}</p>
                            <p class="text-slate-300 text-sm">{{ item.qty }}x {{ "%.2f"|format(item.price_cents / 100) }}€</p>
                        </div>
                        <div class="text-right">
                            <span class="text-lg font-bold text-white">{{ item.qty }}</span>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            
            <!-- Boutons d'action du ticket -->
            <div class="flex gap-2">
                {% if ticket.status == 'pending' %}
                <button class="update-status-btn flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 px-3 rounded-md text-sm font-medium"
                        data-ticket-id="{{ ticket.id }}" 
                        data-status="preparing">
                    Commencer
                </button>
                {% elif ticket.status == 'preparing' %}
                <button class="update-status-btn flex-1 bg-green-600 hover:bg-green-700 text-white py-2 px-3 rounded-md text-sm font-medium"
                        data-ticket-id="{{ ticket.id }}" 
                        data-status="ready">
                    Terminer
                </button>
                {% elif ticket.status == 'ready' %}
                <button class="update-status-btn flex-1 bg-gray-600 hover:bg-gray-700 text-white py-2 px-3 rounded-md text-sm font-medium"
                        data-ticket-id="{{ ticket.id }}" 
                        data-status="completed">
                    Servir
                </button>
                {% endif %}
                
                <!-- Temps écoulé -->
                <div class="text-xs text-slate-400 self-center">
                    <span class="timer" data-created="{{ ticket.created_at.isoformat() }}">--:--</span>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    
    <!-- Message si aucun ticket -->
    {% if not tickets %}
    <div class="text-center py-12">
        <div class="bg-slate-800 rounded-lg p-8 max-w-md mx-auto">
            <div class="text-slate-400 mb-4">
                <svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                </svg>
            </div>
            <h3 class="text-xl font-semibold text-white mb-2">Aucune commande en attente</h3>
            <p class="text-slate-400">Les nouvelles commandes apparaîtront ici automatiquement</p>
        </div>
    </div>
    {% endif %}
</div>

<!-- Styles CSS spécifiques au KDS -->
<style>
.kds-ticket {
    transition: all 0.3s ease;
    animation: slideIn 0.5s ease-out;
}

.kds-ticket:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.kds-ticket.updating {
    opacity: 0.7;
    transform: scale(0.98);
}

.timer {
    font-family: 'Courier New', monospace;
    font-weight: bold;
}

.timer.urgent {
    color: #ef4444;
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Responsive design */
@media (max-width: 768px) {
    .kds-ticket {
        margin-bottom: 1rem;
    }
    
    #tickets-container {
        grid-template-columns: 1fr;
    }
}
</style>

<!-- Script pour initialiser les timers et WebSocket -->
<script>
// Initialiser les timers
function updateTimers() {
    document.querySelectorAll('.timer').forEach(timer => {
        const createdAt = new Date(timer.dataset.created);
        const now = new Date();
        const diff = Math.floor((now - createdAt) / 1000); // en secondes
        
        const minutes = Math.floor(diff / 60);
        const seconds = diff % 60;
        
        timer.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        
        // Marquer comme urgent après 10 minutes
        if (minutes >= 10) {
            timer.classList.add('urgent');
        }
    });
}

// Mettre à jour les timers toutes les secondes
setInterval(updateTimers, 1000);
updateTimers(); // Initialisation immédiate

// Refresh automatique des filtres
document.querySelectorAll('#kds-filters select').forEach(select => {
    select.addEventListener('change', function() {
        document.getElementById('kds-filters').submit();
    });
});
</script>
{% endblock %}


