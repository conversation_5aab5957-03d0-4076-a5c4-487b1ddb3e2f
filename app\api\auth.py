import logging
from flask import Flask
from flask_jwt_extended import <PERSON><PERSON><PERSON>anager
from flask_restx import Api
import redis

from app.extensions import db
from app.modules.accounts.models import User
from .utils import APIRateLimiter


def init_api(app: Flask):
    """Initialise l'API avec toutes ses dépendances"""
    
    # Initialiser JWT
    jwt = JWTManager(app)
    
    # Initialiser Redis pour le rate limiting
    try:
        redis_client = redis.Redis(
            host=app.config.get('REDIS_HOST', 'localhost'),
            port=app.config.get('REDIS_PORT', 6379),
            db=app.config.get('REDIS_DB', 0)
        )
        redis_client.ping()  # Tester la connexion
        app.rate_limiter = APIRateLimiter(redis_client)
        app.logger.info("Redis connecté pour le rate limiting")
    except Exception as e:
        app.logger.warning(f"Redis non disponible pour le rate limiting: {e}")
        app.rate_limiter = None
    
    # Configuration des callbacks JWT
    @jwt.user_identity_loader
    def user_identity_lookup(user):
        return user.id

    @jwt.user_lookup_loader
    def user_lookup_callback(_jwt_header, jwt_data):
        identity = jwt_data["sub"]
        return User.query.filter_by(id=identity).first()

    @jwt.token_in_blocklist_loader
    def check_if_token_revoked(jwt_header, jwt_payload: dict):
        # Implémenter la révocation de tokens si nécessaire
        return False

    @jwt.expired_token_loader
    def expired_token_callback(jwt_header, jwt_payload):
        from .utils import create_api_response
        return create_api_response(
            False,
            message="Token expiré",
            status_code=401
        ), 401

    @jwt.invalid_token_loader
    def invalid_token_callback(error):
        from .utils import create_api_response
        return create_api_response(
            False,
            message="Token invalide",
            status_code=401
        ), 401

    @jwt.unauthorized_loader
    def missing_token_callback(error):
        from .utils import create_api_response
        return create_api_response(
            False,
            message="Token d'authentification requis",
            status_code=401
        ), 401

    app.logger.info("API initialisée avec succès")