from flask import jsonify, request, current_app
from flask_jwt_extended import <PERSON><PERSON><PERSON><PERSON><PERSON>, jwt_required, create_access_token, get_jwt_identity, get_jwt
from functools import wraps
from datetime import datetime, timedelta
import redis
import json
from typing import Dict, Any, Optional

from app.extensions import db
from app.modules.accounts.models import User, Business, Tenant
from app.modules.settings.models import Setting


class APIRateLimiter:
    """Gestionnaire de rate limiting pour l'API"""
    
    def __init__(self, redis_client: redis.Redis):
        self.redis = redis_client
        self.default_limits = {
            'anonymous': {'requests': 100, 'window': 3600},  # 100 requêtes/heure
            'authenticated': {'requests': 1000, 'window': 3600},  # 1000 requêtes/heure
            'premium': {'requests': 10000, 'window': 3600}  # 10000 requêtes/heure
        }
    
    def is_rate_limited(self, key: str, user_type: str = 'anonymous') -> bool:
        """Vérifie si une clé est rate limited"""
        limit_config = self.default_limits.get(user_type, self.default_limits['anonymous'])
        limit = limit_config['requests']
        window = limit_config['window']
        
        # Incrémenter le compteur
        current = self.redis.get(key)
        if current is None:
            self.redis.setex(key, window, 1)
            return False
        
        current = int(current)
        if current >= limit:
            return True
        
        self.redis.incr(key)
        return False
    
    def get_rate_limit_info(self, key: str) -> Dict[str, Any]:
        """Récupère les informations de rate limiting"""
        ttl = self.redis.ttl(key)
        current = self.redis.get(key)
        
        return {
            'remaining': max(0, int(current or 0)),
            'reset_in': ttl if ttl > 0 else 0
        }


def create_api_response(success: bool, data: Any = None, message: str = None, 
                       errors: list = None, status_code: int = 200) -> tuple:
    """Crée une réponse API standardisée"""
    response = {
        'success': success,
        'timestamp': datetime.utcnow().isoformat(),
        'data': data,
        'message': message,
        'errors': errors or []
    }
    return jsonify(response), status_code


def require_permissions(*required_permissions: str):
    """Décorateur pour vérifier les permissions"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            current_user_id = get_jwt_identity()
            user = User.query.get(current_user_id)
            
            if not user:
                return create_api_response(
                    False, 
                    message="Utilisateur non trouvé", 
                    status_code=404
                )
            
            # Vérifier les permissions
            for permission in required_permissions:
                if not user.has_permission(permission):
                    return create_api_response(
                        False,
                        message=f"Permission '{permission}' requise",
                        status_code=403
                    )
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator


def rate_limit(limit_type: str = 'authenticated'):
    """Décorateur pour le rate limiting"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Obtenir l'IP du client
            client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR'))
            user_id = get_jwt_identity() if get_jwt_identity() else 'anonymous'
            key = f"rate_limit:{limit_type}:{user_id}:{client_ip}:{request.endpoint}"
            
            # Vérifier le rate limiting
            if hasattr(current_app, 'rate_limiter'):
                if current_app.rate_limiter.is_rate_limited(key, limit_type):
                    return create_api_response(
                        False,
                        message="Trop de requêtes. Veuillez réessayer plus tard.",
                        status_code=429
                    )
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator