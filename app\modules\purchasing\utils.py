from __future__ import annotations

from typing import List, Dict, Any, Optional
from datetime import datetime, date, timedelta
from decimal import Decimal
from sqlalchemy import func, desc
from app.extensions import db
from app.modules.suppliers.models import Supplier, SupplierProduct
from app.modules.catalog.models import Product
from .models import (
    PurchaseOrder, PurchaseOrderItem, PurchaseReceipt, PurchaseReceiptItem,
    PurchaseOrderStatus, ReceiptStatus
)


class PurchaseAnalytics:
    \"\"\"Classe pour les analyses et statistiques d'achats\"\"\"
    
    def __init__(self, business_id: int):
        self.business_id = business_id
    
    def get_spending_by_supplier(self, period_days: int = 30) -> List[Dict[str, Any]]:
        \"\"\"Analyse des dépenses par fournisseur\"\"\"
        start_date = date.today() - timedelta(days=period_days)
        
        spending = db.session.query(
            Supplier.name,
            func.sum(PurchaseOrder.total_cents).label('total_cents'),
            func.count(PurchaseOrder.id).label('order_count'),
            func.avg(PurchaseOrder.total_cents).label('avg_order_cents')
        ).join(
            PurchaseOrder, Supplier.id == PurchaseOrder.supplier_id_fk
        ).filter(
            PurchaseOrder.business_id_fk == self.business_id,
            PurchaseOrder.order_date >= start_date,
            PurchaseOrder.status != PurchaseOrderStatus.CANCELLED
        ).group_by(Supplier.name).order_by(desc('total_cents')).all()
        
        return [{
            'supplier_name': row[0],
            'total_spent': row[1] / 100 if row[1] else 0,
            'order_count': row[2],
            'avg_order_value': row[3] / 100 if row[3] else 0
        } for row in spending]
    
    def get_monthly_spending_trend(self, months: int = 12) -> List[Dict[str, Any]]:
        \"\"\"Tendance des dépenses mensuelles\"\"\"
        start_date = date.today().replace(day=1) - timedelta(days=months * 31)
        
        monthly_data = db.session.query(
            func.date_trunc('month', PurchaseOrder.order_date).label('month'),
            func.sum(PurchaseOrder.total_cents).label('total_cents'),
            func.count(PurchaseOrder.id).label('order_count')
        ).filter(
            PurchaseOrder.business_id_fk == self.business_id,
            PurchaseOrder.order_date >= start_date,
            PurchaseOrder.status != PurchaseOrderStatus.CANCELLED
        ).group_by('month').order_by('month').all()
        
        return [{
            'month': row[0].strftime('%Y-%m') if row[0] else 'N/A',
            'total_spent': row[1] / 100 if row[1] else 0,
            'order_count': row[2]
        } for row in monthly_data]
    
    def get_delivery_performance(self) -> Dict[str, Any]:
        \"\"\"Performance de livraison des fournisseurs\"\"\"
        # Commandes livrées dans les temps
        on_time_orders = PurchaseOrder.query.filter(
            PurchaseOrder.business_id_fk == self.business_id,
            PurchaseOrder.status == PurchaseOrderStatus.RECEIVED,
            PurchaseOrder.delivery_date <= PurchaseOrder.expected_delivery_date
        ).count()
        
        # Total des commandes livrées
        total_delivered = PurchaseOrder.query.filter(
            PurchaseOrder.business_id_fk == self.business_id,
            PurchaseOrder.status == PurchaseOrderStatus.RECEIVED
        ).count()
        
        on_time_rate = (on_time_orders / total_delivered * 100) if total_delivered > 0 else 0
        
        # Performance par fournisseur
        supplier_performance = db.session.query(
            Supplier.name,
            func.count(PurchaseOrder.id).label('total_orders'),
            func.sum(
                func.case(
                    [(PurchaseOrder.delivery_date <= PurchaseOrder.expected_delivery_date, 1)],
                    else_=0
                )
            ).label('on_time_orders')
        ).join(
            PurchaseOrder, Supplier.id == PurchaseOrder.supplier_id_fk
        ).filter(
            PurchaseOrder.business_id_fk == self.business_id,
            PurchaseOrder.status == PurchaseOrderStatus.RECEIVED
        ).group_by(Supplier.name).all()
        
        supplier_rates = []
        for row in supplier_performance:
            on_time_rate_supplier = (row[2] / row[1] * 100) if row[1] > 0 else 0
            supplier_rates.append({
                'supplier_name': row[0],
                'total_orders': row[1],
                'on_time_orders': row[2],
                'on_time_rate': on_time_rate_supplier
            })
        
        return {
            'overall_on_time_rate': on_time_rate,
            'total_delivered_orders': total_delivered,
            'supplier_performance': supplier_rates
        }
    
    def get_cost_savings_opportunities(self) -> List[Dict[str, Any]]:
        \"\"\"Opportunités d'économies basées sur l'analyse des achats\"\"\"
        opportunities = []
        
        # Analyser les produits achetés auprès de plusieurs fournisseurs
        multi_supplier_products = db.session.query(
            PurchaseOrderItem.product_name,
            func.count(func.distinct(PurchaseOrder.supplier_id_fk)).label('supplier_count'),
            func.min(PurchaseOrderItem.unit_cost_cents).label('min_cost'),
            func.max(PurchaseOrderItem.unit_cost_cents).label('max_cost'),
            func.avg(PurchaseOrderItem.unit_cost_cents).label('avg_cost')
        ).join(
            PurchaseOrder, PurchaseOrderItem.purchase_order_id_fk == PurchaseOrder.id
        ).filter(
            PurchaseOrder.business_id_fk == self.business_id,
            PurchaseOrder.status != PurchaseOrderStatus.CANCELLED
        ).group_by(
            PurchaseOrderItem.product_name
        ).having(
            func.count(func.distinct(PurchaseOrder.supplier_id_fk)) > 1
        ).all()
        
        for row in multi_supplier_products:
            if row[3] and row[2] and row[3] > row[2]:  # max_cost > min_cost
                potential_savings = (row[3] - row[2]) / 100
                opportunities.append({
                    'type': 'price_optimization',
                    'product_name': row[0],
                    'supplier_count': row[1],
                    'min_price': row[2] / 100 if row[2] else 0,
                    'max_price': row[3] / 100 if row[3] else 0,
                    'potential_savings_per_unit': potential_savings,
                    'recommendation': f\"Négocier ou changer de fournisseur pour {row[0]}\"
                })
        
        return opportunities[:10]  # Top 10 opportunités


class PurchaseOptimizer:
    \"\"\"Optimisation des achats et suggestions\"\"\"
    
    def __init__(self, business_id: int):
        self.business_id = business_id
    
    def suggest_reorder_points(self) -> List[Dict[str, Any]]:
        \"\"\"Suggère des points de récommande basés sur l'historique\"\"\"
        # Analyser la consommation moyenne par produit
        consumption_data = db.session.query(
            PurchaseOrderItem.product_name,
            func.sum(PurchaseOrderItem.quantity).label('total_ordered'),
            func.count(func.distinct(PurchaseOrder.order_date)).label('order_frequency'),
            func.avg(PurchaseOrderItem.quantity).label('avg_quantity')
        ).join(
            PurchaseOrder, PurchaseOrderItem.purchase_order_id_fk == PurchaseOrder.id
        ).filter(
            PurchaseOrder.business_id_fk == self.business_id,
            PurchaseOrder.order_date >= date.today() - timedelta(days=90),
            PurchaseOrder.status != PurchaseOrderStatus.CANCELLED
        ).group_by(
            PurchaseOrderItem.product_name
        ).all()
        
        suggestions = []
        for row in consumption_data:
            if row[2] > 0:  # order_frequency > 0
                avg_days_between_orders = 90 / row[2]
                suggested_reorder_point = row[3] * 1.5  # 150% de la quantité moyenne
                
                suggestions.append({
                    'product_name': row[0],
                    'total_ordered_90d': row[1],
                    'order_frequency': row[2],
                    'avg_days_between_orders': avg_days_between_orders,
                    'suggested_reorder_point': suggested_reorder_point,
                    'suggested_order_quantity': row[3]
                })
        
        return sorted(suggestions, key=lambda x: x['total_ordered_90d'], reverse=True)[:20]
    
    def optimize_supplier_selection(self, product_name: str) -> List[Dict[str, Any]]:
        \"\"\"Optimise la sélection de fournisseur pour un produit\"\"\"
        # Analyser les performances par fournisseur pour ce produit
        supplier_stats = db.session.query(
            Supplier.id,
            Supplier.name,
            func.avg(PurchaseOrderItem.unit_cost_cents).label('avg_cost'),
            func.count(PurchaseOrder.id).label('order_count'),
            func.avg(
                func.extract('days', 
                    PurchaseOrder.delivery_date - PurchaseOrder.order_date
                )
            ).label('avg_delivery_days')
        ).join(
            PurchaseOrder, Supplier.id == PurchaseOrder.supplier_id_fk
        ).join(
            PurchaseOrderItem, PurchaseOrder.id == PurchaseOrderItem.purchase_order_id_fk
        ).filter(
            PurchaseOrder.business_id_fk == self.business_id,
            PurchaseOrderItem.product_name.ilike(f\"%{product_name}%\"),
            PurchaseOrder.status == PurchaseOrderStatus.RECEIVED
        ).group_by(
            Supplier.id, Supplier.name
        ).all()
        
        recommendations = []
        for row in supplier_stats:
            score = 0
            
            # Score basé sur le prix (plus bas = meilleur)
            if row[2]:  # avg_cost
                min_cost = min(s[2] for s in supplier_stats if s[2])
                price_score = (min_cost / row[2]) * 40  # 40% du score
                score += price_score
            
            # Score basé sur la fiabilité (plus de commandes = meilleur)
            if row[3]:  # order_count
                max_orders = max(s[3] for s in supplier_stats if s[3])
                reliability_score = (row[3] / max_orders) * 30  # 30% du score
                score += reliability_score
            
            # Score basé sur la rapidité de livraison (plus rapide = meilleur)
            if row[4]:  # avg_delivery_days
                min_delivery = min(s[4] for s in supplier_stats if s[4])
                delivery_score = (min_delivery / row[4]) * 30  # 30% du score
                score += delivery_score
            
            recommendations.append({
                'supplier_id': row[0],
                'supplier_name': row[1],
                'avg_cost': row[2] / 100 if row[2] else 0,
                'order_count': row[3],
                'avg_delivery_days': row[4],
                'score': round(score, 2),
                'recommendation': 'Recommandé' if score >= 70 else 'Acceptable' if score >= 50 else 'À éviter'
            })
        
        return sorted(recommendations, key=lambda x: x['score'], reverse=True)


class PurchaseCostCalculator:
    \"\"\"Calculateur de coûts d'achat avancé\"\"\"
    
    @staticmethod
    def calculate_total_cost_of_ownership(purchase_order: PurchaseOrder) -> Dict[str, Any]:
        \"\"\"Calcule le coût total de possession (TCO)\"\"\"
        base_cost = purchase_order.total_cents
        
        # Coûts additionnels estimés
        shipping_cost = purchase_order.shipping_cents or 0
        handling_cost = base_cost * 0.02  # 2% du coût de base
        storage_cost = base_cost * 0.01  # 1% du coût de base pour stockage
        
        # Coût d'opportunité (délai de livraison)
        delivery_days = 7  # Valeur par défaut
        if purchase_order.expected_delivery_date and purchase_order.order_date:
            delivery_days = (purchase_order.expected_delivery_date - purchase_order.order_date).days
        
        opportunity_cost = base_cost * 0.001 * delivery_days  # 0.1% par jour de délai
        
        total_tco = base_cost + shipping_cost + handling_cost + storage_cost + opportunity_cost
        
        return {
            'base_cost': base_cost / 100,
            'shipping_cost': shipping_cost / 100,
            'handling_cost': handling_cost / 100,
            'storage_cost': storage_cost / 100,
            'opportunity_cost': opportunity_cost / 100,
            'total_tco': total_tco / 100,
            'tco_markup_percentage': ((total_tco - base_cost) / base_cost * 100) if base_cost > 0 else 0
        }
    
    @staticmethod
    def calculate_volume_discounts(supplier_id: int, total_quantity: int) -> Dict[str, Any]:
        \"\"\"Calcule les remises sur volume possibles\"\"\"
        # Simuler des paliers de remise (à adapter selon les accords fournisseurs)
        discount_tiers = [
            {'min_qty': 100, 'discount_rate': 0.05},  # 5% à partir de 100 unités
            {'min_qty': 500, 'discount_rate': 0.10},  # 10% à partir de 500 unités
            {'min_qty': 1000, 'discount_rate': 0.15}, # 15% à partir de 1000 unités
        ]
        
        applicable_discount = 0
        for tier in reversed(discount_tiers):
            if total_quantity >= tier['min_qty']:
                applicable_discount = tier['discount_rate']
                break
        
        return {
            'total_quantity': total_quantity,
            'applicable_discount_rate': applicable_discount,
            'next_tier_quantity': next((tier['min_qty'] for tier in discount_tiers if tier['min_qty'] > total_quantity), None),
            'potential_additional_discount': next((tier['discount_rate'] for tier in discount_tiers if tier['min_qty'] > total_quantity), None)
        }


class SupplierPerformanceTracker:
    \"\"\"Suivi des performances fournisseurs\"\"\"
    
    def __init__(self, business_id: int):
        self.business_id = business_id
    
    def calculate_supplier_score(self, supplier_id: int) -> Dict[str, Any]:
        \"\"\"Calcule un score de performance globale pour un fournisseur\"\"\"
        supplier = Supplier.query.get(supplier_id)
        if not supplier:
            return {'error': 'Fournisseur non trouvé'}
        
        # Récupérer les commandes des 12 derniers mois
        start_date = date.today() - timedelta(days=365)
        orders = PurchaseOrder.query.filter(
            PurchaseOrder.supplier_id_fk == supplier_id,
            PurchaseOrder.business_id_fk == self.business_id,
            PurchaseOrder.order_date >= start_date
        ).all()
        
        if not orders:
            return {
                'supplier_name': supplier.name,
                'score': 0,
                'message': 'Aucune commande dans les 12 derniers mois'
            }
        
        # Calculer différents indicateurs
        total_orders = len(orders)
        completed_orders = len([o for o in orders if o.status == PurchaseOrderStatus.RECEIVED])
        on_time_orders = len([o for o in orders if o.delivery_date and o.expected_delivery_date and o.delivery_date <= o.expected_delivery_date])
        
        # Scores sur 100
        completion_rate = (completed_orders / total_orders * 100) if total_orders > 0 else 0
        on_time_rate = (on_time_orders / completed_orders * 100) if completed_orders > 0 else 0
        
        # Score qualité basé sur les réceptions
        quality_scores = []
        for order in orders:
            for receipt in order.receipts:
                if receipt.quality_rating:
                    quality_scores.append(receipt.quality_rating)
        
        avg_quality = (sum(quality_scores) / len(quality_scores) * 20) if quality_scores else 50  # /5 * 20 = /100
        
        # Score global pondéré
        global_score = (
            completion_rate * 0.3 +  # 30% completion
            on_time_rate * 0.4 +     # 40% ponctualité
            avg_quality * 0.3        # 30% qualité
        )
        
        return {
            'supplier_name': supplier.name,
            'total_orders': total_orders,
            'completion_rate': round(completion_rate, 1),
            'on_time_rate': round(on_time_rate, 1),
            'avg_quality_score': round(avg_quality, 1),
            'global_score': round(global_score, 1),
            'rating': (
                'Excellent' if global_score >= 90 else
                'Très bon' if global_score >= 80 else
                'Bon' if global_score >= 70 else
                'Moyen' if global_score >= 60 else
                'À améliorer'
            )
        }
    
    def get_top_performers(self, limit: int = 10) -> List[Dict[str, Any]]:
        \"\"\"Retourne les meilleurs fournisseurs\"\"\"
        suppliers = Supplier.query.filter_by(
            business_id_fk=self.business_id,
            is_active=True
        ).all()
        
        performances = []
        for supplier in suppliers:
            score_data = self.calculate_supplier_score(supplier.id)
            if 'global_score' in score_data:
                performances.append(score_data)
        
        return sorted(performances, key=lambda x: x['global_score'], reverse=True)[:limit]


def generate_purchase_recommendations(business_id: int) -> Dict[str, Any]:
    \"\"\"Génère des recommandations d'achat globales\"\"\"
    analytics = PurchaseAnalytics(business_id)
    optimizer = PurchaseOptimizer(business_id)
    performance_tracker = SupplierPerformanceTracker(business_id)
    
    return {
        'cost_savings_opportunities': analytics.get_cost_savings_opportunities(),
        'reorder_suggestions': optimizer.suggest_reorder_points()[:5],
        'top_suppliers': performance_tracker.get_top_performers(5),
        'spending_analysis': analytics.get_spending_by_supplier(30),
        'delivery_performance': analytics.get_delivery_performance(),
        'generated_at': datetime.utcnow().isoformat()
    }