{% extends "base.html" %}

{% block title %}Prévision des Ventes{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-chart-line"></i> Prévision des Ventes</h1>
        <button id="trainModelBtn" class="btn btn-primary">
            <i class="fas fa-brain"></i> Entraîner le Modèle
        </button>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Graphique de Prévision</h5>
                </div>
                <div class="card-body">
                    <canvas id="forecastChart" height="100"></canvas>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Prédiction Instantanée</h5>
                </div>
                <div class="card-body">
                    <form id="predictionForm">
                        <div class="mb-3">
                            <label class="form-label">Jour de la semaine</label>
                            <select class="form-select" id="dayOfWeek">
                                <option value="0">Lundi</option>
                                <option value="1">Mardi</option>
                                <option value="2">Mercredi</option>
                                <option value="3">Jeudi</option>
                                <option value="4">Vendredi</option>
                                <option value="5">Samedi</option>
                                <option value="6">Dimanche</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Mois</label>
                            <select class="form-select" id="month">
                                <option value="1">Janvier</option>
                                <option value="2">Février</option>
                                <option value="3">Mars</option>
                                <option value="4">Avril</option>
                                <option value="5">Mai</option>
                                <option value="6">Juin</option>
                                <option value="7">Juillet</option>
                                <option value="8">Août</option>
                                <option value="9">Septembre</option>
                                <option value="10">Octobre</option>
                                <option value="11">Novembre</option>
                                <option value="12">Décembre</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Heure</label>
                            <input type="number" class="form-control" id="hour" min="0" max="23" value="12">
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="isWeekend">
                            <label class="form-check-label" for="isWeekend">Week-end</label>
                        </div>
                        
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-calculator"></i> Prédire
                        </button>
                    </form>
                    
                    <div id="predictionResult" class="mt-4 d-none">
                        <div class="alert alert-info">
                            <h6>Prédiction:</h6>
                            <h3 id="predictedAmount" class="mb-0">€0.00</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Métriques du Modèle</h5>
                </div>
                <div class="card-body">
                    <div class="row" id="modelMetrics">
                        <div class="col-md-4">
                            <div class="text-center">
                                <h3 id="mseValue">-</h3>
                                <p class="text-muted">Erreur Quadratique Moyenne</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <h3 id="r2Value">-</h3>
                                <p class="text-muted">Coefficient de Détermination (R²)</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <h3 id="samplesValue">-</h3>
                                <p class="text-muted">Échantillons d'Entraînement</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Variables globales
    let forecastChart = null;
    
    // Initialisation du graphique
    function initChart() {
        const ctx = document.getElementById('forecastChart').getContext('2d');
        forecastChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'],
                datasets: [{
                    label: 'Ventes Réelles',
                    data: [0, 0, 0, 0, 0, 0, 0],
                    borderColor: 'rgb(54, 162, 235)',
                    backgroundColor: 'rgba(54, 162, 235, 0.2)',
                    tension: 0.1
                }, {
                    label: 'Ventes Prédictes',
                    data: [0, 0, 0, 0, 0, 0, 0],
                    borderColor: 'rgb(255, 99, 132)',
                    backgroundColor: 'rgba(255, 99, 132, 0.2)',
                    borderDash: [5, 5],
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '€' + value;
                            }
                        }
                    }
                }
            }
        });
    }
    
    // Entraîner le modèle
    document.getElementById('trainModelBtn').addEventListener('click', function() {
        fetch('/ai/api/sales_forecast/train', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Modèle entraîné avec succès!');
                
                // Mettre à jour les métriques
                document.getElementById('mseValue').textContent = data.metrics.mse.toFixed(2);
                document.getElementById('r2Value').textContent = data.metrics.r2.toFixed(2);
                document.getElementById('samplesValue').textContent = data.metrics.samples;
            } else {
                alert('Erreur: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur lors de l\'entraînement du modèle');
        });
    });
    
    // Faire une prédiction
    document.getElementById('predictionForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const features = {
            day_of_week: parseInt(document.getElementById('dayOfWeek').value),
            month: parseInt(document.getElementById('month').value),
            hour: parseInt(document.getElementById('hour').value),
            is_weekend: document.getElementById('isWeekend').checked ? 1 : 0
        };
        
        fetch('/ai/api/sales_forecast/predict', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(features)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('predictedAmount').textContent = '€' + data.prediction.toFixed(2);
                document.getElementById('predictionResult').classList.remove('d-none');
            } else {
                alert('Erreur: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur lors de la prédiction');
        });
    });
    
    // Initialiser le graphique quand la page est chargée
    document.addEventListener('DOMContentLoaded', function() {
        initChart();
    });
</script>
{% endblock %}