"""
Gestionnaires d'événements WebSocket pour le module KDS
"""

from flask import current_app, request, session
from flask_login import current_user
from flask_socketio import emit, join_room, leave_room
from datetime import datetime

from app.extensions import socketio
from app.modules.accounts.models import Business
from .models import KdsTicket, KdsScreen


@socketio.on('connect')
def handle_connect():
    """Gestion de la connexion WebSocket"""
    current_app.logger.info(f'Client WebSocket connecté: {request.sid}')
    
    if current_user.is_authenticated:
        # Joindre automatiquement la room de l'entreprise
        business_room = f'business_{current_user.business_id_fk}'
        join_room(business_room)
        current_app.logger.info(f'Utilisateur {current_user.id} rejoint la room {business_room}')
        
        emit('connected', {
            'message': 'Connexion établie',
            'user_id': current_user.id,
            'business_id': current_user.business_id_fk
        })


@socketio.on('disconnect')
def handle_disconnect():
    """Gestion de la déconnexion WebSocket"""
    current_app.logger.info(f'Client WebSocket déconnecté: {request.sid}')
    
    if current_user.is_authenticated:
        business_room = f'business_{current_user.business_id_fk}'
        leave_room(business_room)
        current_app.logger.info(f'Utilisateur {current_user.id} quitte la room {business_room}')


@socketio.on('join_business')
def handle_join_business(data=None):
    """Joindre la room de l'entreprise"""
    if not current_user.is_authenticated:
        emit('error', {'message': 'Non authentifié'})
        return
    
    business_room = f'business_{current_user.business_id_fk}'
    join_room(business_room)
    
    current_app.logger.info(f'Utilisateur {current_user.id} rejoint explicitement la room {business_room}')
    
    emit('joined_business', {
        'business_id': current_user.business_id_fk,
        'room': business_room
    })


@socketio.on('leave_business')
def handle_leave_business(data=None):
    """Quitter la room de l'entreprise"""
    if not current_user.is_authenticated:
        return
    
    business_room = f'business_{current_user.business_id_fk}'
    leave_room(business_room)
    
    current_app.logger.info(f'Utilisateur {current_user.id} quitte explicitement la room {business_room}')
    
    emit('left_business', {
        'business_id': current_user.business_id_fk,
        'room': business_room
    })


@socketio.on('kds_ping')
def handle_kds_ping(data=None):
    """Répondre au ping du KDS pour maintenir la connexion"""
    if current_user.is_authenticated:
        emit('kds_pong', {
            'timestamp': data.get('timestamp') if data else None,
            'server_time': str(datetime.utcnow())
        })


@socketio.on('request_tickets')
def handle_request_tickets(data=None):
    """Demander la liste des tickets actifs"""
    if not current_user.is_authenticated:
        emit('error', {'message': 'Non authentifié'})
        return
    
    try:
        from sqlalchemy.orm import joinedload
        from sqlalchemy import and_
        from app.modules.sales.models import Order, OrderItem
        from app.modules.catalog.models import Product
        
        # Récupérer les tickets actifs
        tickets = KdsTicket.query.join(
            Order, KdsTicket.order_id_fk == Order.id
        ).filter(
            and_(
                Order.business_id_fk == current_user.business_id_fk,
                KdsTicket.status.in_(['pending', 'preparing', 'ready'])
            )
        ).options(
            joinedload(KdsTicket.order)
        ).order_by(KdsTicket.created_at.asc()).all()
        
        tickets_data = []
        for ticket in tickets:
            order_items = OrderItem.query.filter_by(
                order_id_fk=ticket.order_id_fk
            ).options(joinedload(OrderItem.product)).all()
            
            tickets_data.append({
                'id': ticket.id,
                'order_id': ticket.order_id_fk,
                'status': ticket.status,
                'created_at': ticket.created_at.isoformat(),
                'items': [{
                    'id': item.id,
                    'product_name': item.product.name if item.product else 'Produit supprimé',
                    'quantity': item.qty,
                    'price': item.price_cents / 100
                } for item in order_items]
            })
        
        emit('tickets_data', {
            'tickets': tickets_data,
            'total': len(tickets_data)
        })
        
    except Exception as e:
        current_app.logger.error(f'Erreur lors de la récupération des tickets: {e}')
        emit('error', {'message': 'Erreur lors de la récupération des tickets'})


def notify_ticket_update(ticket_id, order_id, old_status, new_status, business_id):
    """
    Notifier tous les clients de la mise à jour d'un ticket
    Cette fonction est appelée depuis les routes
    """
    socketio.emit('ticket_status_updated', {
        'ticket_id': ticket_id,
        'order_id': order_id,
        'old_status': old_status,
        'new_status': new_status,
        'timestamp': str(datetime.utcnow())
    }, room=f'business_{business_id}')


def notify_new_order(order_id, business_id, items_count=0):
    """
    Notifier tous les clients d'une nouvelle commande
    Cette fonction est appelée depuis les routes de vente
    """
    socketio.emit('new_order', {
        'order_id': order_id,
        'business_id': business_id,
        'items_count': items_count,
        'timestamp': str(datetime.utcnow())
    }, room=f'business_{business_id}')


def notify_screen_update(screen_id, screen_name, action, business_id):
    """
    Notifier les clients de la mise à jour d'un écran
    """
    event_name = f'screen_{action}'  # screen_created, screen_updated, screen_deleted
    
    data = {
        'screen_id': screen_id,
        'business_id': business_id,
        'timestamp': str(datetime.utcnow())
    }
    
    if action != 'deleted':
        data['screen_name'] = screen_name
    
    socketio.emit(event_name, data, room=f'business_{business_id}')