from typing import Any, Dict, List, Optional, Union
from sqlalchemy import text
from sqlalchemy.orm import Query, Session
from sqlalchemy.sql import Select
import logging
from functools import wraps
import time


class QueryProfiler:
    """Profileur de requêtes SQL pour identifier les goulots d'étranglement"""
    
    def __init__(self, app=None):
        self.app = app
        self.queries = []
        self.enabled = False
        
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """Initialise le profileur avec l'application Flask"""
        self.enabled = app.config.get('SQL_PROFILING_ENABLED', False)
        
        if self.enabled:
            from flask import g
            from app.extensions import db
            
            @app.before_request
            def before_request():
                g.start_time = time.time()
                g.queries_count = len(db.engine.execute(text("SHOW STATUS LIKE 'Queries'")).fetchall())
            
            @app.after_request
            def after_request(response):
                if hasattr(g, 'start_time'):
                    elapsed = time.time() - g.start_time
                    app.logger.info(f"Request took {elapsed:.2f}s")
                return response
    
    def profile_query(self, func):
        """Décorateur pour profiler une fonction de requête"""
        @wraps(func)
        def wrapper(*args, **kwargs):
            if not self.enabled:
                return func(*args, **kwargs)
            
            start_time = time.time()
            result = func(*args, **kwargs)
            end_time = time.time()
            
            query_info = {
                'function': func.__name__,
                'execution_time': end_time - start_time,
                'timestamp': time.time()
            }
            
            self.queries.append(query_info)
            self.app.logger.info(f"Query {func.__name__} took {end_time - start_time:.4f}s")
            
            return result
        return wrapper
    
    def get_slow_queries(self, threshold: float = 1.0) -> List[Dict]:
        """Récupère les requêtes lentes"""
        return [q for q in self.queries if q['execution_time'] > threshold]
    
    def get_stats(self) -> Dict[str, Any]:
        """Récupère les statistiques de profiling"""
        if not self.queries:
            return {}
        
        total_time = sum(q['execution_time'] for q in self.queries)
        avg_time = total_time / len(self.queries)
        
        return {
            'total_queries': len(self.queries),
            'total_time': total_time,
            'average_time': avg_time,
            'slow_queries': len(self.get_slow_queries())
        }


class QueryOptimizer:
    """Optimiseur de requêtes SQLAlchemy"""
    
    @staticmethod
    def optimize_joins(query: Query, *relationships) -> Query:
        """
        Optimise les jointures en utilisant des chargements spécifiques
        
        Args:
            query: Requête SQLAlchemy
            *relationships: Relations à charger de manière optimisée
        """
        for relationship in relationships:
            if isinstance(relationship, str):
                # Charger avec joinedload pour les relations simples
                from sqlalchemy.orm import joinedload
                query = query.options(joinedload(relationship))
            elif isinstance(relationship, tuple):
                # Charger avec selectinload pour les collections
                from sqlalchemy.orm import selectinload
                query = query.options(selectinload(*relationship))
        
        return query
    
    @staticmethod
    def add_filters_optimized(query: Query, **filters) -> Query:
        """
        Ajoute des filtres de manière optimisée
        
        Args:
            query: Requête SQLAlchemy
            **filters: Filtres à appliquer
        """
        for field, value in filters.items():
            if value is not None:
                # Utiliser des filtres indexés quand possible
                if isinstance(value, (list, tuple)):
                    query = query.filter(getattr(query.column_descriptions[0]['type'], field).in_(value))
                else:
                    query = query.filter(getattr(query.column_descriptions[0]['type'], field) == value)
        
        return query
    
    @staticmethod
    def select_columns_optimized(query: Query, *columns) -> Query:
        """
        Sélectionne uniquement les colonnes nécessaires
        
        Args:
            query: Requête SQLAlchemy
            *columns: Colonnes à sélectionner
        """
        if columns:
            return query.with_entities(*columns)
        return query
    
    @staticmethod
    def add_index_hints(query: Query, model, *index_names) -> Query:
        """
        Ajoute des hints d'index (support limité selon le dialecte)
        
        Args:
            query: Requête SQLAlchemy
            model: Modèle concerné
            *index_names: Noms des index à utiliser
        """
        # Cette implémentation est basique et dépend du dialecte
        # Pour PostgreSQL/MySQL, on pourrait utiliser des hints spécifiques
        return query
    
    @staticmethod
    def batch_load(query: Query, batch_size: int = 1000) -> List[Any]:
        """
        Charge les résultats par lots pour éviter les problèmes de mémoire
        
        Args:
            query: Requête SQLAlchemy
            batch_size: Taille des lots
        """
        results = []
        offset = 0
        
        while True:
            batch = query.offset(offset).limit(batch_size).all()
            if not batch:
                break
            
            results.extend(batch)
            offset += batch_size
            
            # Libérer la mémoire entre les lots
            import gc
            gc.collect()
        
        return results


class DatabaseIndexManager:
    """Gestionnaire d'index de base de données"""
    
    @staticmethod
    def get_missing_indexes(session: Session, table_name: str) -> List[Dict]:
        """
        Identifie les index manquants (implémentation basique)
        
        Args:
            session: Session SQLAlchemy
            table_name: Nom de la table
        """
        # Cette implémentation est très basique et dépend du SGBD
        # Pour MySQL:
        try:
            result = session.execute(text("""
                SELECT column_name, count(*) as freq
                FROM information_schema.statistics 
                WHERE table_name = :table_name
                GROUP BY column_name
                ORDER BY freq DESC
            """), {'table_name': table_name})
            
            return [{'column': row[0], 'frequency': row[1]} for row in result]
        except Exception as e:
            logging.warning(f"Impossible de récupérer les statistiques d'index: {e}")
            return []
    
    @staticmethod
    def suggest_indexes(model) -> List[str]:
        """
        Suggère des index basés sur le modèle
        
        Args:
            model: Classe de modèle SQLAlchemy
        """
        suggestions = []
        
        # Suggérer des index sur les clés étrangères
        for column in model.__table__.columns:
            if column.foreign_keys:
                suggestions.append(f"CREATE INDEX idx_{model.__tablename__}_{column.name} ON {model.__tablename__}({column.name})")
        
        # Suggérer des index sur les colonnes fréquemment utilisées dans les filtres
        indexed_columns = ['id', 'created_at', 'updated_at', 'business_id_fk', 'user_id_fk', 'status']
        for column_name in indexed_columns:
            if hasattr(model, column_name):
                column = getattr(model, column_name)
                if hasattr(column, 'property') and hasattr(column.property, 'columns'):
                    suggestions.append(f"CREATE INDEX idx_{model.__tablename__}_{column_name} ON {model.__tablename__}({column_name})")
        
        return suggestions


# Instance globale du profileur
query_profiler = QueryProfiler()


def init_query_profiler(app):
    """Initialise le profileur de requêtes"""
    query_profiler.init_app(app)