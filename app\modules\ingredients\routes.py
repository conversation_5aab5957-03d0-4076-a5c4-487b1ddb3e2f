from flask import render_template, request, redirect, url_for, flash, jsonify, current_app
from flask_login import login_required, current_user
from . import bp
from .forms import (
    IngredientCategoryForm, IngredientForm, RecipeForm, RecipeItemForm,
    RecipeSearchForm, IngredientSearchForm
)
from .models import IngredientCategory, Ingredient, Recipe, RecipeItem
from app.modules.catalog.models import Product
from app.extensions import db
from sqlalchemy import or_, and_, func
import logging

# Configuration du logger
logger = logging.getLogger(__name__)

@bp.get("/")
@login_required
def index():
    """Page principale du module ingredients"""
    # Statistiques rapides
    total_ingredients = Ingredient.query.filter_by(
        business_id_fk=current_user.business_id_fk
    ).count()
    
    total_recipes = Recipe.query.filter_by(
        business_id_fk=current_user.business_id_fk
    ).count()
    
    low_stock_ingredients = Ingredient.query.filter(
        and_(
            Ingredient.business_id_fk == current_user.business_id_fk,
            Ingredient.current_stock <= Ingredient.min_stock_level
        )
    ).count()
    
    # Calcul de la valeur totale du stock
    total_stock_value = db.session.query(func.sum(Ingredient.current_stock * Ingredient.cost_per_unit_cents)).filter(
        Ingredient.business_id_fk == current_user.business_id_fk
    ).scalar() or 0
    
    # Données pour les graphiques
    # Graphique des catégories
    category_stats = db.session.query(
        IngredientCategory.name,
        func.count(Ingredient.id)
    ).outerjoin(Ingredient).filter(
        IngredientCategory.business_id_fk == current_user.business_id_fk
    ).group_by(IngredientCategory.id, IngredientCategory.name).all()
    
    category_labels = [stat[0] for stat in category_stats]
    category_data = [stat[1] for stat in category_stats]
    
    # Comptage des ingrédients actifs
    active_ingredients_count = Ingredient.query.filter_by(
        business_id_fk=current_user.business_id_fk,
        is_active=True
    ).count()

    # Comptage des recettes actives
    active_recipes_count = Recipe.query.filter_by(
        business_id_fk=current_user.business_id_fk,
        is_active=True
    ).count()

    # Graphique des coûts (simplifié - valeurs moyennes par mois)
    # Pour une implémentation plus complète, il faudrait des données historiques
    cost_labels = ["Jan", "Fév", "Mar", "Avr", "Mai", "Jun", "Jul", "Aoû", "Sep", "Oct", "Nov", "Déc"]
    cost_data = [0] * 12  # Initialisation avec des zéros

    # Récupérer tous les ingrédients et recettes pour les passer au template
    ingredients = Ingredient.query.filter_by(
        business_id_fk=current_user.business_id_fk
    ).order_by(Ingredient.name).all()

    recipes = Recipe.query.filter_by(
        business_id_fk=current_user.business_id_fk
    ).order_by(Recipe.name).all()

    categories = IngredientCategory.query.filter_by(
        business_id_fk=current_user.business_id_fk
    ).order_by(IngredientCategory.name).all()

    return render_template("ingredients/index.html",
                         ingredients=ingredients,
                         recipes=recipes,
                         categories=categories,
                         total_ingredients=total_ingredients,
                         total_recipes=total_recipes,
                         low_stock_ingredients=low_stock_ingredients,
                         low_stock_count=low_stock_ingredients,
                         total_stock_value=total_stock_value,
                         active_ingredients_count=active_ingredients_count,
                         active_recipes_count=active_recipes_count,
                         category_labels=category_labels,
                         category_data=category_data,
                         cost_labels=cost_labels,
                         cost_data=cost_data)


# Routes pour les catégories d'ingrédients
@bp.get("/categories")
@login_required
def categories():
    """Liste des catégories d'ingrédients"""
    categories = IngredientCategory.query.filter_by(
        business_id_fk=current_user.business_id_fk
    ).order_by(IngredientCategory.name).all()
    
    return render_template("ingredients/categories.html", categories=categories)


@bp.get("/categories/new")
@login_required
def new_category():
    """Créer une nouvelle catégorie"""
    form = IngredientCategoryForm()
    return render_template("ingredients/category_form.html", form=form, title="Nouvelle catégorie")


@bp.post("/categories/new")
@login_required
def create_category():
    """Créer une nouvelle catégorie"""
    logger.info("Début de la création d'une catégorie")
    logger.info(f"Method: {request.method}")
    logger.info(f"Content-Type: {request.content_type}")
    logger.info(f"Form data: {request.form}")
    
    form = IngredientCategoryForm(request.form)
    logger.info(f"Form errors: {form.errors}")
    
    if form.validate_on_submit():
        logger.info("Formulaire validé avec succès")
        try:
            category = IngredientCategory(
                business_id_fk=current_user.business_id_fk,
                name=form.name.data,
                description=form.description.data
            )
            db.session.add(category)
            db.session.commit()
            flash('Catégorie créée avec succès!', 'success')
            logger.info("Catégorie créée avec succès")
            return redirect(url_for('ingredients.categories'))
        except Exception as e:
            logger.error(f"Erreur lors de la création de la catégorie: {e}")
            db.session.rollback()
            flash('Erreur lors de la création de la catégorie', 'error')
    else:
        # Log des erreurs de validation
        logger.error(f"Erreurs de validation du formulaire: {form.errors}")
        for field, errors in form.errors.items():
            for error in errors:
                flash(f'Erreur {field}: {error}', 'error')
                logger.error(f'Erreur {field}: {error}')
    
    return render_template("ingredients/category_form.html", form=form, title="Nouvelle catégorie")


@bp.post("/api/categories")
@login_required
def api_create_category():
    """API pour créer une nouvelle catégorie (retourne JSON)"""
    logger.info("Début de la création d'une catégorie via API")
    
    # Vérifier si c'est une requête AJAX
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        try:
            data = request.get_json() or request.form.to_dict()
            logger.info(f"Données reçues: {data}")
            
            # Créer le formulaire avec les données
            form = IngredientCategoryForm(data=data)
            
            if form.validate_on_submit():
                category = IngredientCategory(
                    business_id_fk=current_user.business_id_fk,
                    name=form.name.data,
                    description=form.description.data
                )
                db.session.add(category)
                db.session.commit()
                
                logger.info("Catégorie créée avec succès via API")
                return jsonify({
                    'success': True,
                    'message': 'Catégorie créée avec succès!',
                    'category': {
                        'id': category.id,
                        'name': category.name,
                        'description': category.description
                    }
                }), 201
            else:
                # Retourner les erreurs de validation
                logger.error(f"Erreurs de validation: {form.errors}")
                return jsonify({
                    'success': False,
                    'message': 'Erreur de validation',
                    'errors': form.errors
                }), 400
                
        except Exception as e:
            logger.error(f"Erreur lors de la création de la catégorie via API: {e}")
            db.session.rollback()
            return jsonify({
                'success': False,
                'message': f'Erreur lors de la création de la catégorie: {str(e)}'
            }), 500
    else:
        # Si ce n'est pas une requête AJAX, rediriger vers la méthode normale
        return create_category()


@bp.get("/categories/<int:id>/edit")
@login_required
def edit_category(id):
    """Modifier une catégorie"""
    category = IngredientCategory.query.filter_by(
        id=id, business_id_fk=current_user.business_id_fk
    ).first_or_404()
    
    form = IngredientCategoryForm(obj=category)
    return render_template("ingredients/category_form.html", form=form, category=category, title="Modifier la catégorie")


@bp.post("/categories/<int:id>/edit")
@login_required
def update_category(id):
    """Modifier une catégorie"""
    category = IngredientCategory.query.filter_by(
        id=id, business_id_fk=current_user.business_id_fk
    ).first_or_404()
    
    form = IngredientCategoryForm(obj=category)
    if form.validate_on_submit():
        category.name = form.name.data
        category.description = form.description.data
        db.session.commit()
        flash('Catégorie modifiée avec succès!', 'success')
        return redirect(url_for('ingredients.categories'))
    
    return render_template("ingredients/category_form.html", form=form, category=category, title="Modifier la catégorie")


@bp.post("/categories/<int:id>/delete")
@login_required
def delete_category(id):
    """Supprimer une catégorie"""
    category = IngredientCategory.query.filter_by(
        id=id, business_id_fk=current_user.business_id_fk
    ).first_or_404()
    
    # Vérifier qu'aucun ingrédient n'utilise cette catégorie
    if category.ingredients.count() > 0:
        flash('Impossible de supprimer cette catégorie car elle contient des ingrédients.', 'error')
        return redirect(url_for('ingredients.categories'))
    
    db.session.delete(category)
    db.session.commit()
    flash('Catégorie supprimée avec succès!', 'success')
    return redirect(url_for('ingredients.categories'))


# Routes pour les ingrédients
@bp.get("/ingredients")
@login_required
def ingredients():
    """Liste des ingrédients"""
    page = request.args.get('page', 1, type=int)
    per_page = 20
    
    # Filtres de recherche
    search_form = IngredientSearchForm()
    search_form.category_id.choices = [(0, 'Toutes les catégories')] + [
        (cat.id, cat.name) for cat in IngredientCategory.query.filter_by(
            business_id_fk=current_user.business_id_fk
        ).order_by(IngredientCategory.name).all()
    ]
    
    # Appliquer les filtres
    query = Ingredient.query.filter_by(business_id_fk=current_user.business_id_fk)
    
    if search_form.name.data:
        query = query.filter(Ingredient.name.ilike(f'%{search_form.name.data}%'))
    
    if search_form.category_id.data and search_form.category_id.data != 0:
        query = query.filter_by(category_id_fk=search_form.category_id.data)
    
    if search_form.unit.data:
        query = query.filter_by(unit=search_form.unit.data)
    
    if search_form.low_stock.data:
        query = query.filter(Ingredient.current_stock <= Ingredient.min_stock_level)
    
    ingredients = query.order_by(Ingredient.name).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return render_template("ingredients/ingredients.html", 
                         ingredients=ingredients,
                         search_form=search_form)


@bp.get("/ingredients/new")
@login_required
def new_ingredient():
    """Créer un nouvel ingrédient"""
    form = IngredientForm()
    form.category_id.choices = [(0, 'Aucune catégorie')] + [
        (cat.id, cat.name) for cat in IngredientCategory.query.filter_by(
            business_id_fk=current_user.business_id_fk
        ).order_by(IngredientCategory.name).all()
    ]
    
    return render_template("ingredients/ingredient_form.html", form=form, title="Nouvel ingrédient")


@bp.post("/ingredients/new")
@login_required
def create_ingredient():
    """Créer un nouvel ingrédient"""
    form = IngredientForm()
    form.category_id.choices = [(0, 'Aucune catégorie')] + [
        (cat.id, cat.name) for cat in IngredientCategory.query.filter_by(
            business_id_fk=current_user.business_id_fk
        ).order_by(IngredientCategory.name).all()
    ]
    
    if form.validate_on_submit():
        ingredient = Ingredient(
            business_id_fk=current_user.business_id_fk,
            name=form.name.data,
            sku=form.sku.data,
            category_id_fk=form.category_id.data if form.category_id.data != 0 else None,
            unit=form.unit.data,
            cost_per_unit_cents=form.cost_per_unit_cents.data,
            current_stock=form.current_stock.data,
            min_stock_level=form.min_stock_level.data,
            is_active=form.is_active.data
        )
        db.session.add(ingredient)
        db.session.commit()
        flash('Ingrédient créé avec succès!', 'success')
        return redirect(url_for('ingredients.ingredients'))
    
    return render_template("ingredients/ingredient_form.html", form=form, title="Nouvel ingrédient")


@bp.get("/ingredients/<int:id>/edit")
@login_required
def edit_ingredient(id):
    """Modifier un ingrédient"""
    ingredient = Ingredient.query.filter_by(
        id=id, business_id_fk=current_user.business_id_fk
    ).first_or_404()
    
    form = IngredientForm(obj=ingredient)
    form.category_id.choices = [(0, 'Aucune catégorie')] + [
        (cat.id, cat.name) for cat in IngredientCategory.query.filter_by(
            business_id_fk=current_user.business_id_fk
        ).order_by(IngredientCategory.name).all()
    ]
    
    return render_template("ingredients/ingredient_form.html", form=form, ingredient=ingredient, title="Modifier l'ingrédient")


@bp.post("/ingredients/<int:id>/edit")
@login_required
def update_ingredient(id):
    """Modifier un ingrédient"""
    ingredient = Ingredient.query.filter_by(
        id=id, business_id_fk=current_user.business_id_fk
    ).first_or_404()
    
    form = IngredientForm(obj=ingredient)
    form.category_id.choices = [(0, 'Aucune catégorie')] + [
        (cat.id, cat.name) for cat in IngredientCategory.query.filter_by(
            business_id_fk=current_user.business_id_fk
        ).order_by(IngredientCategory.name).all()
    ]
    
    if form.validate_on_submit():
        ingredient.name = form.name.data
        ingredient.sku = form.sku.data
        ingredient.category_id_fk = form.category_id.data if form.category_id.data != 0 else None
        ingredient.unit = form.unit.data
        ingredient.cost_per_unit_cents = form.cost_per_unit_cents.data
        ingredient.current_stock = form.current_stock.data
        ingredient.min_stock_level = form.min_stock_level.data
        ingredient.is_active = form.is_active.data
        
        db.session.commit()
        flash('Ingrédient modifié avec succès!', 'success')
        return redirect(url_for('ingredients.ingredients'))
    
    return render_template("ingredients/ingredient_form.html", form=form, ingredient=ingredient, title="Modifier l'ingrédient")


@bp.post("/ingredients/<int:id>/delete")
@login_required
def delete_ingredient(id):
    """Supprimer un ingrédient"""
    ingredient = Ingredient.query.filter_by(
        id=id, business_id_fk=current_user.business_id_fk
    ).first_or_404()
    
    # Vérifier qu'aucune recette n'utilise cet ingrédient
    if ingredient.recipe_items.count() > 0:
        flash('Impossible de supprimer cet ingrédient car il est utilisé dans des recettes.', 'error')
        return redirect(url_for('ingredients.ingredients'))
    
    db.session.delete(ingredient)
    db.session.commit()
    flash('Ingrédient supprimé avec succès!', 'success')
    return redirect(url_for('ingredients.ingredients'))


# Routes pour les recettes
@bp.get("/recipes")
@login_required
def recipes():
    """Liste des recettes"""
    page = request.args.get('page', 1, type=int)
    per_page = 20
    
    # Filtres de recherche
    search_form = RecipeSearchForm()
    search_form.category.choices = [(0, 'Toutes les catégories')] + [
        (cat.id, cat.name) for cat in IngredientCategory.query.filter_by(
            business_id_fk=current_user.business_id_fk
        ).order_by(IngredientCategory.name).all()
    ]
    
    # Appliquer les filtres
    query = Recipe.query.filter_by(business_id_fk=current_user.business_id_fk)
    
    if search_form.name.data:
        query = query.filter(Recipe.name.ilike(f'%{search_form.name.data}%'))
    
    if search_form.category.data and search_form.category.data != 0:
        # Filtrer par catégorie d'ingrédient (complexe, à implémenter)
        pass
    
    if search_form.max_preparation_time.data:
        query = query.filter(Recipe.preparation_time_minutes <= search_form.max_preparation_time.data)
    
    if search_form.max_cooking_time.data:
        query = query.filter(Recipe.cooking_time_minutes <= search_form.max_cooking_time.data)
    
    recipes = query.order_by(Recipe.name).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return render_template("ingredients/recipes.html", 
                         recipes=recipes,
                         search_form=search_form)


@bp.get("/recipes/new")
@login_required
def new_recipe():
    """Créer une nouvelle recette"""
    form = RecipeForm()
    return render_template("ingredients/recipe_form.html", form=form, title="Nouvelle recette")


@bp.post("/recipes/new")
@login_required
def create_recipe():
    """Créer une nouvelle recette"""
    form = RecipeForm()
    if form.validate_on_submit():
        recipe = Recipe(
            business_id_fk=current_user.business_id_fk,
            name=form.name.data,
            description=form.description.data,
            preparation_time_minutes=form.preparation_time_minutes.data or 0,
            cooking_time_minutes=form.cooking_time_minutes.data or 0,
            servings=form.servings.data,
            is_active=form.is_active.data
        )
        db.session.add(recipe)
        db.session.commit()
        flash('Recette créée avec succès!', 'success')
        return redirect(url_for('ingredients.recipes'))
    
    return render_template("ingredients/recipe_form.html", form=form, title="Nouvelle recette")


@bp.get("/recipes/<int:id>")
@login_required
def view_recipe(id):
    """Voir une recette"""
    recipe = Recipe.query.filter_by(
        id=id, business_id_fk=current_user.business_id_fk
    ).first_or_404()
    
    # Formulaire pour ajouter des ingrédients
    recipe_item_form = RecipeItemForm()
    recipe_item_form.ingredient_id.choices = [
        (ing.id, f"{ing.name} ({ing.unit})") for ing in Ingredient.query.filter_by(
            business_id_fk=current_user.business_id_fk,
            is_active=True
        ).order_by(Ingredient.name).all()
    ]
    
    return render_template("ingredients/recipe_detail.html", 
                         recipe=recipe,
                         recipe_item_form=recipe_item_form)


@bp.get("/recipes/<int:id>/edit")
@login_required
def edit_recipe(id):
    """Modifier une recette"""
    recipe = Recipe.query.filter_by(
        id=id, business_id_fk=current_user.business_id_fk
    ).first_or_404()
    
    form = RecipeForm(obj=recipe)
    return render_template("ingredients/recipe_form.html", form=form, recipe=recipe, title="Modifier la recette")


@bp.post("/recipes/<int:id>/edit")
@login_required
def update_recipe(id):
    """Modifier une recette"""
    recipe = Recipe.query.filter_by(
        id=id, business_id_fk=current_user.business_id_fk
    ).first_or_404()
    
    form = RecipeForm(obj=recipe)
    if form.validate_on_submit():
        recipe.name = form.name.data
        recipe.description = form.description.data
        recipe.preparation_time_minutes = form.preparation_time_minutes.data or 0
        recipe.cooking_time_minutes = form.cooking_time_minutes.data or 0
        recipe.servings = form.servings.data
        recipe.is_active = form.is_active.data
        
        db.session.commit()
        flash('Recette modifiée avec succès!', 'success')
        return redirect(url_for('ingredients.view_recipe', id=recipe.id))
    
    return render_template("ingredients/recipe_form.html", form=form, recipe=recipe, title="Modifier la recette")


@bp.post("/recipes/<int:id>/delete")
@login_required
def delete_recipe(id):
    """Supprimer une recette"""
    recipe = Recipe.query.filter_by(
        id=id, business_id_fk=current_user.business_id_fk
    ).first_or_404()
    
    db.session.delete(recipe)
    db.session.commit()
    flash('Recette supprimée avec succès!', 'success')
    return redirect(url_for('ingredients.recipes'))


# Routes pour les ingrédients de recette
@bp.post("/recipes/<int:recipe_id>/ingredients")
@login_required
def add_recipe_ingredient(recipe_id):
    """Ajouter un ingrédient à une recette"""
    recipe = Recipe.query.filter_by(
        id=recipe_id, business_id_fk=current_user.business_id_fk
    ).first_or_404()
    
    form = RecipeItemForm()
    form.ingredient_id.choices = [
        (ing.id, f"{ing.name} ({ing.unit})") for ing in Ingredient.query.filter_by(
            business_id_fk=current_user.business_id_fk,
            is_active=True
        ).order_by(Ingredient.name).all()
    ]
    
    if form.validate_on_submit():
        recipe_item = RecipeItem(
            recipe_id_fk=recipe.id,
            ingredient_id_fk=form.ingredient_id.data,
            quantity=form.quantity.data,
            unit=form.unit.data,
            notes=form.notes.data
        )
        db.session.add(recipe_item)
        db.session.commit()
        flash('Ingrédient ajouté à la recette!', 'success')
    else:
        for field, errors in form.errors.items():
            for error in errors:
                flash(f'Erreur dans le champ {getattr(form, field).label.text}: {error}', 'error')
    
    return redirect(url_for('ingredients.view_recipe', id=recipe.id))


@bp.post("/recipes/<int:recipe_id>/ingredients/<int:item_id>/delete")
@login_required
def delete_recipe_ingredient(recipe_id, item_id):
    """Supprimer un ingrédient d'une recette"""
    recipe_item = RecipeItem.query.filter_by(
        id=item_id, recipe_id_fk=recipe_id
    ).first_or_404()
    
    # Vérifier que la recette appartient à l'entreprise
    if recipe_item.recipe.business_id_fk != current_user.business_id_fk:
        flash('Accès non autorisé.', 'error')
        return redirect(url_for('ingredients.recipes'))
    
    db.session.delete(recipe_item)
    db.session.commit()
    flash('Ingrédient supprimé de la recette!', 'success')
    return redirect(url_for('ingredients.view_recipe', id=recipe_id))


# API routes
@bp.get("/api/costs-data")
@login_required
def api_costs_data():
    """API pour récupérer les données de coûts et stocks"""
    # Calcul de la valeur totale du stock
    total_stock_value = db.session.query(func.sum(Ingredient.current_stock * Ingredient.cost_per_unit_cents)).filter(
        Ingredient.business_id_fk == current_user.business_id_fk
    ).scalar() or 0
    
    # Comptage des ingrédients actifs
    active_ingredients_count = Ingredient.query.filter_by(
        business_id_fk=current_user.business_id_fk,
        is_active=True
    ).count()
    
    # Comptage des recettes actives
    active_recipes_count = Recipe.query.filter_by(
        business_id_fk=current_user.business_id_fk,
        is_active=True
    ).count()
    
    # Comptage des ingrédients avec stock faible
    low_stock_count = Ingredient.query.filter(
        and_(
            Ingredient.business_id_fk == current_user.business_id_fk,
            Ingredient.current_stock <= Ingredient.min_stock_level
        )
    ).count()
    
    # Données pour les graphiques
    # Graphique des catégories
    category_stats = db.session.query(
        IngredientCategory.name,
        func.count(Ingredient.id)
    ).outerjoin(Ingredient).filter(
        IngredientCategory.business_id_fk == current_user.business_id_fk
    ).group_by(IngredientCategory.id, IngredientCategory.name).all()
    
    category_labels = [stat[0] for stat in category_stats]
    category_data = [stat[1] for stat in category_stats]
    
    # Graphique des coûts (simplifié - valeurs moyennes par mois)
    # Pour une implémentation plus complète, il faudrait des données historiques
    cost_labels = ["Jan", "Fév", "Mar", "Avr", "Mai", "Jun", "Jul", "Aoû", "Sep", "Oct", "Nov", "Déc"]
    cost_data = [0] * 12  # Initialisation avec des zéros
    
    return jsonify({
        'total_stock_value': total_stock_value,
        'active_ingredients_count': active_ingredients_count,
        'active_recipes_count': active_recipes_count,
        'low_stock_count': low_stock_count,
        'category_labels': category_labels,
        'category_data': category_data,
        'cost_labels': cost_labels,
        'cost_data': cost_data
    })


@bp.get("/api/ingredients")
@login_required
def api_ingredients():
    """API pour récupérer la liste des ingrédients"""
    ingredients = Ingredient.query.filter_by(
        business_id_fk=current_user.business_id_fk,
        is_active=True
    ).order_by(Ingredient.name).all()
    
    return jsonify([{
        'id': ing.id,
        'name': ing.name,
        'sku': ing.sku,
        'unit': ing.unit,
        'current_stock': ing.current_stock,
        'min_stock_level': ing.min_stock_level
    } for ing in ingredients])


@bp.get("/api/recipes")
@login_required
def api_recipes():
    """API pour récupérer la liste des recettes"""
    recipes = Recipe.query.filter_by(
        business_id_fk=current_user.business_id_fk,
        is_active=True
    ).order_by(Recipe.name).all()
    
    return jsonify([{
        'id': recipe.id,
        'name': recipe.name,
        'preparation_time': recipe.preparation_time_minutes,
        'cooking_time': recipe.cooking_time_minutes,
        'servings': recipe.servings
    } for recipe in recipes])
