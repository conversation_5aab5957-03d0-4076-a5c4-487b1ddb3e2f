from datetime import datetime, date, timedelta
from sqlalchemy import func, text, desc, asc
from app.extensions import db
from .models import KPI, KPIHistory, AnalyticsCache
import json


class AnalyticsService:
    """Service principal pour les analyses et KPIs"""
    
    def __init__(self, business_id):
        self.business_id = business_id
    
    def calculate_all_kpis(self):
        """Calcule tous les KPIs actifs pour l'entreprise"""
        kpis = KPI.query.filter_by(
            business_id_fk=self.business_id, 
            is_active=True
        ).all()
        
        results = []
        for kpi in kpis:
            try:
                value = self.calculate_kpi(kpi)
                results.append({
                    'kpi_id': kpi.id,
                    'kpi_code': kpi.kpi_code,
                    'value': value,
                    'success': True
                })
            except Exception as e:
                results.append({
                    'kpi_id': kpi.id,
                    'kpi_code': kpi.kpi_code,
                    'error': str(e),
                    'success': False
                })
        
        return results
    
    def calculate_kpi(self, kpi):
        """Calcule la valeur d'un KPI spécifique"""
        if kpi.calculation_method == 'custom' and kpi.custom_query:
            return self._execute_custom_query(kpi.custom_query)
        elif kpi.calculation_method in ['sum', 'avg', 'count']:
            return self._execute_standard_calculation(kpi)
        else:
            raise ValueError(f"Méthode de calcul non supportée: {kpi.calculation_method}")
    
    def _execute_custom_query(self, query):
        """Exécute une requête SQL personnalisée"""
        try:
            # Remplacer les placeholders dans la requête
            query = query.replace('{business_id}', str(self.business_id))
            
            result = db.session.execute(text(query)).scalar()
            return float(result) if result is not None else 0.0
        except Exception as e:
            raise ValueError(f"Erreur dans la requête personnalisée: {str(e)}")
    
    def _execute_standard_calculation(self, kpi):
        """Exécute un calcul standard (sum, avg, count)"""
        if not kpi.source_table:
            raise ValueError("Table source manquante pour le calcul standard")
        
        # Construction de la requête de base
        if kpi.calculation_method == 'count':
            query = f"SELECT COUNT(*) FROM {kpi.source_table} WHERE business_id_fk = {self.business_id}"
        elif kpi.calculation_method == 'sum':
            if not kpi.source_column:
                raise ValueError("Colonne source manquante pour la somme")
            query = f"SELECT SUM({kpi.source_column}) FROM {kpi.source_table} WHERE business_id_fk = {self.business_id}"
        elif kpi.calculation_method == 'avg':
            if not kpi.source_column:
                raise ValueError("Colonne source manquante pour la moyenne")
            query = f"SELECT AVG({kpi.source_column}) FROM {kpi.source_table} WHERE business_id_fk = {self.business_id}"
        
        result = db.session.execute(text(query)).scalar()
        return float(result) if result is not None else 0.0
    
    def update_kpi_value(self, kpi_id, value, save_history=True):
        """Met à jour la valeur d'un KPI et sauvegarde l'historique"""
        kpi = KPI.query.filter_by(
            id=kpi_id,
            business_id_fk=self.business_id
        ).first()
        
        if not kpi:
            raise ValueError("KPI non trouvé")
        
        # Mettre à jour la valeur actuelle
        kpi.current_value = value
        kpi.last_calculated = datetime.utcnow()
        
        # Sauvegarder l'historique
        if save_history:
            history = KPIHistory(
                kpi_id_fk=kpi_id,
                value=value,
                calculation_date=date.today()
            )
            db.session.add(history)
        
        db.session.commit()
        return kpi
    
    def get_kpi_trend(self, kpi_id, days=30):
        """Récupère la tendance d'un KPI sur une période"""
        end_date = date.today()
        start_date = end_date - timedelta(days=days)
        
        history = KPIHistory.query.filter(
            KPIHistory.kpi_id_fk == kpi_id,
            KPIHistory.calculation_date >= start_date,
            KPIHistory.calculation_date <= end_date
        ).order_by(KPIHistory.calculation_date).all()
        
        return [{
            'date': h.calculation_date.isoformat(),
            'value': float(h.value)
        } for h in history]


class SalesAnalytics:
    """Service spécialisé pour les analyses de ventes"""
    
    def __init__(self, business_id):
        self.business_id = business_id
    
    def get_sales_summary(self, start_date=None, end_date=None):
        """Résumé des ventes sur une période"""
        cache_key = f"sales_summary_{start_date}_{end_date}"
        cached_data = AnalyticsCache.get_cached_data(self.business_id, cache_key)
        
        if cached_data:
            return cached_data
        
        # Si pas de dates spécifiées, prendre le mois en cours
        if not start_date:
            start_date = date.today().replace(day=1)
        if not end_date:
            end_date = date.today()
        
        # Calculer les métriques de vente
        query = """
            SELECT 
                COUNT(*) as total_transactions,
                SUM(total_amount_cents) as total_sales_cents,
                AVG(total_amount_cents) as avg_transaction_cents,
                COUNT(DISTINCT customer_id_fk) as unique_customers
            FROM transactions 
            WHERE business_id_fk = :business_id 
                AND DATE(created_at) BETWEEN :start_date AND :end_date
                AND status = 'completed'
        """
        
        result = db.session.execute(text(query), {
            'business_id': self.business_id,
            'start_date': start_date,
            'end_date': end_date
        }).fetchone()
        
        summary = {
            'period': {
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat()
            },
            'total_transactions': result.total_transactions or 0,
            'total_sales': (result.total_sales_cents or 0) / 100,
            'avg_transaction': (result.avg_transaction_cents or 0) / 100,
            'unique_customers': result.unique_customers or 0
        }
        
        # Mettre en cache pour 1 heure
        AnalyticsCache.set_cached_data(
            self.business_id, cache_key, 'sales_summary', summary, 1
        )
        
        return summary
    
    def get_top_products(self, limit=10, start_date=None, end_date=None):
        """Produits les plus vendus"""
        if not start_date:
            start_date = date.today() - timedelta(days=30)
        if not end_date:
            end_date = date.today()
        
        query = """
            SELECT 
                p.name,
                SUM(ti.quantity) as total_quantity,
                SUM(ti.quantity * ti.unit_price_cents) as total_revenue_cents
            FROM transaction_items ti
            JOIN transactions t ON ti.transaction_id_fk = t.id
            JOIN products p ON ti.product_id_fk = p.id
            WHERE t.business_id_fk = :business_id
                AND DATE(t.created_at) BETWEEN :start_date AND :end_date
                AND t.status = 'completed'
            GROUP BY p.id, p.name
            ORDER BY total_quantity DESC
            LIMIT :limit
        """
        
        results = db.session.execute(text(query), {
            'business_id': self.business_id,
            'start_date': start_date,
            'end_date': end_date,
            'limit': limit
        }).fetchall()
        
        return [{
            'product_name': r.name,
            'quantity_sold': r.total_quantity,
            'revenue': r.total_revenue_cents / 100
        } for r in results]
    
    def get_sales_by_period(self, period='daily', days=30):
        """Ventes par période (daily, weekly, monthly)"""
        end_date = date.today()
        start_date = end_date - timedelta(days=days)
        
        if period == 'daily':
            date_format = 'DATE(created_at)'
            group_format = '%Y-%m-%d'
        elif period == 'weekly':
            date_format = 'DATE_FORMAT(created_at, "%Y-%u")'
            group_format = '%Y-%u'
        else:  # monthly
            date_format = 'DATE_FORMAT(created_at, "%Y-%m")'
            group_format = '%Y-%m'
        
        query = f"""
            SELECT 
                {date_format} as period,
                COUNT(*) as transactions_count,
                SUM(total_amount_cents) as total_sales_cents
            FROM transactions
            WHERE business_id_fk = :business_id
                AND DATE(created_at) BETWEEN :start_date AND :end_date
                AND status = 'completed'
            GROUP BY {date_format}
            ORDER BY period
        """
        
        results = db.session.execute(text(query), {
            'business_id': self.business_id,
            'start_date': start_date,
            'end_date': end_date
        }).fetchall()
        
        return [{
            'period': str(r.period),
            'transactions_count': r.transactions_count,
            'total_sales': r.total_sales_cents / 100
        } for r in results]


class InventoryAnalytics:
    """Service spécialisé pour les analyses d'inventaire"""
    
    def __init__(self, business_id):
        self.business_id = business_id
    
    def get_inventory_summary(self):
        """Résumé de l'état du stock"""
        cache_key = "inventory_summary"
        cached_data = AnalyticsCache.get_cached_data(self.business_id, cache_key)
        
        if cached_data:
            return cached_data
        
        query = """
            SELECT 
                COUNT(*) as total_products,
                SUM(CASE WHEN current_stock <= reorder_level THEN 1 ELSE 0 END) as low_stock_products,
                SUM(CASE WHEN current_stock = 0 THEN 1 ELSE 0 END) as out_of_stock_products,
                SUM(current_stock * cost_price_cents) as total_inventory_value_cents
            FROM products
            WHERE business_id_fk = :business_id
                AND is_active = 1
        """
        
        result = db.session.execute(text(query), {
            'business_id': self.business_id
        }).fetchone()
        
        summary = {
            'total_products': result.total_products or 0,
            'low_stock_products': result.low_stock_products or 0,
            'out_of_stock_products': result.out_of_stock_products or 0,
            'total_inventory_value': (result.total_inventory_value_cents or 0) / 100
        }
        
        # Mettre en cache pour 2 heures
        AnalyticsCache.set_cached_data(
            self.business_id, cache_key, 'inventory_summary', summary, 2
        )
        
        return summary
    
    def get_slow_moving_products(self, days=90, limit=20):
        """Produits à rotation lente"""
        end_date = date.today()
        start_date = end_date - timedelta(days=days)
        
        query = """
            SELECT 
                p.name,
                p.current_stock,
                COALESCE(SUM(ti.quantity), 0) as quantity_sold,
                p.cost_price_cents / 100 as cost_price,
                (p.current_stock * p.cost_price_cents / 100) as stock_value
            FROM products p
            LEFT JOIN transaction_items ti ON p.id = ti.product_id_fk
            LEFT JOIN transactions t ON ti.transaction_id_fk = t.id 
                AND DATE(t.created_at) BETWEEN :start_date AND :end_date
                AND t.status = 'completed'
            WHERE p.business_id_fk = :business_id
                AND p.is_active = 1
                AND p.current_stock > 0
            GROUP BY p.id, p.name, p.current_stock, p.cost_price_cents
            HAVING quantity_sold < (p.current_stock * 0.1)
            ORDER BY stock_value DESC
            LIMIT :limit
        """
        
        results = db.session.execute(text(query), {
            'business_id': self.business_id,
            'start_date': start_date,
            'end_date': end_date,
            'limit': limit
        }).fetchall()
        
        return [{
            'product_name': r.name,
            'current_stock': r.current_stock,
            'quantity_sold': r.quantity_sold,
            'cost_price': r.cost_price,
            'stock_value': r.stock_value
        } for r in results]


class FinancialAnalytics:
    """Service spécialisé pour les analyses financières"""
    
    def __init__(self, business_id):
        self.business_id = business_id
    
    def get_revenue_analysis(self, start_date=None, end_date=None):
        """Analyse des revenus"""
        if not start_date:
            start_date = date.today().replace(day=1)  # Début du mois
        if not end_date:
            end_date = date.today()
        
        query = """
            SELECT 
                SUM(total_amount_cents) as gross_revenue_cents,
                SUM(tax_amount_cents) as total_tax_cents,
                SUM(total_amount_cents - tax_amount_cents) as net_revenue_cents,
                COUNT(*) as total_transactions,
                COUNT(DISTINCT DATE(created_at)) as active_days
            FROM transactions
            WHERE business_id_fk = :business_id
                AND DATE(created_at) BETWEEN :start_date AND :end_date
                AND status = 'completed'
        """
        
        result = db.session.execute(text(query), {
            'business_id': self.business_id,
            'start_date': start_date,
            'end_date': end_date
        }).fetchone()
        
        gross_revenue = (result.gross_revenue_cents or 0) / 100
        net_revenue = (result.net_revenue_cents or 0) / 100
        total_tax = (result.total_tax_cents or 0) / 100
        active_days = result.active_days or 1
        
        return {
            'period': {
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat()
            },
            'gross_revenue': gross_revenue,
            'net_revenue': net_revenue,
            'total_tax': total_tax,
            'total_transactions': result.total_transactions or 0,
            'avg_daily_revenue': gross_revenue / active_days,
            'avg_transaction_value': gross_revenue / (result.total_transactions or 1)
        }


class KPIManager:
    """Gestionnaire pour la création et gestion des KPIs système"""
    
    @staticmethod
    def create_default_kpis(business_id):
        """Crée les KPIs par défaut pour une nouvelle entreprise"""
        default_kpis = [
            {
                'kpi_code': 'SALES_DAILY',
                'name': 'Ventes journalières',
                'description': 'Total des ventes réalisées aujourd\'hui',
                'category': 'sales',
                'calculation_method': 'custom',
                'custom_query': '''
                    SELECT COALESCE(SUM(total_amount_cents), 0) / 100
                    FROM transactions 
                    WHERE business_id_fk = {business_id} 
                        AND DATE(created_at) = CURDATE()
                        AND status = 'completed'
                ''',
                'unit': '€',
                'frequency': 'daily'
            },
            {
                'kpi_code': 'CUSTOMERS_TOTAL',
                'name': 'Nombre total de clients',
                'description': 'Nombre total de clients enregistrés',
                'category': 'sales',
                'calculation_method': 'count',
                'source_table': 'customers',
                'unit': 'clients',
                'frequency': 'daily'
            },
            {
                'kpi_code': 'PRODUCTS_OUT_OF_STOCK',
                'name': 'Produits en rupture',
                'description': 'Nombre de produits actuellement en rupture de stock',
                'category': 'inventory',
                'calculation_method': 'custom',
                'custom_query': '''
                    SELECT COUNT(*)
                    FROM products 
                    WHERE business_id_fk = {business_id} 
                        AND current_stock = 0 
                        AND is_active = 1
                ''',
                'unit': 'produits',
                'frequency': 'daily',
                'threshold_warning': 5,
                'threshold_critical': 10
            },
            {
                'kpi_code': 'TRANSACTIONS_DAILY',
                'name': 'Transactions journalières',
                'description': 'Nombre de transactions réalisées aujourd\'hui',
                'category': 'sales',
                'calculation_method': 'custom',
                'custom_query': '''
                    SELECT COUNT(*)
                    FROM transactions 
                    WHERE business_id_fk = {business_id} 
                        AND DATE(created_at) = CURDATE()
                        AND status = 'completed'
                ''',
                'unit': 'transactions',
                'frequency': 'daily'
            }
        ]
        
        created_kpis = []
        for kpi_data in default_kpis:
            existing = KPI.query.filter_by(
                business_id_fk=business_id,
                kpi_code=kpi_data['kpi_code']
            ).first()
            
            if not existing:
                kpi = KPI(business_id_fk=business_id, **kpi_data)
                db.session.add(kpi)
                created_kpis.append(kpi)
        
        db.session.commit()
        return created_kpis