"""Module pour surveiller et mesurer les performances de l'application"""
import time
import functools
from typing import Any, Callable, Dict
from collections import defaultdict
import logging
from flask import g, request
from app.extensions import cache

logger = logging.getLogger(__name__)

class PerformanceMonitor:
    """Surveille et mesure les performances de l'application"""
    
    def __init__(self):
        self.metrics: Dict[str, list] = defaultdict(list)
    
    def record_metric(self, metric_name: str, value: float):
        """Enregistre une métrique de performance"""
        self.metrics[metric_name].append(value)
    
    def get_average(self, metric_name: str) -> float:
        """Retourne la moyenne d'une métrique"""
        values = self.metrics[metric_name]
        return sum(values) / len(values) if values else 0
    
    def get_percentile(self, metric_name: str, percentile: float) -> float:
        """Retourne un percentile d'une métrique"""
        values = sorted(self.metrics[metric_name])
        if not values:
            return 0
        index = int(len(values) * percentile / 100)
        return values[min(index, len(values) - 1)]
    
    def reset_metrics(self):
        """Réinitialise toutes les métriques"""
        self.metrics.clear()

# Instance globale du moniteur de performance
performance_monitor = PerformanceMonitor()

def timing_decorator(metric_name: str = None):
    """Décorateur pour mesurer le temps d'exécution d'une fonction"""
    def decorator(func: Callable) -> Callable:
        nonlocal metric_name
        if metric_name is None:
            metric_name = f"{func.__module__}.{func.__name__}"
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                end_time = time.time()
                execution_time = (end_time - start_time) * 1000  # en millisecondes
                performance_monitor.record_metric(metric_name, execution_time)
                logger.debug(f"{metric_name} executed in {execution_time:.2f}ms")
        return wrapper
    return decorator

def request_timing_middleware():
    """Middleware pour mesurer le temps de traitement des requêtes"""
    start_time = time.time()
    g.start_time = start_time
    
    # Enregistrer les métriques à la fin de la requête
    @request_finished.connect
    def log_request_performance(sender, response, **extra):
        if hasattr(g, 'start_time'):
            execution_time = (time.time() - g.start_time) * 1000
            endpoint = request.endpoint or 'unknown'
            metric_name = f"request.{endpoint}"
            performance_monitor.record_metric(metric_name, execution_time)
            logger.debug(f"Request to {endpoint} took {execution_time:.2f}ms")

def get_performance_stats() -> Dict[str, Dict[str, float]]:
    """Retourne les statistiques de performance"""
    stats = {}
    for metric_name in performance_monitor.metrics:
        stats[metric_name] = {
            'avg': performance_monitor.get_average(metric_name),
            'p50': performance_monitor.get_percentile(metric_name, 50),
            'p95': performance_monitor.get_percentile(metric_name, 95),
            'p99': performance_monitor.get_percentile(metric_name, 99),
            'count': len(performance_monitor.metrics[metric_name])
        }
    return stats

# Import nécessaire pour le middleware
from flask import request_finished