from flask import render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from sqlalchemy import desc, asc, func, and_, or_
from app.extensions import db
from . import bp
from .forms import (
    CashRegisterForm, EditCashRegisterForm, OpenCashSessionForm, CloseCashSessionForm,
    CashMovementForm, CashSearchForm, QuickCashForm
)
from .models import CashRegister, CashSession, CashMovement, CashCount, CashDrawer
import csv
import io
from datetime import datetime, date


@bp.route("/")
@login_required
def index():
    """Page principale de gestion des caisses"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    search = request.args.get('search', '')
    status = request.args.get('status', 'all')
    location = request.args.get('location', '')
    sort_by = request.args.get('sort_by', 'register_code')
    sort_order = request.args.get('sort_order', 'asc')
    
    # Construction de la requête de base
    query = CashRegister.query.filter_by(business_id_fk=current_user.business.id)
    
    # Filtres de recherche
    if search:
        search_filter = or_(
            CashRegister.register_code.ilike(f'%{search}%'),
            CashRegister.name.ilike(f'%{search}%'),
            CashRegister.location.ilike(f'%{search}%'),
            CashRegister.description.ilike(f'%{search}%')
        )
        query = query.filter(search_filter)
    
    if location:
        query = query.filter(CashRegister.location.ilike(f'%{location}%'))
    
    if status == 'active':
        query = query.filter(CashRegister.is_active == True)
    elif status == 'inactive':
        query = query.filter(CashRegister.is_active == False)
    elif status == 'open':
        # Caisses avec des sessions ouvertes
        open_session_ids = db.session.query(CashSession.register_id_fk).filter(
            CashSession.closed_at.is_(None)
        ).subquery()
        query = query.filter(CashRegister.id.in_(open_session_ids))
    elif status == 'closed':
        # Caisses sans sessions ouvertes
        open_session_ids = db.session.query(CashSession.register_id_fk).filter(
            CashSession.closed_at.is_(None)
        ).subquery()
        query = query.filter(~CashRegister.id.in_(open_session_ids))
    
    # Tri
    if sort_by == 'name':
        order_col = CashRegister.name
    elif sort_by == 'location':
        order_col = CashRegister.location
    elif sort_by == 'created':
        order_col = CashRegister.created_at
    else:
        order_col = CashRegister.register_code
    
    if sort_order == 'desc':
        query = query.order_by(desc(order_col))
    else:
        query = query.order_by(asc(order_col))
    
    # Pagination
    registers = query.paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    # Statistiques pour le dashboard
    total_registers = CashRegister.query.filter_by(business_id_fk=current_user.business.id).count()
    active_registers = CashRegister.query.filter_by(
        business_id_fk=current_user.business.id, 
        is_active=True
    ).count()
    
    # Sessions ouvertes
    open_sessions = db.session.query(CashSession).join(CashRegister).filter(
        CashRegister.business_id_fk == current_user.business.id,
        CashSession.closed_at.is_(None)
    ).count()
    
    # Emplacements disponibles pour les filtres
    locations = db.session.query(CashRegister.location).filter(
        CashRegister.business_id_fk == current_user.business.id,
        CashRegister.location.isnot(None)
    ).distinct().all()
    locations = [loc[0] for loc in locations if loc[0]]
    
    return render_template(
        'cash/index.html',
        registers=registers,
        search=search,
        status=status,
        location=location,
        sort_by=sort_by,
        sort_order=sort_order,
        locations=locations,
        total_registers=total_registers,
        active_registers=active_registers,
        open_sessions=open_sessions
    )


@bp.route("/add", methods=['GET', 'POST'])
@login_required
def add_register():
    """Ajouter une nouvelle caisse"""
    form = CashRegisterForm()
    
    if form.validate_on_submit():
        # Vérifier l'unicité du code
        existing = CashRegister.query.filter_by(
            register_code=form.register_code.data
        ).first()
        
        if existing:
            flash('Ce code de caisse existe déjà.', 'error')
            return render_template('cash/add_register.html', form=form)
        
        register = CashRegister(
            business_id_fk=current_user.business.id,
            register_code=form.register_code.data,
            name=form.name.data,
            location=form.location.data,
            description=form.description.data,
            initial_cash_cents=int((form.initial_cash.data or 0) * 100),
            max_cash_limit_cents=int((form.max_cash_limit.data or 0) * 100) if form.max_cash_limit.data else None,
            receipt_header=form.receipt_header.data,
            receipt_footer=form.receipt_footer.data,
            requires_manager_approval=form.requires_manager_approval.data,
            auto_print_receipts=form.auto_print_receipts.data,
            is_active=form.is_active.data
        )
        
        db.session.add(register)
        db.session.commit()
        
        flash(f'Caisse {register.register_code} ajoutée avec succès!', 'success')
        return redirect(url_for('cash.view_register', id=register.id))
    
    return render_template('cash/add_register.html', form=form)


@bp.route("/<int:id>")
@login_required
def view_register(id):
    """Voir les détails d'une caisse"""
    register = CashRegister.query.filter_by(
        id=id, 
        business_id_fk=current_user.business.id
    ).first_or_404()
    
    # Session actuelle
    current_session = register.current_session
    
    # Dernières sessions
    recent_sessions = CashSession.query.filter_by(
        register_id_fk=id
    ).order_by(desc(CashSession.opened_at)).limit(10).all()
    
    # Mouvements récents
    recent_movements = CashMovement.query.filter_by(
        register_id_fk=id
    ).order_by(desc(CashMovement.created_at)).limit(20).all()
    
    # Statistiques de la caisse
    stats = {
        'total_sessions': CashSession.query.filter_by(register_id_fk=id).count(),
        'open_sessions': CashSession.query.filter_by(
            register_id_fk=id, 
            closed_at=None
        ).count(),
        'total_movements': CashMovement.query.filter_by(register_id_fk=id).count(),
        'current_cash': 0  # Sera calculé dynamiquement
    }
    
    return render_template(
        'cash/view_register.html',
        register=register,
        current_session=current_session,
        recent_sessions=recent_sessions,
        recent_movements=recent_movements,
        stats=stats
    )


@bp.route("/<int:id>/edit", methods=['GET', 'POST'])
@login_required
def edit_register(id):
    """Modifier une caisse"""
    register = CashRegister.query.filter_by(
        id=id, 
        business_id_fk=current_user.business.id
    ).first_or_404()
    
    form = EditCashRegisterForm(obj=register)
    
    # Convertir les centimes en euros pour l'affichage
    if register.initial_cash_cents:
        form.initial_cash.data = register.initial_cash_cents / 100
    if register.max_cash_limit_cents:
        form.max_cash_limit.data = register.max_cash_limit_cents / 100
    
    if form.validate_on_submit():
        register.name = form.name.data
        register.location = form.location.data
        register.description = form.description.data
        register.initial_cash_cents = int((form.initial_cash.data or 0) * 100)
        register.max_cash_limit_cents = int((form.max_cash_limit.data or 0) * 100) if form.max_cash_limit.data else None
        register.receipt_header = form.receipt_header.data
        register.receipt_footer = form.receipt_footer.data
        register.requires_manager_approval = form.requires_manager_approval.data
        register.auto_print_receipts = form.auto_print_receipts.data
        register.is_active = form.is_active.data
        register.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        flash(f'Caisse {register.register_code} modifiée avec succès!', 'success')
        return redirect(url_for('cash.view_register', id=register.id))
    
    return render_template('cash/edit_register.html', form=form, register=register)


@bp.route("/<int:id>/delete", methods=['POST'])
@login_required
def delete_register(id):
    """Supprimer une caisse"""
    register = CashRegister.query.filter_by(
        id=id, 
        business_id_fk=current_user.business.id
    ).first_or_404()
    
    # Vérifier s'il y a des sessions ou mouvements
    has_sessions = CashSession.query.filter_by(register_id_fk=id).first() is not None
    has_movements = CashMovement.query.filter_by(register_id_fk=id).first() is not None
    
    if has_sessions or has_movements:
        flash('Impossible de supprimer cette caisse car elle a des sessions ou mouvements associés.', 'error')
        return redirect(url_for('cash.view_register', id=id))
    
    register_code = register.register_code
    db.session.delete(register)
    db.session.commit()
    
    flash(f'Caisse {register_code} supprimée avec succès!', 'success')
    return redirect(url_for('cash.index'))


@bp.route("/<int:register_id>/open_session", methods=['GET', 'POST'])
@login_required
def open_session(register_id):
    """Ouvrir une session de caisse"""
    register = CashRegister.query.filter_by(
        id=register_id, 
        business_id_fk=current_user.business.id
    ).first_or_404()
    
    # Vérifier qu'aucune session n'est ouverte
    if register.current_session:
        flash('Une session est déjà ouverte pour cette caisse.', 'error')
        return redirect(url_for('cash.view_register', id=register_id))
    
    form = OpenCashSessionForm()
    
    if form.validate_on_submit():
        # Générer un numéro de session unique
        session_count = CashSession.query.filter_by(register_id_fk=register_id).count()
        session_number = f"{register.register_code}-{session_count + 1:04d}"
        
        session = CashSession(
            register_id_fk=register_id,
            employee_id_fk=current_user.id,  # Supposons que current_user est un employé
            session_number=session_number,
            opening_cash_cents=int((form.opening_cash.data or 0) * 100),
            opening_notes=form.opening_notes.data,
            opened_by_id=current_user.id
        )
        
        db.session.add(session)
        db.session.commit()
        
        flash(f'Session {session_number} ouverte avec succès!', 'success')
        return redirect(url_for('cash.view_session', id=session.id))
    
    # Pré-remplir avec le fond de caisse par défaut
    if not form.opening_cash.data and register.initial_cash_cents:
        form.opening_cash.data = register.initial_cash_cents / 100
    
    return render_template('cash/open_session.html', form=form, register=register)


@bp.route("/sessions/<int:id>")
@login_required
def view_session(id):
    """Voir les détails d'une session"""
    session = db.session.query(CashSession).join(CashRegister).filter(
        CashSession.id == id,
        CashRegister.business_id_fk == current_user.business.id
    ).first_or_404()
    
    # Mouvements de la session
    movements = CashMovement.query.filter_by(
        session_id_fk=id
    ).order_by(desc(CashMovement.created_at)).all()
    
    return render_template(
        'cash/view_session.html',
        session=session,
        movements=movements
    )


@bp.route("/sessions/<int:id>/close", methods=['GET', 'POST'])
@login_required
def close_session(id):
    """Fermer une session de caisse"""
    session = db.session.query(CashSession).join(CashRegister).filter(
        CashSession.id == id,
        CashRegister.business_id_fk == current_user.business.id,
        CashSession.closed_at.is_(None)
    ).first_or_404()
    
    form = CloseCashSessionForm()
    
    if form.validate_on_submit():
        # Calculer le cash attendu
        total_movements = db.session.query(func.sum(CashMovement.amount_cents)).filter_by(
            session_id_fk=id
        ).scalar() or 0
        
        expected_cash = session.opening_cash_cents + total_movements
        
        session.closed_at = datetime.utcnow()
        session.closing_cash_counted_cents = int((form.closing_cash_counted.data or 0) * 100)
        session.closing_cash_expected_cents = expected_cash
        session.closing_notes = form.closing_notes.data
        session.closed_by_id = current_user.id
        
        db.session.commit()
        
        # Calculer l'écart
        difference = session.cash_difference
        if difference:
            if difference > 0:
                flash(f'Session fermée. Excédent de {difference:.2f}€', 'warning')
            elif difference < 0:
                flash(f'Session fermée. Manque de {abs(difference):.2f}€', 'warning')
            else:
                flash('Session fermée. Caisse équilibrée.', 'success')
        else:
            flash('Session fermée avec succès!', 'success')
        
        return redirect(url_for('cash.view_session', id=id))
    
    return render_template('cash/close_session.html', form=form, session=session)


@bp.route("/<int:register_id>/movements/add", methods=['GET', 'POST'])
@login_required
def add_movement(register_id):
    """Ajouter un mouvement de caisse"""
    register = CashRegister.query.filter_by(
        id=register_id, 
        business_id_fk=current_user.business.id
    ).first_or_404()
    
    # Vérifier qu'une session est ouverte
    current_session = register.current_session
    if not current_session:
        flash('Aucune session ouverte pour cette caisse.', 'error')
        return redirect(url_for('cash.view_register', id=register_id))
    
    form = CashMovementForm()
    
    if form.validate_on_submit():
        # Ajuster le montant selon le type
        amount_cents = int(form.amount.data * 100)
        
        movement = CashMovement(
            register_id_fk=register_id,
            session_id_fk=current_session.id,
            employee_id_fk=current_user.id,
            amount_cents=amount_cents if form.movement_type.data == 'in' else -amount_cents,
            movement_type=form.movement_type.data,
            reason=form.reason.data,
            notes=form.notes.data
        )
        
        db.session.add(movement)
        db.session.commit()
        
        flash('Mouvement enregistré avec succès!', 'success')
        return redirect(url_for('cash.view_register', id=register_id))
    
    return render_template('cash/add_movement.html', form=form, register=register)


@bp.route("/sessions")
@login_required
def sessions():
    """Liste des sessions de caisse"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    register_id = request.args.get('register_id', type=int)
    
    query = db.session.query(CashSession).join(CashRegister).filter(
        CashRegister.business_id_fk == current_user.business.id
    )
    
    if register_id:
        query = query.filter(CashSession.register_id_fk == register_id)
    
    sessions = query.order_by(desc(CashSession.opened_at)).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    # Liste des caisses pour le filtre
    registers = CashRegister.query.filter_by(
        business_id_fk=current_user.business.id
    ).all()
    
    return render_template(
        'cash/sessions.html',
        sessions=sessions,
        registers=registers,
        register_id=register_id
    )


@bp.route("/movements")
@login_required
def movements():
    """Liste des mouvements de caisse"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 50, type=int)
    register_id = request.args.get('register_id', type=int)
    movement_type = request.args.get('movement_type', '')
    
    query = db.session.query(CashMovement).join(CashRegister).filter(
        CashRegister.business_id_fk == current_user.business.id
    )
    
    if register_id:
        query = query.filter(CashMovement.register_id_fk == register_id)
    
    if movement_type:
        query = query.filter(CashMovement.movement_type == movement_type)
    
    movements = query.order_by(desc(CashMovement.created_at)).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    # Liste des caisses pour le filtre
    registers = CashRegister.query.filter_by(
        business_id_fk=current_user.business.id
    ).all()
    
    return render_template(
        'cash/movements.html',
        movements=movements,
        registers=registers,
        register_id=register_id,
        movement_type=movement_type
    )


@bp.route("/reports")
@login_required
def reports():
    """Rapports de caisse"""
    # Rapport de la journée
    today = date.today()
    start_of_day = datetime.combine(today, datetime.min.time())
    
    # Total des mouvements d'entrée
    total_in = db.session.query(func.sum(CashMovement.amount_cents)).filter(
        CashMovement.amount_cents > 0,
        CashMovement.created_at >= start_of_day
    ).join(CashRegister).filter(
        CashRegister.business_id_fk == current_user.business.id
    ).scalar() or 0
    
    # Total des mouvements de sortie
    total_out = db.session.query(func.sum(CashMovement.amount_cents)).filter(
        CashMovement.amount_cents < 0,
        CashMovement.created_at >= start_of_day
    ).join(CashRegister).filter(
        CashRegister.business_id_fk == current_user.business.id
    ).scalar() or 0
    
    # Sessions ouvertes
    open_sessions = db.session.query(CashSession).join(CashRegister).filter(
        CashRegister.business_id_fk == current_user.business.id,
        CashSession.closed_at.is_(None)
    ).all()
    
    return render_template(
        'cash/reports.html',
        total_in=total_in,
        total_out=total_out,
        open_sessions=open_sessions
    )


@bp.route("/export/movements")
@login_required
def export_movements():
    """Export des mouvements de caisse en CSV"""
    movements = db.session.query(CashMovement).join(CashRegister).filter(
        CashRegister.business_id_fk == current_user.business.id
    ).order_by(desc(CashMovement.created_at)).all()
    
    # Créer le CSV en mémoire
    output = io.StringIO()
    writer = csv.writer(output, quoting=csv.QUOTE_ALL)
    
    # En-têtes
    writer.writerow([
        'Date', 'Caisse', 'Type', 'Montant (€)', 'Raison', 'Notes'
    ])
    
    # Données
    for movement in movements:
        writer.writerow([
            movement.created_at.strftime('%d/%m/%Y %H:%M'),
            movement.register.register_code,
            'Entrée' if movement.movement_type == 'in' else 'Sortie',
            f"{movement.amount_euros:.2f}",
            movement.reason,
            movement.notes
        ])
    
    # Préparer la réponse
    csv_data = output.getvalue()
    output.close()
    
    # Retourner le CSV
    from flask import Response
    return Response(
        csv_data,
        mimetype='text/csv',
        headers={"Content-disposition": "attachment; filename=mouvements_caisse.csv"}
    )