{% extends 'base.html' %}
{% block title %}Modifier Client - {{ customer.display_name }}{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto">
  <!-- En-tête -->
  <div class="mb-6">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold">Modifier Client</h1>
        <p class="text-slate-400 mt-2">{{ customer.display_name }} ({{ customer.customer_code }})</p>
      </div>
      <div class="flex space-x-3">
        <a href="{{ url_for('customers.view_customer', id=customer.id) }}" class="bg-slate-700 hover:bg-slate-600 px-4 py-2 rounded-lg">
          ← Retour au détail
        </a>
        <a href="{{ url_for('customers.index') }}" class="bg-slate-600 hover:bg-slate-500 px-4 py-2 rounded-lg">
          Liste des clients
        </a>
      </div>
    </div>
  </div>

  <!-- Informations actuelles -->
  <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
    <div class="rounded-xl bg-slate-900 border border-slate-700 p-4">
      <div class="text-center">
        <div class="text-2xl mb-2">👤</div>
        <h3 class="font-semibold text-sm">{{ customer.display_name }}</h3>
        <p class="text-xs text-slate-400">{{ customer.customer_code }}</p>
      </div>
    </div>
    
    <div class="rounded-xl bg-slate-900 border border-slate-700 p-4">
      <div class="text-center">
        <div class="text-2xl mb-2">{% if customer.customer_type == 'company' %}🏢{% else %}🏠{% endif %}</div>
        <h3 class="font-semibold text-sm">{{ 'Entreprise' if customer.customer_type == 'company' else 'Particulier' }}</h3>
        <p class="text-xs text-slate-400">Type de client</p>
      </div>
    </div>
    
    <div class="rounded-xl bg-slate-900 border border-slate-700 p-4">
      <div class="text-center">
        <div class="text-2xl mb-2">{% if customer.is_active %}✅{% else %}🚫{% endif %}</div>
        <h3 class="font-semibold text-sm {% if customer.is_active %}text-green-400{% else %}text-red-400{% endif %}">
          {{ 'Actif' if customer.is_active else 'Inactif' }}
        </h3>
        <p class="text-xs text-slate-400">Statut</p>
      </div>
    </div>
    
    <div class="rounded-xl bg-slate-900 border border-slate-700 p-4">
      <div class="text-center">
        <div class="text-2xl mb-2">{% if customer.is_vip %}⭐{% else %}👤{% endif %}</div>
        <h3 class="font-semibold text-sm {% if customer.is_vip %}text-yellow-400{% else %}text-slate-400{% endif %}">
          {{ 'VIP' if customer.is_vip else 'Standard' }}
        </h3>
        <p class="text-xs text-slate-400">Niveau</p>
      </div>
    </div>
  </div>

  <!-- Formulaire de modification -->
  <div class="rounded-xl bg-slate-900 border border-slate-700 p-6">
    <h2 class="text-xl font-semibold mb-6">✏️ Informations du client</h2>
    
    <form method="post" class="space-y-6">
      {{ form.csrf_token }}
      
      <!-- Messages d'erreur -->
      {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
          {% for category, message in messages %}
            <div class="rounded-lg p-4 {% if category == 'error' %}bg-red-900 border border-red-700 text-red-100{% else %}bg-green-900 border border-green-700 text-green-100{% endif %}">
              {{ message }}
            </div>
          {% endfor %}
        {% endif %}
      {% endwith %}

      <!-- Type de client et informations de base -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-slate-300 mb-2">
              {{ form.customer_type.label.text }} <span class="text-red-400">*</span>
            </label>
            {{ form.customer_type(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 focus:ring-2 focus:ring-cyan-500 focus:border-transparent", onchange="toggleCompanyField()") }}
            {% if form.customer_type.errors %}
              <p class="text-red-400 text-sm mt-1">{{ form.customer_type.errors[0] }}</p>
            {% endif %}
          </div>
          
          <div>
            <label class="block text-sm font-medium text-slate-300 mb-2">
              {{ form.first_name.label.text }} <span class="text-red-400">*</span>
            </label>
            {{ form.first_name(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 focus:ring-2 focus:ring-cyan-500 focus:border-transparent") }}
            {% if form.first_name.errors %}
              <p class="text-red-400 text-sm mt-1">{{ form.first_name.errors[0] }}</p>
            {% endif %}
          </div>
          
          <div>
            <label class="block text-sm font-medium text-slate-300 mb-2">
              {{ form.last_name.label.text }} <span class="text-red-400">*</span>
            </label>
            {{ form.last_name(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 focus:ring-2 focus:ring-cyan-500 focus:border-transparent") }}
            {% if form.last_name.errors %}
              <p class="text-red-400 text-sm mt-1">{{ form.last_name.errors[0] }}</p>
            {% endif %}
          </div>
          
          <div id="company-field" style="display: none;">
            <label class="block text-sm font-medium text-slate-300 mb-2">
              {{ form.company_name.label.text }}
            </label>
            {{ form.company_name(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 focus:ring-2 focus:ring-cyan-500 focus:border-transparent") }}
            {% if form.company_name.errors %}
              <p class="text-red-400 text-sm mt-1">{{ form.company_name.errors[0] }}</p>
            {% endif %}
          </div>
          
          <div>
            <label class="block text-sm font-medium text-slate-300 mb-2">
              {{ form.customer_code.label.text }}
            </label>
            {{ form.customer_code(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 focus:ring-2 focus:ring-cyan-500 focus:border-transparent") }}
            {% if form.customer_code.errors %}
              <p class="text-red-400 text-sm mt-1">{{ form.customer_code.errors[0] }}</p>
            {% endif %}
          </div>
        </div>
        
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-slate-300 mb-2">
              {{ form.email.label.text }}
            </label>
            {{ form.email(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 focus:ring-2 focus:ring-cyan-500 focus:border-transparent") }}
            {% if form.email.errors %}
              <p class="text-red-400 text-sm mt-1">{{ form.email.errors[0] }}</p>
            {% endif %}
          </div>
          
          <div class="grid grid-cols-2 gap-3">
            <div>
              <label class="block text-sm font-medium text-slate-300 mb-2">
                {{ form.phone.label.text }}
              </label>
              {{ form.phone(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 focus:ring-2 focus:ring-cyan-500 focus:border-transparent") }}
              {% if form.phone.errors %}
                <p class="text-red-400 text-sm mt-1">{{ form.phone.errors[0] }}</p>
              {% endif %}
            </div>
            
            <div>
              <label class="block text-sm font-medium text-slate-300 mb-2">
                {{ form.mobile.label.text }}
              </label>
              {{ form.mobile(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 focus:ring-2 focus:ring-cyan-500 focus:border-transparent") }}
              {% if form.mobile.errors %}
                <p class="text-red-400 text-sm mt-1">{{ form.mobile.errors[0] }}</p>
              {% endif %}
            </div>
          </div>
          
          <div class="grid grid-cols-2 gap-3">
            <div>
              <label class="block text-sm font-medium text-slate-300 mb-2">
                {{ form.birth_date.label.text }}
              </label>
              {{ form.birth_date(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 focus:ring-2 focus:ring-cyan-500 focus:border-transparent") }}
              {% if form.birth_date.errors %}
                <p class="text-red-400 text-sm mt-1">{{ form.birth_date.errors[0] }}</p>
              {% endif %}
            </div>
            
            <div>
              <label class="block text-sm font-medium text-slate-300 mb-2">
                {{ form.gender.label.text }}
              </label>
              {{ form.gender(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 focus:ring-2 focus:ring-cyan-500 focus:border-transparent") }}
              {% if form.gender.errors %}
                <p class="text-red-400 text-sm mt-1">{{ form.gender.errors[0] }}</p>
              {% endif %}
            </div>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-slate-300 mb-2">
              {{ form.preferred_language.label.text }}
            </label>
            {{ form.preferred_language(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 focus:ring-2 focus:ring-cyan-500 focus:border-transparent") }}
            {% if form.preferred_language.errors %}
              <p class="text-red-400 text-sm mt-1">{{ form.preferred_language.errors[0] }}</p>
            {% endif %}
          </div>
        </div>
      </div>

      <!-- Paramètres avancés -->
      <div class="border-t border-slate-700 pt-6">
        <h3 class="text-lg font-semibold mb-4">⚙️ Paramètres avancés</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium text-slate-300 mb-2">
              {{ form.credit_limit.label.text }}
            </label>
            {{ form.credit_limit(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 focus:ring-2 focus:ring-cyan-500 focus:border-transparent") }}
            {% if form.credit_limit.errors %}
              <p class="text-red-400 text-sm mt-1">{{ form.credit_limit.errors[0] }}</p>
            {% endif %}
            <p class="text-xs text-slate-400 mt-1">Limite de crédit autorisée pour ce client</p>
          </div>
          
          <div class="space-y-4">
            <div class="flex items-center">
              {{ form.is_active(class="w-4 h-4 text-cyan-600 bg-slate-800 border-slate-700 rounded focus:ring-cyan-500 focus:ring-2") }}
              <label class="ml-3 text-sm font-medium text-slate-300">
                {{ form.is_active.label.text }}
              </label>
            </div>
            
            <div class="flex items-center">
              {{ form.is_vip(class="w-4 h-4 text-yellow-600 bg-slate-800 border-slate-700 rounded focus:ring-yellow-500 focus:ring-2") }}
              <label class="ml-3 text-sm font-medium text-slate-300">
                {{ form.is_vip.label.text }}
              </label>
            </div>
            
            <div class="flex items-center">
              {{ form.marketing_consent(class="w-4 h-4 text-green-600 bg-slate-800 border-slate-700 rounded focus:ring-green-500 focus:ring-2") }}
              <label class="ml-3 text-sm font-medium text-slate-300">
                {{ form.marketing_consent.label.text }}
              </label>
            </div>
          </div>
        </div>
      </div>

      <!-- Notes -->
      <div>
        <label class="block text-sm font-medium text-slate-300 mb-2">
          {{ form.notes.label.text }}
        </label>
        {{ form.notes(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 focus:ring-2 focus:ring-cyan-500 focus:border-transparent", rows="4") }}
        {% if form.notes.errors %}
          <p class="text-red-400 text-sm mt-1">{{ form.notes.errors[0] }}</p>
        {% endif %}
      </div>

      <!-- Actions -->
      <div class="flex items-center justify-between pt-6 border-t border-slate-700">
        <a href="{{ url_for('customers.view_customer', id=customer.id) }}" class="bg-slate-700 hover:bg-slate-600 px-6 py-2 rounded-lg">
          Annuler
        </a>
        <div class="flex space-x-3">
          <button type="button" onclick="confirmDelete()" class="bg-red-600 hover:bg-red-500 px-6 py-2 rounded-lg">
            🗑️ Supprimer
          </button>
          {{ form.submit(class="bg-cyan-600 hover:bg-cyan-500 px-6 py-2 rounded-lg font-medium") }}
        </div>
      </div>
    </form>
  </div>

  <!-- Statistiques du client -->
  <div class="mt-6 grid grid-cols-1 md:grid-cols-3 gap-6">
    <div class="rounded-xl bg-slate-900 border border-slate-700 p-6">
      <h3 class="text-lg font-semibold mb-4">📊 Statistiques</h3>
      <div class="space-y-3">
        <div class="flex justify-between">
          <span class="text-slate-400">Commandes totales:</span>
          <span class="font-medium">{{ customer.get_total_orders() }}</span>
        </div>
        <div class="flex justify-between">
          <span class="text-slate-400">Total dépensé:</span>
          <span class="font-medium text-green-400">{{ "%.2f"|format(customer.get_total_spent()) }}€</span>
        </div>
        <div class="flex justify-between">
          <span class="text-slate-400">Points fidélité:</span>
          <span class="font-medium text-purple-400">{{ customer.get_loyalty_points_balance() }}</span>
        </div>
        <div class="flex justify-between">
          <span class="text-slate-400">Créé le:</span>
          <span class="font-medium">{{ customer.created_at.strftime('%d/%m/%Y') }}</span>
        </div>
      </div>
    </div>
    
    <div class="rounded-xl bg-slate-900 border border-slate-700 p-6">
      <h3 class="text-lg font-semibold mb-4">📍 Adresses</h3>
      <div class="space-y-2">
        {% set address_count = customer.addresses|selectattr('is_active')|list|length %}
        <div class="flex justify-between">
          <span class="text-slate-400">Adresses actives:</span>
          <span class="font-medium">{{ address_count }}</span>
        </div>
        {% if address_count > 0 %}
          <a href="{{ url_for('customers.view_customer', id=customer.id) }}#addresses" class="text-cyan-400 hover:text-cyan-300 text-sm">
            Voir les adresses →
          </a>
        {% endif %}
      </div>
    </div>
    
    <div class="rounded-xl bg-slate-900 border border-slate-700 p-6">
      <h3 class="text-lg font-semibold mb-4">⚡ Actions rapides</h3>
      <div class="space-y-2">
        <a href="{{ url_for('customers.view_customer', id=customer.id) }}" class="block w-full bg-blue-600 hover:bg-blue-500 rounded-lg px-4 py-2 text-center">
          👁️ Voir détail
        </a>
        <a href="{{ url_for('customers.add_address', customer_id=customer.id) }}" class="block w-full bg-green-600 hover:bg-green-500 rounded-lg px-4 py-2 text-center">
          🏠 Ajouter adresse
        </a>
        <button onclick="toggleVipStatus()" class="block w-full {% if customer.is_vip %}bg-yellow-600 hover:bg-yellow-500{% else %}bg-gray-600 hover:bg-gray-500{% endif %} rounded-lg px-4 py-2 text-center">
          {{ '⭐ Retirer VIP' if customer.is_vip else '⭐ Marquer VIP' }}
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Modal de confirmation de suppression -->
<div id="deleteModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
  <div class="bg-slate-900 border border-slate-700 rounded-xl p-6 max-w-md w-full mx-4">
    <h3 class="text-lg font-semibold mb-4">⚠️ Confirmer la suppression</h3>
    <p class="text-slate-300 mb-6">
      Êtes-vous sûr de vouloir supprimer ce client ? Cette action est irréversible.
      <br><br>
      <strong>{{ customer.display_name }}</strong> ({{ customer.customer_code }})
    </p>
    <div class="flex justify-end space-x-3">
      <button onclick="closeDeleteModal()" class="px-4 py-2 bg-slate-700 hover:bg-slate-600 rounded-lg">
        Annuler
      </button>
      <form method="post" action="{{ url_for('customers.delete_customer', id=customer.id) }}" class="inline">
        {{ csrf_token() }}
        <button class="px-4 py-2 bg-red-600 hover:bg-red-500 rounded-lg">
          Supprimer définitivement
        </button>
      </form>
    </div>
  </div>
</div>

<script>
function toggleCompanyField() {
  const customerTypeSelect = document.querySelector('select[name="customer_type"]');
  const companyField = document.getElementById('company-field');
  
  if (customerTypeSelect.value === 'company') {
    companyField.style.display = 'block';
  } else {
    companyField.style.display = 'none';
  }
}

function confirmDelete() {
  document.getElementById('deleteModal').classList.remove('hidden');
}

function closeDeleteModal() {
  document.getElementById('deleteModal').classList.add('hidden');
}

function toggleVipStatus() {
  const form = document.createElement('form');
  form.method = 'POST';
  form.action = "{{ url_for('customers.toggle_customer_vip', id=customer.id) }}";
  
  const csrfToken = document.createElement('input');
  csrfToken.type = 'hidden';
  csrfToken.name = 'csrf_token';
  csrfToken.value = "{{ csrf_token() }}";
  
  form.appendChild(csrfToken);
  document.body.appendChild(form);
  form.submit();
}

// Initialisation
document.addEventListener('DOMContentLoaded', function() {
  toggleCompanyField();
});

// Fermeture du modal avec Escape
document.addEventListener('keydown', function(e) {
  if (e.key === 'Escape') {
    closeDeleteModal();
  }
});
</script>
{% endblock %}