{% extends 'base.html' %}
{% block title %}Commande Recette{% endblock %}
{% block content %}
<div class="max-w-4xl mx-auto">
  <div class="flex items-center justify-between mb-6">
    <div>
      <h1 class="text-3xl font-bold">🍳 Commande avec Recettes</h1>
      <p class="text-slate-400 mt-2">Mode restaurant - Gestion des recettes et ingrédients</p>
    </div>
    <div class="flex space-x-3">
      <a href="{{ url_for('sales.orders') }}" class="px-4 py-2 bg-slate-700 hover:bg-slate-600 text-white rounded-lg">
        Retour aux commandes
      </a>
    </div>
  </div>

  <div class="grid grid-cols-12 gap-6">
    <!-- Formulaire de commande recette -->
    <div class="col-span-12 lg:col-span-5">
      <div class="rounded-xl bg-slate-900 border border-slate-700 p-6">
        <h2 class="text-lg font-semibold mb-4">Nouvelle commande recette</h2>
        <form method="post" class="space-y-4">
          {{ form.csrf_token }}
          
          <div>
            <label class="block text-sm font-medium text-slate-300 mb-2">{{ form.recipe_id.label.text }}</label>
            {{ form.recipe_id(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent") }}
            {% if form.recipe_id.errors %}
              <div class="mt-1 text-sm text-red-400">
                {% for error in form.recipe_id.errors %}
                  <p>{{ error }}</p>
                {% endfor %}
              </div>
            {% endif %}
          </div>
          
          <div>
            <label class="block text-sm font-medium text-slate-300 mb-2">{{ form.servings.label.text }}</label>
            {{ form.servings(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent") }}
            <p class="mt-1 text-sm text-slate-400">Nombre de portions à préparer</p>
            {% if form.servings.errors %}
              <div class="mt-1 text-sm text-red-400">
                {% for error in form.servings.errors %}
                  <p>{{ error }}</p>
                {% endfor %}
              </div>
            {% endif %}
          </div>
          
          <div>
            <label class="block text-sm font-medium text-slate-300 mb-2">{{ form.special_instructions.label.text }}</label>
            {{ form.special_instructions(rows="4", class="w-full bg-slate-800 border border-slate-700 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent", placeholder="Instructions spéciales pour la cuisine...") }}
            {% if form.special_instructions.errors %}
              <div class="mt-1 text-sm text-red-400">
                {% for error in form.special_instructions.errors %}
                  <p>{{ error }}</p>
                {% endfor %}
              </div>
            {% endif %}
          </div>
          
          <div class="flex justify-end space-x-3 pt-6 border-t border-slate-700">
            <a href="{{ url_for('sales.orders') }}" class="px-6 py-3 bg-slate-700 hover:bg-slate-600 text-white rounded-lg transition-colors">
              Annuler
            </a>
            {{ form.submit(class="px-6 py-3 bg-green-600 hover:bg-green-500 text-white rounded-lg transition-colors font-medium") }}
          </div>
        </form>
      </div>
    </div>

    <!-- Informations sur la recette sélectionnée -->
    <div class="col-span-12 lg:col-span-7">
      <div class="rounded-xl bg-slate-900 border border-slate-700 p-6">
        <h2 class="text-lg font-semibold mb-4">Détails de la recette</h2>
        
        <div id="recipe-details" class="space-y-4">
          <div class="text-center py-8">
            <div class="text-6xl mb-4">🍳</div>
            <h3 class="text-xl font-semibold text-slate-300 mb-2">Sélectionnez une recette</h3>
            <p class="text-slate-400">Les détails de la recette apparaîtront ici.</p>
          </div>
        </div>
      </div>
      
      <!-- Conseils pour les commandes recettes -->
      <div class="rounded-xl bg-slate-900 border border-slate-700 p-6 mt-6">
        <h3 class="text-lg font-semibold mb-4">💡 Conseils pour les commandes recettes</h3>
        <div class="space-y-2 text-sm text-slate-300">
          <p>• Le prix est calculé automatiquement selon le nombre de portions</p>
          <p>• Les ingrédients nécessaires seront automatiquement déduits du stock</p>
          <p>• Vérifiez la disponibilité des ingrédients avant de valider</p>
          <p>• Les instructions spéciales seront transmises à la cuisine</p>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const recipeSelect = document.querySelector('select[name="recipe_id"]');
    const servingsInput = document.querySelector('input[name="servings"]');
    const detailsContainer = document.getElementById('recipe-details');
    
    if (recipeSelect) {
        recipeSelect.addEventListener('change', function() {
            const recipeId = this.value;
            if (recipeId) {
                loadRecipeDetails(recipeId);
            } else {
                showEmptyState();
            }
        });
    }
    
    if (servingsInput) {
        servingsInput.addEventListener('input', function() {
            updatePriceCalculation();
        });
    }
});

function loadRecipeDetails(recipeId) {
    const container = document.getElementById('recipe-details');
    
    // Afficher un indicateur de chargement
    container.innerHTML = `
        <div class="text-center py-8">
            <div class="text-4xl mb-4">⏳</div>
            <p class="text-slate-400">Chargement des détails...</p>
        </div>
    `;
    
    // Simuler le chargement des détails de la recette
    setTimeout(() => {
        const mockRecipe = {
            id: recipeId,
            name: 'Pizza Margherita',
            description: 'Pizza classique avec tomate, mozzarella et basilic',
            prep_time: 15,
            cook_time: 12,
            servings: 4,
            price_per_serving: 8.50,
            ingredients: [
                { name: 'Pâte à pizza', quantity: '1 boule', unit: 'pièce' },
                { name: 'Sauce tomate', quantity: '100ml', unit: 'ml' },
                { name: 'Mozzarella', quantity: '150g', unit: 'g' },
                { name: 'Basilic frais', quantity: '10 feuilles', unit: 'feuilles' }
            ]
        };
        
        container.innerHTML = `
            <div class="space-y-4">
                <div class="bg-slate-800 rounded-lg p-4">
                    <h3 class="text-lg font-semibold text-cyan-400">${mockRecipe.name}</h3>
                    <p class="text-slate-300 mt-2">${mockRecipe.description}</p>
                </div>
                
                <div class="grid grid-cols-2 gap-4">
                    <div class="bg-slate-800 rounded-lg p-4">
                        <h4 class="font-medium text-slate-300">Temps de préparation</h4>
                        <p class="text-xl font-bold text-orange-400">${mockRecipe.prep_time} min</p>
                    </div>
                    <div class="bg-slate-800 rounded-lg p-4">
                        <h4 class="font-medium text-slate-300">Temps de cuisson</h4>
                        <p class="text-xl font-bold text-red-400">${mockRecipe.cook_time} min</p>
                    </div>
                </div>
                
                <div class="bg-slate-800 rounded-lg p-4">
                    <h4 class="font-medium text-slate-300 mb-3">Ingrédients (pour ${mockRecipe.servings} portions)</h4>
                    <div class="space-y-2">
                        ${mockRecipe.ingredients.map(ing => `
                            <div class="flex justify-between text-sm">
                                <span class="text-slate-300">${ing.name}</span>
                                <span class="text-cyan-400">${ing.quantity}</span>
                            </div>
                        `).join('')}
                    </div>
                </div>
                
                <div class="bg-green-800 border border-green-700 rounded-lg p-4">
                    <h4 class="font-medium text-green-300">Prix par portion</h4>
                    <p class="text-2xl font-bold text-green-400">${mockRecipe.price_per_serving.toFixed(2)} €</p>
                </div>
            </div>
        `;
        
        updatePriceCalculation();
    }, 1000);
}

function updatePriceCalculation() {
    const servingsInput = document.querySelector('input[name="servings"]');
    const servings = parseInt(servingsInput.value) || 1;
    const pricePerServing = 8.50; // Mock price
    
    const totalPrice = servings * pricePerServing;
    
    // Mettre à jour l'affichage du prix total si l'élément existe
    const priceElement = document.querySelector('.total-price');
    if (priceElement) {
        priceElement.textContent = `${totalPrice.toFixed(2)} €`;
    }
}

function showEmptyState() {
    const container = document.getElementById('recipe-details');
    container.innerHTML = `
        <div class="text-center py-8">
            <div class="text-6xl mb-4">🍳</div>
            <h3 class="text-xl font-semibold text-slate-300 mb-2">Sélectionnez une recette</h3>
            <p class="text-slate-400">Les détails de la recette apparaîtront ici.</p>
        </div>
    `;
}
</script>
{% endblock %}