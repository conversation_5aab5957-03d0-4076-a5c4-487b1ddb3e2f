# Règles d'alerte pour l'application POS System
groups:
- name: pos-system-alerts
  rules:
  # Alerte si l'application est down
  - alert: POSApplicationDown
    expr: up{job="pos-system"} == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "POS System application is down"
      description: "The POS System application has been down for more than 1 minute"

  # Alerte si le temps de réponse est trop élevé
  - alert: HighResponseTime
    expr: rate(http_request_duration_seconds_sum[5m]) / rate(http_request_duration_seconds_count[5m]) > 2
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "High response time"
      description: "The average response time is above 2 seconds for more than 2 minutes"

  # Alerte si le taux d'erreurs HTTP est trop élevé
  - alert: HighErrorRate
    expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.05
    for: 1m
    labels:
      severity: warning
    annotations:
      summary: "High error rate"
      description: "More than 5% of requests are returning 5xx errors"

  # Alerte si l'utilisation CPU est trop élevée
  - alert: HighCPUUsage
    expr: rate(process_cpu_seconds_total[5m]) > 0.8
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "High CPU usage"
      description: "CPU usage is above 80% for more than 2 minutes"

  # Alerte si l'utilisation mémoire est trop élevée
  - alert: HighMemoryUsage
    expr: process_resident_memory_bytes / 1024 / 1024 > 500
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "High memory usage"
      description: "Memory usage is above 500MB for more than 2 minutes"

- name: database-alerts
  rules:
  # Alerte si la base de données est down
  - alert: DatabaseDown
    expr: up{job="postgresql"} == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "Database is down"
      description: "The PostgreSQL database has been down for more than 1 minute"

  # Alerte si le nombre de connexions à la base de données est trop élevé
  - alert: HighDatabaseConnections
    expr: pg_stat_database_numbackends > 50
    for: 1m
    labels:
      severity: warning
    annotations:
      summary: "High database connections"
      description: "More than 50 database connections are active"

- name: cache-alerts
  rules:
  # Alerte si Redis est down
  - alert: CacheDown
    expr: up{job="redis"} == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "Cache is down"
      description: "The Redis cache has been down for more than 1 minute"

  # Alerte si le taux de misses du cache est trop élevé
  - alert: HighCacheMissRate
    expr: redis_keyspace_misses_total / (redis_keyspace_hits_total + redis_keyspace_misses_total) > 0.2
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High cache miss rate"
      description: "Cache miss rate is above 20% for more than 5 minutes"