"""Intégration PayPal pour l'application POS System"""
import paypalrestsdk
import logging
from typing import Dict, Any, Optional
from datetime import datetime
from flask import current_app

from app.modules.payments.models import Payment
from app.extensions import db

logger = logging.getLogger(__name__)

class PayPalIntegration:
    """Gestion de l'intégration PayPal"""
    
    def __init__(self):
        """Initialise l'intégration PayPal"""
        self.client_id = current_app.config.get('PAYPAL_CLIENT_ID')
        self.client_secret = current_app.config.get('PAYPAL_CLIENT_SECRET')
        self.mode = current_app.config.get('PAYPAL_MODE', 'sandbox')  # sandbox ou live
        
        if self.client_id and self.client_secret:
            paypalrestsdk.configure({
                "mode": self.mode,
                "client_id": self.client_id,
                "client_secret": self.client_secret
            })
    
    def create_payment(self, amount: float, currency: str = 'EUR', 
                      description: str = 'Paiement POS', 
                      return_url: str = None, 
                      cancel_url: str = None) -> Dict[str, Any]:
        """Crée un paiement PayPal"""
        try:
            payment = paypalrestsdk.Payment({
                "intent": "sale",
                "payer": {
                    "payment_method": "paypal"
                },
                "transactions": [{
                    "amount": {
                        "total": f"{amount:.2f}",
                        "currency": currency
                    },
                    "description": description
                }],
                "redirect_urls": {
                    "return_url": return_url or "http://localhost:5000/payments/paypal/execute",
                    "cancel_url": cancel_url or "http://localhost:5000/payments/paypal/cancel"
                }
            })
            
            if payment.create():
                logger.info(f"Paiement PayPal créé: {payment.id}")
                return {
                    'success': True,
                    'payment_id': payment.id,
                    'approval_url': next(link.href for link in payment.links if link.rel == 'approval_url')
                }
            else:
                logger.error(f"Erreur lors de la création du paiement PayPal: {payment.error}")
                return {
                    'success': False,
                    'error': payment.error
                }
                
        except Exception as e:
            logger.error(f"Erreur inattendue lors de la création du paiement PayPal: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def execute_payment(self, payment_id: str, payer_id: str) -> Dict[str, Any]:
        """Exécute un paiement PayPal"""
        try:
            payment = paypalrestsdk.Payment.find(payment_id)
            
            if payment.execute({"payer_id": payer_id}):
                logger.info(f"Paiement PayPal exécuté: {payment.id}")
                return {
                    'success': True,
                    'payment_id': payment.id,
                    'state': payment.state,
                    'transactions': payment.transactions
                }
            else:
                logger.error(f"Erreur lors de l'exécution du paiement PayPal: {payment.error}")
                return {
                    'success': False,
                    'error': payment.error
                }
                
        except Exception as e:
            logger.error(f"Erreur inattendue lors de l'exécution du paiement PayPal: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_payment_details(self, payment_id: str) -> Dict[str, Any]:
        """Récupère les détails d'un paiement PayPal"""
        try:
            payment = paypalrestsdk.Payment.find(payment_id)
            
            return {
                'success': True,
                'payment_id': payment.id,
                'state': payment.state,
                'create_time': payment.create_time,
                'update_time': payment.update_time,
                'transactions': payment.transactions,
                'payer': payment.payer
            }
            
        except Exception as e:
            logger.error(f"Erreur lors de la récupération des détails du paiement PayPal: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def refund_payment(self, sale_id: str, amount: Optional[float] = None, 
                      currency: str = 'EUR') -> Dict[str, Any]:
        """Rembourse un paiement PayPal"""
        try:
            sale = paypalrestsdk.Sale.find(sale_id)
            
            refund_data = {}
            if amount:
                refund_data['amount'] = {
                    'total': f"{amount:.2f}",
                    'currency': currency
                }
            
            refund = sale.refund(refund_data)
            
            if refund.success():
                logger.info(f"Remboursement PayPal créé: {refund.id}")
                return {
                    'success': True,
                    'refund_id': refund.id,
                    'state': refund.state
                }
            else:
                logger.error(f"Erreur lors du remboursement PayPal: {refund.error}")
                return {
                    'success': False,
                    'error': refund.error
                }
                
        except Exception as e:
            logger.error(f"Erreur inattendue lors du remboursement PayPal: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def create_billing_plan(self, name: str, description: str, 
                          frequency: str, frequency_interval: int,
                          amount: float, currency: str = 'EUR') -> Dict[str, Any]:
        """Crée un plan de facturation PayPal"""
        try:
            billing_plan = paypalrestsdk.BillingPlan({
                "name": name,
                "description": description,
                "type": "FIXED",
                "payment_definitions": [{
                    "name": "Paiement régulier",
                    "type": "REGULAR",
                    "frequency": frequency,  # DAY, WEEK, MONTH, YEAR
                    "frequency_interval": str(frequency_interval),
                    "amount": {
                        "value": f"{amount:.2f}",
                        "currency": currency
                    }
                }],
                "merchant_preferences": {
                    "auto_bill_amount": "yes",
                    "cancel_url": "http://localhost:5000/billing/cancel",
                    "return_url": "http://localhost:5000/billing/execute"
                }
            })
            
            if billing_plan.create():
                logger.info(f"Plan de facturation PayPal créé: {billing_plan.id}")
                return {
                    'success': True,
                    'plan_id': billing_plan.id,
                    'state': billing_plan.state
                }
            else:
                logger.error(f"Erreur lors de la création du plan de facturation PayPal: {billing_plan.error}")
                return {
                    'success': False,
                    'error': billing_plan.error
                }
                
        except Exception as e:
            logger.error(f"Erreur inattendue lors de la création du plan de facturation PayPal: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def create_billing_agreement(self, plan_id: str, name: str, description: str,
                               start_date: str) -> Dict[str, Any]:
        """Crée un accord de facturation PayPal"""
        try:
            billing_agreement = paypalrestsdk.BillingAgreement({
                "name": name,
                "description": description,
                "start_date": start_date,
                "plan": {
                    "id": plan_id
                },
                "payer": {
                    "payment_method": "paypal"
                }
            })
            
            if billing_agreement.create():
                logger.info(f"Accord de facturation PayPal créé: {billing_agreement.id}")
                return {
                    'success': True,
                    'agreement_id': billing_agreement.id,
                    'state': billing_agreement.state,
                    'approval_url': next(link.href for link in billing_agreement.links if link.rel == 'approval_url')
                }
            else:
                logger.error(f"Erreur lors de la création de l'accord de facturation PayPal: {billing_agreement.error}")
                return {
                    'success': False,
                    'error': billing_agreement.error
                }
                
        except Exception as e:
            logger.error(f"Erreur inattendue lors de la création de l'accord de facturation PayPal: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def execute_billing_agreement(self, token: str) -> Dict[str, Any]:
        """Exécute un accord de facturation PayPal"""
        try:
            billing_agreement = paypalrestsdk.BillingAgreement.execute(token)
            
            if billing_agreement:
                logger.info(f"Accord de facturation PayPal exécuté: {billing_agreement.id}")
                return {
                    'success': True,
                    'agreement_id': billing_agreement.id,
                    'state': billing_agreement.state
                }
            else:
                logger.error("Erreur lors de l'exécution de l'accord de facturation PayPal")
                return {
                    'success': False,
                    'error': "Erreur lors de l'exécution de l'accord de facturation"
                }
                
        except Exception as e:
            logger.error(f"Erreur inattendue lors de l'exécution de l'accord de facturation PayPal: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_billing_agreement_details(self, agreement_id: str) -> Dict[str, Any]:
        """Récupère les détails d'un accord de facturation PayPal"""
        try:
            agreement = paypalrestsdk.BillingAgreement.find(agreement_id)
            
            return {
                'success': True,
                'agreement_id': agreement.id,
                'state': agreement.state,
                'name': agreement.name,
                'description': agreement.description,
                'start_date': agreement.start_date,
                'payer': agreement.payer
            }
            
        except Exception as e:
            logger.error(f"Erreur lors de la récupération des détails de l'accord de facturation PayPal: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def cancel_billing_agreement(self, agreement_id: str, note: str) -> Dict[str, Any]:
        """Annule un accord de facturation PayPal"""
        try:
            agreement = paypalrestsdk.BillingAgreement.find(agreement_id)
            
            if agreement.cancel({"note": note}):
                logger.info(f"Accord de facturation PayPal annulé: {agreement.id}")
                return {
                    'success': True,
                    'agreement_id': agreement.id,
                    'state': agreement.state
                }
            else:
                logger.error(f"Erreur lors de l'annulation de l'accord de facturation PayPal: {agreement.error}")
                return {
                    'success': False,
                    'error': agreement.error
                }
                
        except Exception as e:
            logger.error(f"Erreur inattendue lors de l'annulation de l'accord de facturation PayPal: {e}")
            return {
                'success': False,
                'error': str(e)
            }