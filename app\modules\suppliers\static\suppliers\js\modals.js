// Gestion des modals avec Tailwind CSS pour le module Suppliers

// Fonction pour ouvrir un modal
function openModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.remove('hidden');
        modal.classList.add('flex');
        document.body.classList.add('overflow-hidden');
    }
}

// Fonction pour fermer un modal
function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.add('hidden');
        modal.classList.remove('flex');
        document.body.classList.remove('overflow-hidden');
    }
}

// Fermer le modal en cliquant sur le fond
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('modal-backdrop')) {
        const modal = e.target.closest('.modal');
        if (modal) {
            closeModal(modal.id);
        }
    }
});

// Fermer le modal avec la touche Échap
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        const openModal = document.querySelector('.modal.flex');
        if (openModal) {
            closeModal(openModal.id);
        }
    }
});

// Fonction spécifique pour la confirmation de suppression d'un fournisseur
function confirmDelete(supplierId, supplierName) {
    // Mettre à jour le contenu du modal avec les informations du fournisseur
    document.getElementById('supplierName').textContent = supplierName;
    document.getElementById('deleteForm').action = '/suppliers/' + supplierId + '/delete';
    
    // Ouvrir le modal
    openModal('deleteModal');
}