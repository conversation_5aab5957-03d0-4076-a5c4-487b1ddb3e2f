<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#007bff">
    <meta name="description" content="POS System GT2 - Rapports Mobile">
    
    <title>Rapports Mobile</title>
    
    <!-- PWA Manifest -->
    <link rel="manifest" href="/static/pwa/manifest.json">
    
    <!-- Icons -->
    <link rel="icon" type="image/png" sizes="192x192" href="/static/pwa/icons/icon-192x192.png">
    <link rel="apple-touch-icon" sizes="180x180" href="/static/pwa/icons/icon-180x180.png">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="/static/css/mobile.css">
</head>
<body>
    <div class="pos-interface">
        <!-- Header -->
        <header class="pos-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h5 mb-0">Rapports</h1>
                    <small id="connectionStatus">En ligne</small>
                </div>
                <div>
                    <button class="btn btn-sm btn-light me-2" id="exportButton">
                        <i class="fas fa-download"></i>
                    </button>
                    <button class="btn btn-sm btn-light" id="backButton">
                        <i class="fas fa-arrow-left"></i>
                    </button>
                </div>
            </div>
        </header>
        
        <!-- Content -->
        <main class="pos-content">
            <!-- Période -->
            <div class="card mb-3">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-0">Période</h6>
                            <small class="text-muted">Aujourd'hui</small>
                        </div>
                        <button class="btn btn-sm btn-outline-primary" id="changePeriodButton">
                            <i class="fas fa-calendar me-1"></i>Changer
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Statistiques principales -->
            <div class="row mb-3">
                <div class="col-6">
                    <div class="card">
                        <div class="card-body text-center">
                            <div class="stat-value text-primary">€1,245</div>
                            <div class="stat-label">CA Total</div>
                        </div>
                    </div>
                </div>
                <div class="col-6">
                    <div class="card">
                        <div class="card-body text-center">
                            <div class="stat-value text-success">42</div>
                            <div class="stat-label">Commandes</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mb-3">
                <div class="col-6">
                    <div class="card">
                        <div class="card-body text-center">
                            <div class="stat-value text-warning">€29.64</div>
                            <div class="stat-label">Panier Moyen</div>
                        </div>
                    </div>
                </div>
                <div class="col-6">
                    <div class="card">
                        <div class="card-body text-center">
                            <div class="stat-value text-info">18</div>
                            <div class="stat-label">Clients</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Graphiques -->
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="mb-0">Ventes par Heure</h6>
                </div>
                <div class="card-body">
                    <div class="chart-placeholder bg-light" style="height: 150px;">
                        <div class="d-flex align-items-center justify-content-center h-100">
                            <small class="text-muted">Graphique des ventes par heure</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="mb-0">Produits Populaires</h6>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-2">
                        <span>Burger Classique</span>
                        <span class="badge bg-primary">15</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>Frites</span>
                        <span class="badge bg-primary">12</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>Coca-Cola</span>
                        <span class="badge bg-primary">10</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>Pizza</span>
                        <span class="badge bg-primary">8</span>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>Glace Vanille</span>
                        <span class="badge bg-primary">6</span>
                    </div>
                </div>
            </div>
            
            <!-- Performances -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Performances</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Temps de Service Moyen</span>
                            <span>2m 34s</span>
                        </div>
                        <div class="progress mt-1">
                            <div class="progress-bar bg-success" style="width: 75%"></div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Taux de Satisfaction</span>
                            <span>92%</span>
                        </div>
                        <div class="progress mt-1">
                            <div class="progress-bar bg-info" style="width: 92%"></div>
                        </div>
                    </div>
                    
                    <div>
                        <div class="d-flex justify-content-between">
                            <span>Efficacité du Personnel</span>
                            <span>87%</span>
                        </div>
                        <div class="progress mt-1">
                            <div class="progress-bar bg-warning" style="width: 87%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
        
        <!-- Footer -->
        <footer class="pos-footer">
            <div class="text-center">
                <small>Dernière mise à jour: 14:45</small>
            </div>
        </footer>
    </div>
    
    <!-- Toast Container -->
    <div id="toastContainer" class="toast-container"></div>
    
    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/mobile.js"></script>
    
    <!-- Service Worker Registration -->
    <script>
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('/static/pwa/service-worker.js')
                    .then(function(registration) {
                        console.log('Service Worker enregistré avec succès:', registration);
                    })
                    .catch(function(error) {
                        console.log('Erreur lors de l\'enregistrement du Service Worker:', error);
                    });
            });
        }
        
        // Fonctionnalité de retour en arrière
        document.getElementById('backButton').addEventListener('click', function() {
            window.history.back();
        });
        
        // Fonctionnalité d'export
        document.getElementById('exportButton').addEventListener('click', function() {
            showNotification('Rapport exporté avec succès', 'success');
        });
        
        // Fonctionnalité de changement de période
        document.getElementById('changePeriodButton').addEventListener('click', function() {
            showNotification('Fonction de changement de période à implémenter', 'info');
        });
        
        // Fonction pour afficher les notifications
        function showNotification(message, type = 'info') {
            const toastContainer = document.getElementById('toastContainer') || 
                document.createElement('div');
            
            if (!document.getElementById('toastContainer')) {
                toastContainer.id = 'toastContainer';
                toastContainer.className = 'toast-container';
                document.body.appendChild(toastContainer);
            }
            
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.innerHTML = `
                <div class="toast-message">${message}</div>
            `;
            
            toastContainer.appendChild(toast);
            
            // Supprimer automatiquement après 3 secondes
            setTimeout(() => {
                toast.remove();
            }, 3000);
        }
    </script>
</body>
</html>