from app import create_app, db

app = create_app(seed_data=False)

with app.app_context():
    # Importer tous les modèles
    from app.modules.accounts import models as accounts_models
    from app.modules.catalog import models as catalog_models
    from app.modules.sales import models as sales_models
    from app.modules.inventory import models as inventory_models
    from app.modules.ingredients import models as ingredients_models
    from app.modules.variants import models as variants_models
    from app.modules.expenses import models as expenses_models
    from app.modules.menu_planning import models as menu_planning_models
    from app.modules.loyalty import models as loyalty_models
    from app.modules.pharmacy import models as pharmacy_models
    from app.modules.reports import models as reports_models
    from app.modules.tables import models as tables_models
    from app.modules.kds import models as kds_models
    from app.modules.payments import models as payments_models
    from app.modules.customers import models as customers_models
    from app.modules.staff import models as staff_models
    from app.modules.cash import models as cash_models
    from app.modules.suppliers import models as suppliers_models
    from app.modules.purchasing import models as purchasing_models
    from app.modules.settings import models as settings_models
    from app.modules.audit import models as audit_models
    from app.modules.integrations import models as integrations_models
    from app.modules.notifications import models as notifications_models
    from app.modules.ai import models as ai_models
    from app.modules.delivery import models as delivery_models
    from app.modules.support import models as support_models
    
    # Créer les tables dans l'ordre correct pour respecter les dépendances
    # Commencer par les tables de base sans dépendances
    db.create_all(tables=[
        accounts_models.User.__table__,
        accounts_models.Business.__table__,
        settings_models.BusinessConfiguration.__table__,
        settings_models.BusinessType.__table__,
        staff_models.Staff.__table__,
        customers_models.Customer.__table__,
        suppliers_models.Supplier.__table__,
        catalog_models.Category.__table__,
        catalog_models.Product.__table__,
        catalog_models.TaxRule.__table__,
        ingredients_models.IngredientCategory.__table__,
        ingredients_models.Ingredient.__table__,
        ingredients_models.Recipe.__table__,
        variants_models.ProductAttribute.__table__,
        variants_models.AttributeValue.__table__,
        variants_models.ProductVariant.__table__,
        tables_models.DiningRoom.__table__,
        tables_models.Table.__table__,
        sales_models.Order.__table__,
        sales_models.OrderItem.__table__,
    ])
    
    # Puis créer les tables avec des dépendances
    db.create_all()
    
    print("Toutes les tables ont été créées avec succès")