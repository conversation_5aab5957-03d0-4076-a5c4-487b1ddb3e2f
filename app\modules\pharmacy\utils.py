"""
Utilitaires pour le module pharmacy
Logique de vérification des interactions et conformité réglementaire
"""
from __future__ import annotations

import json
from datetime import datetime, date, timedelta
from typing import List, Dict, Optional, Tuple, Any
from dataclasses import dataclass
from decimal import Decimal

from sqlalchemy import and_, or_, func
from sqlalchemy.orm import Session

from app.extensions import db
from app.models import Business, Customer, User
from app.modules.pharmacy.models import (
    Medication, Prescription, PrescriptionItem, DrugInteraction,
    ComplianceCheck, MedicationStockMovement, PatientAllergy,
    MedicationClass, PrescriptionStatus, InteractionSeverity
)


@dataclass
class InteractionResult:
    """Résultat d'une vérification d'interaction"""
    has_interaction: bool
    severity: InteractionSeverity
    description: str
    recommendations: str
    contraindicated: bool
    monitoring_required: bool


@dataclass
class ComplianceResult:
    """Résultat d'un contrôle de conformité"""
    passed: bool
    check_type: str
    severity_level: str
    description: str
    failure_reason: Optional[str] = None
    recommendations: Optional[str] = None
    action_required: bool = False


class PharmacyInteractionChecker:
    """Vérificateur d'interactions médicamenteuses"""
    
    def __init__(self, business_id: int):
        self.business_id = business_id
    
    def check_medication_interactions(
        self, 
        medication_ids: List[int]
    ) -> List[InteractionResult]:
        """
        Vérifier les interactions entre une liste de médicaments
        
        Args:
            medication_ids: Liste des IDs des médicaments
            
        Returns:
            Liste des interactions trouvées
        """
        interactions = []
        
        if len(medication_ids) < 2:
            return interactions
        
        # Rechercher toutes les interactions connues
        for i, med1_id in enumerate(medication_ids):
            for med2_id in medication_ids[i + 1:]:
                interaction = self._check_pair_interaction(med1_id, med2_id)
                if interaction:
                    interactions.append(interaction)
        
        return interactions
    
    def _check_pair_interaction(
        self, 
        medication1_id: int, 
        medication2_id: int
    ) -> Optional[InteractionResult]:
        """Vérifier l'interaction entre deux médicaments"""
        
        # Rechercher dans la base de données
        interaction = DrugInteraction.query.filter(
            DrugInteraction.business_id_fk == self.business_id,
            DrugInteraction.is_active == True,
            or_(
                and_(
                    DrugInteraction.medication1_id_fk == medication1_id,
                    DrugInteraction.medication2_id_fk == medication2_id
                ),
                and_(
                    DrugInteraction.medication1_id_fk == medication2_id,
                    DrugInteraction.medication2_id_fk == medication1_id
                )
            )
        ).first()
        
        if interaction:
            return InteractionResult(
                has_interaction=True,
                severity=interaction.severity,
                description=interaction.description,
                recommendations=interaction.recommendations or "",
                contraindicated=interaction.contraindicated,
                monitoring_required=interaction.monitoring_required
            )
        
        return None
    
    def check_prescription_interactions(
        self, 
        prescription: Prescription
    ) -> List[InteractionResult]:
        """Vérifier les interactions dans une ordonnance"""
        medication_ids = [item.medication_id_fk for item in prescription.items]
        return self.check_medication_interactions(medication_ids)
    
    def check_patient_allergies(
        self, 
        customer: Customer, 
        medication_ids: List[int]
    ) -> List[ComplianceResult]:
        """Vérifier les allergies du patient"""
        results = []
        
        if not customer.allergies:
            return results
        
        medications = Medication.query.filter(
            Medication.id.in_(medication_ids),
            Medication.business_id_fk == self.business_id
        ).all()
        
        for medication in medications:
            for allergy in customer.allergies:
                if self._check_allergy_conflict(medication, allergy):
                    results.append(ComplianceResult(
                        passed=False,
                        check_type="allergy_check",
                        severity_level="critical",
                        description=f"Allergie connue: {allergy.allergen_name}",
                        failure_reason=f"Le patient est allergique à {allergy.allergen_name}",
                        recommendations="Ne pas délivrer ce médicament. Consulter le médecin pour une alternative.",
                        action_required=True
                    ))
        
        return results
    
    def _check_allergy_conflict(
        self, 
        medication: Medication, 
        allergy: PatientAllergy
    ) -> bool:
        """Vérifier si un médicament entre en conflit avec une allergie"""
        # Vérification simple par nom (peut être améliorée avec une base de données d'allergènes)
        allergen_lower = allergy.allergen_name.lower()
        
        # Vérifier le nom du médicament
        if allergen_lower in medication.name.lower():
            return True
        
        # Vérifier les principes actifs
        if medication.active_ingredients:
            if allergen_lower in medication.active_ingredients.lower():
                return True
        
        # Vérifier le nom générique
        if medication.generic_name and allergen_lower in medication.generic_name.lower():
            return True
        
        return False


class PharmacyComplianceChecker:
    """Vérificateur de conformité réglementaire"""
    
    def __init__(self, business_id: int):
        self.business_id = business_id
    
    def check_prescription_compliance(
        self, 
        prescription: Prescription
    ) -> List[ComplianceResult]:
        """Vérifier la conformité complète d'une ordonnance"""
        results = []
        
        # Vérifications de base
        results.extend(self._check_prescription_validity(prescription))
        results.extend(self._check_controlled_substances(prescription))
        results.extend(self._check_stock_availability(prescription))
        results.extend(self._check_medication_expiry(prescription))
        
        return results
    
    def _check_prescription_validity(
        self, 
        prescription: Prescription
    ) -> List[ComplianceResult]:
        """Vérifier la validité de l'ordonnance"""
        results = []
        
        # Vérifier la date d'expiration
        if prescription.is_expired:
            results.append(ComplianceResult(
                passed=False,
                check_type="prescription_expiry",
                severity_level="error",
                description="Ordonnance expirée",
                failure_reason=f"Ordonnance expirée depuis le {prescription.validity_date}",
                recommendations="Demander une nouvelle ordonnance au médecin",
                action_required=True
            ))
        
        # Vérifier les informations du médecin
        if not prescription.doctor_name or len(prescription.doctor_name.strip()) < 2:
            results.append(ComplianceResult(
                passed=False,
                check_type="doctor_information",
                severity_level="warning",
                description="Informations du médecin incomplètes",
                failure_reason="Nom du médecin manquant ou incomplet",
                recommendations="Vérifier l'identité du médecin prescripteur"
            ))
        
        return results
    
    def _check_controlled_substances(
        self, 
        prescription: Prescription
    ) -> List[ComplianceResult]:
        """Vérifier les substances contrôlées"""
        results = []
        
        for item in prescription.items:
            medication = item.medication
            if medication and medication.medication_class == MedicationClass.CONTROLLED:
                # Vérifier les exigences pour les stupéfiants
                if not prescription.doctor_rpps:
                    results.append(ComplianceResult(
                        passed=False,
                        check_type="controlled_substance",
                        severity_level="error",
                        description=f"Substance contrôlée: {medication.name}",
                        failure_reason="Numéro RPPS du médecin requis pour les stupéfiants",
                        recommendations="Demander le numéro RPPS du médecin",
                        action_required=True
                    ))
                
                # Vérifier la durée du traitement
                if item.duration_days and item.duration_days > 28:
                    results.append(ComplianceResult(
                        passed=False,
                        check_type="controlled_substance_duration",
                        severity_level="warning",
                        description=f"Durée de traitement excessive: {item.duration_days} jours",
                        failure_reason="Durée de traitement supérieure à 28 jours pour un stupéfiant",
                        recommendations="Vérifier la durée prescrite avec le médecin"
                    ))
        
        return results
    
    def _check_stock_availability(
        self, 
        prescription: Prescription
    ) -> List[ComplianceResult]:
        """Vérifier la disponibilité des stocks"""
        results = []
        
        for item in prescription.items:
            medication = item.medication
            if medication:
                remaining_to_dispense = item.remaining_quantity
                
                if medication.current_stock < remaining_to_dispense:
                    results.append(ComplianceResult(
                        passed=False,
                        check_type="stock_availability",
                        severity_level="warning",
                        description=f"Stock insuffisant: {medication.name}",
                        failure_reason=f"Stock actuel: {medication.current_stock}, requis: {remaining_to_dispense}",
                        recommendations="Commander le médicament ou proposer une alternative"
                    ))
        
        return results
    
    def _check_medication_expiry(
        self, 
        prescription: Prescription
    ) -> List[ComplianceResult]:
        """Vérifier les dates d'expiration des médicaments"""
        results = []
        
        # Vérifier les lots proches de l'expiration
        for item in prescription.items:
            medication = item.medication
            if medication:
                # Rechercher les mouvements de stock avec dates d'expiration
                upcoming_expiry = MedicationStockMovement.query.filter(
                    MedicationStockMovement.medication_id_fk == medication.id,
                    MedicationStockMovement.expiry_date.isnot(None),
                    MedicationStockMovement.expiry_date <= date.today() + timedelta(days=30)
                ).first()
                
                if upcoming_expiry:
                    results.append(ComplianceResult(
                        passed=True,
                        check_type="medication_expiry",
                        severity_level="info",
                        description=f"Attention: {medication.name} expire bientôt",
                        recommendations=f"Lot {upcoming_expiry.batch_number} expire le {upcoming_expiry.expiry_date}"
                    ))
        
        return results


class PharmacyStockManager:
    """Gestionnaire de stock pour la pharmacie"""
    
    def __init__(self, business_id: int):
        self.business_id = business_id
    
    def update_stock_from_prescription(
        self, 
        prescription: Prescription, 
        user_id: int
    ) -> bool:
        """Mettre à jour le stock lors de la délivrance d'une ordonnance"""
        try:
            for item in prescription.items:
                if item.dispensed_quantity > 0:
                    self._create_stock_movement(
                        medication_id=item.medication_id_fk,
                        quantity=-item.dispensed_quantity,
                        movement_type="out",
                        reference_type="prescription",
                        reference_id=prescription.id,
                        user_id=user_id,
                        notes=f"Délivrance ordonnance {prescription.prescription_number}"
                    )
                    
                    # Mettre à jour le stock actuel
                    medication = item.medication
                    medication.current_stock -= item.dispensed_quantity
            
            db.session.commit()
            return True
            
        except Exception as e:
            db.session.rollback()
            raise e
    
    def add_stock(
        self, 
        medication_id: int, 
        quantity: int, 
        unit_cost_cents: int = 0,
        batch_number: str = None,
        expiry_date: date = None,
        supplier_name: str = None,
        user_id: int = None,
        notes: str = None
    ) -> bool:
        """Ajouter du stock"""
        try:
            self._create_stock_movement(
                medication_id=medication_id,
                quantity=quantity,
                movement_type="in",
                reference_type="purchase",
                user_id=user_id,
                unit_cost_cents=unit_cost_cents,
                batch_number=batch_number,
                expiry_date=expiry_date,
                supplier_name=supplier_name,
                notes=notes
            )
            
            # Mettre à jour le stock actuel
            medication = Medication.query.get(medication_id)
            if medication:
                medication.current_stock += quantity
            
            db.session.commit()
            return True
            
        except Exception as e:
            db.session.rollback()
            raise e
    
    def _create_stock_movement(
        self,
        medication_id: int,
        quantity: int,
        movement_type: str,
        user_id: int,
        reference_type: str = None,
        reference_id: int = None,
        unit_cost_cents: int = 0,
        batch_number: str = None,
        expiry_date: date = None,
        supplier_name: str = None,
        notes: str = None
    ):
        """Créer un mouvement de stock"""
        movement = MedicationStockMovement(
            business_id_fk=self.business_id,
            medication_id_fk=medication_id,
            movement_type=movement_type,
            quantity=quantity,
            batch_number=batch_number,
            expiry_date=expiry_date,
            supplier_name=supplier_name,
            reference_type=reference_type,
            reference_id=reference_id,
            unit_cost_cents=unit_cost_cents,
            total_cost_cents=quantity * unit_cost_cents,
            created_by_user_id=user_id,
            notes=notes
        )
        
        db.session.add(movement)
    
    def get_low_stock_medications(self) -> List[Medication]:
        """Obtenir les médicaments en stock faible"""
        return Medication.query.filter(
            Medication.business_id_fk == self.business_id,
            Medication.current_stock <= Medication.minimum_stock,
            Medication.is_active == True
        ).all()
    
    def get_expiring_medications(self, days_ahead: int = 30) -> List[Dict]:
        """Obtenir les médicaments qui expirent bientôt"""
        expiry_date = date.today() + timedelta(days=days_ahead)
        
        movements = db.session.query(
            MedicationStockMovement,
            Medication
        ).join(
            Medication, MedicationStockMovement.medication_id_fk == Medication.id
        ).filter(
            MedicationStockMovement.business_id_fk == self.business_id,
            MedicationStockMovement.expiry_date.isnot(None),
            MedicationStockMovement.expiry_date <= expiry_date,
            Medication.is_active == True
        ).all()
        
        return [
            {
                'medication': movement[1],
                'batch_number': movement[0].batch_number,
                'expiry_date': movement[0].expiry_date,
                'quantity': movement[0].quantity
            }
            for movement in movements
        ]


class PharmacyReportGenerator:
    """Générateur de rapports pour la pharmacie"""
    
    def __init__(self, business_id: int):
        self.business_id = business_id
    
    def generate_daily_sales_report(self, report_date: date = None) -> Dict:
        """Générer un rapport de ventes quotidien"""
        if not report_date:
            report_date = date.today()
        
        # Ordonnances délivrées aujourd'hui
        prescriptions = Prescription.query.filter(
            Prescription.business_id_fk == self.business_id,
            func.date(Prescription.dispensed_at) == report_date,
            Prescription.status == PrescriptionStatus.DISPENSED
        ).all()
        
        total_prescriptions = len(prescriptions)
        total_amount = sum(p.total_amount_cents for p in prescriptions)
        total_reimbursed = sum(p.reimbursed_amount_cents for p in prescriptions)
        
        # Médicaments les plus vendus
        medication_sales = {}
        for prescription in prescriptions:
            for item in prescription.items:
                med_name = item.medication.name if item.medication else "Inconnu"
                if med_name not in medication_sales:
                    medication_sales[med_name] = 0
                medication_sales[med_name] += item.dispensed_quantity
        
        top_medications = sorted(
            medication_sales.items(), 
            key=lambda x: x[1], 
            reverse=True
        )[:10]
        
        return {
            'report_date': report_date,
            'total_prescriptions': total_prescriptions,
            'total_amount_cents': total_amount,
            'total_reimbursed_cents': total_reimbursed,
            'top_medications': top_medications,
            'generated_at': datetime.utcnow()
        }
    
    def generate_stock_report(self) -> Dict:
        """Générer un rapport de stock"""
        medications = Medication.query.filter_by(
            business_id_fk=self.business_id,
            is_active=True
        ).all()
        
        total_medications = len(medications)
        total_stock_value = sum(med.stock_value_cents for med in medications)
        low_stock_count = sum(1 for med in medications if med.is_low_stock)
        
        # Top 10 médicaments par valeur
        top_by_value = sorted(
            medications,
            key=lambda x: x.stock_value_cents,
            reverse=True
        )[:10]
        
        return {
            'total_medications': total_medications,
            'total_stock_value_cents': total_stock_value,
            'low_stock_count': low_stock_count,
            'top_medications_by_value': [
                {
                    'name': med.name,
                    'stock': med.current_stock,
                    'value_cents': med.stock_value_cents
                }
                for med in top_by_value
            ],
            'generated_at': datetime.utcnow()
        }


def calculate_prescription_totals(prescription: Prescription) -> None:
    """Calculer les totaux d'une ordonnance"""
    total_items = 0
    total_amount_cents = 0
    reimbursed_amount_cents = 0
    
    for item in prescription.items:
        total_items += 1
        total_amount_cents += item.total_price_cents
        reimbursed_amount_cents += item.reimbursed_amount_cents
    
    prescription.total_items = total_items
    prescription.total_amount_cents = total_amount_cents
    prescription.reimbursed_amount_cents = reimbursed_amount_cents


def validate_prescription_item(item: PrescriptionItem) -> List[str]:
    """Valider un article d'ordonnance"""
    errors = []
    
    if not item.medication:
        errors.append("Médicament requis")
        return errors
    
    if item.prescribed_quantity <= 0:
        errors.append("Quantité prescrite doit être positive")
    
    if item.dispensed_quantity < 0:
        errors.append("Quantité délivrée ne peut pas être négative")
    
    if item.dispensed_quantity > item.prescribed_quantity:
        errors.append("Quantité délivrée ne peut pas dépasser la quantité prescrite")
    
    if not item.dosage_instructions or len(item.dosage_instructions.strip()) < 5:
        errors.append("Instructions de posologie requises")
    
    # Vérifier le stock disponible
    if item.medication.current_stock < item.dispensed_quantity:
        errors.append(f"Stock insuffisant (disponible: {item.medication.current_stock})")
    
    return errors


def format_medication_name(medication: Medication) -> str:
    """Formater le nom complet d'un médicament"""
    parts = [medication.name]
    
    if medication.dosage:
        parts.append(medication.dosage)
    
    if medication.pharmaceutical_form:
        parts.append(f"({medication.pharmaceutical_form})")
    
    return " ".join(parts)


def get_medication_by_identifier(business_id: int, identifier: str) -> Optional[Medication]:
    """Rechercher un médicament par différents identifiants"""
    # Recherche par code CIP
    medication = Medication.query.filter_by(
        business_id_fk=business_id,
        cip_code=identifier,
        is_active=True
    ).first()
    
    if medication:
        return medication
    
    # Recherche par code-barres
    medication = Medication.query.filter_by(
        business_id_fk=business_id,
        barcode=identifier,
        is_active=True
    ).first()
    
    if medication:
        return medication
    
    # Recherche par nom (exacte)
    medication = Medication.query.filter_by(
        business_id_fk=business_id,
        name=identifier,
        is_active=True
    ).first()
    
    return medication