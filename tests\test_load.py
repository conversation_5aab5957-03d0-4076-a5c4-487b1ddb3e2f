"""Tests de charge pour l'application POS"""
import unittest
import threading
import time
import requests
from concurrent.futures import ThreadPoolExecutor, as_completed
from tests import BaseTestCase

class LoadTestCase(BaseTestCase):
    """Tests de charge pour l'application"""
    
    def setUp(self):
        """Initialisation pour les tests de charge"""
        super().setUp()
        self.base_url = 'http://localhost:5000'  # URL de l'application en cours d'exécution
    
    def test_concurrent_users(self):
        """Test de l'application avec plusieurs utilisateurs concurrents"""
        def user_session():
            """Simule une session utilisateur"""
            try:
                # Accéder à la page d'accueil
                response = self.client.get('/', follow_redirects=True)
                if response.status_code != 200:
                    return False
                
                # Accéder à la page POS
                response = self.client.get('/pos/sale', follow_redirects=True)
                if response.status_code != 200:
                    return False
                
                # Accéder aux produits
                response = self.client.get('/catalog/products', follow_redirects=True)
                if response.status_code != 200:
                    return False
                
                return True
            except Exception:
                return False
        
        # Simuler 10 utilisateurs concurrents
        num_users = 10
        successful_sessions = 0
        
        with ThreadPoolExecutor(max_workers=num_users) as executor:
            futures = [executor.submit(user_session) for _ in range(num_users)]
            
            for future in as_completed(futures):
                if future.result():
                    successful_sessions += 1
        
        # Vérifier que la majorité des sessions ont réussi
        self.assertGreaterEqual(successful_sessions, num_users * 0.8)
    
    def test_database_connection_pooling(self):
        """Test du pooling de connexions à la base de données"""
        import time
        from app.extensions import db
        
        def database_query():
            """Effectue une requête à la base de données"""
            try:
                # Effectuer une requête simple
                result = db.session.execute(db.text("SELECT 1")).fetchone()
                return result[0] == 1
            except Exception:
                return False
        
        # Exécuter plusieurs requêtes simultanément
        num_queries = 20
        successful_queries = 0
        
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(database_query) for _ in range(num_queries)]
            
            for future in as_completed(futures):
                if future.result():
                    successful_queries += 1
        
        end_time = time.time()
        
        # Vérifier que toutes les requêtes ont réussi
        self.assertEqual(successful_queries, num_queries)
        
        # Vérifier que le temps total est raisonnable
        total_time = end_time - start_time
        self.assertLess(total_time, 5.0)  # Moins de 5 secondes pour 20 requêtes
    
    def test_cache_performance_under_load(self):
        """Test des performances du cache sous charge"""
        from app.utils.cache import cache_manager
        
        def cache_operation(key, value):
            """Effectue une opération de cache"""
            try:
                # Écriture dans le cache
                cache_manager.set(key, value, timeout=60)
                
                # Lecture du cache
                cached_value = cache_manager.get(key)
                
                return cached_value == value
            except Exception:
                return False
        
        # Effectuer plusieurs opérations de cache simultanément
        num_operations = 50
        successful_operations = 0
        
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = []
            for i in range(num_operations):
                futures.append(executor.submit(
                    cache_operation, 
                    f"test_key_{i}", 
                    f"test_value_{i}"
                ))
            
            for future in as_completed(futures):
                if future.result():
                    successful_operations += 1
        
        end_time = time.time()
        
        # Vérifier que toutes les opérations ont réussi
        self.assertEqual(successful_operations, num_operations)
        
        # Vérifier les performances
        total_time = end_time - start_time
        avg_time_per_operation = total_time / num_operations
        self.assertLess(avg_time_per_operation, 0.01)  # Moins de 10ms par opération

class StressTestCase(BaseTestCase):
    """Tests de stress pour l'application"""
    
    def test_high_volume_data_processing(self):
        """Test du traitement de grandes quantités de données"""
        from app.modules.catalog.models import Product
        from app.modules.pos.models import POSOrder, POSOrderItem
        import random
        
        # Créer une grande quantité de données de test
        num_products = 1000
        products = []
        
        # Créer des produits
        for i in range(num_products):
            product = Product(
                sku=f'BULK{i:04d}',
                name=f'Bulk Product {i}',
                description=f'Description for bulk product {i}',
                price_cents=random.randint(100, 10000),  # 1€ à 100€
                business_id_fk=1
            )
            products.append(product)
        
        # Mesurer le temps d'insertion
        start_time = time.time()
        for product in products:
            db.session.add(product)
        db.session.commit()
        insert_time = time.time() - start_time
        
        # Vérifier que tous les produits ont été insérés
        total_products = Product.query.count()
        self.assertGreaterEqual(total_products, num_products)
        
        # Mesurer le temps de requête
        start_time = time.time()
        retrieved_products = Product.query.limit(100).all()
        query_time = time.time() - start_time
        
        # Vérifier que la requête est rapide
        self.assertLess(query_time, 1.0)  # Moins d'1 seconde pour 100 produits
        
        # Nettoyer les données de test
        for product in products:
            db.session.delete(product)
        db.session.commit()
    
    def test_concurrent_database_writes(self):
        """Test d'écritures concurrentes dans la base de données"""
        from app.modules.accounts.models import User
        import threading
        import time
        
        def create_user_batch(start_id, count):
            """Crée un lot d'utilisateurs"""
            users = []
            for i in range(count):
                user = User(
                    username=f'bulk_user_{start_id + i}',
                    email=f'bulk{start_id + i}@test.com',
                    first_name='Bulk',
                    last_name='User'
                )
                user.set_password('bulk_password_123')
                users.append(user)
            
            # Insérer tous les utilisateurs
            for user in users:
                db.session.add(user)
            db.session.commit()
            
            return len(users)
        
        # Créer plusieurs lots d'utilisateurs simultanément
        batch_size = 50
        num_batches = 4
        total_users_created = 0
        
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=num_batches) as executor:
            futures = []
            for i in range(num_batches):
                futures.append(executor.submit(
                    create_user_batch,
                    i * batch_size,
                    batch_size
                ))
            
            for future in as_completed(futures):
                total_users_created += future.result()
        
        end_time = time.time()
        
        # Vérifier que tous les utilisateurs ont été créés
        self.assertEqual(total_users_created, batch_size * num_batches)
        
        # Vérifier le temps de traitement
        total_time = end_time - start_time
        self.assertLess(total_time, 10.0)  # Moins de 10 secondes pour 200 utilisateurs

if __name__ == '__main__':
    unittest.main()