#!/bin/bash
# Script de sauvegarde automatique pour l'application POS System

# Configuration
BACKUP_DIR="/backups"
DATE=$(date +"%Y%m%d_%H%M%S")
BACKUP_NAME="pos_system_backup_$DATE"
DB_NAME="pos_system"
DB_USER="pos_user"
DB_HOST="database"

# Créer le répertoire de sauvegarde
mkdir -p $BACKUP_DIR

# Sauvegarde de la base de données
echo "Starting database backup..."
pg_dump -h $DB_HOST -U $DB_USER -d $DB_NAME > $BACKUP_DIR/${BACKUP_NAME}.sql

if [ $? -eq 0 ]; then
    echo "Database backup completed successfully"
    
    # Compresser la sauvegarde
    gzip $BACKUP_DIR/${BACKUP_NAME}.sql
    
    # Supprimer les sauvegardes plus anciennes que 30 jours
    find $BACKUP_DIR -name "pos_system_backup_*.sql.gz" -mtime +30 -delete
    
    echo "Backup completed: $BACKUP_DIR/${BACKUP_NAME}.sql.gz"
else
    echo "Database backup failed"
    exit 1
fi

# Sauvegarde des fichiers de l'application
echo "Starting application files backup..."
tar -czf $BACKUP_DIR/${BACKUP_NAME}_files.tar.gz \
    --exclude='instance/uploads/tmp/*' \
    --exclude='*.log' \
    /app/instance/

if [ $? -eq 0 ]; then
    echo "Application files backup completed successfully"
    
    # Supprimer les sauvegardes de fichiers plus anciennes que 30 jours
    find $BACKUP_DIR -name "pos_system_backup_*_files.tar.gz" -mtime +30 -delete
    
    echo "Files backup completed: $BACKUP_DIR/${BACKUP_NAME}_files.tar.gz"
else
    echo "Application files backup failed"
    exit 1
fi

echo "All backups completed successfully"