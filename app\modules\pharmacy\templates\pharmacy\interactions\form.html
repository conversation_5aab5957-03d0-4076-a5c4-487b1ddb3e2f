{% extends "base.html" %}

{% block title %}
    {% if interaction %}
        Modifier Interaction
    {% else %}
        Nouvelle Interaction
    {% endif %}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">{{ title }}</h1>
                <a href="{{ url_for('pharmacy.interactions') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> Retour à la liste
                </a>
            </div>

            <form method="POST" novalidate>
                {{ form.hidden_tag() }}
                
                <div class="row">
                    <!-- Informations de l'interaction -->
                    <div class="col-lg-8">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Médicaments Concernés</h5>
                            </div>
                            <div class="card-body">
                                {{ form.medication1_id_fk(style="display: none;") }}
                                {{ form.medication2_id_fk(style="display: none;") }}
                                
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label class="form-label">Premier médicament *</label>
                                        <div class="input-group">
                                            <input type="text" class="form-control" id="medication1-search" 
                                                   placeholder="Rechercher le premier médicament..." autocomplete="off">
                                            <button type="button" class="btn btn-outline-primary" id="clear-med1-btn">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                        <div id="medication1-results" class="list-group position-absolute w-100" style="z-index: 1000; display: none;"></div>
                                        <div id="selected-medication1" class="mt-2" style="display: none;">
                                            <div class="alert alert-info">
                                                <strong id="medication1-name"></strong><br>
                                                <small id="medication1-details"></small>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <label class="form-label">Deuxième médicament *</label>
                                        <div class="input-group">
                                            <input type="text" class="form-control" id="medication2-search" 
                                                   placeholder="Rechercher le deuxième médicament..." autocomplete="off">
                                            <button type="button" class="btn btn-outline-primary" id="clear-med2-btn">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                        <div id="medication2-results" class="list-group position-absolute w-100" style="z-index: 1000; display: none;"></div>
                                        <div id="selected-medication2" class="mt-2" style="display: none;">
                                            <div class="alert alert-info">
                                                <strong id="medication2-name"></strong><br>
                                                <small id="medication2-details"></small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                {% if form.medication1_id_fk.errors or form.medication2_id_fk.errors %}
                                    <div class="text-danger mt-2">
                                        {% for error in form.medication1_id_fk.errors + form.medication2_id_fk.errors %}
                                            <small>{{ error }}</small><br>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Classification de l'interaction -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Classification de l'Interaction</h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form.severity.label(class="form-label") }}
                                            {{ form.severity(class="form-select" + (" is-invalid" if form.severity.errors else "")) }}
                                            {% if form.severity.errors %}
                                                <div class="invalid-feedback">
                                                    {{ form.severity.errors[0] }}
                                                </div>
                                            {% endif %}
                                            <small class="form-text text-muted">
                                                <strong>Mineure :</strong> Effets cliniques limités<br>
                                                <strong>Modérée :</strong> Nécessite surveillance<br>
                                                <strong>Majeure :</strong> Éviter l'association<br>
                                                <strong>Contre-indiquée :</strong> Ne jamais associer
                                            </small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form.interaction_type.label(class="form-label") }}
                                            {{ form.interaction_type(class="form-control" + (" is-invalid" if form.interaction_type.errors else "")) }}
                                            {% if form.interaction_type.errors %}
                                                <div class="invalid-feedback">
                                                    {{ form.interaction_type.errors[0] }}
                                                </div>
                                            {% endif %}
                                            <small class="form-text text-muted">
                                                Ex: Pharmacocinétique, Pharmacodynamique, Additive
                                            </small>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row g-3">
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            {{ form.contraindicated(class="form-check-input") }}
                                            {{ form.contraindicated.label(class="form-check-label") }}
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            {{ form.monitoring_required(class="form-check-input") }}
                                            {{ form.monitoring_required.label(class="form-check-label") }}
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            {{ form.is_active(class="form-check-input") }}
                                            {{ form.is_active.label(class="form-check-label") }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Description de l'interaction -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Description de l'Interaction</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    {{ form.description.label(class="form-label") }}
                                    {{ form.description(class="form-control" + (" is-invalid" if form.description.errors else "")) }}
                                    {% if form.description.errors %}
                                        <div class="invalid-feedback">
                                            {{ form.description.errors[0] }}
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <div class="mb-3">
                                    {{ form.mechanism.label(class="form-label") }}
                                    {{ form.mechanism(class="form-control" + (" is-invalid" if form.mechanism.errors else "")) }}
                                    {% if form.mechanism.errors %}
                                        <div class="invalid-feedback">
                                            {{ form.mechanism.errors[0] }}
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <div class="mb-3">
                                    {{ form.clinical_effects.label(class="form-label") }}
                                    {{ form.clinical_effects(class="form-control" + (" is-invalid" if form.clinical_effects.errors else "")) }}
                                    {% if form.clinical_effects.errors %}
                                        <div class="invalid-feedback">
                                            {{ form.clinical_effects.errors[0] }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Gestion et recommandations -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Gestion et Recommandations</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    {{ form.recommendations.label(class="form-label") }}
                                    {{ form.recommendations(class="form-control" + (" is-invalid" if form.recommendations.errors else "")) }}
                                    {% if form.recommendations.errors %}
                                        <div class="invalid-feedback">
                                            {{ form.recommendations.errors[0] }}
                                        </div>
                                    {% endif %}
                                    <small class="form-text text-muted">
                                        Décrivez les actions à entreprendre : surveillance, ajustement posologique, alternative thérapeutique, etc.
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Panneau latéral -->
                    <div class="col-lg-4">
                        <!-- Niveau de preuve -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Niveau de Preuve</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    {{ form.evidence_level.label(class="form-label") }}
                                    {{ form.evidence_level(class="form-select" + (" is-invalid" if form.evidence_level.errors else "")) }}
                                    {% if form.evidence_level.errors %}
                                        <div class="invalid-feedback">
                                            {{ form.evidence_level.errors[0] }}
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <div class="mb-3">
                                    {{ form.references.label(class="form-label") }}
                                    {{ form.references(class="form-control" + (" is-invalid" if form.references.errors else "")) }}
                                    {% if form.references.errors %}
                                        <div class="invalid-feedback">
                                            {{ form.references.errors[0] }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Guide de saisie -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Guide de Saisie</h5>
                            </div>
                            <div class="card-body">
                                <h6>Niveaux de sévérité :</h6>
                                <ul class="small">
                                    <li><span class="badge bg-success">Mineure</span> : Surveillance standard</li>
                                    <li><span class="badge bg-warning">Modérée</span> : Surveillance renforcée</li>
                                    <li><span class="badge bg-danger">Majeure</span> : Éviter l'association</li>
                                    <li><span class="badge bg-dark">Contre-indiquée</span> : Interdit</li>
                                </ul>
                                
                                <h6 class="mt-3">Conseils :</h6>
                                <ul class="small">
                                    <li>Décrivez précisément le mécanisme</li>
                                    <li>Mentionnez les effets cliniques</li>
                                    <li>Proposez des alternatives</li>
                                    <li>Citez vos sources</li>
                                </ul>
                            </div>
                        </div>

                        <!-- Interactions existantes -->
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Vérification</h5>
                            </div>
                            <div class="card-body">
                                <div id="interaction-check" class="text-muted">
                                    <p class="mb-0">Sélectionnez deux médicaments pour vérifier s'il existe déjà une interaction.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Boutons d'action -->
                <div class="d-flex justify-content-end gap-2 mb-4">
                    <a href="{{ url_for('pharmacy.interactions') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i> Annuler
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> 
                        {% if interaction %}Modifier{% else %}Créer{% endif %} l'Interaction
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    setupMedicationSearch('medication1');
    setupMedicationSearch('medication2');
    
    // Vérifier les interactions existantes
    const med1Field = document.querySelector('input[name="medication1_id_fk"]');
    const med2Field = document.querySelector('input[name="medication2_id_fk"]');
    
    function checkExistingInteraction() {
        if (med1Field.value && med2Field.value) {
            // Ici on pourrait faire un appel API pour vérifier
            document.getElementById('interaction-check').innerHTML = 
                '<p class="text-info">Vérification des interactions existantes...</p>';
        }
    }
    
    med1Field.addEventListener('change', checkExistingInteraction);
    med2Field.addEventListener('change', checkExistingInteraction);
});

function setupMedicationSearch(medicationNumber) {
    const searchInput = document.getElementById(`medication${medicationNumber}-search`);
    const resultsDiv = document.getElementById(`medication${medicationNumber}-results`);
    const selectedDiv = document.getElementById(`selected-medication${medicationNumber}`);
    const hiddenField = document.querySelector(`input[name="medication${medicationNumber}_id_fk"]`);
    const clearBtn = document.getElementById(`clear-med${medicationNumber}-btn`);
    
    let searchTimeout;
    
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        const query = this.value.trim();
        
        if (query.length < 2) {
            resultsDiv.style.display = 'none';
            return;
        }
        
        searchTimeout = setTimeout(() => {
            fetch(`/pharmacy/api/medications/search?q=${encodeURIComponent(query)}`)
                .then(response => response.json())
                .then(medications => {
                    resultsDiv.innerHTML = '';
                    
                    if (medications.length > 0) {
                        medications.forEach(medication => {
                            const item = document.createElement('button');
                            item.type = 'button';
                            item.className = 'list-group-item list-group-item-action';
                            item.innerHTML = `
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">${medication.name}</h6>
                                    <small>${medication.medication_class}</small>
                                </div>
                                <p class="mb-1">${medication.generic_name || ''}</p>
                                <small>CIP: ${medication.cip_code || 'N/A'}</small>
                            `;
                            
                            item.addEventListener('click', function() {
                                selectMedication(medicationNumber, medication);
                            });
                            
                            resultsDiv.appendChild(item);
                        });
                        
                        resultsDiv.style.display = 'block';
                    } else {
                        resultsDiv.style.display = 'none';
                    }
                })
                .catch(error => {
                    console.error('Erreur lors de la recherche:', error);
                    resultsDiv.style.display = 'none';
                });
        }, 300);
    });
    
    clearBtn.addEventListener('click', function() {
        clearMedication(medicationNumber);
    });
    
    // Cacher les résultats lors du clic ailleurs
    document.addEventListener('click', function(e) {
        if (!searchInput.contains(e.target) && !resultsDiv.contains(e.target)) {
            resultsDiv.style.display = 'none';
        }
    });
}

function selectMedication(medicationNumber, medication) {
    const searchInput = document.getElementById(`medication${medicationNumber}-search`);
    const resultsDiv = document.getElementById(`medication${medicationNumber}-results`);
    const selectedDiv = document.getElementById(`selected-medication${medicationNumber}`);
    const hiddenField = document.querySelector(`input[name="medication${medicationNumber}_id_fk"]`);
    
    hiddenField.value = medication.id;
    searchInput.value = medication.name;
    resultsDiv.style.display = 'none';
    
    document.getElementById(`medication${medicationNumber}-name`).textContent = medication.name;
    document.getElementById(`medication${medicationNumber}-details`).textContent = 
        `${medication.generic_name || ''} - ${medication.medication_class}`;
    selectedDiv.style.display = 'block';
}

function clearMedication(medicationNumber) {
    const searchInput = document.getElementById(`medication${medicationNumber}-search`);
    const selectedDiv = document.getElementById(`selected-medication${medicationNumber}`);
    const hiddenField = document.querySelector(`input[name="medication${medicationNumber}_id_fk"]`);
    
    searchInput.value = '';
    hiddenField.value = '';
    selectedDiv.style.display = 'none';
}

// Raccourcis clavier
document.addEventListener('keydown', function(e) {
    if (e.ctrlKey && e.key === 's') {
        e.preventDefault();
        document.querySelector('form').submit();
    } else if (e.key === 'Escape') {
        window.location.href = "{{ url_for('pharmacy.interactions') }}";
    }
});
</script>
{% endblock %}