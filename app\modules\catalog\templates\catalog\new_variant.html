{% extends 'base.html' %}
{% block title %}Nouvelle Variante - {{ product.name }}{% endblock %}
{% block content %}
<div class="max-w-4xl mx-auto">
  <div class="flex items-center justify-between mb-6">
    <div>
      <h1 class="text-3xl font-bold">Nouvelle Variante</h1>
      <p class="text-slate-400 mt-2">Pour le produit : {{ product.name }} ({{ product.sku }})</p>
    </div>
    <div class="flex space-x-3">
      <a href="{{ url_for('catalog.product_variants', id=product.id) }}" class="px-4 py-2 bg-slate-700 hover:bg-slate-600 text-white rounded-lg">
        Retour aux variantes
      </a>
      <a href="{{ url_for('catalog.index') }}" class="px-4 py-2 bg-slate-600 hover:bg-slate-500 text-white rounded-lg">
        Catalogue
      </a>
    </div>
  </div>

  <div class="rounded-xl bg-slate-900 border border-slate-700 p-6">
    <form method="post" class="space-y-6">
      {{ form.csrf_token }}
      
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div>
          <label class="block text-sm font-medium text-slate-300 mb-2">{{ form.name.label.text }}</label>
          {{ form.name(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent") }}
          {% if form.name.errors %}
            <div class="mt-1 text-sm text-red-400">
              {% for error in form.name.errors %}
                <p>{{ error }}</p>
              {% endfor %}
            </div>
          {% endif %}
        </div>
        
        <div>
          <label class="block text-sm font-medium text-slate-300 mb-2">{{ form.sku.label.text }}</label>
          {{ form.sku(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent") }}
          {% if form.sku.errors %}
            <div class="mt-1 text-sm text-red-400">
              {% for error in form.sku.errors %}
                <p>{{ error }}</p>
              {% endfor %}
            </div>
          {% endif %}
        </div>
      </div>
      
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div>
          <label class="block text-sm font-medium text-slate-300 mb-2">{{ form.price_cents.label.text }}</label>
          {{ form.price_cents(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent") }}
          <p class="mt-1 text-sm text-slate-400">Ex: 1500 pour 15.00 €</p>
          {% if form.price_cents.errors %}
            <div class="mt-1 text-sm text-red-400">
              {% for error in form.price_cents.errors %}
                <p>{{ error }}</p>
              {% endfor %}
            </div>
          {% endif %}
        </div>
        
        <div>
          <label class="block text-sm font-medium text-slate-300 mb-2">{{ form.cost_cents.label.text }}</label>
          {{ form.cost_cents(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent") }}
          <p class="mt-1 text-sm text-slate-400">Coût de production (optionnel)</p>
          {% if form.cost_cents.errors %}
            <div class="mt-1 text-sm text-red-400">
              {% for error in form.cost_cents.errors %}
                <p>{{ error }}</p>
              {% endfor %}
            </div>
          {% endif %}
        </div>
        
        <div>
          <label class="block text-sm font-medium text-slate-300 mb-2">{{ form.stock_qty.label.text }}</label>
          {{ form.stock_qty(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent") }}
          {% if form.stock_qty.errors %}
            <div class="mt-1 text-sm text-red-400">
              {% for error in form.stock_qty.errors %}
                <p>{{ error }}</p>
              {% endfor %}
            </div>
          {% endif %}
        </div>
      </div>
      
      <div class="flex justify-end space-x-3 pt-6 border-t border-slate-700">
        <a href="{{ url_for('catalog.product_variants', id=product.id) }}" class="px-6 py-3 bg-slate-700 hover:bg-slate-600 text-white rounded-lg transition-colors">
          Annuler
        </a>
        {{ form.submit(class="px-6 py-3 bg-cyan-600 hover:bg-cyan-500 text-white rounded-lg transition-colors font-medium") }}
      </div>
    </form>
  </div>
  
  <div class="rounded-xl bg-slate-900 border border-slate-700 p-6 mt-6">
    <h3 class="text-lg font-semibold mb-4">Information du produit</h3>
    <div class="grid grid-cols-2 gap-4 text-sm">
      <div>
        <span class="text-slate-400">SKU:</span>
        <span class="text-white font-mono ml-2">{{ product.sku }}</span>
      </div>
      <div>
        <span class="text-slate-400">Catégorie:</span>
        <span class="text-white ml-2">{{ product.category.name if product.category else 'Aucune' }}</span>
      </div>
      <div>
        <span class="text-slate-400">Prix de base:</span>
        <span class="text-cyan-400 font-medium ml-2">{{ (product.price_cents/100)|round(2) }} €</span>
      </div>
      <div>
        <span class="text-slate-400">Stock:</span>
        <span class="text-white font-medium ml-2">{{ product.stock_qty }}</span>
      </div>
    </div>
  </div>
  
  <div class="rounded-xl bg-slate-900 border border-slate-700 p-6 mt-6">
    <h3 class="text-lg font-semibold mb-4">💡 Conseils pour les variantes</h3>
    <div class="space-y-2 text-sm text-slate-300">
      <p>• Utilisez un SKU unique pour chaque variante (ex: {{ product.sku }}-RED-M)</p>
      <p>• Le prix peut être différent du produit de base selon les attributs</p>
      <p>• Les variantes sont parfaites pour gérer les tailles, couleurs, matériaux, etc.</p>
      <p>• Le stock de chaque variante est géré indépendamment</p>
    </div>
  </div>
</div>
{% endblock %}