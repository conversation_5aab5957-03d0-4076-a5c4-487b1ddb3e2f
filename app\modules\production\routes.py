from flask import render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from sqlalchemy import desc, asc, func, and_, or_
from sqlalchemy.orm import joinedload
from datetime import datetime, timedelta

from app.extensions import db
from app.models import Business, Product, User
from . import bp
from .models import (
    WorkCenter, BOM, BOMItem, ProductionStep, ProductionOrder,
    ProductionOrderStep, MaterialConsumption, QualityCheck
)
from .forms import (
    WorkCenterForm, BOMForm, BOMItemForm, ProductionStepForm,
    ProductionOrderForm, EditProductionOrderForm, ProductionOrderStepForm,
    MaterialConsumptionForm, QualityCheckForm, ProductionSearchForm,
    BulkProductionUpdateForm
)


@bp.get("/")
@login_required
def index():
    """Page d'accueil du module production avec dashboard"""
    business = Business.query.get_or_404(current_user.business_id_fk)
    
    # Statistiques générales
    total_orders = ProductionOrder.query.filter_by(business_id_fk=business.id).count()
    active_orders = ProductionOrder.query.filter(
        and_(
            ProductionOrder.business_id_fk == business.id,
            ProductionOrder.status.in_(['released', 'in_progress'])
        )
    ).count()
    
    overdue_orders = ProductionOrder.query.filter(
        and_(
            ProductionOrder.business_id_fk == business.id,
            ProductionOrder.planned_end_date < datetime.utcnow(),
            ProductionOrder.status.in_(['released', 'in_progress'])
        )
    ).count()
    
    # Ordres récents
    recent_orders = ProductionOrder.query.filter_by(
        business_id_fk=business.id
    ).order_by(desc(ProductionOrder.created_at)).limit(5).all()
    
    # Centres de travail actifs
    work_centers = WorkCenter.query.filter_by(
        business_id_fk=business.id,
        is_active=True
    ).count()
    
    return render_template(
        "production/index.html",
        total_orders=total_orders,
        active_orders=active_orders,
        overdue_orders=overdue_orders,
        recent_orders=recent_orders,
        work_centers=work_centers
    )


# === CENTRES DE TRAVAIL ===

@bp.route("/work-centers", methods=["GET", "POST"])
@login_required
def work_centers():
    """Gestion des centres de travail"""
    business = Business.query.get_or_404(current_user.business_id_fk)
    form = WorkCenterForm()
    
    if request.method == "POST" and form.validate_on_submit():
        center = WorkCenter(
            business_id_fk=business.id,
            name=form.name.data,
            description=form.description.data,
            capacity_per_hour=form.capacity_per_hour.data,
            cost_per_hour=int(form.cost_per_hour.data * 100),  # Convertir en centimes
            is_active=form.is_active.data
        )
        
        db.session.add(center)
        db.session.commit()
        
        flash(f"Centre de travail '{center.name}' créé avec succès!", "success")
        return redirect(url_for("production.work_centers"))
    
    centers = WorkCenter.query.filter_by(business_id_fk=business.id).all()
    
    return render_template(
        "production/work_centers.html",
        form=form,
        centers=centers
    )


@bp.route("/work-centers/<int:center_id>/edit", methods=["GET", "POST"])
@login_required
def edit_work_center(center_id):
    """Éditer un centre de travail"""
    business = Business.query.get_or_404(current_user.business_id_fk)
    center = WorkCenter.query.filter_by(
        id=center_id,
        business_id_fk=business.id
    ).first_or_404()
    
    form = WorkCenterForm(obj=center)
    # Convertir les centimes en euros pour l'affichage
    form.cost_per_hour.data = center.cost_per_hour / 100
    
    if request.method == "POST" and form.validate_on_submit():
        center.name = form.name.data
        center.description = form.description.data
        center.capacity_per_hour = form.capacity_per_hour.data
        center.cost_per_hour = int(form.cost_per_hour.data * 100)
        center.is_active = form.is_active.data
        
        db.session.commit()
        flash(f"Centre de travail '{center.name}' mis à jour!", "success")
        return redirect(url_for("production.work_centers"))
    
    return render_template(
        "production/edit_work_center.html",
        form=form,
        center=center
    )


@bp.post("/work-centers/<int:center_id>/delete")
@login_required
def delete_work_center(center_id):
    """Supprimer un centre de travail"""
    business = Business.query.get_or_404(current_user.business_id_fk)
    center = WorkCenter.query.filter_by(
        id=center_id,
        business_id_fk=business.id
    ).first_or_404()
    
    # Vérifier s'il y a des étapes de production liées
    if center.production_steps:
        flash("Impossible de supprimer ce centre : il est utilisé dans des étapes de production.", "error")
        return redirect(url_for("production.work_centers"))
    
    center_name = center.name
    db.session.delete(center)
    db.session.commit()
    
    flash(f"Centre de travail '{center_name}' supprimé.", "success")
    return redirect(url_for("production.work_centers"))


# === NOMENCLATURES (BOM) ===

@bp.route("/boms", methods=["GET", "POST"])
@login_required
def boms():
    """Gestion des nomenclatures (Bill of Materials)"""
    business = Business.query.get_or_404(current_user.business_id_fk)
    form = BOMForm()
    
    # Peupler les choix de produits
    products = Product.query.filter_by(business_id_fk=business.id).all()
    form.product_id.choices = [(p.id, p.name) for p in products]
    
    if request.method == "POST" and form.validate_on_submit():
        bom = BOM(
            business_id_fk=business.id,
            product_id_fk=form.product_id.data,
            name=form.name.data,
            description=form.description.data,
            version=form.version.data,
            quantity_produced=form.quantity_produced.data,
            is_active=form.is_active.data
        )
        
        db.session.add(bom)
        db.session.commit()
        
        flash(f"Nomenclature '{bom.name}' créée avec succès!", "success")
        return redirect(url_for("production.bom_details", bom_id=bom.id))
    
    boms_list = BOM.query.filter_by(
        business_id_fk=business.id
    ).options(joinedload(BOM.product)).all()
    
    return render_template(
        "production/boms.html",
        form=form,
        boms=boms_list
    )


@bp.route("/boms/<int:bom_id>")
@login_required
def bom_details(bom_id):
    """Détails d'une nomenclature avec ses composants et étapes"""
    business = Business.query.get_or_404(current_user.business_id_fk)
    bom = BOM.query.filter_by(
        id=bom_id,
        business_id_fk=business.id
    ).options(
        joinedload(BOM.product),
        joinedload(BOM.bom_items).joinedload(BOMItem.product),
        joinedload(BOM.production_steps).joinedload(ProductionStep.work_center)
    ).first_or_404()
    
    return render_template(
        "production/bom_details.html",
        bom=bom
    )


@bp.route("/boms/<int:bom_id>/items", methods=["GET", "POST"])
@login_required
def bom_items(bom_id):
    """Gestion des composants d'une nomenclature"""
    business = Business.query.get_or_404(current_user.business_id_fk)
    bom = BOM.query.filter_by(
        id=bom_id,
        business_id_fk=business.id
    ).first_or_404()
    
    form = BOMItemForm()
    
    # Peupler les choix de produits
    products = Product.query.filter_by(business_id_fk=business.id).all()
    form.product_id.choices = [(p.id, p.name) for p in products]
    
    if request.method == "POST" and form.validate_on_submit():
        item = BOMItem(
            bom_id_fk=bom.id,
            product_id_fk=form.product_id.data,
            quantity=form.quantity.data,
            unit_cost_cents=int(form.unit_cost.data * 100),
            notes=form.notes.data
        )
        
        db.session.add(item)
        db.session.commit()
        
        flash("Composant ajouté à la nomenclature!", "success")
        return redirect(url_for("production.bom_items", bom_id=bom.id))
    
    return render_template(
        "production/bom_items.html",
        bom=bom,
        form=form
    )


@bp.route("/boms/<int:bom_id>/steps", methods=["GET", "POST"])
@login_required
def bom_steps(bom_id):
    """Gestion des étapes de production d'une nomenclature"""
    business = Business.query.get_or_404(current_user.business_id_fk)
    bom = BOM.query.filter_by(
        id=bom_id,
        business_id_fk=business.id
    ).first_or_404()
    
    form = ProductionStepForm()
    
    # Peupler les choix de centres de travail
    work_centers = WorkCenter.query.filter_by(
        business_id_fk=business.id,
        is_active=True
    ).all()
    form.work_center_id.choices = [(wc.id, wc.name) for wc in work_centers]
    
    if request.method == "POST" and form.validate_on_submit():
        step = ProductionStep(
            bom_id_fk=bom.id,
            work_center_id_fk=form.work_center_id.data,
            step_number=form.step_number.data,
            name=form.name.data,
            description=form.description.data,
            setup_time_minutes=form.setup_time_minutes.data,
            run_time_minutes=form.run_time_minutes.data,
            is_optional=form.is_optional.data
        )
        
        db.session.add(step)
        db.session.commit()
        
        flash("Étape ajoutée à la nomenclature!", "success")
        return redirect(url_for("production.bom_steps", bom_id=bom.id))
    
    return render_template(
        "production/bom_steps.html",
        bom=bom,
        form=form
    )


# === ORDRES DE PRODUCTION ===

@bp.route("/orders", methods=["GET", "POST"])
@login_required
def production_orders():
    """Liste des ordres de production avec recherche et filtrage"""
    business = Business.query.get_or_404(current_user.business_id_fk)
    search_form = ProductionSearchForm()
    
    # Peupler les choix pour les filtres
    work_centers = WorkCenter.query.filter_by(
        business_id_fk=business.id,
        is_active=True
    ).all()
    search_form.work_center_id.choices = [("", "Tous les centres")] + [
        (str(wc.id), wc.name) for wc in work_centers
    ]
    
    # Construction de la requête avec filtres
    query = ProductionOrder.query.filter_by(business_id_fk=business.id)
    
    if search_form.search.data:
        search_term = f"%{search_form.search.data}%"
        query = query.join(BOM).join(Product).filter(
            or_(
                ProductionOrder.order_number.like(search_term),
                Product.name.like(search_term),
                BOM.name.like(search_term)
            )
        )
    
    if search_form.status.data:
        query = query.filter(ProductionOrder.status == search_form.status.data)
    
    if search_form.priority.data:
        query = query.filter(ProductionOrder.priority == search_form.priority.data)
    
    if search_form.date_range.data:
        now = datetime.now()
        if search_form.date_range.data == 'today':
            query = query.filter(func.date(ProductionOrder.planned_start_date) == now.date())
        elif search_form.date_range.data == 'week':
            week_start = now - timedelta(days=now.weekday())
            query = query.filter(ProductionOrder.planned_start_date >= week_start)
        elif search_form.date_range.data == 'month':
            month_start = now.replace(day=1)
            query = query.filter(ProductionOrder.planned_start_date >= month_start)
        elif search_form.date_range.data == 'overdue':
            query = query.filter(
                and_(
                    ProductionOrder.planned_end_date < now,
                    ProductionOrder.status.in_(['released', 'in_progress'])
                )
            )
    
    # Tri
    sort_mapping = {
        'created_desc': desc(ProductionOrder.created_at),
        'created_asc': asc(ProductionOrder.created_at),
        'start_date_asc': asc(ProductionOrder.planned_start_date),
        'end_date_asc': asc(ProductionOrder.planned_end_date),
        'priority': asc(ProductionOrder.priority),
        'status': asc(ProductionOrder.status),
        'order_number': asc(ProductionOrder.order_number)
    }
    
    sort_by = search_form.sort_by.data or 'created_desc'
    if sort_by in sort_mapping:
        query = query.order_by(sort_mapping[sort_by])
    
    orders = query.options(
        joinedload(ProductionOrder.bom).joinedload(BOM.product)
    ).limit(50).all()
    
    return render_template(
        "production/orders.html",
        search_form=search_form,
        orders=orders
    )


@bp.route("/orders/create", methods=["GET", "POST"])
@login_required
def create_production_order():
    """Créer un nouvel ordre de production"""
    business = Business.query.get_or_404(current_user.business_id_fk)
    form = ProductionOrderForm()
    
    # Peupler les choix de BOM
    boms = BOM.query.filter_by(
        business_id_fk=business.id,
        is_active=True
    ).options(joinedload(BOM.product)).all()
    form.bom_id.choices = [(b.id, f"{b.name} ({b.product.name})") for b in boms]
    
    if request.method == "POST" and form.validate_on_submit():
        order = ProductionOrder(
            business_id_fk=business.id,
            bom_id_fk=form.bom_id.data,
            order_number=form.order_number.data or None,  # Sera généré si vide
            quantity_to_produce=form.quantity_to_produce.data,
            priority=form.priority.data,
            planned_start_date=form.planned_start_date.data,
            planned_end_date=form.planned_end_date.data,
            notes=form.notes.data
        )
        
        # Générer un numéro d'ordre si non fourni
        if not order.order_number:
            order.order_number = order.generate_order_number()
        
        # Calculer le coût estimé
        order.calculate_estimated_cost()
        
        db.session.add(order)
        db.session.commit()
        
        flash(f"Ordre de production #{order.order_number} créé avec succès!", "success")
        return redirect(url_for("production.order_details", order_id=order.id))
    
    return render_template(
        "production/create_order.html",
        form=form
    )


@bp.route("/orders/<int:order_id>")
@login_required
def order_details(order_id):
    """Détails d'un ordre de production"""
    business = Business.query.get_or_404(current_user.business_id_fk)
    order = ProductionOrder.query.filter_by(
        id=order_id,
        business_id_fk=business.id
    ).options(
        joinedload(ProductionOrder.bom).joinedload(BOM.product),
        joinedload(ProductionOrder.order_steps).joinedload(ProductionOrderStep.production_step),
        joinedload(ProductionOrder.material_consumptions).joinedload(MaterialConsumption.product)
    ).first_or_404()
    
    return render_template(
        "production/order_details.html",
        order=order
    )


@bp.route("/orders/<int:order_id>/edit", methods=["GET", "POST"])
@login_required
def edit_production_order(order_id):
    """Éditer un ordre de production"""
    business = Business.query.get_or_404(current_user.business_id_fk)
    order = ProductionOrder.query.filter_by(
        id=order_id,
        business_id_fk=business.id
    ).first_or_404()
    
    form = EditProductionOrderForm(obj=order)
    # Convertir les centimes en euros
    form.actual_cost.data = order.actual_cost_cents / 100 if order.actual_cost_cents else 0
    
    if request.method == "POST" and form.validate_on_submit():
        order.status = form.status.data
        order.quantity_produced = form.quantity_produced.data
        order.actual_start_date = form.actual_start_date.data
        order.actual_end_date = form.actual_end_date.data
        order.actual_cost_cents = int(form.actual_cost.data * 100) if form.actual_cost.data else 0
        order.notes = form.notes.data
        
        db.session.commit()
        flash(f"Ordre #{order.order_number} mis à jour!", "success")
        return redirect(url_for("production.order_details", order_id=order.id))
    
    return render_template(
        "production/edit_order.html",
        form=form,
        order=order
    )


@bp.post("/orders/<int:order_id>/delete")
@login_required
def delete_production_order(order_id):
    """Supprimer un ordre de production"""
    business = Business.query.get_or_404(current_user.business_id_fk)
    order = ProductionOrder.query.filter_by(
        id=order_id,
        business_id_fk=business.id
    ).first_or_404()
    
    if order.status in ['in_progress', 'completed']:
        flash("Impossible de supprimer un ordre en cours ou terminé.", "error")
        return redirect(url_for("production.order_details", order_id=order.id))
    
    order_number = order.order_number
    db.session.delete(order)
    db.session.commit()
    
    flash(f"Ordre #{order_number} supprimé.", "success")
    return redirect(url_for("production.production_orders"))


# === API ENDPOINTS ===

@bp.get("/api/orders/<int:order_id>/progress")
@login_required
def api_order_progress(order_id):
    """API pour obtenir la progression d'un ordre"""
    business = Business.query.get_or_404(current_user.business_id_fk)
    order = ProductionOrder.query.filter_by(
        id=order_id,
        business_id_fk=business.id
    ).first_or_404()
    
    return jsonify({
        'order_id': order.id,
        'order_number': order.order_number,
        'status': order.status,
        'progress_percentage': order.progress_percentage,
        'quantity_to_produce': float(order.quantity_to_produce),
        'quantity_produced': float(order.quantity_produced),
        'is_overdue': order.is_overdue
    })


@bp.get("/api/work-centers/<int:center_id>/capacity")
@login_required
def api_work_center_capacity(center_id):
    """API pour obtenir la capacité d'un centre de travail"""
    business = Business.query.get_or_404(current_user.business_id_fk)
    center = WorkCenter.query.filter_by(
        id=center_id,
        business_id_fk=business.id
    ).first_or_404()
    
    # Calculer la charge actuelle (ordres en cours)
    current_load = db.session.query(
        func.sum(ProductionStep.setup_time_minutes + ProductionStep.run_time_minutes)
    ).join(ProductionOrderStep).join(ProductionOrder).filter(
        and_(
            ProductionStep.work_center_id_fk == center.id,
            ProductionOrder.status.in_(['released', 'in_progress']),
            ProductionOrderStep.status.in_(['pending', 'in_progress'])
        )
    ).scalar() or 0
    
    return jsonify({
        'center_id': center.id,
        'name': center.name,
        'capacity_per_hour': float(center.capacity_per_hour),
        'cost_per_hour': center.cost_per_hour / 100,
        'current_load_minutes': current_load,
        'utilization_percentage': min(100, (current_load / 60) / float(center.capacity_per_hour) * 100)
    })