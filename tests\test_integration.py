"""Tests d'intégration pour l'application POS"""
import unittest
from datetime import datetime, timedelta
from tests import BaseTestCase

class IntegrationTestCase(BaseTestCase):
    """Tests d'intégration pour l'application"""
    
    def create_test_business(self):
        """Helper pour créer une entreprise de test"""
        from app.modules.accounts.models import Business, BusinessType
        
        # Créer un type d'entreprise
        business_type = BusinessType(
            name='Test Business Type',
            description='Type for integration tests'
        )
        self.db.session.add(business_type)
        self.db.session.commit()
        
        # Créer une entreprise
        business = Business(
            name='Test Business',
            business_type_id=business_type.id,
            address='123 Test Street',
            phone='555-0123',
            email='<EMAIL>'
        )
        self.db.session.add(business)
        self.db.session.commit()
        
        return business
    
    def create_test_user(self, business_id):
        """Helper pour créer un utilisateur de test"""
        from app.modules.accounts.models import User
        
        user = User(
            username='testuser',
            email='<EMAIL>',
            first_name='Test',
            last_name='User',
            business_id_fk=business_id
        )
        user.set_password('testpassword')
        self.db.session.add(user)
        self.db.session.commit()
        
        return user
    
    def create_test_products(self, business_id, category_id):
        """Helper pour créer des produits de test"""
        from app.modules.catalog.models import Product
        
        products = []
        for i in range(5):
            product = Product(
                sku=f'TEST{i:03d}',
                name=f'Test Product {i}',
                description=f'Description for test product {i}',
                category_id_fk=category_id,
                price_cents=(i + 1) * 1000,  # 10.00€, 20.00€, etc.
                cost_cents=(i + 1) * 500,    # 5.00€, 10.00€, etc.
                business_id_fk=business_id
            )
            self.db.session.add(product)
            products.append(product)
        
        self.db.session.commit()
        return products
    
    def test_complete_order_flow(self):
        """Test du flux complet de commande"""
        # Créer une entreprise de test
        business = self.create_test_business()
        
        # Créer un utilisateur de test
        user = self.create_test_user(business.id)
        
        # Créer une catégorie de test
        from app.modules.catalog.models import Category
        category = Category(
            name='Test Category',
            business_id_fk=business.id
        )
        self.db.session.add(category)
        self.db.session.commit()
        
        # Créer des produits de test
        products = self.create_test_products(business.id, category.id)
        
        # Créer une commande POS
        from app.modules.pos.models import POSOrder, POSOrderItem
        order = POSOrder(
            business_id_fk=business.id,
            customer_name='Integration Test Customer',
            total_cents=3000,  # 30.00€
            status='completed'
        )
        self.db.session.add(order)
        self.db.session.commit()
        
        # Ajouter des items à la commande
        for i, product in enumerate(products[:3]):
            order_item = POSOrderItem(
                order_id_fk=order.id,
                product_id_fk=product.id,
                quantity=1,
                unit_price_cents=product.price_cents,
                total_price_cents=product.price_cents
            )
            self.db.session.add(order_item)
        
        self.db.session.commit()
        
        # Vérifier que la commande a été créée correctement
        retrieved_order = POSOrder.query.get(order.id)
        self.assertIsNotNone(retrieved_order)
        self.assertEqual(retrieved_order.customer_name, 'Integration Test Customer')
        self.assertEqual(len(retrieved_order.items), 3)
        self.assertEqual(retrieved_order.total, 30.00)
        
        # Vérifier que les items de commande sont corrects
        total_items_value = sum(item.total_price for item in retrieved_order.items)
        self.assertEqual(total_items_value, 30.00)
    
    def test_inventory_update_on_sale(self):
        """Test de la mise à jour de l'inventaire lors d'une vente"""
        # Créer une entreprise de test
        business = self.create_test_business()
        
        # Créer une catégorie de test
        from app.modules.catalog.models import Category
        category = Category(
            name='Inventory Test Category',
            business_id_fk=business.id
        )
        self.db.session.add(category)
        self.db.session.commit()
        
        # Créer un produit avec stock
        from app.modules.catalog.models import Product
        product = Product(
            sku='INV001',
            name='Inventory Test Product',
            description='Product for inventory testing',
            category_id_fk=category.id,
            price_cents=1500,  # 15.00€
            cost_cents=1000,   # 10.00€
            stock_qty=100,     # 100 unités en stock
            business_id_fk=business.id
        )
        self.db.session.add(product)
        self.db.session.commit()
        
        # Vérifier le stock initial
        initial_stock = product.stock_qty
        self.assertEqual(initial_stock, 100)
        
        # Créer une commande qui utilise ce produit
        from app.modules.pos.models import POSOrder, POSOrderItem
        order = POSOrder(
            business_id_fk=business.id,
            customer_name='Inventory Test Customer',
            total_cents=1500,
            status='completed'
        )
        self.db.session.add(order)
        self.db.session.commit()
        
        # Ajouter un item à la commande
        order_item = POSOrderItem(
            order_id_fk=order.id,
            product_id_fk=product.id,
            quantity=5,        # Vendre 5 unités
            unit_price_cents=1500,
            total_price_cents=7500  # 75.00€
        )
        self.db.session.add(order_item)
        self.db.session.commit()
        
        # Recharger le produit pour voir la mise à jour du stock
        updated_product = Product.query.get(product.id)
        
        # Vérifier que le stock a été mis à jour
        self.assertEqual(updated_product.stock_qty, 95)  # 100 - 5 = 95
    
    def test_user_authentication_flow(self):
        """Test du flux d'authentification utilisateur"""
        # Créer une entreprise de test
        business = self.create_test_business()
        
        # Créer un utilisateur de test
        from app.modules.accounts.models import User
        user = User(
            username='auth_test_user',
            email='<EMAIL>',
            first_name='Auth',
            last_name='Test',
            business_id_fk=business.id
        )
        user.set_password('auth_test_password')
        self.db.session.add(user)
        self.db.session.commit()
        
        # Tester l'authentification
        retrieved_user = User.query.filter_by(username='auth_test_user').first()
        self.assertIsNotNone(retrieved_user)
        self.assertTrue(retrieved_user.check_password('auth_test_password'))
        self.assertFalse(retrieved_user.check_password('wrong_password'))
        
        # Tester la représentation de l'utilisateur
        self.assertIn('auth_test_user', str(retrieved_user))
        self.assertIn('Auth', repr(retrieved_user))

if __name__ == '__main__':
    unittest.main()