{% extends 'base.html' %}
{% block title %}Templates de Rapports{% endblock %}

{% block head %}
<style>
    .templates-page {
        background: linear-gradient(135deg, #6B73FF 0%, #000DFF 100%);
        min-height: 100vh;
        padding: 20px 0;
    }
    
    .templates-content {
        background: white;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        padding: 30px;
        margin-top: 20px;
    }
    
    .template-card {
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 20px;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }
    
    .template-card:hover {
        border-color: #6B73FF;
        box-shadow: 0 8px 25px rgba(107, 115, 255, 0.15);
        transform: translateY(-2px);
    }
    
    .template-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: linear-gradient(45deg, #6B73FF, #000DFF);
    }
    
    .template-title {
        color: #333;
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 10px;
    }
    
    .template-description {
        color: #666;
        margin-bottom: 15px;
        line-height: 1.5;
    }
    
    .template-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 20px;
        padding-top: 15px;
        border-top: 1px solid #f0f0f0;
    }
    
    .template-type {
        padding: 5px 15px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
    }
    
    .type-sales { background: #e3f2fd; color: #1976d2; }
    .type-financial { background: #f3e5f5; color: #7b1fa2; }
    .type-inventory { background: #e8f5e8; color: #388e3c; }
    .type-customer { background: #fff3e0; color: #f57c00; }
    .type-supplier { background: #fce4ec; color: #c2185b; }
    .type-production { background: #f1f8e9; color: #689f38; }
    
    .btn-template {
        background: linear-gradient(45deg, #6B73FF, #000DFF);
        border: none;
        color: white;
        padding: 8px 20px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
        margin: 2px;
    }
    
    .btn-template:hover {
        transform: translateY(-1px);
        box-shadow: 0 5px 15px rgba(107, 115, 255, 0.4);
        color: white;
        text-decoration: none;
    }
    
    .btn-template.btn-secondary {
        background: #6c757d;
    }
    
    .btn-template.btn-success {
        background: #28a745;
    }
    
    .btn-template.btn-danger {
        background: #dc3545;
    }
    
    .search-bar {
        background: white;
        border-radius: 25px;
        padding: 15px 25px;
        margin-bottom: 30px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    
    .search-input {
        border: none;
        outline: none;
        font-size: 1.1rem;
        width: 100%;
        background: transparent;
    }
    
    .filters {
        display: flex;
        gap: 15px;
        margin-bottom: 20px;
        flex-wrap: wrap;
    }
    
    .filter-select {
        border: 1px solid #ddd;
        border-radius: 20px;
        padding: 8px 15px;
        background: white;
        font-size: 0.9rem;
    }
    
    .pagination-container {
        display: flex;
        justify-content: center;
        margin-top: 30px;
    }
    
    .no-templates {
        text-align: center;
        padding: 60px 20px;
        color: #666;
    }
    
    .no-templates i {
        font-size: 4rem;
        margin-bottom: 20px;
        color: #ddd;
    }
</style>
{% endblock %}

{% block content %}
<div class="templates-page">
    <div class="container-fluid">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="text-white text-center mb-4">
                    <i class="fas fa-file-alt me-3"></i>
                    Templates de Rapports
                </h1>
                
                <div class="text-center">
                    <a href="{{ url_for('reports.new_template') }}" class="btn-template">
                        <i class="fas fa-plus me-2"></i>Nouveau Template
                    </a>
                    <a href="{{ url_for('reports.index') }}" class="btn-template btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Retour
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Contenu principal -->
        <div class="templates-content">
            <!-- Barre de recherche et filtres -->
            <div class="search-bar">
                <form method="GET" class="d-flex align-items-center">
                    <i class="fas fa-search text-muted me-3"></i>
                    <input type="text" name="search" class="search-input" 
                           placeholder="Rechercher un template..." 
                           value="{{ request.args.get('search', '') }}">
                </form>
            </div>
            
            <div class="filters">
                <form method="GET" class="d-flex gap-3 align-items-center flex-wrap">
                    <input type="hidden" name="search" value="{{ request.args.get('search', '') }}">
                    
                    <select name="report_type" class="filter-select" onchange="this.form.submit()">
                        <option value="">Tous les types</option>
                        <option value="sales" {{ 'selected' if request.args.get('report_type') == 'sales' }}>Ventes</option>
                        <option value="financial" {{ 'selected' if request.args.get('report_type') == 'financial' }}>Financier</option>
                        <option value="inventory" {{ 'selected' if request.args.get('report_type') == 'inventory' }}>Inventaire</option>
                        <option value="customer" {{ 'selected' if request.args.get('report_type') == 'customer' }}>Clients</option>
                        <option value="supplier" {{ 'selected' if request.args.get('report_type') == 'supplier' }}>Fournisseurs</option>
                        <option value="production" {{ 'selected' if request.args.get('report_type') == 'production' }}>Production</option>
                    </select>
                    
                    <button type="submit" class="btn-template btn-secondary">
                        <i class="fas fa-filter me-2"></i>Filtrer
                    </button>
                    
                    {% if request.args.get('search') or request.args.get('report_type') %}
                    <a href="{{ url_for('reports.list_templates') }}" class="btn-template btn-secondary">
                        <i class="fas fa-times me-2"></i>Effacer
                    </a>
                    {% endif %}
                </form>
            </div>
            
            <!-- Liste des templates -->
            {% if templates.items %}
                {% for template in templates.items %}
                <div class="template-card">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <h3 class="template-title">{{ template.name }}</h3>
                            <p class="template-description">
                                {{ template.description or 'Aucune description disponible' }}
                            </p>
                            
                            <div class="template-meta">
                                <div class="d-flex align-items-center gap-3">
                                    <span class="template-type type-{{ template.report_type.value }}">
                                        {{ template.report_type.value }}
                                    </span>
                                    
                                    <small class="text-muted">
                                        <i class="fas fa-user me-1"></i>
                                        {{ template.created_by.username if template.created_by else 'Système' }}
                                    </small>
                                    
                                    <small class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>
                                        {{ template.created_at.strftime('%d/%m/%Y') }}
                                    </small>
                                    
                                    {% if template.is_public %}
                                    <span class="badge bg-success">
                                        <i class="fas fa-globe me-1"></i>Public
                                    </span>
                                    {% else %}
                                    <span class="badge bg-secondary">
                                        <i class="fas fa-lock me-1"></i>Privé
                                    </span>
                                    {% endif %}
                                </div>
                                
                                <div class="template-actions">
                                    <a href="{{ url_for('reports.view_template', id=template.id) }}" 
                                       class="btn-template">
                                        <i class="fas fa-eye me-2"></i>Voir
                                    </a>
                                    
                                    <button onclick="executeTemplate({{ template.id }})" 
                                            class="btn-template btn-success">
                                        <i class="fas fa-play me-2"></i>Exécuter
                                    </button>
                                    
                                    {% if template.created_by_id == current_user.id or current_user.is_admin %}
                                    <a href="{{ url_for('reports.edit_template', id=template.id) }}" 
                                       class="btn-template btn-secondary">
                                        <i class="fas fa-edit me-2"></i>Modifier
                                    </a>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
                
                <!-- Pagination -->
                {% if templates.pages > 1 %}
                <div class="pagination-container">
                    <nav aria-label="Pagination des templates">
                        <ul class="pagination">
                            {% if templates.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('reports.list_templates', page=templates.prev_num, search=request.args.get('search'), report_type=request.args.get('report_type')) }}">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                            {% endif %}
                            
                            {% for page_num in templates.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != templates.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('reports.list_templates', page=page_num, search=request.args.get('search'), report_type=request.args.get('report_type')) }}">
                                            {{ page_num }}
                                        </a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if templates.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('reports.list_templates', page=templates.next_num, search=request.args.get('search'), report_type=request.args.get('report_type')) }}">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
                {% endif %}
                
            {% else %}
                <div class="no-templates">
                    <i class="fas fa-file-alt"></i>
                    <h3>Aucun template trouvé</h3>
                    <p class="text-muted">
                        {% if request.args.get('search') or request.args.get('report_type') %}
                        Aucun template ne correspond à vos critères de recherche.
                        {% else %}
                        Commencez par créer votre premier template de rapport.
                        {% endif %}
                    </p>
                    <a href="{{ url_for('reports.new_template') }}" class="btn-template mt-3">
                        <i class="fas fa-plus me-2"></i>Créer un Template
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Modal d'exécution -->
<div class="modal fade" id="executionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Exécution du Template</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="executionStatus">
                    <div class="d-flex align-items-center">
                        <div class="spinner-border spinner-border-sm me-3" role="status"></div>
                        <span>Exécution en cours...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function executeTemplate(templateId) {
    // Afficher le modal
    const modal = new bootstrap.Modal(document.getElementById('executionModal'));
    modal.show();
    
    // Réinitialiser le statut
    document.getElementById('executionStatus').innerHTML = `
        <div class="d-flex align-items-center">
            <div class="spinner-border spinner-border-sm me-3" role="status"></div>
            <span>Exécution en cours...</span>
        </div>
    `;
    
    // Faire l'appel AJAX
    fetch(`/reports/templates/${templateId}/execute`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
        },
        body: JSON.stringify({})
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            document.getElementById('executionStatus').innerHTML = `
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    ${data.message}
                </div>
                <div class="text-center">
                    <a href="/reports/executions/${data.execution_id}" class="btn-template">
                        Voir le Résultat
                    </a>
                </div>
            `;
        } else {
            document.getElementById('executionStatus').innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    ${data.message}
                </div>
            `;
        }
    })
    .catch(error => {
        document.getElementById('executionStatus').innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle me-2"></i>
                Erreur lors de l'exécution: ${error.message}
            </div>
        `;
    });
}

// Auto-submit du formulaire de recherche avec délai
let searchTimeout;
document.querySelector('.search-input').addEventListener('input', function() {
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(() => {
        this.closest('form').submit();
    }, 500);
});
</script>
{% endblock %}