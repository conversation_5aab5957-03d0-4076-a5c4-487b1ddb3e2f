{% extends "base.html" %}

{% block title %}Optimisation des Stocks{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-boxes"></i> Optimisation des Stocks</h1>
        <button id="analyzeInventoryBtn" class="btn btn-primary">
            <i class="fas fa-sync"></i> Analyser les Stocks
        </button>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Alertes de Stock</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle"></i> Stocks Faibles</h6>
                        <div id="lowStockAlerts">
                            <p class="text-muted">Aucune alerte de stock faible.</p>
                        </div>
                    </div>
                    
                    <div class="alert alert-info mt-3">
                        <h6><i class="fas fa-info-circle"></i> Stocks Élevés</h6>
                        <div id="highStockAlerts">
                            <p class="text-muted">Aucune alerte de stock élevé.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Métriques de Stock</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <h3 id="turnoverRate">-</h3>
                            <p class="text-muted">Taux de Rotation</p>
                        </div>
                        <div class="col-6">
                            <h3 id="totalIn">-</h3>
                            <p class="text-muted">Total Entrées</p>
                        </div>
                    </div>
                    <div class="row text-center mt-3">
                        <div class="col-6">
                            <h3 id="totalOut">-</h3>
                            <p class="text-muted">Total Sorties</p>
                        </div>
                        <div class="col-6">
                            <h3 id="efficiency">-</h3>
                            <p class="text-muted">Efficacité (%)</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Recommandations d'Optimisation</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6><i class="fas fa-calculator"></i> Quantité Économique de Commande (EOQ)</h6>
                                    <p class="mb-2">Calcul optimal des commandes pour minimiser les coûts.</p>
                                    <button class="btn btn-sm btn-outline-primary" id="calculateEOQBtn">
                                        Calculer EOQ
                                    </button>
                                    <div id="eoqResult" class="mt-2 d-none">
                                        <p class="mb-0"><strong>EOQ:</strong> <span id="eoqValue">-</span> unités</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6><i class="fas fa-shield-alt"></i> Stock de Sécurité</h6>
                                    <p class="mb-2">Calcul du stock de sécurité pour éviter les ruptures.</p>
                                    <button class="btn btn-sm btn-outline-success" id="calculateSafetyStockBtn">
                                        Calculer Stock de Sécurité
                                    </button>
                                    <div id="safetyStockResult" class="mt-2 d-none">
                                        <p class="mb-0"><strong>Stock de Sécurité:</strong> <span id="safetyStockValue">-</span> unités</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6><i class="fas fa-redo"></i> Point de Commande</h6>
                                    <p class="mb-2">Niveau de stock déclenchant une nouvelle commande.</p>
                                    <button class="btn btn-sm btn-outline-warning" id="calculateReorderPointBtn">
                                        Calculer Point de Commande
                                    </button>
                                    <div id="reorderPointResult" class="mt-2 d-none">
                                        <p class="mb-0"><strong>Point de Commande:</strong> <span id="reorderPointValue">-</span> unités</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Prévisions de Demandes</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Produit</label>
                                <select class="form-select" id="forecastProductSelect">
                                    <option value="">Sélectionner un produit...</option>
                                    <!-- Les produits seront chargés dynamiquement -->
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Période</label>
                                <select class="form-select" id="forecastPeriodSelect">
                                    <option value="7">7 jours</option>
                                    <option value="30" selected>30 jours</option>
                                    <option value="90">90 jours</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <button id="forecastDemandBtn" class="btn btn-info" disabled>
                        <i class="fas fa-chart-line"></i> Prévoir la Demande
                    </button>
                    
                    <div id="forecastResult" class="mt-4 d-none">
                        <h6>Prévision de Demande:</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>Demande Prévue:</strong> <span id="forecastedDemand">0</span> unités</p>
                                <p><strong>Intervalle de Confiance:</strong> ±<span id="confidenceInterval">0</span> unités</p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Date de Prochaine Commande:</strong> <span id="nextOrderDate">-</span></p>
                                <p><strong>Quantité Recommandée:</strong> <span id="recommendedQuantity">0</span> unités</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Charger la liste des produits
    function loadProducts() {
        // Dans une vraie application, cela viendrait de l'API
        // Pour l'instant, nous simulons quelques produits
        const products = [
            {id: 1, name: 'Burger Classique', sku: 'BURG001'},
            {id: 2, name: 'Frites', sku: 'FRIT001'},
            {id: 3, name: 'Coca-Cola', sku: 'COKE001'},
            {id: 4, name: 'Salade César', sku: 'SALA001'},
            {id: 5, name: 'Pizza Margherita', sku: 'PIZZ001'}
        ];
        
        const select = document.getElementById('forecastProductSelect');
        products.forEach(product => {
            const option = document.createElement('option');
            option.value = product.id;
            option.textContent = `${product.name} (${product.sku})`;
            select.appendChild(option);
        });
    }
    
    // Activer le bouton de prévision quand un produit est sélectionné
    document.getElementById('forecastProductSelect').addEventListener('change', function() {
        document.getElementById('forecastDemandBtn').disabled = !this.value;
    });
    
    // Analyser les stocks
    document.getElementById('analyzeInventoryBtn').addEventListener('click', function() {
        fetch('/ai/api/inventory_optimization/analyze')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const analysis = data.analysis;
                
                // Mettre à jour les métriques
                document.getElementById('turnoverRate').textContent = analysis.turnover_rate.toFixed(2);
                document.getElementById('totalIn').textContent = analysis.total_in;
                document.getElementById('totalOut').textContent = analysis.total_out;
                
                // Calculer l'efficacité
                const efficiency = analysis.total_out > 0 ? 
                    (analysis.total_out / (analysis.total_in + analysis.total_out) * 100).toFixed(1) : '0.0';
                document.getElementById('efficiency').textContent = efficiency;
                
                // Afficher les alertes de stock faible
                const lowStockAlerts = document.getElementById('lowStockAlerts');
                if (analysis.low_stock_products && analysis.low_stock_products.length > 0) {
                    lowStockAlerts.innerHTML = '';
                    analysis.low_stock_products.forEach(product => {
                        const alert = document.createElement('div');
                        alert.className = 'alert alert-warning mb-2';
                        alert.innerHTML = `
                            <strong>${product.name}</strong> (${product.sku})<br>
                            <small>Stock actuel: ${product.current_stock} unités</small>
                        `;
                        lowStockAlerts.appendChild(alert);
                    });
                } else {
                    lowStockAlerts.innerHTML = '<p class="text-muted">Aucune alerte de stock faible.</p>';
                }
                
                // Afficher les alertes de stock élevé
                const highStockAlerts = document.getElementById('highStockAlerts');
                if (analysis.high_stock_products && analysis.high_stock_products.length > 0) {
                    highStockAlerts.innerHTML = '';
                    analysis.high_stock_products.forEach(product => {
                        const alert = document.createElement('div');
                        alert.className = 'alert alert-info mb-2';
                        alert.innerHTML = `
                            <strong>${product.name}</strong> (${product.sku})<br>
                            <small>Stock actuel: ${product.current_stock} unités</small>
                        `;
                        highStockAlerts.appendChild(alert);
                    });
                } else {
                    highStockAlerts.innerHTML = '<p class="text-muted">Aucune alerte de stock élevé.</p>';
                }
            } else {
                alert('Erreur: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur lors de l\'analyse des stocks');
        });
    });
    
    // Calculer la Quantité Économique de Commande (EOQ)
    document.getElementById('calculateEOQBtn').addEventListener('click', function() {
        // Simulation de calcul EOQ
        const eoq = Math.round(Math.sqrt(2 * 1000 * 50 / 2)); // Formule: √(2DS/H)
        document.getElementById('eoqValue').textContent = eoq;
        document.getElementById('eoqResult').classList.remove('d-none');
    });
    
    // Calculer le Stock de Sécurité
    document.getElementById('calculateSafetyStockBtn').addEventListener('click', function() {
        // Simulation de calcul du stock de sécurité
        const safetyStock = Math.round(1.65 * 10 * Math.sqrt(5)); // Formule: Z × σ × √(L)
        document.getElementById('safetyStockValue').textContent = safetyStock;
        document.getElementById('safetyStockResult').classList.remove('d-none');
    });
    
    // Calculer le Point de Commande
    document.getElementById('calculateReorderPointBtn').addEventListener('click', function() {
        // Simulation de calcul du point de commande
        const reorderPoint = Math.round(20 * 5 + 37); // Formule: d × L + SS
        document.getElementById('reorderPointValue').textContent = reorderPoint;
        document.getElementById('reorderPointResult').classList.remove('d-none');
    });
    
    // Prévoir la demande
    document.getElementById('forecastDemandBtn').addEventListener('click', function() {
        // Simulation de prévision de demande
        document.getElementById('forecastedDemand').textContent = '150';
        document.getElementById('confidenceInterval').textContent = '25';
        document.getElementById('nextOrderDate').textContent = '2023-06-15';
        document.getElementById('recommendedQuantity').textContent = '200';
        
        document.getElementById('forecastResult').classList.remove('d-none');
    });
    
    // Charger les produits quand la page est prête
    document.addEventListener('DOMContentLoaded', function() {
        loadProducts();
    });
</script>
{% endblock %}