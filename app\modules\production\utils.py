"""
Utilitaires pour la planification et les calculs de production
"""

from datetime import datetime, timedelta
from decimal import Decimal
from typing import List, Dict, Optional, Tuple
from sqlalchemy import and_, or_, func
from sqlalchemy.orm import joinedload

from app.extensions import db
from .models import (
    WorkCenter, BOM, BOMItem, ProductionStep, ProductionOrder,
    ProductionOrderStep, MaterialConsumption, QualityCheck
)


class ProductionPlanner:
    """Planificateur de production avec optimisation des ressources"""
    
    def __init__(self, business_id: int):
        self.business_id = business_id
    
    def calculate_production_time(self, bom_id: int, quantity: Decimal) -> Dict:
        """
        Calcule le temps total de production pour une BOM et une quantité donnée
        
        Returns:
            Dict avec total_minutes, setup_time, run_time, critical_path
        """
        bom = BOM.query.get(bom_id)
        if not bom:
            return {"error": "BOM non trouvée"}
        
        steps = ProductionStep.query.filter_by(bom_id_fk=bom_id).order_by(
            ProductionStep.step_number
        ).all()
        
        total_setup_time = 0
        total_run_time = 0
        critical_path = []
        
        for step in steps:
            if not step.is_optional:
                setup_time = step.setup_time_minutes
                run_time = int(step.run_time_minutes * float(quantity))
                
                total_setup_time += setup_time
                total_run_time += run_time
                
                critical_path.append({
                    'step_number': step.step_number,
                    'name': step.name,
                    'work_center': step.work_center.name if step.work_center else 'Non défini',
                    'setup_time': setup_time,
                    'run_time': run_time,
                    'total_time': setup_time + run_time
                })
        
        return {
            'total_minutes': total_setup_time + total_run_time,
            'setup_time': total_setup_time,
            'run_time': total_run_time,
            'critical_path': critical_path,
            'estimated_hours': (total_setup_time + total_run_time) / 60
        }
    
    def calculate_material_requirements(self, bom_id: int, quantity: Decimal) -> List[Dict]:
        """
        Calcule les besoins en matériaux pour une production
        
        Returns:
            Liste des matériaux avec quantités requises
        """
        bom_items = BOMItem.query.filter_by(bom_id_fk=bom_id).options(
            joinedload(BOMItem.product)
        ).all()
        
        requirements = []
        total_cost = 0
        
        for item in bom_items:
            required_qty = item.quantity * quantity
            item_cost = int(float(required_qty) * item.unit_cost_cents)
            total_cost += item_cost
            
            requirements.append({
                'product_id': item.product_id_fk,
                'product_name': item.product.name if item.product else 'Produit supprimé',
                'unit_quantity': item.quantity,
                'required_quantity': required_qty,
                'unit_cost_cents': item.unit_cost_cents,
                'total_cost_cents': item_cost,
                'notes': item.notes
            })\n        \n        return {\n            'materials': requirements,\n            'total_cost_cents': total_cost,\n            'total_items': len(requirements)\n        }\n    \n    def find_optimal_start_date(self, bom_id: int, quantity: Decimal, \n                               required_date: datetime = None) -> Dict:\n        \"\"\"\n        Trouve la date de début optimale en tenant compte des capacités\n        \n        Args:\n            bom_id: ID de la BOM\n            quantity: Quantité à produire\n            required_date: Date de fin souhaitée (optionnel)\n        \n        Returns:\n            Dict avec suggested_start_date, suggested_end_date, warnings\n        \"\"\"\n        production_time = self.calculate_production_time(bom_id, quantity)\n        \n        if 'error' in production_time:\n            return production_time\n        \n        total_hours = production_time['estimated_hours']\n        \n        # Si aucune date requise, commencer dès que possible\n        if not required_date:\n            suggested_start = datetime.now() + timedelta(hours=1)  # Buffer d'1 heure\n        else:\n            # Calculer en arrière depuis la date requise\n            suggested_start = required_date - timedelta(hours=total_hours + 2)  # Buffer de 2h\n        \n        suggested_end = suggested_start + timedelta(hours=total_hours)\n        \n        # Vérifier les conflits avec d'autres ordres\n        conflicts = self._check_capacity_conflicts(bom_id, suggested_start, suggested_end)\n        \n        warnings = []\n        if conflicts:\n            warnings.append(f\"{len(conflicts)} conflit(s) de capacité détecté(s)\")\n            # Reprogrammer après les conflits\n            latest_conflict_end = max(conflict['end_date'] for conflict in conflicts)\n            suggested_start = latest_conflict_end + timedelta(hours=1)\n            suggested_end = suggested_start + timedelta(hours=total_hours)\n        \n        # Vérifier les heures ouvrables (8h-18h, lun-ven)\n        suggested_start, suggested_end, work_warnings = self._adjust_for_working_hours(\n            suggested_start, suggested_end\n        )\n        warnings.extend(work_warnings)\n        \n        return {\n            'suggested_start_date': suggested_start,\n            'suggested_end_date': suggested_end,\n            'estimated_duration_hours': total_hours,\n            'warnings': warnings,\n            'conflicts': conflicts\n        }\n    \n    def _check_capacity_conflicts(self, bom_id: int, start_date: datetime, \n                                end_date: datetime) -> List[Dict]:\n        \"\"\"\n        Vérifie les conflits de capacité avec d'autres ordres\n        \"\"\"\n        # Récupérer les centres de travail utilisés par cette BOM\n        work_centers = db.session.query(WorkCenter).join(\n            ProductionStep, WorkCenter.id == ProductionStep.work_center_id_fk\n        ).filter(\n            ProductionStep.bom_id_fk == bom_id\n        ).all()\n        \n        conflicts = []\n        \n        for wc in work_centers:\n            # Chercher les ordres qui se chevauchent\n            overlapping_orders = db.session.query(ProductionOrder).join(\n                ProductionOrderStep\n            ).join(\n                ProductionStep\n            ).filter(\n                and_(\n                    ProductionStep.work_center_id_fk == wc.id,\n                    ProductionOrder.status.in_(['planned', 'released', 'in_progress']),\n                    or_(\n                        and_(\n                            ProductionOrder.planned_start_date <= start_date,\n                            ProductionOrder.planned_end_date >= start_date\n                        ),\n                        and_(\n                            ProductionOrder.planned_start_date <= end_date,\n                            ProductionOrder.planned_end_date >= end_date\n                        ),\n                        and_(\n                            ProductionOrder.planned_start_date >= start_date,\n                            ProductionOrder.planned_end_date <= end_date\n                        )\n                    )\n                )\n            ).all()\n            \n            for order in overlapping_orders:\n                conflicts.append({\n                    'work_center_id': wc.id,\n                    'work_center_name': wc.name,\n                    'order_number': order.order_number,\n                    'start_date': order.planned_start_date,\n                    'end_date': order.planned_end_date\n                })\n        \n        return conflicts\n    \n    def _adjust_for_working_hours(self, start_date: datetime, \n                                end_date: datetime) -> Tuple[datetime, datetime, List[str]]:\n        \"\"\"\n        Ajuste les dates pour respecter les heures ouvrables\n        \"\"\"\n        warnings = []\n        \n        # Heures ouvrables: 8h-18h, Lun-Ven (weekday 0-4)\n        working_start_hour = 8\n        working_end_hour = 18\n        \n        # Ajuster la date de début\n        if start_date.weekday() > 4:  # Weekend\n            # Passer au lundi suivant\n            days_to_add = 7 - start_date.weekday()\n            start_date = start_date.replace(\n                hour=working_start_hour, minute=0, second=0, microsecond=0\n            ) + timedelta(days=days_to_add)\n            warnings.append(\"Date de début ajustée (éviter le weekend)\")\n        \n        elif start_date.hour < working_start_hour:\n            start_date = start_date.replace(\n                hour=working_start_hour, minute=0, second=0, microsecond=0\n            )\n            warnings.append(\"Heure de début ajustée (avant les heures ouvrables)\")\n        \n        elif start_date.hour >= working_end_hour:\n            # Passer au jour ouvrable suivant\n            start_date = start_date.replace(\n                hour=working_start_hour, minute=0, second=0, microsecond=0\n            ) + timedelta(days=1)\n            \n            if start_date.weekday() > 4:\n                days_to_add = 7 - start_date.weekday()\n                start_date += timedelta(days=days_to_add)\n            \n            warnings.append(\"Date de début reportée au jour ouvrable suivant\")\n        \n        # Recalculer la date de fin\n        duration = end_date - start_date\n        end_date = start_date + duration\n        \n        return start_date, end_date, warnings\n    \n    def optimize_production_sequence(self, order_ids: List[int]) -> List[Dict]:\n        \"\"\"\n        Optimise la séquence de plusieurs ordres de production\n        \n        Args:\n            order_ids: Liste des IDs d'ordres à optimiser\n        \n        Returns:\n            Liste des ordres optimisés avec nouvelles dates\n        \"\"\"\n        orders = ProductionOrder.query.filter(\n            ProductionOrder.id.in_(order_ids)\n        ).options(\n            joinedload(ProductionOrder.bom)\n        ).all()\n        \n        # Trier par priorité puis par date de fin souhaitée\n        priority_order = {'urgent': 0, 'high': 1, 'normal': 2, 'low': 3}\n        \n        sorted_orders = sorted(orders, key=lambda o: (\n            priority_order.get(o.priority, 2),\n            o.planned_end_date or datetime.max\n        ))\n        \n        optimized_sequence = []\n        current_time = datetime.now()\n        \n        for order in sorted_orders:\n            # Calculer le temps de production\n            time_calc = self.calculate_production_time(\n                order.bom_id_fk, \n                order.quantity_to_produce\n            )\n            \n            if 'error' in time_calc:\n                continue\n            \n            # Programmer cet ordre après le précédent\n            suggested_start = current_time + timedelta(minutes=30)  # Buffer\n            suggested_end = suggested_start + timedelta(\n                minutes=time_calc['total_minutes']\n            )\n            \n            # Ajuster pour les heures ouvrables\n            suggested_start, suggested_end, warnings = self._adjust_for_working_hours(\n                suggested_start, suggested_end\n            )\n            \n            optimized_sequence.append({\n                'order_id': order.id,\n                'order_number': order.order_number,\n                'priority': order.priority,\n                'current_start': order.planned_start_date,\n                'current_end': order.planned_end_date,\n                'suggested_start': suggested_start,\n                'suggested_end': suggested_end,\n                'duration_minutes': time_calc['total_minutes'],\n                'warnings': warnings\n            })\n            \n            # Mettre à jour le temps actuel pour le prochain ordre\n            current_time = suggested_end\n        \n        return optimized_sequence\n    \n    def calculate_workload_by_center(self, date_from: datetime = None, \n                                   date_to: datetime = None) -> Dict:\n        \"\"\"\n        Calcule la charge de travail par centre de travail\n        \n        Returns:\n            Dict avec la charge par centre et les statistiques\n        \"\"\"\n        if not date_from:\n            date_from = datetime.now()\n        if not date_to:\n            date_to = date_from + timedelta(days=30)\n        \n        work_centers = WorkCenter.query.filter_by(\n            business_id_fk=self.business_id,\n            is_active=True\n        ).all()\n        \n        workload_data = {}\n        total_workload = 0\n        \n        for wc in work_centers:\n            # Calculer la charge pour ce centre\n            orders_workload = db.session.query(\n                func.sum(ProductionStep.setup_time_minutes + ProductionStep.run_time_minutes)\n            ).join(\n                ProductionOrderStep\n            ).join(\n                ProductionOrder\n            ).filter(\n                and_(\n                    ProductionStep.work_center_id_fk == wc.id,\n                    ProductionOrder.planned_start_date >= date_from,\n                    ProductionOrder.planned_end_date <= date_to,\n                    ProductionOrder.status.in_(['planned', 'released', 'in_progress'])\n                )\n            ).scalar() or 0\n            \n            # Calculer la capacité disponible\n            days_in_period = (date_to - date_from).days\n            working_days = days_in_period * 5 / 7  # Approximation 5j/7\n            working_hours_per_day = 10  # 8h-18h\n            \n            available_capacity_minutes = working_days * working_hours_per_day * 60\n            capacity_per_hour_minutes = float(wc.capacity_per_hour) * 60\n            \n            total_capacity_minutes = available_capacity_minutes * capacity_per_hour_minutes / 60\n            \n            utilization_rate = (orders_workload / total_capacity_minutes * 100) if total_capacity_minutes > 0 else 0\n            \n            workload_data[wc.id] = {\n                'name': wc.name,\n                'capacity_per_hour': float(wc.capacity_per_hour),\n                'workload_minutes': orders_workload,\n                'available_capacity_minutes': total_capacity_minutes,\n                'utilization_rate': min(100, utilization_rate),\n                'cost_per_hour': wc.cost_per_hour / 100,\n                'is_overloaded': utilization_rate > 100\n            }\n            \n            total_workload += orders_workload\n        \n        return {\n            'period_from': date_from,\n            'period_to': date_to,\n            'work_centers': workload_data,\n            'total_workload_minutes': total_workload,\n            'total_workload_hours': total_workload / 60,\n            'overloaded_centers': [\n                wc_data['name'] for wc_data in workload_data.values() \n                if wc_data['is_overloaded']\n            ]\n        }\n\n\nclass CostCalculator:\n    \"\"\"Calculateur de coûts de production\"\"\"\n    \n    @staticmethod\n    def calculate_bom_cost(bom_id: int, quantity: Decimal = 1) -> Dict:\n        \"\"\"\n        Calcule le coût complet d'une BOM\n        \n        Returns:\n            Dict avec détail des coûts matériaux, main d'œuvre, total\n        \"\"\"\n        bom = BOM.query.get(bom_id)\n        if not bom:\n            return {\"error\": \"BOM non trouvée\"}\n        \n        # Coûts matériaux\n        material_cost = 0\n        material_details = []\n        \n        for item in bom.bom_items:\n            item_quantity = item.quantity * quantity\n            item_cost = int(float(item_quantity) * item.unit_cost_cents)\n            material_cost += item_cost\n            \n            material_details.append({\n                'product_name': item.product.name if item.product else 'Produit supprimé',\n                'unit_quantity': item.quantity,\n                'total_quantity': item_quantity,\n                'unit_cost_cents': item.unit_cost_cents,\n                'total_cost_cents': item_cost\n            })\n        \n        # Coûts main d'œuvre\n        labor_cost = 0\n        labor_details = []\n        \n        for step in bom.production_steps:\n            if step.work_center:\n                step_time_hours = (step.setup_time_minutes + step.run_time_minutes) / 60\n                step_cost = int(step_time_hours * step.work_center.cost_per_hour)\n                labor_cost += step_cost\n                \n                labor_details.append({\n                    'step_name': step.name,\n                    'work_center': step.work_center.name,\n                    'time_minutes': step.setup_time_minutes + step.run_time_minutes,\n                    'cost_per_hour': step.work_center.cost_per_hour,\n                    'total_cost_cents': step_cost\n                })\n        \n        total_cost = material_cost + labor_cost\n        \n        return {\n            'bom_name': bom.name,\n            'quantity': quantity,\n            'material_cost_cents': material_cost,\n            'labor_cost_cents': labor_cost,\n            'total_cost_cents': total_cost,\n            'material_details': material_details,\n            'labor_details': labor_details,\n            'cost_per_unit_cents': int(total_cost / float(quantity)) if quantity > 0 else 0\n        }\n    \n    @staticmethod\n    def calculate_order_profitability(order_id: int) -> Dict:\n        \"\"\"\n        Calcule la rentabilité d'un ordre de production\n        \n        Returns:\n            Dict avec coûts estimés, réels, marge, rentabilité\n        \"\"\"\n        order = ProductionOrder.query.get(order_id)\n        if not order:\n            return {\"error\": \"Ordre non trouvé\"}\n        \n        # Coût estimé basé sur la BOM\n        estimated_cost_data = CostCalculator.calculate_bom_cost(\n            order.bom_id_fk, \n            order.quantity_to_produce\n        )\n        \n        if 'error' in estimated_cost_data:\n            return estimated_cost_data\n        \n        # Coût réel basé sur les consommations\n        actual_material_cost = sum(\n            consumption.total_cost_cents \n            for consumption in order.material_consumptions\n        )\n        \n        actual_labor_cost = order.actual_cost_cents - actual_material_cost if order.actual_cost_cents else 0\n        actual_total_cost = order.actual_cost_cents or 0\n        \n        # Prix de vente (si défini)\n        selling_price = 0\n        if order.bom and order.bom.product and order.bom.product.price_cents:\n            selling_price = int(\n                order.bom.product.price_cents * float(order.quantity_produced or order.quantity_to_produce)\n            )\n        \n        # Calculs de rentabilité\n        estimated_margin = selling_price - estimated_cost_data['total_cost_cents']\n        actual_margin = selling_price - actual_total_cost\n        \n        estimated_margin_rate = (estimated_margin / selling_price * 100) if selling_price > 0 else 0\n        actual_margin_rate = (actual_margin / selling_price * 100) if selling_price > 0 else 0\n        \n        return {\n            'order_number': order.order_number,\n            'quantity_to_produce': order.quantity_to_produce,\n            'quantity_produced': order.quantity_produced,\n            'selling_price_cents': selling_price,\n            \n            'estimated_cost_cents': estimated_cost_data['total_cost_cents'],\n            'estimated_material_cost_cents': estimated_cost_data['material_cost_cents'],\n            'estimated_labor_cost_cents': estimated_cost_data['labor_cost_cents'],\n            'estimated_margin_cents': estimated_margin,\n            'estimated_margin_rate': estimated_margin_rate,\n            \n            'actual_cost_cents': actual_total_cost,\n            'actual_material_cost_cents': actual_material_cost,\n            'actual_labor_cost_cents': actual_labor_cost,\n            'actual_margin_cents': actual_margin,\n            'actual_margin_rate': actual_margin_rate,\n            \n            'cost_variance_cents': actual_total_cost - estimated_cost_data['total_cost_cents'],\n            'cost_variance_rate': ((actual_total_cost - estimated_cost_data['total_cost_cents']) / \n                                 estimated_cost_data['total_cost_cents'] * 100) if estimated_cost_data['total_cost_cents'] > 0 else 0\n        }"