{% extends 'base.html' %}
{% block title %}Centres de travail{% endblock %}

{% block content %}
<div class=\"max-w-6xl mx-auto p-6\">
    <!-- En-tête -->
    <div class=\"flex justify-between items-center mb-6\">
        <div>
            <h1 class=\"text-2xl font-bold text-white mb-2\">Centres de travail</h1>
            <p class=\"text-slate-400\">Gérez vos postes et centres de production</p>
        </div>
        
        <div class=\"flex gap-3\">
            <a href=\"{{ url_for('production.index') }}\" 
               class=\"bg-slate-700 hover:bg-slate-600 text-white px-4 py-2 rounded-md text-sm font-medium\">
                ← Retour
            </a>
        </div>
    </div>
    
    <!-- Formulaire de création -->
    <div class=\"bg-slate-800 rounded-lg p-6 mb-6\">
        <h2 class=\"text-lg font-semibold text-white mb-4\">Ajouter un centre de travail</h2>
        
        <form method=\"POST\">
            {{ form.hidden_tag() }}
            
            <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\">
                <div>
                    {{ form.name.label(class=\"block text-sm font-medium text-slate-300 mb-2\") }}
                    {{ form.name(class=\"w-full bg-slate-700 border border-slate-600 text-white rounded-md px-3 py-2\") }}
                    {% if form.name.errors %}
                        <div class=\"text-red-400 text-sm mt-1\">
                            {% for error in form.name.errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
                
                <div>
                    {{ form.capacity_per_hour.label(class=\"block text-sm font-medium text-slate-300 mb-2\") }}
                    {{ form.capacity_per_hour(class=\"w-full bg-slate-700 border border-slate-600 text-white rounded-md px-3 py-2\") }}
                    {% if form.capacity_per_hour.errors %}
                        <div class=\"text-red-400 text-sm mt-1\">
                            {% for error in form.capacity_per_hour.errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
            </div>
            
            <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\">
                <div>
                    {{ form.cost_per_hour.label(class=\"block text-sm font-medium text-slate-300 mb-2\") }}
                    {{ form.cost_per_hour(class=\"w-full bg-slate-700 border border-slate-600 text-white rounded-md px-3 py-2\") }}
                    {% if form.cost_per_hour.errors %}
                        <div class=\"text-red-400 text-sm mt-1\">
                            {% for error in form.cost_per_hour.errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
                
                <div class=\"flex items-center pt-8\">
                    {{ form.is_active(class=\"w-4 h-4 text-blue-600 bg-slate-700 border-slate-600 rounded focus:ring-blue-500\") }}
                    {{ form.is_active.label(class=\"ml-2 text-sm font-medium text-slate-300\") }}
                </div>
            </div>
            
            <div class=\"mb-6\">
                {{ form.description.label(class=\"block text-sm font-medium text-slate-300 mb-2\") }}
                {{ form.description(class=\"w-full bg-slate-700 border border-slate-600 text-white rounded-md px-3 py-2\") }}
                {% if form.description.errors %}
                    <div class=\"text-red-400 text-sm mt-1\">
                        {% for error in form.description.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
            
            <button type=\"submit\" 
                    class=\"bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md font-medium\">
                Créer le centre
            </button>
        </form>
    </div>
    
    <!-- Liste des centres existants -->
    <div class=\"bg-slate-800 rounded-lg overflow-hidden\">
        <div class=\"px-6 py-4 border-b border-slate-700\">
            <h2 class=\"text-lg font-semibold text-white\">Centres existants</h2>
        </div>
        
        {% if centers %}
        <div class=\"divide-y divide-slate-700\">
            {% for center in centers %}
            <div class=\"px-6 py-4 hover:bg-slate-750\">
                <div class=\"flex justify-between items-start\">
                    <div class=\"flex-1\">
                        <div class=\"flex items-center gap-3 mb-2\">
                            <h3 class=\"text-white font-medium\">{{ center.name }}</h3>
                            
                            {% if center.is_active %}
                            <span class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\">
                                Actif
                            </span>
                            {% else %}
                            <span class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800\">
                                Inactif
                            </span>
                            {% endif %}
                        </div>
                        
                        {% if center.description %}
                        <p class=\"text-sm text-slate-400 mb-2\">{{ center.description }}</p>
                        {% endif %}
                        
                        <div class=\"flex gap-6 text-sm text-slate-400\">
                            <span>Capacité: {{ center.capacity_per_hour }}/h</span>
                            <span>Coût: {{ \"%.2f\"|format(center.cost_per_hour / 100) }}€/h</span>
                            <span>ID: {{ center.id }}</span>
                        </div>
                    </div>
                    
                    <div class=\"flex gap-3\">
                        <!-- API pour obtenir la capacité -->
                        <button onclick=\"loadCapacity({{ center.id }})\" 
                                class=\"bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm font-medium\">
                            Charge
                        </button>
                        
                        <!-- Bouton d'édition -->
                        <a href=\"{{ url_for('production.edit_work_center', center_id=center.id) }}\" 
                           class=\"bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-1 rounded text-sm font-medium\">
                            Éditer
                        </a>
                        
                        <!-- Bouton de suppression -->
                        <form method=\"POST\" action=\"{{ url_for('production.delete_work_center', center_id=center.id) }}\" 
                              class=\"inline\" 
                              onsubmit=\"return confirm('Êtes-vous sûr de vouloir supprimer ce centre de travail ?')\">
                            <button type=\"submit\" 
                                    class=\"bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm font-medium\">
                                Supprimer
                            </button>
                        </form>
                    </div>
                </div>
                
                <!-- Zone d'affichage de la charge (masquée par défaut) -->
                <div id=\"capacity-{{ center.id }}\" class=\"hidden mt-3 p-3 bg-slate-700 rounded\">
                    <div class=\"text-sm text-slate-300\">
                        <div class=\"flex justify-between items-center mb-2\">
                            <span>Charge actuelle:</span>
                            <span id=\"load-{{ center.id }}\">--</span>
                        </div>
                        <div class=\"flex justify-between items-center mb-2\">
                            <span>Utilisation:</span>
                            <span id=\"utilization-{{ center.id }}\">--%</span>
                        </div>
                        <div class=\"w-full bg-slate-600 rounded-full h-2\">
                            <div id=\"progress-{{ center.id }}\" class=\"bg-blue-500 h-2 rounded-full\" style=\"width: 0%\"></div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class=\"px-6 py-8 text-center\">
            <div class=\"text-slate-400 mb-4\">
                <svg class=\"w-12 h-12 mx-auto\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">
                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z\"></path>
                </svg>
            </div>
            <h3 class=\"text-lg font-medium text-white mb-2\">Aucun centre configuré</h3>
            <p class=\"text-slate-400\">Créez votre premier centre de travail ci-dessus</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- Script pour charger la capacité -->
<script>
function loadCapacity(centerId) {
    const capacityDiv = document.getElementById(`capacity-${centerId}`);
    
    if (capacityDiv.classList.contains('hidden')) {
        // Afficher et charger les données
        fetch(`/production/api/work-centers/${centerId}/capacity`)
            .then(response => response.json())
            .then(data => {
                document.getElementById(`load-${centerId}`).textContent = 
                    `${Math.round(data.current_load_minutes)} min`;
                document.getElementById(`utilization-${centerId}`).textContent = 
                    `${data.utilization_percentage.toFixed(1)}%`;
                document.getElementById(`progress-${centerId}`).style.width = 
                    `${Math.min(100, data.utilization_percentage)}%`;
                
                capacityDiv.classList.remove('hidden');
            })
            .catch(error => {
                console.error('Erreur:', error);
                alert('Erreur lors du chargement des données');
            });
    } else {
        // Masquer
        capacityDiv.classList.add('hidden');
    }
}
</script>
{% endblock %}