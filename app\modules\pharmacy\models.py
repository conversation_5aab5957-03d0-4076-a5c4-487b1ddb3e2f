from __future__ import annotations

from datetime import datetime, date, timedelta
from decimal import Decimal
from typing import Optional, List
from enum import Enum as PyEnum
from sqlalchemy import Column, Integer, String, Text, DateTime, Date, Numeric, Foreign<PERSON>ey, <PERSON>olean, Enum
from sqlalchemy.orm import relationship

from app.extensions import db


class MedicationClass(PyEnum):
    """Classification des médicaments"""
    PRESCRIPTION = "prescription"  # Sur ordonnance
    OTC = "otc"  # En vente libre
    CONTROLLED = "controlled"  # Stupéfiants/psychotropes
    HOSPITAL_ONLY = "hospital_only"  # Usage hospitalier uniquement


class PrescriptionStatus(PyEnum):
    """Statut des ordonnances"""
    PENDING = "pending"  # En attente
    VALIDATED = "validated"  # Validée par pharmacien
    DISPENSED = "dispensed"  # Délivrée
    PARTIALLY_DISPENSED = "partially_dispensed"  # Partiellement délivrée
    CANCELLED = "cancelled"  # Annulée
    EXPIRED = "expired"  # Expirée


class InteractionSeverity(PyEnum):
    """Sévérité des interactions médicamenteuses"""
    MINOR = "minor"  # Mineure
    MODERATE = "moderate"  # Modérée
    MAJOR = "major"  # Majeure
    CONTRAINDICATED = "contraindicated"  # Contre-indiquée


class Medication(db.Model):
    """Médicament avec toutes les informations pharmaceutiques"""
    __tablename__ = "medications"
    
    id = Column(Integer, primary_key=True)
    business_id_fk = Column(Integer, ForeignKey("businesses.id"), nullable=False, index=True)
    
    # Informations de base
    name = Column(String(200), nullable=False, index=True)
    generic_name = Column(String(200), index=True)  # DCI (Dénomination Commune Internationale)
    brand_name = Column(String(200))
    
    # Codes d'identification
    cip_code = Column(String(20), unique=True, index=True)  # Code CIP (France)
    atc_code = Column(String(10), index=True)  # Code ATC (Anatomique Thérapeutique Chimique)
    barcode = Column(String(50), index=True)
    
    # Classification
    medication_class = Column(Enum(MedicationClass), nullable=False, default=MedicationClass.OTC)
    therapeutic_class = Column(String(100))  # Classe thérapeutique
    
    # Forme pharmaceutique
    pharmaceutical_form = Column(String(100))  # comprimé, gélule, solution, etc.
    dosage = Column(String(100))  # 500mg, 10ml, etc.
    unit_of_measure = Column(String(20), default="unité")
    
    # Composition
    active_ingredients = Column(Text)  # JSON des principes actifs
    excipients = Column(Text)  # Excipients notoires
    
    # Informations réglementaires
    marketing_authorization = Column(String(50))  # AMM
    is_generic = Column(Boolean, default=False)
    is_biosimilar = Column(Boolean, default=False)
    
    # Conditions de conservation
    storage_temperature_min = Column(Integer)  # °C
    storage_temperature_max = Column(Integer)  # °C
    storage_conditions = Column(Text)  # Conditions spéciales
    
    # Validité
    shelf_life_months = Column(Integer, default=36)  # Durée de vie en mois
    
    # Prix et remboursement
    price_cents = Column(Integer, default=0)  # Prix en centimes
    reimbursement_rate = Column(Numeric(5, 2), default=0.0)  # Taux de remboursement (%)
    
    # Alertes et contre-indications
    contraindications = Column(Text)
    side_effects = Column(Text)
    warnings = Column(Text)
    
    # Posologie
    adult_dosage = Column(Text)
    pediatric_dosage = Column(Text)
    elderly_dosage = Column(Text)
    
    # Stock et gestion
    current_stock = Column(Integer, default=0)
    minimum_stock = Column(Integer, default=0)
    maximum_stock = Column(Integer, default=100)
    
    # Audit
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relations
    business = relationship("Business", backref="medications")
    # Spécifier explicitement la clé étrangère pour éviter l'ambiguïté
    prescriptions = relationship("PrescriptionItem", foreign_keys="PrescriptionItem.medication_id_fk", back_populates="medication")
    interactions_as_drug1 = relationship("DrugInteraction", foreign_keys="DrugInteraction.medication1_id_fk", back_populates="medication1")
    interactions_as_drug2 = relationship("DrugInteraction", foreign_keys="DrugInteraction.medication2_id_fk", back_populates="medication2")
    stock_movements = relationship("MedicationStockMovement", back_populates="medication")
    
    __table_args__ = (
        db.Index("idx_medication_name", "name"),
        db.Index("idx_medication_generic", "generic_name"),
        db.Index("idx_medication_class", "medication_class"),
        db.Index("idx_medication_business", "business_id_fk"),
    )
    
    def __repr__(self):
        return f"<Medication {self.name}>"
    
    @property
    def is_prescription_only(self) -> bool:
        """Vérifier si le médicament nécessite une ordonnance"""
        return self.medication_class in [MedicationClass.PRESCRIPTION, MedicationClass.CONTROLLED]
    
    @property
    def is_low_stock(self) -> bool:
        """Vérifier si le stock est faible"""
        return self.current_stock <= self.minimum_stock
    
    @property
    def stock_value_cents(self) -> int:
        """Valeur du stock en centimes"""
        return self.current_stock * self.price_cents


class Prescription(db.Model):
    """Ordonnance médicale"""
    __tablename__ = "prescriptions"
    
    id = Column(Integer, primary_key=True)
    business_id_fk = Column(Integer, ForeignKey("businesses.id"), nullable=False, index=True)
    customer_id_fk = Column(Integer, ForeignKey("customers.id"), nullable=False, index=True)
    
    # Informations de l'ordonnance
    prescription_number = Column(String(50), unique=True, nullable=False, index=True)
    prescription_date = Column(Date, nullable=False, index=True)
    validity_date = Column(Date, nullable=False)  # Date limite d'utilisation
    
    # Médecin prescripteur
    doctor_name = Column(String(200), nullable=False)
    doctor_rpps = Column(String(20))  # Numéro RPPS du médecin
    doctor_specialty = Column(String(100))
    
    # Statut et traitement
    status = Column(Enum(PrescriptionStatus), default=PrescriptionStatus.PENDING, index=True)
    validated_by_pharmacist_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    validated_at = Column(DateTime)
    dispensed_by_pharmacist_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    dispensed_at = Column(DateTime)
    
    # Informations patient
    patient_weight = Column(Numeric(5, 1))  # Poids en kg
    patient_allergies = Column(Text)  # Allergies connues
    
    # Notes et observations
    doctor_notes = Column(Text)
    pharmacist_notes = Column(Text)
    
    # Totaux
    total_items = Column(Integer, default=0)
    total_amount_cents = Column(Integer, default=0)
    reimbursed_amount_cents = Column(Integer, default=0)
    
    # Audit
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relations
    business = relationship("Business", backref="prescriptions")
    customer = relationship("Customer", backref="prescriptions")
    validated_by = relationship("User", foreign_keys=[validated_by_pharmacist_id])
    dispensed_by = relationship("User", foreign_keys=[dispensed_by_pharmacist_id])
    items = relationship("PrescriptionItem", back_populates="prescription", cascade="all, delete-orphan")
    
    __table_args__ = (
        db.Index("idx_prescription_date", "prescription_date"),
        db.Index("idx_prescription_status", "status"),
        db.Index("idx_prescription_customer", "customer_id_fk"),
    )
    
    def __repr__(self):
        return f"<Prescription {self.prescription_number}>"
    
    @property
    def is_expired(self) -> bool:
        """Vérifier si l'ordonnance est expirée"""
        return date.today() > self.validity_date
    
    @property
    def days_until_expiry(self) -> int:
        """Nombre de jours avant expiration"""
        return (self.validity_date - date.today()).days
    
    def calculate_totals(self):
        """Recalculer les totaux de l'ordonnance"""
        self.total_items = len(self.items)
        self.total_amount_cents = sum(item.total_price_cents for item in self.items)
        self.reimbursed_amount_cents = sum(item.reimbursed_amount_cents for item in self.items)


class PrescriptionItem(db.Model):
    """Article d'une ordonnance"""
    __tablename__ = "prescription_items"
    
    id = Column(Integer, primary_key=True)
    prescription_id_fk = Column(Integer, ForeignKey("prescriptions.id"), nullable=False, index=True)
    medication_id_fk = Column(Integer, ForeignKey("medications.id"), nullable=False, index=True)
    
    # Prescription médicale
    prescribed_quantity = Column(Integer, nullable=False)  # Quantité prescrite
    dispensed_quantity = Column(Integer, default=0)  # Quantité délivrée
    
    # Posologie
    dosage_instructions = Column(Text, nullable=False)  # "1 comprimé matin et soir"
    duration_days = Column(Integer)  # Durée du traitement en jours
    
    # Substitution
    substitution_allowed = Column(Boolean, default=True)
    substitute_medication_id = Column(Integer, ForeignKey("medications.id"), nullable=True)
    substitution_reason = Column(String(200))
    
    # Prix et remboursement
    unit_price_cents = Column(Integer, nullable=False)
    total_price_cents = Column(Integer, nullable=False)
    reimbursed_amount_cents = Column(Integer, default=0)
    
    # Notes
    pharmacist_notes = Column(Text)
    
    # Audit
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    # Relations
    prescription = relationship("Prescription", back_populates="items")
    medication = relationship("Medication", foreign_keys=[medication_id_fk], back_populates="prescriptions")
    substitute_medication = relationship("Medication", foreign_keys=[substitute_medication_id])
    
    def __repr__(self):
        return f"<PrescriptionItem {self.medication.name if self.medication else 'Unknown'} x{self.prescribed_quantity}>"
    
    @property
    def is_fully_dispensed(self) -> bool:
        """Vérifier si l'article est complètement délivré"""
        return self.dispensed_quantity >= self.prescribed_quantity
    
    @property
    def remaining_quantity(self) -> int:
        """Quantité restante à délivrer"""
        return max(0, self.prescribed_quantity - self.dispensed_quantity)


class DrugInteraction(db.Model):
    """Interaction médicamenteuse"""
    __tablename__ = "drug_interactions"
    
    id = Column(Integer, primary_key=True)
    business_id_fk = Column(Integer, ForeignKey("businesses.id"), nullable=False, index=True)
    
    # Médicaments concernés
    medication1_id_fk = Column(Integer, ForeignKey("medications.id"), nullable=False, index=True)
    medication2_id_fk = Column(Integer, ForeignKey("medications.id"), nullable=False, index=True)
    
    # Sévérité et type
    severity = Column(Enum(InteractionSeverity), nullable=False, index=True)
    interaction_type = Column(String(100))  # pharmacocinétique, pharmacodynamique
    
    # Description
    description = Column(Text, nullable=False)
    mechanism = Column(Text)  # Mécanisme de l'interaction
    clinical_effects = Column(Text)  # Effets cliniques
    
    # Gestion
    recommendations = Column(Text)  # Recommandations
    monitoring_required = Column(Boolean, default=False)
    contraindicated = Column(Boolean, default=False)
    
    # Références
    evidence_level = Column(String(20))  # A, B, C, D selon niveau de preuve
    references = Column(Text)
    
    # Audit
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relations
    business = relationship("Business", backref="drug_interactions")
    medication1 = relationship("Medication", foreign_keys=[medication1_id_fk], back_populates="interactions_as_drug1")
    medication2 = relationship("Medication", foreign_keys=[medication2_id_fk], back_populates="interactions_as_drug2")
    
    __table_args__ = (
        db.Index("idx_interaction_severity", "severity"),
        db.Index("idx_interaction_meds", "medication1_id_fk", "medication2_id_fk"),
        db.UniqueConstraint("medication1_id_fk", "medication2_id_fk", name="uq_drug_interaction"),
    )
    
    def __repr__(self):
        return f"<DrugInteraction {self.medication1.name if self.medication1 else 'Med1'} + {self.medication2.name if self.medication2 else 'Med2'}>"


class ComplianceCheck(db.Model):
    """Contrôle de conformité réglementaire"""
    __tablename__ = "compliance_checks"
    
    id = Column(Integer, primary_key=True)
    business_id_fk = Column(Integer, ForeignKey("businesses.id"), nullable=False, index=True)
    prescription_id_fk = Column(Integer, ForeignKey("prescriptions.id"), nullable=True, index=True)
    medication_id_fk = Column(Integer, ForeignKey("medications.id"), nullable=True, index=True)
    
    # Type de contrôle
    check_type = Column(String(50), nullable=False, index=True)  # expiry, interaction, dosage, etc.
    check_category = Column(String(50), index=True)  # safety, regulatory, quality
    
    # Résultat
    passed = Column(Boolean, nullable=False, index=True)
    severity_level = Column(String(20), default="info")  # info, warning, error, critical
    
    # Description
    check_description = Column(Text, nullable=False)
    failure_reason = Column(Text)  # Raison de l'échec si applicable
    recommendations = Column(Text)  # Recommandations d'action
    
    # Actions
    action_required = Column(Boolean, default=False)
    action_taken = Column(Text)
    resolved_by_user_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    resolved_at = Column(DateTime)
    
    # Audit
    checked_by_user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    checked_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    # Relations
    business = relationship("Business", backref="compliance_checks")
    prescription = relationship("Prescription", backref="compliance_checks")
    medication = relationship("Medication", backref="compliance_checks")
    checked_by = relationship("User", foreign_keys=[checked_by_user_id])
    resolved_by = relationship("User", foreign_keys=[resolved_by_user_id])
    
    __table_args__ = (
        db.Index("idx_compliance_type", "check_type"),
        db.Index("idx_compliance_passed", "passed"),
        db.Index("idx_compliance_date", "checked_at"),
    )
    
    def __repr__(self):
        return f"<ComplianceCheck {self.check_type} - {'PASS' if self.passed else 'FAIL'}>"


class MedicationStockMovement(db.Model):
    """Mouvement de stock des médicaments"""
    __tablename__ = "medication_stock_movements"
    
    id = Column(Integer, primary_key=True)
    business_id_fk = Column(Integer, ForeignKey("businesses.id"), nullable=False, index=True)
    medication_id_fk = Column(Integer, ForeignKey("medications.id"), nullable=False, index=True)
    
    # Mouvement
    movement_type = Column(String(20), nullable=False, index=True)  # in, out, adjustment, expired
    quantity = Column(Integer, nullable=False)  # Positif pour entrée, négatif pour sortie
    
    # Lot et expiration
    batch_number = Column(String(50), index=True)
    expiry_date = Column(Date, index=True)
    supplier_name = Column(String(200))
    
    # Référence
    reference_type = Column(String(50))  # prescription, purchase, adjustment, etc.
    reference_id = Column(Integer)  # ID de la référence
    
    # Prix
    unit_cost_cents = Column(Integer, default=0)
    total_cost_cents = Column(Integer, default=0)
    
    # Audit
    created_by_user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    notes = Column(Text)
    
    # Relations
    business = relationship("Business", backref="medication_stock_movements")
    medication = relationship("Medication", back_populates="stock_movements")
    created_by = relationship("User")
    
    __table_args__ = (
        db.Index("idx_stock_movement_type", "movement_type"),
        db.Index("idx_stock_movement_date", "created_at"),
        db.Index("idx_stock_expiry", "expiry_date"),
    )
    
    def __repr__(self):
        return f"<MedicationStockMovement {self.medication.name if self.medication else 'Unknown'} {self.quantity:+d}>"


class PatientAllergy(db.Model):
    """Allergies des patients"""
    __tablename__ = "patient_allergies"
    
    id = Column(Integer, primary_key=True)
    business_id_fk = Column(Integer, ForeignKey("businesses.id"), nullable=False, index=True)
    customer_id_fk = Column(Integer, ForeignKey("customers.id"), nullable=False, index=True)
    
    # Allergie
    allergen_name = Column(String(200), nullable=False, index=True)
    allergen_type = Column(String(50), index=True)  # medication, food, environmental
    
    # Sévérité
    severity = Column(String(20), default="moderate")  # mild, moderate, severe
    reactions = Column(Text)  # Réactions observées
    
    # Informations
    diagnosed_date = Column(Date)
    notes = Column(Text)
    
    # Audit
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relations
    business = relationship("Business", backref="patient_allergies")
    customer = relationship("Customer", backref="allergies")
    
    def __repr__(self):
        return f"<PatientAllergy {self.allergen_name}>"