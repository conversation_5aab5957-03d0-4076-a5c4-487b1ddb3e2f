from __future__ import annotations

from datetime import datetime, date, timedelta
from flask import render_template, request, redirect, url_for, flash, jsonify, abort, send_file
from flask_login import login_required, current_user
from sqlalchemy.orm import joinedload
from sqlalchemy import and_, or_, desc, func
import json

from app.extensions import db
from app.modules.suppliers.models import Supplier, SupplierProduct
from app.modules.catalog.models import Product
from . import bp
from .models import (
    PurchaseOrder, PurchaseOrderItem, PurchaseReceipt, PurchaseReceiptItem,
    PurchaseRequisition, PurchaseRequisitionItem,
    PurchaseOrderStatus, ReceiptStatus
)
from .forms import (
    PurchaseOrderForm, PurchaseOrderItemForm, QuickPurchaseOrderForm,
    PurchaseReceiptForm, PurchaseReceiptItemForm, QuickReceiptForm,
    PurchaseRequisitionForm, PurchaseRequisitionItemForm,
    PurchaseOrderSearchForm, PurchaseReceiptSearchForm,
    SupplierOrderForm, BulkPurchaseOrderUpdateForm, PurchaseAnalyticsForm
)


@bp.route("/")
@login_required
def index():
    """Dashboard du module purchasing"""
    
    # Statistiques rapides
    today = date.today()
    current_month = today.replace(day=1)
    
    # Bons de commande en cours
    pending_orders = PurchaseOrder.query.filter_by(
        business_id_fk=current_user.business.id
    ).filter(
        PurchaseOrder.status.in_([PurchaseOrderStatus.PENDING, PurchaseOrderStatus.APPROVED, PurchaseOrderStatus.SENT])
    ).count()
    
    # Commandes à réceptionner
    orders_to_receive = PurchaseOrder.query.filter_by(
        business_id_fk=current_user.business.id
    ).filter(
        PurchaseOrder.status.in_([PurchaseOrderStatus.CONFIRMED, PurchaseOrderStatus.SENT, PurchaseOrderStatus.PARTIAL])
    ).count()
    
    # Dépenses du mois
    monthly_spending = db.session.query(
        func.coalesce(func.sum(PurchaseOrder.total_cents), 0)
    ).filter(
        PurchaseOrder.business_id_fk == current_user.business.id,
        PurchaseOrder.order_date >= current_month,
        PurchaseOrder.status != PurchaseOrderStatus.CANCELLED
    ).scalar() or 0
    
    # Nombre de fournisseurs actifs
    active_suppliers = Supplier.query.filter_by(
        business_id_fk=current_user.business.id,
        is_active=True
    ).count()
    
    # Commandes récentes
    recent_orders = PurchaseOrder.query.filter_by(
        business_id_fk=current_user.business.id
    ).order_by(desc(PurchaseOrder.created_at)).limit(5).all()
    
    # Réceptions récentes
    recent_receipts = PurchaseReceipt.query.filter_by(
        business_id_fk=current_user.business.id
    ).order_by(desc(PurchaseReceipt.created_at)).limit(5).all()
    
    # Formulaire de commande rapide
    quick_order_form = QuickPurchaseOrderForm(current_user.business.id)
    
    return render_template("purchasing/index.html",
                         pending_orders=pending_orders,
                         orders_to_receive=orders_to_receive,
                         monthly_spending=monthly_spending / 100,
                         active_suppliers=active_suppliers,
                         recent_orders=recent_orders,
                         recent_receipts=recent_receipts,
                         quick_order_form=quick_order_form)


# === PURCHASE ORDERS ===

@bp.route("/orders")
@login_required
def purchase_orders():
    """Liste des bons de commande"""
    
    search_form = PurchaseOrderSearchForm(current_user.business.id)
    
    # Construction de la requête
    query = PurchaseOrder.query.filter_by(business_id_fk=current_user.business.id)
    
    # Filtres de recherche
    if request.args.get('search'):
        search_term = f"%{request.args.get('search')}%"
        query = query.filter(
            or_(
                PurchaseOrder.order_number.ilike(search_term),
                PurchaseOrder.reference.ilike(search_term),
                PurchaseOrder.supplier_reference.ilike(search_term)
            )
        )
    
    if request.args.get('status'):
        query = query.filter(PurchaseOrder.status == request.args.get('status'))
    
    if request.args.get('supplier_id', type=int):
        query = query.filter(PurchaseOrder.supplier_id_fk == request.args.get('supplier_id'))
    
    if request.args.get('date_from'):
        date_from = datetime.strptime(request.args.get('date_from'), '%Y-%m-%d').date()
        query = query.filter(PurchaseOrder.order_date >= date_from)
    
    if request.args.get('date_to'):
        date_to = datetime.strptime(request.args.get('date_to'), '%Y-%m-%d').date()
        query = query.filter(PurchaseOrder.order_date <= date_to)
    
    # Pagination
    page = request.args.get('page', 1, type=int)
    orders = query.order_by(desc(PurchaseOrder.created_at)).paginate(
        page=page, per_page=20, error_out=False
    )
    
    return render_template("purchasing/purchase_orders.html",
                         orders=orders,
                         search_form=search_form)


@bp.route("/orders/new", methods=["GET", "POST"])
@login_required
def new_purchase_order():
    """Créer un nouveau bon de commande"""
    
    form = PurchaseOrderForm(current_user.business.id)
    
    if form.validate_on_submit():
        try:
            # Générer le numéro de commande
            last_order = PurchaseOrder.query.filter_by(
                business_id_fk=current_user.business.id
            ).order_by(desc(PurchaseOrder.id)).first()
            
            if last_order and last_order.order_number:
                last_num = int(last_order.order_number.split('-')[-1])
                order_number = f"PO-{datetime.now().year}-{last_num + 1:04d}"
            else:
                order_number = f"PO-{datetime.now().year}-0001"
            
            # Créer le bon de commande
            purchase_order = PurchaseOrder(
                business_id_fk=current_user.business.id,
                supplier_id_fk=form.supplier_id.data,
                order_number=order_number,
                reference=form.reference.data,
                supplier_reference=form.supplier_reference.data,
                order_date=form.order_date.data,
                expected_delivery_date=form.expected_delivery_date.data,
                delivery_address=form.delivery_address.data,
                delivery_instructions=form.delivery_instructions.data,
                payment_terms=form.payment_terms.data,
                currency=form.currency.data,
                discount_cents=int((form.discount_amount.data or 0) * 100),
                tax_cents=int((form.tax_amount.data or 0) * 100),
                shipping_cents=int((form.shipping_amount.data or 0) * 100),
                notes=form.notes.data,
                internal_notes=form.internal_notes.data
            )
            
            db.session.add(purchase_order)
            db.session.commit()
            
            flash(f"Bon de commande {order_number} créé avec succès!", "success")
            return redirect(url_for('purchasing.edit_purchase_order', id=purchase_order.id))
            
        except Exception as e:
            db.session.rollback()
            flash(f"Erreur lors de la création du bon de commande: {str(e)}", "error")
    
    return render_template("purchasing/new_purchase_order.html", form=form)


@bp.route("/orders/<int:id>")
@login_required
def view_purchase_order(id):
    """Voir les détails d'un bon de commande"""
    
    purchase_order = PurchaseOrder.query.filter_by(
        id=id,
        business_id_fk=current_user.business.id
    ).options(
        joinedload(PurchaseOrder.items),
        joinedload(PurchaseOrder.receipts)
    ).first_or_404()
    
    return render_template("purchasing/view_purchase_order.html",
                         purchase_order=purchase_order)


@bp.route("/orders/<int:id>/edit", methods=["GET", "POST"])
@login_required
def edit_purchase_order(id):
    """Modifier un bon de commande"""
    
    purchase_order = PurchaseOrder.query.filter_by(
        id=id,
        business_id_fk=current_user.business.id
    ).first_or_404()
    
    form = PurchaseOrderForm(current_user.business.id, obj=purchase_order)
    item_form = PurchaseOrderItemForm()
    
    if form.validate_on_submit():
        try:
            form.populate_obj(purchase_order)
            purchase_order.discount_cents = int((form.discount_amount.data or 0) * 100)
            purchase_order.tax_cents = int((form.tax_amount.data or 0) * 100)
            purchase_order.shipping_cents = int((form.shipping_amount.data or 0) * 100)
            purchase_order.updated_at = datetime.utcnow()
            purchase_order.calculate_totals()
            
            db.session.commit()
            flash("Bon de commande mis à jour avec succès!", "success")
            return redirect(url_for('purchasing.view_purchase_order', id=purchase_order.id))
            
        except Exception as e:
            db.session.rollback()
            flash(f"Erreur lors de la mise à jour: {str(e)}", "error")
    
    return render_template("purchasing/edit_purchase_order.html",
                         purchase_order=purchase_order,
                         form=form,
                         item_form=item_form)


@bp.route("/orders/<int:id>/items/add", methods=["POST"])
@login_required
def add_purchase_order_item(id):
    """Ajouter un article au bon de commande"""
    
    purchase_order = PurchaseOrder.query.filter_by(
        id=id,
        business_id_fk=current_user.business.id
    ).first_or_404()
    
    form = PurchaseOrderItemForm()
    
    if form.validate_on_submit():
        try:
            item = PurchaseOrderItem(
                purchase_order_id_fk=purchase_order.id,
                product_id_fk=form.product_id.data if form.product_id.data else None,
                supplier_product_id_fk=form.supplier_product_id.data if form.supplier_product_id.data else None,
                product_name=form.product_name.data,
                supplier_reference=form.supplier_reference.data,
                description=form.description.data,
                quantity=form.quantity.data,
                unit_of_measure=form.unit_of_measure.data,
                unit_cost_cents=int(form.unit_cost.data * 100),
                expected_date=form.expected_date.data,
                notes=form.notes.data
            )
            
            item.calculate_total()
            db.session.add(item)
            purchase_order.calculate_totals()
            db.session.commit()
            
            flash("Article ajouté avec succès!", "success")
            
        except Exception as e:
            db.session.rollback()
            flash(f"Erreur lors de l'ajout de l'article: {str(e)}", "error")
    
    return redirect(url_for('purchasing.edit_purchase_order', id=id))


@bp.route("/orders/<int:order_id>/items/<int:item_id>/delete", methods=["POST"])
@login_required
def delete_purchase_order_item(order_id, item_id):
    """Supprimer un article du bon de commande"""
    
    purchase_order = PurchaseOrder.query.filter_by(
        id=order_id,
        business_id_fk=current_user.business.id
    ).first_or_404()
    
    item = PurchaseOrderItem.query.filter_by(
        id=item_id,
        purchase_order_id_fk=order_id
    ).first_or_404()
    
    try:
        db.session.delete(item)
        purchase_order.calculate_totals()
        db.session.commit()
        flash("Article supprimé avec succès!", "success")
        
    except Exception as e:
        db.session.rollback()
        flash(f"Erreur lors de la suppression: {str(e)}", "error")
    
    return redirect(url_for('purchasing.edit_purchase_order', id=order_id))


@bp.route("/orders/<int:id>/approve", methods=["POST"])
@login_required
def approve_purchase_order(id):
    """Approuver un bon de commande"""
    
    purchase_order = PurchaseOrder.query.filter_by(
        id=id,
        business_id_fk=current_user.business.id
    ).first_or_404()
    
    if purchase_order.status == PurchaseOrderStatus.DRAFT:
        try:
            purchase_order.status = PurchaseOrderStatus.APPROVED
            purchase_order.approved_at = datetime.utcnow()
            db.session.commit()
            flash("Bon de commande approuvé avec succès!", "success")
            
        except Exception as e:
            db.session.rollback()
            flash(f"Erreur lors de l'approbation: {str(e)}", "error")
    else:
        flash("Ce bon de commande ne peut pas être approuvé dans son état actuel.", "warning")
    
    return redirect(url_for('purchasing.view_purchase_order', id=id))


@bp.route("/quick-order", methods=["POST"])
@login_required
def quick_order():
    """Créer rapidement un bon de commande simple"""
    
    form = QuickPurchaseOrderForm(current_user.business.id)
    
    if form.validate_on_submit():
        try:
            # Générer le numéro de commande
            last_order = PurchaseOrder.query.filter_by(
                business_id_fk=current_user.business.id
            ).order_by(desc(PurchaseOrder.id)).first()
            
            if last_order and last_order.order_number:
                last_num = int(last_order.order_number.split('-')[-1])
                order_number = f"PO-{datetime.now().year}-{last_num + 1:04d}"
            else:
                order_number = f"PO-{datetime.now().year}-0001"
            
            # Créer le bon de commande
            purchase_order = PurchaseOrder(
                business_id_fk=current_user.business.id,
                supplier_id_fk=form.supplier_id.data,
                order_number=order_number,
                reference=form.reference.data,
                expected_delivery_date=form.expected_delivery_date.data,
                notes=form.notes.data
            )
            
            db.session.add(purchase_order)
            db.session.flush()
            
            # Créer l'article
            item = PurchaseOrderItem(
                purchase_order_id_fk=purchase_order.id,
                product_name=form.product_name.data,
                quantity=form.quantity.data,
                unit_cost_cents=int(form.unit_cost.data * 100)
            )
            item.calculate_total()
            
            db.session.add(item)
            purchase_order.calculate_totals()
            db.session.commit()
            
            flash(f"Commande rapide {order_number} créée avec succès!", "success")
            
        except Exception as e:
            db.session.rollback()
            flash(f"Erreur lors de la création: {str(e)}", "error")
    
    return redirect(url_for('purchasing.index'))


# === RECEIPTS ===

@bp.route("/receipts")
@login_required
def purchase_receipts():
    """Liste des réceptions"""
    
    search_form = PurchaseReceiptSearchForm(current_user.business.id)
    
    query = PurchaseReceipt.query.filter_by(business_id_fk=current_user.business.id)
    
    # Filtres de recherche
    if request.args.get('search'):
        search_term = f"%{request.args.get('search')}%"
        query = query.filter(
            or_(
                PurchaseReceipt.receipt_number.ilike(search_term),
                PurchaseReceipt.delivery_note_number.ilike(search_term)
            )
        )
    
    if request.args.get('status'):
        query = query.filter(PurchaseReceipt.status == request.args.get('status'))
    
    # Pagination
    page = request.args.get('page', 1, type=int)
    receipts = query.order_by(desc(PurchaseReceipt.created_at)).paginate(
        page=page, per_page=20, error_out=False
    )
    
    return render_template("purchasing/purchase_receipts.html",
                         receipts=receipts,
                         search_form=search_form)


@bp.route("/receipts/new", methods=["GET", "POST"])
@login_required
def new_purchase_receipt():
    """Créer une nouvelle réception"""
    
    form = PurchaseReceiptForm(current_user.business.id)
    
    if form.validate_on_submit():
        try:
            # Générer le numéro de réception
            last_receipt = PurchaseReceipt.query.filter_by(
                business_id_fk=current_user.business.id
            ).order_by(desc(PurchaseReceipt.id)).first()
            
            if last_receipt and last_receipt.receipt_number:
                last_num = int(last_receipt.receipt_number.split('-')[-1])
                receipt_number = f"REC-{datetime.now().year}-{last_num + 1:04d}"
            else:
                receipt_number = f"REC-{datetime.now().year}-0001"
            
            receipt = PurchaseReceipt(
                business_id_fk=current_user.business.id,
                purchase_order_id_fk=form.purchase_order_id.data,
                receipt_number=receipt_number,
                delivery_note_number=form.delivery_note_number.data,
                supplier_invoice_number=form.supplier_invoice_number.data,
                receipt_date=form.receipt_date.data,
                delivery_date=form.delivery_date.data,
                delivery_person=form.delivery_person.data,
                delivery_company=form.delivery_company.data,
                delivery_notes=form.delivery_notes.data,
                quality_check_done=form.quality_check_done.data,
                quality_issues=form.quality_issues.data,
                quality_rating=form.quality_rating.data if form.quality_rating.data else None,
                notes=form.notes.data,
                internal_notes=form.internal_notes.data
            )
            
            db.session.add(receipt)
            db.session.commit()
            
            flash(f"Réception {receipt_number} créée avec succès!", "success")
            return redirect(url_for('purchasing.edit_purchase_receipt', id=receipt.id))
            
        except Exception as e:
            db.session.rollback()
            flash(f"Erreur lors de la création de la réception: {str(e)}", "error")
    
    return render_template("purchasing/new_purchase_receipt.html", form=form)


@bp.route("/receipts/<int:id>")
@login_required
def view_purchase_receipt(id):
    """Voir les détails d'une réception"""
    
    receipt = PurchaseReceipt.query.filter_by(
        id=id,
        business_id_fk=current_user.business.id
    ).options(
        joinedload(PurchaseReceipt.items),
        joinedload(PurchaseReceipt.purchase_order)
    ).first_or_404()
    
    return render_template("purchasing/view_purchase_receipt.html",
                         receipt=receipt)


# === API ROUTES ===

@bp.route("/api/orders/<int:id>/status", methods=["POST"])
@login_required
def api_update_order_status(id):
    """API pour mettre à jour le statut d'une commande"""
    
    purchase_order = PurchaseOrder.query.filter_by(
        id=id,
        business_id_fk=current_user.business.id
    ).first_or_404()
    
    data = request.get_json()
    new_status = data.get('status')
    
    if new_status not in [status.value for status in PurchaseOrderStatus]:
        return jsonify({'success': False, 'message': 'Statut invalide'}), 400
    
    try:
        purchase_order.status = PurchaseOrderStatus(new_status)
        purchase_order.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Statut mis à jour avec succès',
            'order': {
                'id': purchase_order.id,
                'status': purchase_order.status.value,
                'order_number': purchase_order.order_number
            }
        })
    
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)}), 500


@bp.route("/api/supplier/<int:supplier_id>/products")
@login_required
def api_supplier_products(supplier_id):
    """API pour récupérer les produits d'un fournisseur"""
    
    supplier = Supplier.query.filter_by(
        id=supplier_id,
        business_id_fk=current_user.business.id
    ).first_or_404()
    
    products = SupplierProduct.query.filter_by(
        supplier_id_fk=supplier_id,
        is_active=True
    ).all()
    
    return jsonify({
        'success': True,
        'products': [{
            'id': product.id,
            'name': product.product_name,
            'reference': product.supplier_reference,
            'unit_price': product.unit_price,
            'unit_of_measure': product.unit_of_measure,
            'minimum_quantity': product.minimum_quantity
        } for product in products]
    })


@bp.route("/api/analytics/spending")
@login_required  
def api_spending_analytics():
    """API pour les analytics de dépenses"""
    
    period = request.args.get('period', 'month')
    
    if period == 'month':
        start_date = date.today().replace(day=1)
    elif period == 'quarter':
        month = date.today().month
        quarter_start = ((month - 1) // 3) * 3 + 1
        start_date = date.today().replace(month=quarter_start, day=1)
    else:
        start_date = date.today().replace(month=1, day=1)
    
    # Dépenses par fournisseur
    spending_by_supplier = db.session.query(
        Supplier.name,
        func.sum(PurchaseOrder.total_cents).label('total')
    ).join(
        PurchaseOrder, Supplier.id == PurchaseOrder.supplier_id_fk
    ).filter(
        PurchaseOrder.business_id_fk == current_user.business.id,
        PurchaseOrder.order_date >= start_date,
        PurchaseOrder.status != PurchaseOrderStatus.CANCELLED
    ).group_by(Supplier.name).all()
    
    return jsonify({
        'success': True,
        'spending_by_supplier': [{
            'supplier': row[0],
            'amount': row[1] / 100 if row[1] else 0
        } for row in spending_by_supplier]
    })


