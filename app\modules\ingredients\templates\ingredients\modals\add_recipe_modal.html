<!-- Modal Ajout Recette -->
<div id="addRecipeModal" class="hidden fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <!-- Fond noir transparent -->
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
            <div class="absolute inset-0 bg-gray-900 opacity-75" onclick="document.getElementById('addRecipeModal').classList.add('hidden')"></div>
        </div>

        <!-- Contenu du modal -->
        <div class="inline-block align-bottom bg-slate-900 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-6xl sm:w-full border border-slate-700">
            <div class="bg-green-600 px-4 py-3 border-b border-slate-700">
                <h3 class="text-lg leading-6 font-medium text-white">
                    <i class="fas fa-utensils mr-2"></i>Nouvelle Recette
                </h3>
            </div>
            <form id="addRecipeForm" method="POST" action="{{ url_for('ingredients.create_recipe') }}" class="px-4 py-5 sm:p-6">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Informations de base de la recette -->
                    <div>
                        <h4 class="text-lg font-medium text-cyan-400 mb-4">Informations de la Recette</h4>
                        
                        <div class="mb-4">
                            <label for="recipeName" class="block text-sm font-medium text-slate-300 mb-1">Nom de la recette *</label>
                            <input type="text" id="recipeName" name="name" required
                                   class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 text-slate-100 focus:outline-none focus:ring-2 focus:ring-green-500">
                        </div>
                        
                        <div class="mb-4">
                            <label for="recipeProduct" class="block text-sm font-medium text-slate-300 mb-1">Produit associé *</label>
                            <select id="recipeProduct" name="product_id" required
                                    class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 text-slate-100 focus:outline-none focus:ring-2 focus:ring-green-500">
                                <option value="">Sélectionner un produit</option>
                                {% for product in products %}
                                <option value="{{ product.id }}">{{ product.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="mb-4">
                            <label for="recipeDescription" class="block text-sm font-medium text-slate-300 mb-1">Description</label>
                            <textarea id="recipeDescription" name="description" rows="3"
                                      class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 text-slate-100 focus:outline-none focus:ring-2 focus:ring-green-500"
                                      placeholder="Description de la recette, instructions spéciales..."></textarea>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label for="recipeServings" class="block text-sm font-medium text-slate-300 mb-1">Nombre de portions</label>
                                <input type="number" id="recipeServings" name="servings" min="1" value="1"
                                       class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 text-slate-100 focus:outline-none focus:ring-2 focus:ring-green-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-slate-300 mb-1">Statut</label>
                                <div class="flex items-center">
                                    <input type="checkbox" id="recipeActive" name="is_active" checked
                                           class="h-4 w-4 text-green-600 focus:ring-green-500 border-slate-700 rounded bg-slate-800">
                                    <label for="recipeActive" class="ml-2 block text-sm text-slate-300">
                                        Recette active
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Temps de préparation -->
                    <div>
                        <h4 class="text-lg font-medium text-cyan-400 mb-4">Temps de Préparation</h4>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label for="recipePrepTime" class="block text-sm font-medium text-slate-300 mb-1">Temps de préparation (min)</label>
                                <input type="number" id="recipePrepTime" name="preparation_time_minutes" min="0" value="0"
                                       class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 text-slate-100 focus:outline-none focus:ring-2 focus:ring-green-500">
                            </div>
                            <div>
                                <label for="recipeCookTime" class="block text-sm font-medium text-slate-300 mb-1">Temps de cuisson (min)</label>
                                <input type="number" id="recipeCookTime" name="cooking_time_minutes" min="0" value="0"
                                       class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 text-slate-100 focus:outline-none focus:ring-2 focus:ring-green-500">
                            </div>
                        </div>
                        
                        <div class="bg-blue-900/30 border border-blue-800 rounded-lg p-3 mb-4">
                            <div class="flex items-center">
                                <i class="fas fa-info-circle text-blue-400 mr-2"></i>
                                <div>
                                    <strong class="text-blue-300">Temps total estimé:</strong>
                                    <span id="totalTime" class="text-blue-200">0</span> minutes
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <label for="recipeNotes" class="block text-sm font-medium text-slate-300 mb-1">Notes de préparation</label>
                            <textarea id="recipeNotes" name="notes" rows="3"
                                      class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 text-slate-100 focus:outline-none focus:ring-2 focus:ring-green-500"
                                      placeholder="Notes spéciales, conseils de préparation..."></textarea>
                        </div>
                    </div>
                </div>
                
                <hr class="my-6 border-slate-700">
                
                <!-- Gestion des ingrédients -->
                <div>
                    <h4 class="text-lg font-medium text-cyan-400 mb-4">Ingrédients de la Recette</h4>
                    
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-slate-700">
                            <thead class="bg-slate-800">
                                <tr>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-slate-400 uppercase tracking-wider">Ingrédient</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-slate-400 uppercase tracking-wider">Quantité</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-slate-400 uppercase tracking-wider">Unité</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-slate-400 uppercase tracking-wider">Coût</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-slate-400 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="recipeIngredientsBody" class="divide-y divide-slate-800">
                                <!-- Les ingrédients seront ajoutés ici dynamiquement -->
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="flex justify-between items-center mt-4">
                        <button type="button" class="px-3 py-2 bg-slate-700 hover:bg-slate-600 text-slate-200 rounded-lg text-sm flex items-center"
                                onclick="addIngredientRow()">
                            <i class="fas fa-plus mr-1"></i>Ajouter un ingrédient
                        </button>
                        
                        <div class="text-right">
                            <div class="grid grid-cols-2 gap-2">
                                <div class="text-slate-300">Coût total des ingrédients:</div>
                                <div class="text-xl font-semibold text-green-400" id="totalIngredientCost">0.00 €</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="mt-6 flex justify-end space-x-3">
                    <button type="button" 
                            class="px-4 py-2 border border-slate-700 rounded-lg text-slate-300 hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-green-500"
                            onclick="document.getElementById('addRecipeModal').classList.add('hidden')">
                        <i class="fas fa-times mr-1"></i>Annuler
                    </button>
                    <button type="submit"
                            class="px-4 py-2 bg-green-600 rounded-lg text-white hover:bg-green-500 focus:outline-none focus:ring-2 focus:ring-green-500">
                        <i class="fas fa-save mr-1"></i>Enregistrer la Recette
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
let ingredientRowCounter = 0;

function addIngredientRow() {
    const tbody = document.getElementById('recipeIngredientsBody');
    const row = document.createElement('tr');
    row.id = `ingredient-row-${ingredientRowCounter}`;
    
    row.innerHTML = `
        <td class="px-4 py-3 whitespace-nowrap">
            <select class="w-full bg-slate-800 border border-slate-700 rounded-lg px-2 py-1 text-slate-100 text-sm focus:outline-none focus:ring-1 focus:ring-green-500 ingredient-select" name="ingredients[${ingredientRowCounter}][ingredient_id]" required>
                <option value="">Sélectionner un ingrédient</option>
                {% for ingredient in ingredients %}
                <option value="{{ ingredient.id }}" data-cost="{{ ingredient.cost_per_unit_cents }}" data-unit="{{ ingredient.unit }}">
                    {{ ingredient.name }} ({{ ingredient.unit }})
                </option>
                {% endfor %}
            </select>
        </td>
        <td class="px-4 py-3 whitespace-nowrap">
            <input type="number" class="w-full bg-slate-800 border border-slate-700 rounded-lg px-2 py-1 text-slate-100 text-sm focus:outline-none focus:ring-1 focus:ring-green-500 ingredient-quantity" 
                   name="ingredients[${ingredientRowCounter}][quantity]" 
                   step="0.01" min="0" required>
        </td>
        <td class="px-4 py-3 whitespace-nowrap">
            <input type="text" class="w-full bg-slate-800 border border-slate-700 rounded-lg px-2 py-1 text-slate-100 text-sm ingredient-unit" 
                   name="ingredients[${ingredientRowCounter}][unit]" readonly>
        </td>
        <td class="px-4 py-3 whitespace-nowrap">
            <span class="ingredient-cost text-slate-300">0.00 €</span>
        </td>
        <td class="px-4 py-3 whitespace-nowrap text-right">
            <button type="button" class="p-1 bg-red-600 hover:bg-red-500 rounded text-white" onclick="removeIngredientRow(${ingredientRowCounter})">
                <i class="fas fa-trash text-xs"></i>
            </button>
        </td>
    `;
    
    tbody.appendChild(row);
    
    // Ajouter les event listeners
    const ingredientSelect = row.querySelector('.ingredient-select');
    const quantityInput = row.querySelector('.ingredient-quantity');
    
    ingredientSelect.addEventListener('change', function() {
        updateIngredientUnit(this);
        calculateIngredientCost(this);
    });
    
    quantityInput.addEventListener('input', function() {
        calculateIngredientCost(this);
    });
    
    ingredientRowCounter++;
}

function removeIngredientRow(rowId) {
    document.getElementById(`ingredient-row-${rowId}`).remove();
    calculateTotalCost();
}

function updateIngredientUnit(select) {
    const selectedOption = select.options[select.selectedIndex];
    const unit = selectedOption.getAttribute('data-unit');
    const unitInput = select.closest('tr').querySelector('.ingredient-unit');
    unitInput.value = unit || '';
}

function calculateIngredientCost(element) {
    const row = element.closest('tr');
    const select = row.querySelector('.ingredient-select');
    const quantityInput = row.querySelector('.ingredient-quantity');
    const costSpan = row.querySelector('.ingredient-cost');
    
    const selectedOption = select.options[select.selectedIndex];
    const costPerUnit = parseFloat(selectedOption.getAttribute('data-cost')) || 0;
    const quantity = parseFloat(quantityInput.value) || 0;
    
    const totalCost = (costPerUnit * quantity) / 100;
    costSpan.textContent = totalCost.toFixed(2) + ' €';
    
    calculateTotalCost();
}

function calculateTotalCost() {
    let total = 0;
    document.querySelectorAll('.ingredient-cost').forEach(span => {
        const value = parseFloat(span.textContent.replace(' €', '')) || 0;
        total += value;
    });
    document.getElementById('totalIngredientCost').textContent = total.toFixed(2) + ' €';
}

// Calcul du temps total
function updateTotalTime() {
    const prepTime = parseInt(document.getElementById('recipePrepTime').value) || 0;
    const cookTime = parseInt(document.getElementById('recipeCookTime').value) || 0;
    const totalTime = prepTime + cookTime;
    document.getElementById('totalTime').textContent = totalTime;
}

document.addEventListener('DOMContentLoaded', function() {
    // Ajouter les event listeners pour le temps total
    document.getElementById('recipePrepTime').addEventListener('input', updateTotalTime);
    document.getElementById('recipeCookTime').addEventListener('input', updateTotalTime);
    
    // Validation du formulaire
    const form = document.getElementById('addRecipeForm');
    form.addEventListener('submit', function(e) {
        if (!form.checkValidity()) {
            e.preventDefault();
            e.stopPropagation();
        }
        form.classList.add('was-validated');
    });
});
</script>