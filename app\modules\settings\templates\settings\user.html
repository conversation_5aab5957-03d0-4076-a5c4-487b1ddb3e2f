{% extends "base.html" %}

{% block title %}Paramètres utilisateur - {{ super() }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">Accueil</a></li>
                        <li class="breadcrumb-item"><a href="{{ url_for('settings.index') }}">Paramètres</a></li>
                        <li class="breadcrumb-item active">Paramètres utilisateur</li>
                    </ol>
                </div>
                <h4 class="page-title">Paramètres utilisateur</h4>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="header-title">Informations personnelles et préférences</h4>
                    <p class="text-muted mb-0">Modifiez vos informations personnelles et préférences d'utilisation</p>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('settings.save_user_settings') }}">
                        {{ form.hidden_tag() }}
                        
                        <div class="row">
                            <!-- Informations personnelles -->
                            <div class="col-lg-6">
                                <h5 class="mb-3">Informations personnelles</h5>
                                
                                <div class="mb-3">
                                    {{ form.email.label(class="form-label") }}
                                    {{ form.email(class="form-control") }}
                                    {% if form.email.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.email.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form.first_name.label(class="form-label") }}
                                            {{ form.first_name(class="form-control") }}
                                            {% if form.first_name.errors %}
                                                <div class="invalid-feedback d-block">
                                                    {% for error in form.first_name.errors %}
                                                        {{ error }}
                                                    {% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form.last_name.label(class="form-label") }}
                                            {{ form.last_name(class="form-control") }}
                                            {% if form.last_name.errors %}
                                                <div class="invalid-feedback d-block">
                                                    {% for error in form.last_name.errors %}
                                                        {{ error }}
                                                    {% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>

                                <hr class="my-4">

                                <h5 class="mb-3">Changer le mot de passe</h5>
                                
                                <div class="mb-3">
                                    {{ form.current_password.label(class="form-label") }}
                                    {{ form.current_password(class="form-control") }}
                                    {% if form.current_password.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.current_password.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form.new_password.label(class="form-label") }}
                                            {{ form.new_password(class="form-control") }}
                                            {% if form.new_password.errors %}
                                                <div class="invalid-feedback d-block">
                                                    {% for error in form.new_password.errors %}
                                                        {{ error }}
                                                    {% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form.confirm_password.label(class="form-label") }}
                                            {{ form.confirm_password(class="form-control") }}
                                            {% if form.confirm_password.errors %}
                                                <div class="invalid-feedback d-block">
                                                    {% for error in form.confirm_password.errors %}
                                                        {{ error }}
                                                    {% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Préférences -->
                            <div class="col-lg-6">
                                <h5 class="mb-3">Préférences d'affichage</h5>
                                
                                <div class="mb-3">
                                    {{ form.theme.label(class="form-label") }}
                                    {% set current_theme = preferences.get('theme', 'light') %}
                                    {{ form.theme(class="form-select") }}
                                    <script>
                                        document.querySelector('select[name="theme"]').value = '{{ current_theme }}';
                                    </script>
                                    {% if form.theme.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.theme.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <div class="mb-3">
                                    {{ form.language.label(class="form-label") }}
                                    {% set current_lang = preferences.get('language', 'fr') %}
                                    {{ form.language(class="form-select") }}
                                    <script>
                                        document.querySelector('select[name="language"]').value = '{{ current_lang }}';
                                    </script>
                                    {% if form.language.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.language.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <hr class="my-4">

                                <h5 class="mb-3">Notifications</h5>
                                
                                <div class="mb-3">
                                    <div class="form-check">
                                        {% set email_notif = preferences.get('notifications_email', 'True') == 'True' %}
                                        {{ form.notifications_email(class="form-check-input", checked=email_notif) }}
                                        {{ form.notifications_email.label(class="form-check-label") }}
                                    </div>
                                    {% if form.notifications_email.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.notifications_email.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <div class="mb-3">
                                    <div class="form-check">
                                        {% set browser_notif = preferences.get('notifications_browser', 'True') == 'True' %}
                                        {{ form.notifications_browser(class="form-check-input", checked=browser_notif) }}
                                        {{ form.notifications_browser.label(class="form-check-label") }}
                                    </div>
                                    {% if form.notifications_browser.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.notifications_browser.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="text-end">
                                    <a href="{{ url_for('settings.index') }}" class="btn btn-secondary me-2">Annuler</a>
                                    {{ form.submit(class="btn btn-primary") }}
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}