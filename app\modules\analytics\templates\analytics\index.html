{% extends "base.html" %}

{% block title %}Tableau de bord Analytics{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- En-tête -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h1 class="h3 mb-0">
                    <i class="fas fa-chart-line text-primary me-2"></i>
                    Tableau de bord Analytics
                </h1>
                <div class="btn-group">
                    <a href="{{ url_for('analytics.kpis') }}" class="btn btn-outline-primary">
                        <i class="fas fa-tachometer-alt me-1"></i>KPIs
                    </a>
                    <a href="{{ url_for('analytics.reports') }}" class="btn btn-outline-info">
                        <i class="fas fa-file-alt me-1"></i>Rapports
                    </a>
                    <a href="{{ url_for('analytics.export') }}" class="btn btn-outline-success">
                        <i class="fas fa-download me-1"></i>Exporter
                    </a>
                    <div class="dropdown">
                        <button class="btn btn-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-plus me-1"></i>Nouveau
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('analytics.add_kpi') }}">
                                <i class="fas fa-tachometer-alt me-1"></i>KPI
                            </a></li>
                            <li><a class="dropdown-item" href="#">
                                <i class="fas fa-chart-bar me-1"></i>Rapport
                            </a></li>
                            <li><a class="dropdown-item" href="#">
                                <i class="fas fa-th-large me-1"></i>Tableau de bord
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- KPIs principaux -->
    <div class="row mb-4">
        <div class="col-12">
            <h5 class="mb-3">
                <i class="fas fa-tachometer-alt me-2"></i>
                Indicateurs clés de performance
                <button class="btn btn-sm btn-outline-primary ms-2" onclick="refreshAllKPIs()">
                    <i class="fas fa-sync-alt"></i> Actualiser
                </button>
            </h5>
            <div class="row">
                {% if key_kpis %}
                    {% for kpi in key_kpis %}
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card h-100 kpi-card {{ kpi.status }}">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <div>
                                        <h6 class="card-title mb-1">{{ kpi.name }}</h6>
                                        <small class="text-muted">{{ kpi.kpi_code }}</small>
                                    </div>
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-link text-muted" type="button" data-bs-toggle="dropdown">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                        <ul class="dropdown-menu dropdown-menu-end">
                                            <li><a class="dropdown-item" href="{{ url_for('analytics.view_kpi', id=kpi.id) }}">
                                                <i class="fas fa-eye me-1"></i>Voir détails
                                            </a></li>
                                            <li><a class="dropdown-item" href="#" onclick="calculateKPI({{ kpi.id }})">
                                                <i class="fas fa-calculator me-1"></i>Recalculer
                                            </a></li>
                                        </ul>
                                    </div>
                                </div>
                                
                                <div class="kpi-value mb-2">
                                    <h3 class="mb-0">
                                        {% if kpi.current_value %}
                                            {{ "%.2f"|format(kpi.current_value) }}
                                        {% else %}
                                            --
                                        {% endif %}
                                        {% if kpi.unit %}
                                            <small class="text-muted">{{ kpi.unit }}</small>
                                        {% endif %}
                                    </h3>
                                </div>
                                
                                {% if kpi.target_value %}
                                <div class="progress mb-2" style="height: 6px;">
                                    <div class="progress-bar" style="width: {{ kpi.progress_percentage or 0 }}%"></div>
                                </div>
                                <small class="text-muted">
                                    Objectif: {{ "%.2f"|format(kpi.target_value) }} {{ kpi.unit or "" }}
                                </small>
                                {% endif %}
                                
                                {% if kpi.last_calculated %}
                                <div class="text-muted small mt-2">
                                    <i class="fas fa-clock me-1"></i>
                                    {{ kpi.last_calculated.strftime('%d/%m/%Y %H:%M') }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                <div class="col-12">
                    <div class="text-center py-4">
                        <i class="fas fa-tachometer-alt fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Aucun KPI configuré</h5>
                        <p class="text-muted">Commencez par créer vos premiers indicateurs de performance.</p>
                        <a href="{{ url_for('analytics.setup') }}" class="btn btn-primary">
                            <i class="fas fa-cog me-1"></i>Configuration initiale
                        </a>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Résumés par catégorie -->
    <div class="row mb-4">
        <!-- Résumé des ventes -->
        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-shopping-cart me-2"></i>
                        Ventes (30 derniers jours)
                    </h6>
                </div>
                <div class="card-body">
                    {% if sales_summary %}
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <h4 class="text-primary mb-1">{{ "%.0f"|format(sales_summary.total_sales) }}€</h4>
                            <small class="text-muted">Chiffre d'affaires</small>
                        </div>
                        <div class="col-6 mb-3">
                            <h4 class="text-success mb-1">{{ sales_summary.total_transactions }}</h4>
                            <small class="text-muted">Transactions</small>
                        </div>
                        <div class="col-6">
                            <h5 class="text-info mb-1">{{ "%.2f"|format(sales_summary.avg_transaction) }}€</h5>
                            <small class="text-muted">Panier moyen</small>
                        </div>
                        <div class="col-6">
                            <h5 class="text-warning mb-1">{{ sales_summary.unique_customers }}</h5>
                            <small class="text-muted">Clients uniques</small>
                        </div>
                    </div>
                    {% else %}
                    <p class="text-muted text-center">Aucune donnée de vente</p>
                    {% endif %}
                </div>
                <div class="card-footer">
                    <a href="{{ url_for('analytics.sales_analytics') }}" class="btn btn-sm btn-outline-primary w-100">
                        <i class="fas fa-chart-bar me-1"></i>Analyse détaillée
                    </a>
                </div>
            </div>
        </div>

        <!-- Résumé de l'inventaire -->
        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header bg-success text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-boxes me-2"></i>
                        État de l'inventaire
                    </h6>
                </div>
                <div class="card-body">
                    {% if inventory_summary %}
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <h4 class="text-primary mb-1">{{ inventory_summary.total_products }}</h4>
                            <small class="text-muted">Produits total</small>
                        </div>
                        <div class="col-6 mb-3">
                            <h4 class="text-danger mb-1">{{ inventory_summary.out_of_stock_products }}</h4>
                            <small class="text-muted">En rupture</small>
                        </div>
                        <div class="col-6">
                            <h5 class="text-warning mb-1">{{ inventory_summary.low_stock_products }}</h5>
                            <small class="text-muted">Stock faible</small>
                        </div>
                        <div class="col-6">
                            <h5 class="text-success mb-1">{{ "%.0f"|format(inventory_summary.total_inventory_value) }}€</h5>
                            <small class="text-muted">Valeur stock</small>
                        </div>
                    </div>
                    {% else %}
                    <p class="text-muted text-center">Aucune donnée d'inventaire</p>
                    {% endif %}
                </div>
                <div class="card-footer">
                    <a href="{{ url_for('analytics.inventory_analytics') }}" class="btn btn-sm btn-outline-success w-100">
                        <i class="fas fa-warehouse me-1"></i>Analyse détaillée
                    </a>
                </div>
            </div>
        </div>

        <!-- Résumé financier -->
        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-euro-sign me-2"></i>
                        Finances (mois en cours)
                    </h6>
                </div>
                <div class="card-body">
                    {% if financial_summary %}
                    <div class="row text-center">
                        <div class="col-12 mb-3">
                            <h4 class="text-primary mb-1">{{ "%.0f"|format(financial_summary.gross_revenue) }}€</h4>
                            <small class="text-muted">Revenus bruts</small>
                        </div>
                        <div class="col-6 mb-2">
                            <h6 class="text-success mb-1">{{ "%.0f"|format(financial_summary.net_revenue) }}€</h6>
                            <small class="text-muted">Revenus nets</small>
                        </div>
                        <div class="col-6 mb-2">
                            <h6 class="text-warning mb-1">{{ "%.0f"|format(financial_summary.total_tax) }}€</h6>
                            <small class="text-muted">Taxes</small>
                        </div>
                        <div class="col-12">
                            <small class="text-muted">
                                Moyenne quotidienne: {{ "%.0f"|format(financial_summary.avg_daily_revenue) }}€
                            </small>
                        </div>
                    </div>
                    {% else %}
                    <p class="text-muted text-center">Aucune donnée financière</p>
                    {% endif %}
                </div>
                <div class="card-footer">
                    <a href="{{ url_for('analytics.financial_analytics') }}" class="btn btn-sm btn-outline-info w-100">
                        <i class="fas fa-calculator me-1"></i>Analyse détaillée
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Graphiques et analyses -->
    <div class="row">
        <!-- Tendance des ventes -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        Évolution des ventes (14 derniers jours)
                    </h6>
                </div>
                <div class="card-body">
                    <canvas id="salesTrendChart" height="100"></canvas>
                </div>
            </div>
        </div>

        <!-- Top produits -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-star me-2"></i>
                        Top 5 Produits
                    </h6>
                </div>
                <div class="card-body">
                    {% if top_products %}
                    <div class="list-group list-group-flush">
                        {% for product in top_products %}
                        <div class="list-group-item px-0 d-flex justify-content-between align-items-center">
                            <div>
                                <div class="fw-bold">{{ product.product_name }}</div>
                                <small class="text-muted">{{ product.quantity_sold }} vendus</small>
                            </div>
                            <span class="badge bg-primary rounded-pill">
                                {{ "%.0f"|format(product.revenue) }}€
                            </span>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <p class="text-muted text-center">Aucune donnée de produits</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Graphique des tendances de ventes
{% if sales_trend %}
const salesTrendData = {{ sales_trend | tojson }};
const ctx = document.getElementById('salesTrendChart').getContext('2d');

new Chart(ctx, {
    type: 'line',
    data: {
        labels: salesTrendData.map(item => item.period),
        datasets: [{
            label: 'Ventes (€)',
            data: salesTrendData.map(item => item.total_sales),
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.1)',
            tension: 0.1,
            fill: true
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return value + '€';
                    }
                }
            }
        },
        plugins: {
            legend: {
                display: false
            }
        }
    }
});
{% endif %}

// Fonctions utilitaires
function refreshAllKPIs() {
    fetch('/analytics/api/calculate_all_kpis', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', `${data.success_count} KPIs mis à jour avec succès!`);
            setTimeout(() => window.location.reload(), 1000);
        } else {
            showAlert('danger', 'Erreur lors de la mise à jour des KPIs');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'Erreur de communication');
    });
}

function calculateKPI(kpiId) {
    fetch(`/analytics/kpis/${kpiId}/calculate`, {
        method: 'POST'
    })
    .then(response => {
        if (response.ok) {
            window.location.reload();
        } else {
            showAlert('danger', 'Erreur lors du calcul du KPI');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'Erreur de communication');
    });
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>

<style>
.kpi-card.good {
    border-left: 4px solid #28a745;
}

.kpi-card.warning {
    border-left: 4px solid #ffc107;
}

.kpi-card.critical {
    border-left: 4px solid #dc3545;
}

.kpi-card.unknown {
    border-left: 4px solid #6c757d;
}

.kpi-value h3 {
    font-weight: 600;
    color: #495057;
}

.card-header h6 {
    font-weight: 600;
}
</style>
{% endblock %}