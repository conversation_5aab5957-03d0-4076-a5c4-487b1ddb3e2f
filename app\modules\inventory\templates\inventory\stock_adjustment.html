{% extends 'base.html' %}
{% block title %}Ajustement des Stocks{% endblock %}
{% block content %}
<div class="max-w-4xl mx-auto">
  <div class="flex items-center justify-between mb-6">
    <div>
      <h1 class="text-3xl font-bold">Ajustement des Stocks</h1>
      <p class="text-slate-400 mt-2">Modifier manuellement les quantités en stock</p>
    </div>
    <div class="flex space-x-3">
      <a href="{{ url_for('inventory.stock_movements') }}" class="px-4 py-2 bg-slate-700 hover:bg-slate-600 text-white rounded-lg">
        Mouvements de Stock
      </a>
      <a href="{{ url_for('inventory.index') }}" class="px-4 py-2 bg-slate-600 hover:bg-slate-500 text-white rounded-lg">
        Inventaire
      </a>
    </div>
  </div>

  <div class="rounded-xl bg-slate-900 border border-slate-700 p-6">
    <form method="post" class="space-y-6">
      {{ form.csrf_token }}
      
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div>
          <label class="block text-sm font-medium text-slate-300 mb-2">{{ form.product_id.label.text }}</label>
          {{ form.product_id(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent") }}
          {% if form.product_id.errors %}
            <div class="mt-1 text-sm text-red-400">
              {% for error in form.product_id.errors %}
                <p>{{ error }}</p>
              {% endfor %}
            </div>
          {% endif %}
        </div>
        
        <div>
          <label class="block text-sm font-medium text-slate-300 mb-2">{{ form.warehouse_id.label.text }}</label>
          {{ form.warehouse_id(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent") }}
          {% if form.warehouse_id.errors %}
            <div class="mt-1 text-sm text-red-400">
              {% for error in form.warehouse_id.errors %}
                <p>{{ error }}</p>
              {% endfor %}
            </div>
          {% endif %}
        </div>
      </div>
      
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div>
          <label class="block text-sm font-medium text-slate-300 mb-2">{{ form.adjustment_type.label.text }}</label>
          {{ form.adjustment_type(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent") }}
          {% if form.adjustment_type.errors %}
            <div class="mt-1 text-sm text-red-400">
              {% for error in form.adjustment_type.errors %}
                <p>{{ error }}</p>
              {% endfor %}
            </div>
          {% endif %}
        </div>
        
        <div>
          <label class="block text-sm font-medium text-slate-300 mb-2">{{ form.quantity.label.text }}</label>
          {{ form.quantity(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent") }}
          {% if form.quantity.errors %}
            <div class="mt-1 text-sm text-red-400">
              {% for error in form.quantity.errors %}
                <p>{{ error }}</p>
              {% endfor %}
            </div>
          {% endif %}
        </div>
      </div>
      
      <div>
        <label class="block text-sm font-medium text-slate-300 mb-2">{{ form.reason.label.text }}</label>
        {{ form.reason(rows="4", class="w-full bg-slate-800 border border-slate-700 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent", placeholder="Expliquez la raison de cet ajustement...") }}
        {% if form.reason.errors %}
          <div class="mt-1 text-sm text-red-400">
            {% for error in form.reason.errors %}
              <p>{{ error }}</p>
            {% endfor %}
          </div>
        {% endif %}
      </div>
      
      <div class="flex justify-end space-x-3 pt-6 border-t border-slate-700">
        <a href="{{ url_for('inventory.index') }}" class="px-6 py-3 bg-slate-700 hover:bg-slate-600 text-white rounded-lg transition-colors">
          Annuler
        </a>
        {{ form.submit(class="px-6 py-3 bg-cyan-600 hover:bg-cyan-500 text-white rounded-lg transition-colors font-medium") }}
      </div>
    </form>
  </div>
  
  <div class="rounded-xl bg-slate-900 border border-slate-700 p-6 mt-6">
    <h3 class="text-lg font-semibold mb-4">💡 Types d'ajustement</h3>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
      <div class="bg-slate-800 rounded-lg p-4">
        <h4 class="font-medium text-green-400 mb-2">Ajouter</h4>
        <p class="text-slate-300">Ajoute la quantité spécifiée au stock actuel. Utile pour les réceptions de marchandises.</p>
      </div>
      <div class="bg-slate-800 rounded-lg p-4">
        <h4 class="font-medium text-red-400 mb-2">Retirer</h4>
        <p class="text-slate-300">Retire la quantité spécifiée du stock actuel. Utile pour les pertes ou ventes non enregistrées.</p>
      </div>
      <div class="bg-slate-800 rounded-lg p-4">
        <h4 class="font-medium text-blue-400 mb-2">Définir</h4>
        <p class="text-slate-300">Fixe le stock à la quantité exacte spécifiée. Utile pour les inventaires physiques.</p>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const adjustmentType = document.querySelector('select[name="adjustment_type"]');
    const quantityField = document.querySelector('input[name="quantity"]');
    const quantityLabel = quantityField.previousElementSibling;
    
    function updateQuantityLabel() {
        const type = adjustmentType.value;
        switch (type) {
            case 'add':
                quantityLabel.textContent = 'Quantité à ajouter';
                quantityField.placeholder = 'Ex: 50';
                break;
            case 'remove':
                quantityLabel.textContent = 'Quantité à retirer';
                quantityField.placeholder = 'Ex: 10';
                break;
            case 'set':
                quantityLabel.textContent = 'Nouvelle quantité';
                quantityField.placeholder = 'Ex: 100';
                break;
        }
    }
    
    if (adjustmentType) {
        adjustmentType.addEventListener('change', updateQuantityLabel);
        updateQuantityLabel(); // Set initial label
    }
});
</script>
{% endblock %}