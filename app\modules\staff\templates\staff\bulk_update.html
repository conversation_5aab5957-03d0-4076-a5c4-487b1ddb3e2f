{% extends 'base.html' %}
{% block title %}Mise à jour en lot - Personnel{% endblock %}

{% block content %}
<div class="max-w-6xl mx-auto">
  <!-- En-tête -->
  <div class="mb-6">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold">📝 Mise à jour en lot</h1>
        <p class="text-slate-400 mt-2">Modifiez plusieurs employés simultanément</p>
      </div>
      <a href="{{ url_for('staff.index') }}" class="bg-slate-700 hover:bg-slate-600 px-4 py-2 rounded-lg">
        ← Retour au personnel
      </a>
    </div>
  </div>

  <!-- Instructions -->
  <div class="mb-6 rounded-xl bg-blue-900 border border-blue-700 p-6">
    <h2 class="text-lg font-semibold mb-2 text-blue-100">💡 Instructions</h2>
    <div class="text-blue-200 space-y-2">
      <p>1. <strong>Sélectionnez</strong> les employés à modifier en cochant les cases</p>
      <p>2. <strong>Choisissez</strong> l'action à effectuer dans le formulaire</p>
      <p>3. <strong>Remplissez</strong> les champs nécessaires selon l'action</p>
      <p>4. <strong>Cliquez</strong> sur "Appliquer" pour effectuer les modifications</p>
    </div>
  </div>

  <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <!-- Sélection des employés -->
    <div class="lg:col-span-2">
      <div class="rounded-xl bg-slate-900 border border-slate-700 p-6">
        <div class="flex items-center justify-between mb-4">
          <h2 class="text-xl font-semibold">👥 Sélection des employés</h2>
          <div class="flex space-x-2">
            <button onclick="selectAll()" class="px-3 py-1 bg-cyan-600 hover:bg-cyan-500 rounded text-sm">
              Tout sélectionner
            </button>
            <button onclick="selectNone()" class="px-3 py-1 bg-slate-700 hover:bg-slate-600 rounded text-sm">
              Tout désélectionner
            </button>
          </div>
        </div>
        
        <!-- Filtres rapides -->
        <div class="mb-4 flex flex-wrap gap-2">
          <button onclick="filterByStatus('active')" class="px-3 py-1 bg-green-700 hover:bg-green-600 rounded text-sm">
            Actifs seulement
          </button>
          <button onclick="filterByStatus('inactive')" class="px-3 py-1 bg-red-700 hover:bg-red-600 rounded text-sm">
            Inactifs seulement
          </button>
          <button onclick="filterByStatus('all')" class="px-3 py-1 bg-slate-700 hover:bg-slate-600 rounded text-sm">
            Tous
          </button>
        </div>
        
        <!-- Liste des employés -->
        <div class="space-y-2 max-h-96 overflow-y-auto" id="employee-list">
          <!-- Les employés seraient chargés via JavaScript ou du backend -->
          <div class="bg-slate-800 border border-slate-700 rounded-lg p-3 employee-item" data-status="active" data-department="Cuisine">
            <div class="flex items-center">
              <input type="checkbox" class="employee-checkbox w-4 h-4 text-cyan-600 bg-slate-800 border-slate-700 rounded" 
                     value="1" onchange="updateSelection()">
              <div class="ml-3 flex-1">
                <div class="flex items-center space-x-3">
                  <h3 class="font-medium">Dupont Jean</h3>
                  <span class="px-2 py-1 bg-green-900 text-green-300 text-xs rounded-full">Actif</span>
                  <span class="px-2 py-1 bg-blue-900 text-blue-300 text-xs rounded-full">EMP001</span>
                </div>
                <div class="text-sm text-slate-400 mt-1">
                  Chef de cuisine • Cuisine
                </div>
              </div>
            </div>
          </div>
          
          <div class="bg-slate-800 border border-slate-700 rounded-lg p-3 employee-item" data-status="active" data-department="Service">
            <div class="flex items-center">
              <input type="checkbox" class="employee-checkbox w-4 h-4 text-cyan-600 bg-slate-800 border-slate-700 rounded" 
                     value="2" onchange="updateSelection()">
              <div class="ml-3 flex-1">
                <div class="flex items-center space-x-3">
                  <h3 class="font-medium">Martin Sophie</h3>
                  <span class="px-2 py-1 bg-green-900 text-green-300 text-xs rounded-full">Actif</span>
                  <span class="px-2 py-1 bg-blue-900 text-blue-300 text-xs rounded-full">EMP002</span>
                </div>
                <div class="text-sm text-slate-400 mt-1">
                  Serveuse • Service
                </div>
              </div>
            </div>
          </div>
          
          <div class="bg-slate-800 border border-slate-700 rounded-lg p-3 employee-item" data-status="inactive" data-department="Management">
            <div class="flex items-center">
              <input type="checkbox" class="employee-checkbox w-4 h-4 text-cyan-600 bg-slate-800 border-slate-700 rounded" 
                     value="3" onchange="updateSelection()">
              <div class="ml-3 flex-1">
                <div class="flex items-center space-x-3">
                  <h3 class="font-medium">Bernard Lucas</h3>
                  <span class="px-2 py-1 bg-red-900 text-red-300 text-xs rounded-full">Inactif</span>
                  <span class="px-2 py-1 bg-blue-900 text-blue-300 text-xs rounded-full">EMP003</span>
                </div>
                <div class="text-sm text-slate-400 mt-1">
                  Manager • Management
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Compteur de sélection -->
        <div class="mt-4 pt-4 border-t border-slate-700">
          <p class="text-sm text-slate-400">
            <span id="selected-count">0</span> employé(s) sélectionné(s)
          </p>
        </div>
      </div>
    </div>

    <!-- Formulaire d'action -->
    <div class="lg:col-span-1">
      <div class="rounded-xl bg-slate-900 border border-slate-700 p-6">
        <h2 class="text-xl font-semibold mb-6">⚡ Actions à effectuer</h2>
        
        <form method="post" class="space-y-4">
          {{ form.csrf_token }}
          {{ form.employee_ids(id="employee-ids") }}
          
          <!-- Messages -->
          {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
              {% for category, message in messages %}
                <div class="rounded-lg p-4 {% if category == 'error' %}bg-red-900 border border-red-700 text-red-100{% else %}bg-green-900 border border-green-700 text-green-100{% endif %}">
                  {{ message }}
                </div>
              {% endfor %}
            {% endif %}
          {% endwith %}

          <div>
            <label class="block text-sm font-medium text-slate-300 mb-2">
              {{ form.action.label.text }}
            </label>
            {{ form.action(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 focus:ring-2 focus:ring-cyan-500", onchange="toggleActionFields()") }}
          </div>
          
          <!-- Champs conditionnels -->
          <div id="department-field" class="hidden">
            <label class="block text-sm font-medium text-slate-300 mb-2">
              {{ form.new_department.label.text }}
            </label>
            {{ form.new_department(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 focus:ring-2 focus:ring-cyan-500", placeholder="Nouveau département") }}
          </div>
          
          <div id="position-field" class="hidden">
            <label class="block text-sm font-medium text-slate-300 mb-2">
              {{ form.new_position.label.text }}
            </label>
            {{ form.new_position(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-3 py-2 focus:ring-2 focus:ring-cyan-500", placeholder="Nouveau poste") }}
          </div>
          
          <!-- Aperçu de l'action -->
          <div id="action-preview" class="bg-slate-800 border border-slate-700 rounded-lg p-4">
            <h3 class="font-medium mb-2">📋 Aperçu de l'action</h3>
            <p id="preview-text" class="text-sm text-slate-400">
              Sélectionnez des employés et une action pour voir l'aperçu
            </p>
          </div>
          
          {{ form.submit(class="w-full bg-cyan-600 hover:bg-cyan-500 rounded-lg px-4 py-3 font-medium disabled:opacity-50 disabled:cursor-not-allowed", id="apply-button", disabled=true) }}
        </form>
      </div>
      
      <!-- Historique des actions -->
      <div class="mt-6 rounded-xl bg-slate-900 border border-slate-700 p-6">
        <h3 class="text-lg font-semibold mb-4">📜 Historique récent</h3>
        <div class="space-y-3">
          <div class="text-sm">
            <div class="flex justify-between items-center">
              <span class="text-slate-400">15:30 - Aujourd'hui</span>
              <span class="px-2 py-1 bg-green-900 text-green-300 text-xs rounded">Succès</span>
            </div>
            <p class="text-slate-300 mt-1">3 employés activés</p>
          </div>
          
          <div class="text-sm">
            <div class="flex justify-between items-center">
              <span class="text-slate-400">14:15 - Aujourd'hui</span>
              <span class="px-2 py-1 bg-blue-900 text-blue-300 text-xs rounded">Info</span>
            </div>
            <p class="text-slate-300 mt-1">5 employés changés de département</p>
          </div>
          
          <div class="text-sm">
            <div class="flex justify-between items-center">
              <span class="text-slate-400">10:45 - Hier</span>
              <span class="px-2 py-1 bg-yellow-900 text-yellow-300 text-xs rounded">Export</span>
            </div>
            <p class="text-slate-300 mt-1">Données exportées (12 employés)</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Statistiques par département/poste -->
  <div class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
    <div class="rounded-xl bg-slate-900 border border-slate-700 p-6">
      <h3 class="text-lg font-semibold mb-4">🏢 Par département</h3>
      <div class="space-y-2">
        <div class="flex justify-between items-center">
          <span class="text-slate-400">Cuisine</span>
          <div class="flex items-center space-x-2">
            <span class="text-sm">5 employés</span>
            <button onclick="selectByDepartment('Cuisine')" class="px-2 py-1 bg-cyan-600 hover:bg-cyan-500 rounded text-xs">
              Sélectionner
            </button>
          </div>
        </div>
        <div class="flex justify-between items-center">
          <span class="text-slate-400">Service</span>
          <div class="flex items-center space-x-2">
            <span class="text-sm">8 employés</span>
            <button onclick="selectByDepartment('Service')" class="px-2 py-1 bg-cyan-600 hover:bg-cyan-500 rounded text-xs">
              Sélectionner
            </button>
          </div>
        </div>
        <div class="flex justify-between items-center">
          <span class="text-slate-400">Management</span>
          <div class="flex items-center space-x-2">
            <span class="text-sm">2 employés</span>
            <button onclick="selectByDepartment('Management')" class="px-2 py-1 bg-cyan-600 hover:bg-cyan-500 rounded text-xs">
              Sélectionner
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <div class="rounded-xl bg-slate-900 border border-slate-700 p-6">
      <h3 class="text-lg font-semibold mb-4">💼 Par statut</h3>
      <div class="space-y-2">
        <div class="flex justify-between items-center">
          <span class="text-slate-400">Actifs</span>
          <div class="flex items-center space-x-2">
            <span class="text-sm text-green-400">13 employés</span>
            <button onclick="selectByStatus('active')" class="px-2 py-1 bg-green-600 hover:bg-green-500 rounded text-xs">
              Sélectionner
            </button>
          </div>
        </div>
        <div class="flex justify-between items-center">
          <span class="text-slate-400">Inactifs</span>
          <div class="flex items-center space-x-2">
            <span class="text-sm text-red-400">2 employés</span>
            <button onclick="selectByStatus('inactive')" class="px-2 py-1 bg-red-600 hover:bg-red-500 rounded text-xs">
              Sélectionner
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
// Gestion de la sélection
function selectAll() {
  const checkboxes = document.querySelectorAll('.employee-checkbox:not(:disabled)');
  checkboxes.forEach(cb => {
    if (cb.closest('.employee-item').style.display !== 'none') {
      cb.checked = true;
    }
  });
  updateSelection();
}

function selectNone() {
  const checkboxes = document.querySelectorAll('.employee-checkbox');
  checkboxes.forEach(cb => cb.checked = false);
  updateSelection();
}

function selectByDepartment(department) {
  selectNone();
  const items = document.querySelectorAll(`[data-department="${department}"]`);
  items.forEach(item => {
    if (item.style.display !== 'none') {
      const checkbox = item.querySelector('.employee-checkbox');
      if (checkbox) checkbox.checked = true;
    }
  });
  updateSelection();
}

function selectByStatus(status) {
  selectNone();
  const items = document.querySelectorAll(`[data-status="${status}"]`);
  items.forEach(item => {
    if (item.style.display !== 'none') {
      const checkbox = item.querySelector('.employee-checkbox');
      if (checkbox) checkbox.checked = true;
    }
  });
  updateSelection();
}

// Filtrage
function filterByStatus(status) {
  const items = document.querySelectorAll('.employee-item');
  items.forEach(item => {
    if (status === 'all' || item.getAttribute('data-status') === status) {
      item.style.display = 'block';
    } else {
      item.style.display = 'none';
      // Décocher les éléments cachés
      const checkbox = item.querySelector('.employee-checkbox');
      if (checkbox) checkbox.checked = false;
    }
  });
  updateSelection();
}

// Mise à jour de la sélection
function updateSelection() {
  const checkboxes = document.querySelectorAll('.employee-checkbox:checked');
  const selectedIds = Array.from(checkboxes).map(cb => cb.value);
  
  // Mettre à jour le compteur
  document.getElementById('selected-count').textContent = selectedIds.length;
  
  // Mettre à jour le champ caché
  document.getElementById('employee-ids').value = selectedIds.join(',');
  
  // Activer/désactiver le bouton
  const applyButton = document.getElementById('apply-button');
  applyButton.disabled = selectedIds.length === 0;
  
  // Mettre à jour l'aperçu
  updatePreview();
}

// Gestion des champs d'action
function toggleActionFields() {
  const action = document.querySelector('select[name="action"]').value;
  const departmentField = document.getElementById('department-field');
  const positionField = document.getElementById('position-field');
  
  // Cacher tous les champs
  departmentField.classList.add('hidden');
  positionField.classList.add('hidden');
  
  // Afficher le champ approprié
  if (action === 'update_department') {
    departmentField.classList.remove('hidden');
  } else if (action === 'update_position') {
    positionField.classList.remove('hidden');
  }
  
  updatePreview();
}

// Aperçu de l'action
function updatePreview() {
  const selectedCount = document.querySelectorAll('.employee-checkbox:checked').length;
  const action = document.querySelector('select[name="action"]').value;
  const previewText = document.getElementById('preview-text');
  
  if (selectedCount === 0) {
    previewText.textContent = 'Sélectionnez des employés et une action pour voir l\'aperçu';
    return;
  }
  
  let actionText = '';
  switch (action) {
    case 'activate':
      actionText = `Activer ${selectedCount} employé(s)`;
      break;
    case 'deactivate':
      actionText = `Désactiver ${selectedCount} employé(s)`;
      break;
    case 'update_department':
      const newDept = document.querySelector('input[name="new_department"]').value;
      actionText = `Changer le département de ${selectedCount} employé(s)`;
      if (newDept) actionText += ` vers "${newDept}"`;
      break;
    case 'update_position':
      const newPos = document.querySelector('input[name="new_position"]').value;
      actionText = `Changer le poste de ${selectedCount} employé(s)`;
      if (newPos) actionText += ` vers "${newPos}"`;
      break;
    case 'export':
      actionText = `Exporter les données de ${selectedCount} employé(s)`;
      break;
    default:
      actionText = `Action sur ${selectedCount} employé(s)`;
  }
  
  previewText.textContent = actionText;
}

// Écouter les changements
document.addEventListener('DOMContentLoaded', function() {
  // Écouter les changements sur les champs
  const newDeptField = document.querySelector('input[name="new_department"]');
  const newPosField = document.querySelector('input[name="new_position"]');
  
  if (newDeptField) newDeptField.addEventListener('input', updatePreview);
  if (newPosField) newPosField.addEventListener('input', updatePreview);
  
  // Initialiser l'aperçu
  updatePreview();
});

// Confirmation avant soumission
document.querySelector('form').addEventListener('submit', function(e) {
  const selectedCount = document.querySelectorAll('.employee-checkbox:checked').length;
  const action = document.querySelector('select[name="action"]').value;
  
  if (selectedCount === 0) {
    e.preventDefault();
    alert('Veuillez sélectionner au moins un employé');
    return;
  }
  
  let confirmText = `Confirmer l'action sur ${selectedCount} employé(s) ?`;
  if (!confirm(confirmText)) {
    e.preventDefault();
  }
});
</script>
{% endblock %}