from __future__ import annotations

from flask_wtf import FlaskForm
from wtforms import (
    StringField, TextAreaField, SelectField, IntegerField, DecimalField,
    DateTimeField, BooleanField, FieldList, FormField, HiddenField,
    SelectMultipleField
)
from wtforms.validators import DataRequired, Length, NumberRange, Optional
from wtforms.widgets import TextArea


class WorkCenterForm(FlaskForm):
    """Formulaire pour créer/éditer un centre de travail"""
    name = StringField(
        "Nom du centre de travail",
        validators=[DataRequired(), Length(min=1, max=120)],
        render_kw={"placeholder": "Ex: Usinage, Assemblage, Peinture..."}
    )
    
    description = TextAreaField(
        "Description",
        validators=[Optional(), Length(max=500)],
        render_kw={"placeholder": "Description détaillée du centre de travail", "rows": 3}
    )
    
    capacity_per_hour = DecimalField(
        "Capacité par heure",
        validators=[DataRequired(), NumberRange(min=0.1, max=1000)],
        default=1.0,
        render_kw={"placeholder": "1.0", "step": "0.1"}
    )
    
    cost_per_hour = DecimalField(
        "Coût par heure (€)",
        validators=[DataRequired(), NumberRange(min=0, max=10000)],
        default=0.0,
        render_kw={"placeholder": "0.00", "step": "0.01"}
    )
    
    is_active = BooleanField(
        "Centre actif",
        default=True
    )


class BOMItemForm(FlaskForm):
    """Formulaire pour un item de nomenclature"""
    product_id = SelectField(
        "Produit/Matériau",
        validators=[DataRequired()],
        coerce=int,
        choices=[]
    )
    
    quantity = DecimalField(
        "Quantité",
        validators=[DataRequired(), NumberRange(min=0.001, max=10000)],
        default=1.0,
        render_kw={"placeholder": "1.000", "step": "0.001"}
    )
    
    unit_cost = DecimalField(
        "Coût unitaire (€)",
        validators=[DataRequired(), NumberRange(min=0, max=10000)],
        default=0.0,
        render_kw={"placeholder": "0.00", "step": "0.01"}
    )
    
    notes = TextAreaField(
        "Notes",
        validators=[Optional(), Length(max=300)],
        render_kw={"placeholder": "Notes optionnelles...", "rows": 2}
    )


class ProductionStepForm(FlaskForm):
    """Formulaire pour une étape de production"""
    work_center_id = SelectField(
        "Centre de travail",
        validators=[DataRequired()],
        coerce=int,
        choices=[]
    )
    
    step_number = IntegerField(
        "Numéro d'étape",
        validators=[DataRequired(), NumberRange(min=1, max=100)],
        default=1
    )
    
    name = StringField(
        "Nom de l'étape",
        validators=[DataRequired(), Length(min=1, max=120)],
        render_kw={"placeholder": "Ex: Découpe, Assemblage, Contrôle..."}
    )
    
    description = TextAreaField(
        "Description",
        validators=[Optional(), Length(max=500)],
        render_kw={"placeholder": "Description détaillée de l'étape", "rows": 3}
    )
    
    setup_time_minutes = IntegerField(
        "Temps de préparation (minutes)",
        validators=[DataRequired(), NumberRange(min=0, max=1440)],
        default=0
    )
    
    run_time_minutes = IntegerField(
        "Temps d'exécution (minutes)",
        validators=[DataRequired(), NumberRange(min=1, max=1440)],
        default=60
    )
    
    is_optional = BooleanField(
        "Étape optionnelle",
        default=False
    )


class BOMForm(FlaskForm):
    """Formulaire pour créer/éditer une nomenclature (BOM)"""
    product_id = SelectField(
        "Produit final",
        validators=[DataRequired()],
        coerce=int,
        choices=[]
    )
    
    name = StringField(
        "Nom de la nomenclature",
        validators=[DataRequired(), Length(min=1, max=120)],
        render_kw={"placeholder": "Ex: BOM iPhone 15 Pro Max"}
    )
    
    description = TextAreaField(
        "Description",
        validators=[Optional(), Length(max=500)],
        render_kw={"placeholder": "Description de la nomenclature", "rows": 3}
    )
    
    version = StringField(
        "Version",
        validators=[DataRequired(), Length(min=1, max=20)],
        default="1.0",
        render_kw={"placeholder": "1.0"}
    )
    
    quantity_produced = DecimalField(
        "Quantité produite",
        validators=[DataRequired(), NumberRange(min=0.001, max=10000)],
        default=1.0,
        render_kw={"placeholder": "1.000", "step": "0.001"}
    )
    
    is_active = BooleanField(
        "Nomenclature active",
        default=True
    )


class ProductionOrderForm(FlaskForm):
    """Formulaire pour créer/éditer un ordre de production"""
    bom_id = SelectField(
        "Nomenclature (BOM)",
        validators=[DataRequired()],
        coerce=int,
        choices=[]
    )
    
    order_number = StringField(
        "Numéro d'ordre",
        validators=[Optional(), Length(max=50)],
        render_kw={"placeholder": "Généré automatiquement si vide"}
    )
    
    quantity_to_produce = DecimalField(
        "Quantité à produire",
        validators=[DataRequired(), NumberRange(min=0.001, max=100000)],
        default=1.0,
        render_kw={"placeholder": "1.000", "step": "0.001"}
    )
    
    priority = SelectField(
        "Priorité",
        choices=[
            ("low", "Faible"),
            ("normal", "Normale"),
            ("high", "Élevée"),
            ("urgent", "Urgente")
        ],
        default="normal"
    )
    
    planned_start_date = DateTimeField(
        "Date de début prévue",
        validators=[Optional()],
        format='%Y-%m-%d %H:%M'
    )
    
    planned_end_date = DateTimeField(
        "Date de fin prévue",
        validators=[Optional()],
        format='%Y-%m-%d %H:%M'
    )
    
    notes = TextAreaField(
        "Notes",
        validators=[Optional(), Length(max=1000)],
        render_kw={"placeholder": "Notes et instructions spéciales...", "rows": 4}
    )


class EditProductionOrderForm(FlaskForm):
    """Formulaire pour éditer un ordre de production existant"""
    status = SelectField(
        "Statut",
        choices=[
            ("draft", "Brouillon"),
            ("planned", "Planifié"),
            ("released", "Libéré"),
            ("in_progress", "En cours"),
            ("completed", "Terminé"),
            ("cancelled", "Annulé")
        ],
        validators=[DataRequired()]
    )
    
    quantity_produced = DecimalField(
        "Quantité produite",
        validators=[Optional(), NumberRange(min=0, max=100000)],
        default=0.0,
        render_kw={"placeholder": "0.000", "step": "0.001"}
    )
    
    actual_start_date = DateTimeField(
        "Date de début réelle",
        validators=[Optional()],
        format='%Y-%m-%d %H:%M'
    )
    
    actual_end_date = DateTimeField(
        "Date de fin réelle",
        validators=[Optional()],
        format='%Y-%m-%d %H:%M'
    )
    
    actual_cost = DecimalField(
        "Coût réel (€)",
        validators=[Optional(), NumberRange(min=0, max=1000000)],
        default=0.0,
        render_kw={"placeholder": "0.00", "step": "0.01"}
    )
    
    notes = TextAreaField(
        "Notes",
        validators=[Optional(), Length(max=1000)],
        render_kw={"placeholder": "Notes et observations...", "rows": 4}
    )


class ProductionOrderStepForm(FlaskForm):
    """Formulaire pour gérer une étape d'ordre de production"""
    status = SelectField(
        "Statut de l'étape",
        choices=[
            ("pending", "En attente"),
            ("in_progress", "En cours"),
            ("completed", "Terminée"),
            ("skipped", "Ignorée")
        ],
        validators=[DataRequired()]
    )
    
    assigned_user_id = SelectField(
        "Opérateur assigné",
        validators=[Optional()],
        coerce=int,
        choices=[]
    )
    
    actual_start_time = DateTimeField(
        "Heure de début réelle",
        validators=[Optional()],
        format='%Y-%m-%d %H:%M'
    )
    
    actual_end_time = DateTimeField(
        "Heure de fin réelle",
        validators=[Optional()],
        format='%Y-%m-%d %H:%M'
    )
    
    quantity_processed = DecimalField(
        "Quantité traitée",
        validators=[Optional(), NumberRange(min=0, max=100000)],
        default=0.0,
        render_kw={"placeholder": "0.000", "step": "0.001"}
    )
    
    quality_check_passed = BooleanField(
        "Contrôle qualité réussi",
        default=True
    )
    
    notes = TextAreaField(
        "Notes de l'étape",
        validators=[Optional(), Length(max=500)],
        render_kw={"placeholder": "Observations, problèmes, remarques...", "rows": 3}
    )


class MaterialConsumptionForm(FlaskForm):
    """Formulaire pour enregistrer la consommation de matériaux"""
    product_id = SelectField(
        "Matériau",
        validators=[DataRequired()],
        coerce=int,
        choices=[]
    )
    
    planned_quantity = DecimalField(
        "Quantité prévue",
        validators=[DataRequired(), NumberRange(min=0.001, max=100000)],
        render_kw={"placeholder": "0.000", "step": "0.001"}
    )
    
    actual_quantity = DecimalField(
        "Quantité réelle",
        validators=[DataRequired(), NumberRange(min=0, max=100000)],
        render_kw={"placeholder": "0.000", "step": "0.001"}
    )
    
    unit_cost = DecimalField(
        "Coût unitaire (€)",
        validators=[DataRequired(), NumberRange(min=0, max=10000)],
        render_kw={"placeholder": "0.00", "step": "0.01"}
    )
    
    notes = TextAreaField(
        "Notes",
        validators=[Optional(), Length(max=300)],
        render_kw={"placeholder": "Remarques sur la consommation...", "rows": 2}
    )


class QualityCheckForm(FlaskForm):
    """Formulaire pour un contrôle qualité"""
    check_type = SelectField(
        "Type de contrôle",
        choices=[
            ("visual", "Contrôle visuel"),
            ("measurement", "Mesure dimensionnelle"),
            ("functional", "Test fonctionnel"),
            ("electrical", "Test électrique"),
            ("pressure", "Test de pression"),
            ("other", "Autre")
        ],
        validators=[DataRequired()]
    )
    
    description = TextAreaField(
        "Description du contrôle",
        validators=[DataRequired(), Length(min=1, max=500)],
        render_kw={"placeholder": "Décrivez le contrôle effectué...", "rows": 3}
    )
    
    passed = BooleanField(
        "Contrôle réussi",
        default=True
    )
    
    measured_value = StringField(
        "Valeur mesurée",
        validators=[Optional(), Length(max=100)],
        render_kw={"placeholder": "Ex: 15.2 mm, 220V, OK..."}
    )
    
    expected_value = StringField(
        "Valeur attendue",
        validators=[Optional(), Length(max=100)],
        render_kw={"placeholder": "Ex: 15.0 ± 0.5 mm, 230V ± 10V..."}
    )
    
    tolerance = StringField(
        "Tolérance",
        validators=[Optional(), Length(max=50)],
        render_kw={"placeholder": "Ex: ± 0.5 mm, ± 5%..."}
    )
    
    notes = TextAreaField(
        "Observations",
        validators=[Optional(), Length(max=500)],
        render_kw={"placeholder": "Observations et commentaires...", "rows": 3}
    )


class ProductionSearchForm(FlaskForm):
    """Formulaire de recherche et filtrage pour la production"""
    search = StringField(
        "Recherche",
        validators=[Optional(), Length(max=100)],
        render_kw={"placeholder": "Numéro d'ordre, produit, BOM..."}
    )
    
    status = SelectField(
        "Statut",
        choices=[
            ("", "Tous les statuts"),
            ("draft", "Brouillon"),
            ("planned", "Planifié"),
            ("released", "Libéré"),
            ("in_progress", "En cours"),
            ("completed", "Terminé"),
            ("cancelled", "Annulé")
        ],
        default=""
    )
    
    priority = SelectField(
        "Priorité",
        choices=[
            ("", "Toutes les priorités"),
            ("low", "Faible"),
            ("normal", "Normale"),
            ("high", "Élevée"),
            ("urgent", "Urgente")
        ],
        default=""
    )
    
    work_center_id = SelectField(
        "Centre de travail",
        choices=[],
        coerce=int,
        default=""
    )
    
    date_range = SelectField(
        "Période",
        choices=[
            ("", "Toutes les dates"),
            ("today", "Aujourd'hui"),
            ("week", "Cette semaine"),
            ("month", "Ce mois"),
            ("overdue", "En retard")
        ],
        default=""
    )
    
    sort_by = SelectField(
        "Trier par",
        choices=[
            ("created_desc", "Date de création (récent)"),
            ("created_asc", "Date de création (ancien)"),
            ("start_date_asc", "Date de début (proche)"),
            ("end_date_asc", "Date de fin (proche)"),
            ("priority", "Priorité"),
            ("status", "Statut"),
            ("order_number", "Numéro d'ordre")
        ],
        default="created_desc"
    )


class BulkProductionUpdateForm(FlaskForm):
    """Formulaire pour mise à jour en lot des ordres de production"""
    order_ids = HiddenField("IDs des ordres", validators=[DataRequired()])
    
    action = SelectField(
        "Action",
        choices=[
            ("update_status", "Changer le statut"),
            ("update_priority", "Changer la priorité"),
            ("assign_worker", "Assigner un opérateur"),
            ("reschedule", "Reprogrammer")
        ],
        validators=[DataRequired()]
    )
    
    new_status = SelectField(
        "Nouveau statut",
        choices=[
            ("draft", "Brouillon"),
            ("planned", "Planifié"),
            ("released", "Libéré"),
            ("in_progress", "En cours"),
            ("completed", "Terminé"),
            ("cancelled", "Annulé")
        ],
        validators=[Optional()]
    )
    
    new_priority = SelectField(
        "Nouvelle priorité",
        choices=[
            ("low", "Faible"),
            ("normal", "Normale"),
            ("high", "Élevée"),
            ("urgent", "Urgente")
        ],
        validators=[Optional()]
    )
    
    assigned_user_id = SelectField(
        "Opérateur à assigner",
        validators=[Optional()],
        coerce=int,
        choices=[]
    )
    
    new_start_date = DateTimeField(
        "Nouvelle date de début",
        validators=[Optional()],
        format='%Y-%m-%d %H:%M'
    )
    
    new_end_date = DateTimeField(
        "Nouvelle date de fin",
        validators=[Optional()],
        format='%Y-%m-%d %H:%M'
    )