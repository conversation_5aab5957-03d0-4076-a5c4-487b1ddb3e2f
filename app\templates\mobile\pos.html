<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#007bff">
    <meta name="description" content="POS System GT2 - Application Mobile">
    
    <title>POS Mobile</title>
    
    <!-- PWA Manifest -->
    <link rel="manifest" href="/static/pwa/manifest.json">
    
    <!-- Icons -->
    <link rel="icon" type="image/png" sizes="192x192" href="/static/pwa/icons/icon-192x192.png">
    <link rel="apple-touch-icon" sizes="180x180" href="/static/pwa/icons/icon-180x180.png">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="/static/css/mobile.css">
</head>
<body>
    <div class="pos-interface">
        <!-- Header -->
        <header class="pos-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h5 mb-0">POS Mobile</h1>
                    <small id="connectionStatus">En ligne</small>
                </div>
                <div>
                    <button class="btn btn-sm btn-light me-2" id="syncButton">
                        <i class="fas fa-sync"></i>
                    </button>
                    <button class="btn btn-sm btn-light" id="menuButton">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
        </header>
        
        <!-- Content -->
        <main class="pos-content">
            <!-- Catégories -->
            <div class="categories-scroll mb-3">
                <div class="d-flex overflow-auto">
                    <button class="btn btn-outline-primary btn-sm me-2 active">Tous</button>
                    <button class="btn btn-outline-secondary btn-sm me-2">Burgers</button>
                    <button class="btn btn-outline-secondary btn-sm me-2">Boissons</button>
                    <button class="btn btn-outline-secondary btn-sm me-2">Desserts</button>
                    <button class="btn btn-outline-secondary btn-sm">Extras</button>
                </div>
            </div>
            
            <!-- Grille de produits -->
            <div class="products-grid">
                <div class="product-item" data-product-id="1">
                    <div class="product-image bg-light d-flex align-items-center justify-content-center">
                        <i class="fas fa-hamburger fa-2x text-muted"></i>
                    </div>
                    <div class="product-name">Burger Classique</div>
                    <div class="product-price">€8.50</div>
                </div>
                
                <div class="product-item" data-product-id="2">
                    <div class="product-image bg-light d-flex align-items-center justify-content-center">
                        <i class="fas fa-french-fries fa-2x text-muted"></i>
                    </div>
                    <div class="product-name">Frites</div>
                    <div class="product-price">€3.50</div>
                </div>
                
                <div class="product-item" data-product-id="3">
                    <div class="product-image bg-light d-flex align-items-center justify-content-center">
                        <i class="fas fa-wine-bottle fa-2x text-muted"></i>
                    </div>
                    <div class="product-name">Coca-Cola</div>
                    <div class="product-price">€2.50</div>
                </div>
                
                <div class="product-item" data-product-id="4">
                    <div class="product-image bg-light d-flex align-items-center justify-content-center">
                        <i class="fas fa-ice-cream fa-2x text-muted"></i>
                    </div>
                    <div class="product-name">Glace Vanille</div>
                    <div class="product-price">€4.00</div>
                </div>
                
                <div class="product-item" data-product-id="5">
                    <div class="product-image bg-light d-flex align-items-center justify-content-center">
                        <i class="fas fa-pizza-slice fa-2x text-muted"></i>
                    </div>
                    <div class="product-name">Pizza</div>
                    <div class="product-price">€12.00</div>
                </div>
                
                <div class="product-item" data-product-id="6">
                    <div class="product-image bg-light d-flex align-items-center justify-content-center">
                        <i class="fas fa-coffee fa-2x text-muted"></i>
                    </div>
                    <div class="product-name">Café</div>
                    <div class="product-price">€2.00</div>
                </div>
            </div>
            
            <!-- Panier -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Panier (<span id="cartCount">0</span>)</h6>
                </div>
                <div class="card-body">
                    <div id="cartItems">
                        <p class="text-muted mb-0">Panier vide</p>
                    </div>
                </div>
            </div>
            
            <!-- Boutons d'action -->
            <div class="action-buttons">
                <button class="action-button btn-pay" id="payButton">
                    <i class="fas fa-euro-sign me-2"></i>Payer
                </button>
                <button class="action-button btn-cancel" id="cancelButton">
                    <i class="fas fa-times me-2"></i>Annuler
                </button>
                <button class="action-button btn-discount" id="discountButton">
                    <i class="fas fa-percent me-2"></i>Réduction
                </button>
            </div>
            
            <!-- Clavier numérique -->
            <div class="numpad">
                <button class="numpad-button">1</button>
                <button class="numpad-button">2</button>
                <button class="numpad-button">3</button>
                <button class="numpad-button">4</button>
                <button class="numpad-button">5</button>
                <button class="numpad-button">6</button>
                <button class="numpad-button">7</button>
                <button class="numpad-button">8</button>
                <button class="numpad-button">9</button>
                <button class="numpad-button function">C</button>
                <button class="numpad-button">0</button>
                <button class="numpad-button function">←</button>
            </div>
        </main>
        
        <!-- Footer -->
        <footer class="pos-footer">
            <div class="d-flex justify-content-between">
                <div>
                    <small>Commandes: <span id="orderCount">0</span></small>
                </div>
                <div>
                    <small>Total: <strong id="totalAmount">€0.00</strong></small>
                </div>
            </div>
        </footer>
    </div>
    
    <!-- Toast Container -->
    <div id="toastContainer" class="toast-container"></div>
    
    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/mobile.js"></script>
    
    <!-- Service Worker Registration -->
    <script>
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('/static/pwa/service-worker.js')
                    .then(function(registration) {
                        console.log('Service Worker enregistré avec succès:', registration);
                    })
                    .catch(function(error) {
                        console.log('Erreur lors de l\'enregistrement du Service Worker:', error);
                    });
            });
        }
    </script>
</body>
</html>