{% extends 'base.html' %}
{% block title %}Modifier Recette - {{ recipe.name }}{% endblock %}
{% block content %}
<div class="max-w-4xl mx-auto">
  <div class="flex items-center justify-between mb-6">
    <div>
      <h1 class="text-3xl font-bold">Modifier la Recette</h1>
      <p class="text-slate-400 mt-2">{{ recipe.name }} - Produit : {{ recipe.product.name }}</p>
    </div>
    <div class="flex space-x-3">
      <a href="{{ url_for('catalog.product_recipes', id=recipe.product_id_fk) }}" class="px-4 py-2 bg-slate-700 hover:bg-slate-600 text-white rounded-lg">
        Retour aux recettes
      </a>
      <a href="{{ url_for('catalog.index') }}" class="px-4 py-2 bg-slate-600 hover:bg-slate-500 text-white rounded-lg">
        Catalogue
      </a>
    </div>
  </div>

  <div class="rounded-xl bg-slate-900 border border-slate-700 p-6">
    <form method="post" class="space-y-6">
      {{ form.csrf_token }}
      
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div>
          <label class="block text-sm font-medium text-slate-300 mb-2">{{ form.name.label.text }}</label>
          {{ form.name(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent") }}
          {% if form.name.errors %}
            <div class="mt-1 text-sm text-red-400">
              {% for error in form.name.errors %}
                <p>{{ error }}</p>
              {% endfor %}
            </div>
          {% endif %}
        </div>
        
        <div>
          <label class="block text-sm font-medium text-slate-300 mb-2">{{ form.servings.label.text }}</label>
          {{ form.servings(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent") }}
          {% if form.servings.errors %}
            <div class="mt-1 text-sm text-red-400">
              {% for error in form.servings.errors %}
                <p>{{ error }}</p>
              {% endfor %}
            </div>
          {% endif %}
        </div>
      </div>
      
      <div>
        <label class="block text-sm font-medium text-slate-300 mb-2">{{ form.description.label.text }}</label>
        {{ form.description(rows="4", class="w-full bg-slate-800 border border-slate-700 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent") }}
        {% if form.description.errors %}
          <div class="mt-1 text-sm text-red-400">
            {% for error in form.description.errors %}
              <p>{{ error }}</p>
            {% endfor %}
          </div>
        {% endif %}
      </div>
      
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div>
          <label class="block text-sm font-medium text-slate-300 mb-2">{{ form.preparation_time_minutes.label.text }}</label>
          {{ form.preparation_time_minutes(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent") }}
          {% if form.preparation_time_minutes.errors %}
            <div class="mt-1 text-sm text-red-400">
              {% for error in form.preparation_time_minutes.errors %}
                <p>{{ error }}</p>
              {% endfor %}
            </div>
          {% endif %}
        </div>
        
        <div>
          <label class="block text-sm font-medium text-slate-300 mb-2">{{ form.cooking_time_minutes.label.text }}</label>
          {{ form.cooking_time_minutes(class="w-full bg-slate-800 border border-slate-700 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent") }}
          {% if form.cooking_time_minutes.errors %}
            <div class="mt-1 text-sm text-red-400">
              {% for error in form.cooking_time_minutes.errors %}
                <p>{{ error }}</p>
              {% endfor %}
            </div>
          {% endif %}
        </div>
      </div>
      
      <div class="flex justify-end space-x-3 pt-6 border-t border-slate-700">
        <a href="{{ url_for('catalog.product_recipes', id=recipe.product_id_fk) }}" class="px-6 py-3 bg-slate-700 hover:bg-slate-600 text-white rounded-lg transition-colors">
          Annuler
        </a>
        {{ form.submit(class="px-6 py-3 bg-cyan-600 hover:bg-cyan-500 text-white rounded-lg transition-colors font-medium") }}
      </div>
    </form>
  </div>
  
  <div class="rounded-xl bg-slate-900 border border-slate-700 p-6 mt-6">
    <h3 class="text-lg font-semibold mb-4">Informations de la recette</h3>
    <div class="grid grid-cols-2 gap-4 text-sm">
      <div>
        <span class="text-slate-400">Créée le:</span>
        <span class="text-white ml-2">{{ recipe.created_at.strftime('%d/%m/%Y') if recipe.created_at else 'N/A' }}</span>
      </div>
      <div>
        <span class="text-slate-400">Modifiée le:</span>
        <span class="text-white ml-2">{{ recipe.updated_at.strftime('%d/%m/%Y') if recipe.updated_at else 'N/A' }}</span>
      </div>
      <div>
        <span class="text-slate-400">Produit:</span>
        <span class="text-white ml-2">{{ recipe.product.name }}</span>
      </div>
      <div>
        <span class="text-slate-400">SKU Produit:</span>
        <span class="text-white font-mono ml-2">{{ recipe.product.sku }}</span>
      </div>
    </div>
  </div>
</div>
{% endblock %}